﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class ConjuntoService : ServiceBase, IConjuntoService
    {
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IConjuntoRepository _conjuntoRepository;
        private readonly IConjuntoEmpresaRepository _conjuntoEmpresaRepository;

        public ConjuntoService(IProprietarioRepository proprietarioRepository, IConjuntoRepository conjuntoRepository, IConjuntoEmpresaRepository conjuntoEmpresaRepository)
        {
            _proprietarioRepository = proprietarioRepository;
            _conjuntoRepository = conjuntoRepository;
            _conjuntoEmpresaRepository = conjuntoEmpresaRepository;
        }
        
        public ValidationResult IsValidToCrud(Conjunto conjunto, EProcesso processo)
        {
            var validation = new ValidationResult();
            var repository = _conjuntoRepository;
            var verbo = processo == EProcesso.Create ? "montar" : "alterar";

            if (string.IsNullOrEmpty(conjunto.PlacaCavalo))
                validation.Add($"Informe um cavalo para {verbo} o conjunto!");

            if (conjunto.IdMotoristaBase == 0)
                validation.Add("Informe o motorista do conjunto!");


            if (processo == EProcesso.Create)
            {
                var conjuntos = repository
                    .Find(x =>
                        x.PlacaCavalo == conjunto.PlacaCavalo &&
                        x.IdMotoristaBase == conjunto.IdMotoristaBase
                    )
                    .Include(x => x.Carretas).ToList();

                if (conjuntos.Count() > 0)
                {
                    foreach (var carreta in conjunto.Carretas)
                    {
                        if (carreta.IdTipoCarreta == 0)
                            validation.Add($"Informe um tipo de carreta válido para a placa {carreta.Placa}!");

                        conjuntos = conjuntos
                            .Where(x => x.Carretas.Select(z => z.Placa).Contains(carreta.Placa) && conjunto.Carretas.Count() == x.Carretas.Count())
                            .ToList();
                    }

                    if (conjuntos.Any())
                    {
                        return validation.Add("Conjunto já cadastrado!");
                    }

                }
            }

            if (processo == EProcesso.Update)
            {
                if (conjunto.IdTipoCavalo == 0)
                    validation.Add($"Informe um tipo de veículo para a placa {conjunto.PlacaCavalo}!");

                foreach (var carreta in conjunto.Carretas)
                {
                    if (carreta.IdTipoCarreta == 0)
                        validation.Add($"Informe um tipo de carreta válido para a placa {carreta.Placa}!");
                }
            }
            return validation;
        }

        public Conjunto GetConjuntoByPlacaCPF(string CPF, string placa)
        {
            var montado = _conjuntoRepository
               .Find(x => x.PlacaCavalo == placa && x.StatusConjunto == EStatusConjunto.Montado)
               .FirstOrDefault();

            if (montado != null)
                return montado;

            return _conjuntoRepository
               .Find(x => x.PlacaCavalo == placa)
               .OrderByDescending(x => x.DataCadastro)
               .ThenByDescending(x => x.DataAtualizacao)
               .FirstOrDefault();
        }



        public Conjunto Get(int id)
        {
            return _conjuntoRepository.Get(id);
        }

        public ValidationResult Add(Conjunto entity)
        {
            try
            {
                ValidationResult validation = IsValidToCrud(entity, EProcesso.Create);

                if (!validation.IsValid)
                    return validation;

                entity.DataCadastro = DateTime.Now;

                _conjuntoRepository.Add(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Conjunto entity)
        {
            try
            {
                ValidationResult validation = IsValidToCrud(entity, EProcesso.Update);

                entity.DataAtualizacao = DateTime.Now;

                if (!validation.IsValid)
                    return validation;

                _conjuntoRepository.Update(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public List<Conjunto> GetTodos(int IdMotoristaBase)
        {
            return _conjuntoRepository.Find(x => x.IdMotoristaBase == IdMotoristaBase).ToList();
        }



        public IQueryable<Conjunto> GetQuery(int idConjunto)
        {
            return _conjuntoRepository.Where(c => c.IdConjunto == idConjunto);
        }




        public List<Conjunto> GetMotoristasBaseByPlaca(string placa)
        {
            return _conjuntoRepository.Find(x => x.PlacaCavalo == placa).ToList();
        }

        public List<Conjunto> GetConjuntoMontadoByPlaca(string placa)
        {
            return _conjuntoRepository
                            .Find(x => x.PlacaCavalo == placa && x.StatusConjunto == EStatusConjunto.Montado)
                            .OrderByDescending(x => x.DataIntegracaoGR)
                            .ToList();
        }

        public string GetByCPFPlacaConjuntoMontado(string CPF)
        {
            return _conjuntoRepository
                .Find(x => x.StatusConjunto == EStatusConjunto.Montado)
                .Select(x => x.PlacaCavalo)
                .FirstOrDefault();
        }

        public ValidationResult AddGestorFrota(int IdConjunto, int IdGestorFrota)
        {
            var repository = _conjuntoRepository;

            var validationResult = new ValidationResult();

            Conjunto conjunto = Get(IdConjunto);

            if (conjunto == null)
                return validationResult.Add("conjunto não encontrado!");

            conjunto.IdGestorFrota = IdGestorFrota;

            repository.Update(conjunto);

            return validationResult;

        }

        public ValidationResult RemoveGestorFrota(List<int> IdsConjunto, int IdGestorFrota)
        {
            var repository = _conjuntoRepository;

            var validationResult = new ValidationResult();

            var conjuntos = GetByIdGestor(IdGestorFrota);

            if (conjuntos == null)
                return validationResult.Add("conjunto não encontrado!");

            //            conjunto.IdGestorFrota = IdGestorFrota;

            var conjsDesvincular = conjuntos.Where(x => !IdsConjunto.Contains(x.IdConjunto)).ToList();

            foreach (var conjunto in conjsDesvincular)
            {
                conjunto.IdGestorFrota = null;
                repository.Update(conjunto);
            }

            return validationResult;
        }

        public ValidationResult AtulizarStatusConjunto(int IdConjunto, string cpf)
        {
            var repository = _conjuntoRepository;

            var validationResult = new ValidationResult();

            Conjunto conjDesmontar = repository
                .FirstOrDefault(x => x.StatusConjunto == EStatusConjunto.Montado);

            if (conjDesmontar != null)
            {
                conjDesmontar.StatusConjunto = EStatusConjunto.Desmontado;
                conjDesmontar.DataAtualizacao = DateTime.Now;
                repository.Update(conjDesmontar);
            }

            Conjunto conjunto = Get(IdConjunto);

            if (conjunto == null)
                return validationResult.Add("Conjunto não encontrado!");

            conjunto.StatusConjunto = EStatusConjunto.Montado;
            conjunto.DataAtualizacao = DateTime.Now;

            repository.Update(conjunto);

            return validationResult;
        }

        public ValidationResult AlterarTipoVeiculoConjunto(int idConjunto, string placa, bool ehCavalo, int? tipoVeiculo)
        {
            ValidationResult validation = new ValidationResult();
            var repositorioConjunto = _conjuntoRepository;
            if (ehCavalo)
            {
                var cavaloBanco = repositorioConjunto.Find(x => x.IdConjunto == idConjunto && x.PlacaCavalo == placa).FirstOrDefault();
                if (cavaloBanco != null && cavaloBanco.IdTipoCavalo != tipoVeiculo )
                {
                    cavaloBanco.IdTipoCavalo = tipoVeiculo.Value;
                    //cavaloBanco.StatusGR = EStatusGR.PreCadastro;
                    repositorioConjunto.Update(cavaloBanco);
                }
                else
                {
                    return validation.Add("Veiculo não encontrado");
                }
            }
            else
            {
                var carretasBanco = repositorioConjunto.Find(x => x.IdConjunto == idConjunto).Select(x => x.Carretas).FirstOrDefault();
                if (carretasBanco != null)
                {
                    var carretaEncontrada = carretasBanco.Find(x => x.Placa == placa && x.IdConjunto == idConjunto);
                    if (carretaEncontrada != null && carretaEncontrada.IdTipoCarreta != tipoVeiculo)
                    {
                        var conjunto = repositorioConjunto.Find(x => x.IdConjunto == idConjunto).FirstOrDefault();
                        carretaEncontrada.IdTipoCarreta = tipoVeiculo;
                        conjunto.Carretas = carretasBanco;
                        //conjunto.StatusGR = EStatusGR.PreCadastro;
                        repositorioConjunto.Update(conjunto);
                    }
                }
                else
                {
                    return validation.Add("Nenhuma carreta foi encontrada");
                }
            }
            return validation;
        }

        public List<Conjunto> GetByIdGestor(int IdGestorFrota)
        {
            return _conjuntoRepository
                .Find(x => x.IdGestorFrota == IdGestorFrota && x.StatusConjunto == EStatusConjunto.Montado)
                .Include(x => x.Carretas)
                .ToList();
        }

        private bool CanUpdateType(Conjunto @new, Conjunto old)
        {
            var hasChanged = false;

            if (old.Carretas != null && @new.Carretas != null)
            {
                hasChanged =  old.Carretas.Any(x => @new.Carretas.Any(y => y.Placa == x.Placa && y.IdTipoCarreta != x.IdTipoCarreta));
            }

            return (@new.IdTipoCavalo != old.IdTipoCavalo || hasChanged );
        }

        public ValidationResult AtualizarTipos(int IdConjunto, Conjunto conjunto)
        {

            var conjuntoRepository = _conjuntoRepository;

            var conjuntoBanco = conjuntoRepository
                .Include(x => x.Carretas)
                .FirstOrDefault(x => x.IdConjunto == IdConjunto);

            if (!CanUpdateType(conjunto, conjuntoBanco))
                return new ValidationResult();

            if (conjuntoBanco != null)
            {

                

                conjuntoBanco.IdTipoCavalo = conjunto.IdTipoCavalo;

                var carretas = new List<ConjuntoCarreta>();

                foreach (var carreta in conjuntoBanco.Carretas)
                {
                    var carretaUpdate = conjunto.Carretas.FirstOrDefault(x => x.Placa == carreta.Placa);

                    if (carretaUpdate == null)
                    {
                        carretas.Add(carreta);
                        continue;
                    }

                    carreta.IdTipoCarreta = carretaUpdate.IdTipoCarreta;
                    carretas.Add(carreta);
                }

                conjuntoBanco.Carretas = carretas;

                
                //conjuntoBanco.StatusGR = EStatusGR.PreCadastro;
                conjuntoBanco.StatusConjunto = EStatusConjunto.Montado;

                conjuntoRepository.Update( conjuntoBanco);

            }

            return new ValidationResult();
        }

        public List<Conjunto> GetByCPFCNPJGestor(List<string> CPFCNPJs)
        {

            var idsProprietario = new List<int?>();

            foreach (var cpfcnpj in CPFCNPJs)
            {
                var idProprietario = _proprietarioRepository.GetIdPorCpfCnpj(cpfcnpj);
                if (idProprietario.HasValue)
                    idsProprietario.Add(idProprietario.Value);
            }

            var conjuntosEmpresa = _conjuntoEmpresaRepository
                .Find(x => idsProprietario.Contains(x.Veiculo.IdProprietario))
                .Include(x => x.Veiculo)
                .Include(x => x.Veiculo.Proprietario)
                .Select(x => x.IdConjunto);

            return _conjuntoRepository.Where(x => conjuntosEmpresa.Contains(x.IdConjunto) && x.StatusConjunto == EStatusConjunto.Montado)
                .Include(x => x.Carretas)
                .ToList();
        }


        /*public ValidationResult CriarNovoConjunto(ConjuntoModel conjuntoModel, int? idProprietarioBase = null)
        {

            var validation = new ValidationResult();

            if (!conjuntoModel.IdTipoCavalo.HasValue)
                return validation.Add("Conjunto não encontrado, favor informar um tipo veículo para que ele possa ser cadastrado!");

            var conjunto = new Conjunto();

            conjunto.PlacaCavalo = conjuntoModel.PlacaCavalo;
            conjunto.IdTipoCavalo = conjuntoModel.IdTipoCavalo.Value;
            conjunto.StatusGR = EStatusGR.PreCadastro;
            conjunto.DataIntegracaoGR = DateTime.Now;
            conjunto.IdProprietarioBase = idProprietarioBase;

            conjunto.StatusConjunto = EStatusConjunto.Montado;
            conjunto.DataCadastro = DateTime.Now;
            conjunto.Carretas = conjuntoModel?.Carretas?.Select(x => new ConjuntoCarreta()
            {
                IdTipoCarreta = x.IdTipoCarreta,
                Placa = x.Placa
            }).ToList();

            validation = Add(conjunto);

            if (!validation.IsValid)
                return validation;

            var veiculoCavalo = _veiculoService.GetVeiculoPorPlaca(conjunto.PlacaCavalo);

            if (veiculoCavalo != null && conjuntoModel.IdTipoCavalo != null)
            {
                veiculoCavalo.IdTipoCavalo = conjunto.IdTipoCavalo;
                veiculoCavalo.ComTracao = true;
                veiculoCavalo.DataUltimaAtualizacao = DateTime.Now;

                _veiculoRepository.Update(veiculoCavalo);
            }

            if ((conjunto?.Carretas != null && (conjunto?.Carretas.Count() > 0)) && (conjuntoModel.Carretas != null && (conjuntoModel.Carretas.Count() > 0)))
            {
                List<ConjuntoCarreta> conjuntoCarretas = new List<ConjuntoCarreta>();

                foreach (var carreta in conjunto.Carretas)
                {
                    var veiculoCarreta = _veiculoService.GetVeiculoPorPlaca(carreta.Placa);

                    if (veiculoCarreta != null && carreta.IdTipoCarreta != null)
                    {
                        veiculoCarreta.IdTipoCarreta = carreta.IdTipoCarreta;
                        veiculoCarreta.ComTracao = false;
                        veiculoCarreta.DataUltimaAtualizacao = DateTime.Now;

                        _veiculoRepository.Update(veiculoCarreta);
                    }
                }
            }


            //Cria o conjunto empresa

            return validation;
        }*/

        public ValidationResult MontarConjunto(string CPF, int IdConjunto)
        {
            try
            {

                #region Monta conjunto
                var conjunto = Get(IdConjunto);

                if (conjunto != null)
                {
                    conjunto.StatusConjunto = EStatusConjunto.Montado;
                    Update(conjunto);
                }
                #endregion

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

        }

        public ConjuntoEmpresa GetConjuntoEmpresa(int IdConjunto)
        {
            return _conjuntoEmpresaRepository
                .Include(x => x.Motorista)
                .Include(x => x.Veiculo)
                .Include(x => x.Veiculo.Proprietario)
                .Include(x => x.ConjuntosCarrata)
                .Include(x => x.ConjuntosCarrata.Select(y => y.Veiculo))
                .FirstOrDefault(x => x.IdConjunto == IdConjunto);
        }


        


}
}

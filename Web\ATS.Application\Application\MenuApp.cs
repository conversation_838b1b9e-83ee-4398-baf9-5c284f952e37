﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Menu;

namespace ATS.Application.Application
{
    public class MenuApp : AppBase, IMenuApp
    {
        private readonly IMenuService _menuService;
        private readonly IModuloMenuService _moduloMenuService;

        public MenuApp(IMenuService menuService, IModuloMenuService moduloMenuService)
        {
            _menuService = menuService;
            _moduloMenuService = moduloMenuService;
        }

        /// <summary>
        /// Método utilizado para buscar Menu.
        /// </summary>
        /// <param name="id">Id de Menu</param>
        /// <returns>Entidade Menu</returns>
        public Menu Get(int id)
        {
            return _menuService.Get(id);
        }

        /// <summary>
        /// Método utilizado para incluir Menu.
        /// </summary>
        /// <param name="entity">Entidade de Menu</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Menu entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _menuService.Add(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        public List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa)
        {
            return _menuService.GetMenusDisponiveisPorEmpresaModuloAutorizacao(idModulo, idEmpresa, verificarAutorizacaoEmpresa);
        }

        /// <summary>
        /// Método utilizado para alterar Menu.
        /// </summary>
        /// <param name="entity">Entidade de Menu</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Menu entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _menuService.Update(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                var modulosMenu = entity.ModuloMenus.ToList();

                if (entity.ModuloMenus != null)
                {
                    var validationDeleteRange = _moduloMenuService.DeleteRange(entity.IdMenu);
                    if (!validationDeleteRange.IsValid)
                        return validationDeleteRange;
                    foreach (var moduloMenu in modulosMenu)
                    {
                        var validationModulo = _moduloMenuService.Add(moduloMenu);
                        if (!validationModulo.IsValid)
                            return validationModulo;
                    }
                }

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para listar menus ativos
        /// </summary>
        /// <returns>IQueryable de Menu</returns>
        public IQueryable<Menu> All()
        {
            return _menuService.All();
        }

        /// <summary>
        /// Método utilizado para consultar Menu.
        /// </summary>
        /// <param name="descricao">Descrição de Menu</param>
        /// <returns>IQueryable de MenuGrid</returns>
        public IQueryable<MenuGrid> Consultar(string descricao)
        {
            return _menuService.Consultar(descricao);
        }

        /// <summary>
        /// Método utilizado para listar Menu por Grupo de Usuario.
        /// </summary>
        /// <param name="idGrupoUsuario">Id de Grupo de Usuario</param>
        /// <returns>IQueryable de Menu</returns>
        public IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _menuService.GetPorIdGrupoUsuario(idGrupoUsuario);
        }

        /// <summary>
        /// Método utilizado para listar Menu por Grupo de Usuario.
        /// </summary>
        /// <param name="idUsuario">Id de Grupo de Usuario</param>
        /// <param name="idGrupoUsuarioMotorista"></param>
        /// <param name="idGrupoUsuarioProprietario"></param>
        /// <returns>IQueryable de Menu</returns>
        public MenusUsuarioDto GetMenusPermitidos(int idUsuario)
        {
            return new MenusUsuarioDto
            {
                menus = _menuService.GetMenusPermitidos(idUsuario)
            };
        }

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia 
        /// </summary>
        /// <param name="idGrupoUsuario"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _menuService.GetArvoreMenuPorIdGrupoUsuario(idGrupoUsuario);
        }

        /// <summary>
        /// Inativar um menu
        /// </summary>
        /// <param name="idMenu">ID do menu</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idMenu)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _menuService.Inativar(idMenu);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar um menu
        /// </summary>
        /// <param name="idMenu">ID do menu</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idMenu)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _menuService.Reativar(idMenu);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Busca o menu por IdentificadorPermissao
        /// </summary>
        /// <param name="identificadorPermissao">Identificador de Permissao do menu</param>
        /// <returns>Objeto Menu</returns>
        public Menu GetPorIdentificadorPermissao(int identificadorPermissao)
        {
            return _menuService.GetPorIdentificadorPermissao(identificadorPermissao);
        }

        /// <summary>
        /// Retorna os menus pelo módulo
        /// </summary>
        /// <param name="idModulo"></param>
        /// <param name="idGrupoUsuario"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="perfil"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil)
        {
            return _menuService.GetMenusPorModulo(idModulo, idGrupoUsuario, idEmpresa, perfil);
        }

        public object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _menuService.ConsultaGrid(take, page, orderFilters, filters);
        }

        public ValidationResult AlterarStatus(int menuId)
        {
            return _menuService.AlterarStatus(menuId);
        }

        public List<MenusPaiCadastroMenu> GetMenusPai()
        {
            return _menuService.All().Where(o => o.Ativo && o.IsMenuPai).Select(o => new MenusPaiCadastroMenu
            {
                Codigo = o.IdMenu,
                Descricao = o.Descricao
            }).ToList();
        }

        public ValidationResult Cadastrar(MenuCadastroRequest request)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                var response = _menuService.Cadastrar(request);
            
                if (response.IsValid)
                    transaction.Complete();

                return response;   
            }
        }

        public object ObterParaEditar(int id)
        {
            return _menuService.ObterParaEditar(id);
        }
    }
}
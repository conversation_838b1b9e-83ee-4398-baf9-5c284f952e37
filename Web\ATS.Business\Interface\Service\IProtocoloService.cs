﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Models.Protocolo;

namespace ATS.Domain.Interface.Service
{
    public interface IProtocoloService : IService<Protocolo>
    {
        KeyValuePair<ValidationResult, int?> Add(Protocolo protocolo, int? IdUsuario);
        ValidationResult Update(Protocolo protocolo);
        Protocolo Get(int idProtocolo);
        IEnumerable<Protocolo> GetAll();
        List<object> ConsultarDocumentosProtocolo(int idEmpresa, int? idProtocolo);
        object ConsultarGrid(int? idEstabelecimento, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult AddOcorrencia(int idProtocoloEvento, int idMotivo, string descricao, Usuario usuarioLogado);
        ValidationResult ResolverProtocolo(int IdProtocolo, Usuario usuarioLogado);
        ValidationResult Resolver(int IdProtocoloEvento);
        IQueryable<ProtocoloEvento> GetByIdViagemEvento(int idViagemEvento);
        List<Protocolo> ConsultarRelatorioProtocolos(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);

        ValidationResult RealizarAnaliseAbono(int idViagemEvento, int idProtocolo, int idUsuario,
            bool? isCartaFrete, int? idMotivoRejeicao, string descricaoMotivo,
            EStatusAnaliseAbono statusAnaliseAbono);

        Protocolo AfterUpdate(Protocolo newProtocolo_, Protocolo oldProtocolo_,
            bool retornarEventosPagosNoCartao = false);

        ValidationResult Receber(List<int> ids);

        object ConsultarEventosOriginal(int idProtocolo, int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        List<RelatorioEventosVinculados> ConsultarEventosOriginalReport(int idProtocolo, int take, int page,
            OrderFilters order, List<QueryFilters> filters);

        PagamentoFreteModel ConsultarPorToken(string token, Usuario usuarioLogado, List<int> idsEstabelecimentosUsuario, int? idProtocolo);
        ValidationResult SetAnalisado(int? IdProtocoloEvento, bool analisado);
        List<Protocolo> Get(List<int> idsProtocolo);
        IQueryable<Protocolo> GetQuery(List<int> idsProtocolo);
        void EmAnalise(int idProtocolo);
        Protocolo GetComInclude(int idProtocolo);
        List<Protocolo> GetProtocolos(List<int> idsProtocolo);
        object ConsultarDescontosPorId(int idProtocoloEvento);

        List<string> ConsultarPagamentosPorProtocolo(int IdProtocolo);
        Object GetMotivosProtocoloEvento(int IdViagemEvento);
        bool IsRejected(int IdViagemEvento);
        bool ViagemEventoNotAprovadoOuRejeitado(int IdViagemEvento);
        

        byte[] GerarRelatorioGrid(int? idEstabelecimento, int? idEmpresa, OrderFilters order,
            List<QueryFilters> filters, string tipoArquivo, string logo);

        List<PagamentoModel> GetDataToGridAndReport(int? idEstabelecimento, int? idEmpresa, OrderFilters order,
            List<QueryFilters> filters, int? page = null, int? take = null);

        ValidationResult IsValidToCrud(Protocolo protocolo, EProcesso operacao);
        object ConsultarAnexos(int idProtocolo);
        List<ProtocoloAnexo> GetAnexos(int idProtocolo);

        object ConsultarTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        byte[] GerarRelatorioTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo);

        object ConsultarTriagemProtocoloAssociacao(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, IList<int?> idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters);

        object ConsultarPagamentos(int idProtocolo, int? take, int? page, OrderFilters order,
            List<QueryFilters> filters);

        ValidationResult Aprovar(int idProtocolo, DateTime? dataPrevisaoPagamento, int? IdUsuarioAprovacao);
        ValidationResult Rejeitar(int idProtocolo, int idMotivo, string detalhamento, Usuario UsuarioLogado);
        ValidationResult ReverterRejeicao(int idProtocolo);
        void UpdateViagemEvento(int IdViagemEvento, int? IdProtocolo);
        ValidationResult RejeitarPagamento(int idProtocoloEvento, int? idMotivo, string detalhamento, Usuario UsuarioLogado);
        void UpdateProtocolValue(int IdProtocolo);
        decimal CalcularTaxaAntecipacao(int idProtocolo, DateTime dataAntecipacao);

        ValidationResult EnviarSolicitacaoAntecipacao(int idProtocolo, decimal valorPagantecipado,
            DateTime dataPagantecipado);

        ValidationResult EnviarNotificacaoProtocolo(Protocolo protocolo, string emailDestinatario,
            string nomeEmpresa, byte[] logoEmpresa, string nomeAplicativo);

        ValidationResult EnviarEmailDesconto(Protocolo protocolo, string emailDestinatario, int? idEmpresa,
            string nomeEmpresa, byte[] logoEmpresa, decimal valorDesconto, string nomeAplicativo, byte[] anexo,
            string nomeAnexo, decimal pesoChegadaDe, decimal? pesoChegadaPara, decimal? ValorOriginal,
            decimal? ValorComDesconto, bool descontoAbonoRejeitado);

        double getTaxaEstabelecimento(int? IdEstabelecimentoBase, int? IdEmpresa);

        object ConsultarTriagemAntecipacaoProtocolo(int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters, Usuario UsuarioLogado);

        object ConsultarTriagemAntecipacaoOcorrenciaProtocolo(int? idProtocolo, int? idEmpresa,
            List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page,
            OrderFilters order,
            List<QueryFilters> filters);

        List<RelatorioProtocoloOcorrencia> ConsultarTriagemAntecipacaoOcorrenciaProtocolo(int? idProtocolo,
            int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        ValidationResult AprovarAntecipacao(int idProtocoloAntecipacao);
        ValidationResult RejeitarAntecipacao(int idProtocoloAntecipacao, int idMotivo, string detalhamento);
        ValidationResult ReenviarAntecipacao(int idProtocoloAntecipacao, DateTime dataPagamentoAntecipado);

        List<Protocolo> ConsultarProtocolos(int? idEmpresa, List<int> idEstabelecimento,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, EStatusProtocoloEvento? status);

        object GetAnteciopacoes(int? idEmpresa, int ano, int mes);
        ValidationResult RealizarPagamento(int idProtocolo);

        ValidationResult RealizarDesconto(int idProtocoloEvento, decimal? valorDesconto,
            decimal? pesoChegada, int? idMotivoDesconto, string observacaoDesconto, string tokenAnexoDesconto,
            bool abonoDescontado);

        ValidationResult RemoverDesconto(int idProtocoloEvento);

        ValidationResult AlterarPesoChegada(int idProtocoloEvento, decimal? pesoChegada, int? numeroSacas,
            bool hasAbono = false);

        /// <summary>
        /// Altera o vinculo de um pagamento para outro protocolo
        /// </summary>
        /// <param name="idProtocolo">Código do protocolo ao qual o pagamento será vinculado</param>
        /// <param name="token">Código do pagamento</param>
        /// <param name="idUsuario">Código do usuário que está realizando o processo</param>
        /// <param name="idEmpresa">Código da empresa que está realizando o processo</param>
        /// <returns></returns>
        ValidationResult VincularPagamentoProtocolo(int idProtocolo, string token, int idUsuario);

        int GerarProtocoloPorViagemEvento(string token, int idEstabelecimento);

        object ConsultarRecebimentoProtocolos(int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        byte[] GerarRelatorioGridRecebimentoProtocolo(OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo);

        IQueryable<Protocolo> GetDataToGridAndReportRecebimentoProtocolos(OrderFilters order,
            List<QueryFilters> filters);


        object ConsultarGridProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters);
        
        List<ProtocoloConsultaModel> ConsultarProtocolos(int idEmpresa, int idProtocolo,
            bool includeEstabelecimentoBase, bool setCpfUsuarioLogadoComoAprovador,
            bool retornarEventosPagosNoCartao);

        Protocolo GetComEvento(int idProtocolo);

        object ConsultarGridAssociacao(int? idEstabelecimento, int take, int page, OrderFilters order,
            List<QueryFilters> filters, int? idEmpresa, int? idAssociacao = 0);

        List<int> ConsultarIdProtocoloEvento(int IdProtocolo);
        ProtocoloEvento GetProtocoloEvento(int IdProtocoloEvento);
        List<ProtocoloEvento> GetProtocolosEventos(List<int> ids);
        object ConsultarProtocoloEvento(int IdProtocolo);
        void SetProtocoloAsAprovado(int idProtocolo);
        byte[] GerarRelatorioEtiquetas(List<int> idsProtocolos, string logo);
        byte[] GerarRelatorioCapa(List<int> idsProtocolos, string logo);
        bool VerificarPagamentosSemProtocolo(int idEstabelecimentoBase);


        object GetDadosAnaliseAbono(int idProtocolo, int idViagemEvento);
    }
}
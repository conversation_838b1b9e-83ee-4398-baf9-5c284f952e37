﻿using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Common.Request
{
    public class VeiculoIntegrarRequestModel : RequestBase
    {
        public string CPFCNPJProprietario { get; set; }
        public string CNPJFilial { get; set; }
        public string CPFMotorista { get; set; }
        public string CPFCNPJUsuario { get; set; }

        public string Placa { get; set; }
        public string Chassi { get; set; }
        public int? AnoFabricacao { get; set; }
        public int? AnoModelo { get; set; }
        public string Marca { get; set; }
        public ETipoContrato TipoContrato { get; set; }
        public string Modelo { get; set; }
        public bool? ComTracao { get; set; }
        public ETipoRodagem? TipoRodagem { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdTipoCarreta { get; set; }
        public int? IdTecnologia { get; set; }
        public int? QuantidadeEixos { get; set; }
        public long? NumeroFrota { get; set; }
        public int? CodigoDaOperacao { get; set; }
        public string Municipio { get; set; }
        public int? IBGECidade { get; set; }
        public string RENAVAM { get; set; }
        public string CorVeiculo { get; set; }

        public ValidationResult ValidarEntrada()
        {
            var validation = new ValidationResult();

            if (string.IsNullOrEmpty(Placa))
                validation.Add("Placa não informada no cadastro do veículo.", EFaultType.Error);
            else if (Placa.Length > 10)
                validation.Add("Placa não pode ter mais de 10 caracteres no cadastro do veículo.", EFaultType.Error);

            if (string.IsNullOrEmpty(Chassi))
                validation.Add("Chassi não informado no cadastro de veículo.", EFaultType.Error);
            else if (Chassi.Length > 22)
                validation.Add("Chassi não pode ter mais de 22 caracteres no cadastro de veículo.", EFaultType.Error);

            if (string.IsNullOrEmpty(Marca))
                validation.Add("Marca não informada no cadastro de veículo.", EFaultType.Error);
            else if (Marca.Length > 20)
                validation.Add("Marca não pode ter mais de 20 caracteres no cadastro do veículo.", EFaultType.Error);

            if (string.IsNullOrEmpty(Modelo))
                validation.Add("Modelo não informado no cadastro de veículo.", EFaultType.Error);
            else if (Modelo.Length > 20)
                validation.Add("Modelo não pode ter mais de 20 caracteres no cadastro de veículo.", EFaultType.Error);
            
            return validation;
        }
    }
}
﻿using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class SolicitarCompraPedagioResponseDTO
    {
        public EResultadoCompraPedagio Status { get; set; }
        public string Mensagem { get; set; }
        public decimal? Valor { get; set; }
        
        /// <summary>
        /// Protocolo de requisição da integração entre ATS e serviço de pedágio
        /// </summary>
        public long? ProtocoloRequisicao { get; set; }

        /// <summary>
        /// Protocolo de resposta único do serviço de pedágio para identificar o processamento da transação
        /// </summary>
        public int? ProtocoloProcessamento { get; set; }
        
        public bool EstornoSaldoResidualSolicitado { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public string AvisoTransportador { get; set; }
        public FornecedorEnum? Fornecedor { get; set; }
        public string CnpjFornecedor { get; set; }
        public bool CompraCredenciaisExtratta { get; set; } = false;
    }
}

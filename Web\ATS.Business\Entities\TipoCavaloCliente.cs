﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    public class TipoCavaloCliente
    {
        public int IdTipoCavalo { get; set; }
        public int IdCliente { get; set; }
        public string Nome { get; set; }

        #region Referências
        public virtual TipoCavalo TipoCavalo { get; set; }
        public virtual Cliente Cliente { get; set; }
        #endregion
    }
}

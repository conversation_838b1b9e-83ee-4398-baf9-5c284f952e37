﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Interface.Service;
using ATS.Domain.Enum;
using System.Reflection;
using System.ComponentModel;
using System;

namespace ATS.Domain.Service
{
    public class GrupoUsuarioMenuService : ServiceBase, IGrupoUsuarioMenuService
    {
        private readonly IGrupoUsuarioMenuRepository _repository;
        private readonly IUsuarioRepository _usuarioRepository;

        public GrupoUsuarioMenuService(IGrupoUsuarioMenuRepository repository, IUsuarioRepository usuarioRepository)
        {
            _repository = repository;
            _usuarioRepository = usuarioRepository;
        }
        
        public IQueryable<GrupoUsuarioMenu> GetAllMenusPorGrupoUsuario(int idGrupoUsuario)
        {
            return _repository.Find(x => x.IdGrupoUsuario == idGrupoUsuario);
        }

        public IQueryable<GrupoUsuarioMenu> GetAllPorIdMenu(int idMenu)
        {
            return _repository.Find(x => x.IdMenu == idMenu);
        }

        public ValidationResult DeletePorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _repository.DeletePorIdGrupoUsuario(idGrupoUsuario);
        }

        public bool HasMenuLiberado(int idUsuario, EMenu idMenu)
        {
            var idGrupoUsuario = _usuarioRepository.Get(idUsuario).IdGrupoUsuario;
            FieldInfo field = idMenu.GetType().GetField(idMenu.ToString());
            DescriptionAttribute attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            var link = attribute.Description;
            return _repository.Include(x => x.Menu).Any(x => x.IdGrupoUsuario == idGrupoUsuario && x.IdMenu == x.Menu.IdMenu && x.Menu.Link.Equals(link));
        }

    }
}
﻿using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Request.Mensagem
{
    public class AtualizarMensagemRequest : RequestBase
    {
        public string CPFCNPJUsuario { get; set; }
        public List<MensagemControllerAtualizaMensagemRequestModel> Mensagens { get; set; }
    }
    public class MensagemControllerAtualizaMensagemRequestModel
    {
        public int IdMensagem { get; set; }
        public DateTime? DataHoraLeitura { get; set; }
    }
}
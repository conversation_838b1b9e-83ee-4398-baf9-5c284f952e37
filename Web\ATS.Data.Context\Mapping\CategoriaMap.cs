﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CategoriaMap : EntityTypeConfiguration<Categoria>
    {
        public CategoriaMap()
        {
            ToTable("CATEGORIA");

            HasKey(x => x.IdCategoria);

            Property(t => t.IdCategoria)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.Descricao)
                .HasMaxLength(200)
                .IsRequired();

            Property(x => x.UsuarioCadastro)
                .HasMaxLength(200)
                .IsOptional();

            Property(x => x.UsuarioAtualizacao)
                .HasMaxLength(200)
                .IsOptional();

            Property(x => x.DataCadastro)
                .IsOptional();

            Property(x => x.DataAtualizacao)
                .IsOptional();
        }
    }
}

﻿namespace ATS.Domain.Entities
{
    public class LayoutCartaoItem
    {
        /// <summary>
        /// Id do LayOut
        /// </summary>
        public int IdLayout { get; set; }

        /// <summary>
        /// Chave correspondente ao Enum TipoProcessoCartao
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Item no meio homologado
        /// </summary>
        public int ItemCartao { get; set; }

        public virtual LayoutCartao LayoutCartao { get; set; }
    }
}
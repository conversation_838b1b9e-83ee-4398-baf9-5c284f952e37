﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
     public class SerproCacheResultadoMap : EntityTypeConfiguration<SerproCacheResultado>
     {
          public SerproCacheResultadoMap()
          {
              ToTable("SERPRO_CACHE_RESULTADO");

              HasKey(x => x.Id);
              
              Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
              Property(c => c.Similaridade)              
                  .IsRequired()
                  .HasColumnType("decimal")
                  .HasPrecision(17, 2);

              Property(c => c.Valido).IsRequired();
              Property(c => c.Tipo).IsRequired();

              HasRequired(x => x.SerproCache)
                  .WithMany(x => x.SerproCacheResultado)
                  .HasForeignKey(x => x.SerproCacheId);
          }
     }
}

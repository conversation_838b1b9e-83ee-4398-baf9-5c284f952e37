﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using ATS.Domain.Interface.Service;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Infra.ApiClient;

namespace ATS.Domain.Entities
{
    public class WebhookActionDependencies
    {
        public WebhookActionDependencies(IParametrosGenericoService parametrosGenericoService, IParametrosEmpresaService parametrosEmpresaService)
        {
            ParametrosGenericoService = parametrosGenericoService;
            ParametrosEmpresaService = parametrosEmpresaService;
        }

        public IParametrosGenericoService ParametrosGenericoService { get; }
        public IParametrosEmpresaService ParametrosEmpresaService { get; }
    }
    
    public class Webhook
    {
        public int Id { get; set; }
        public WebhookTipoEvento Tipo { get; set; }
        public int IdRegistro { get; set; }
        public string Verbo { get; set; } = "POST";
        public string Headers { get; set; }
        public string Endpoint { get; set; }
        //public DateTime DataCadastro { get; set; }
        public int? Tempo { get; set; }
        //referece à aplicação alvo.
        public string Aplicacao { get; set; } = "";

        public Dictionary<string, string> GetHeadersAsDict(string separator = "=",
            string includeContentType = "application/json")
        {
            if (Headers.IsNullOrWhiteSpace())
                return new Dictionary<string, string>();

            var result = new Dictionary<string, string>();
            var lines = Headers.Split(new[] {Environment.NewLine}, StringSplitOptions.None);
            foreach (var line in lines)
            {
                var index = line.IndexOf(":", StringComparison.Ordinal);
                
                // Compatibilidade com registros antigos no DB, pode ser removido em breve.
                if (index == -1)
                    index = line.IndexOf(separator, StringComparison.Ordinal);
                
                if (index <= 0)
                    result.Add(line, string.Empty);

                var key = line.Substring(0, index);
                var value = line.Substring(index + 1, line.Length - index - 1);
                result.Add(key, value);
            }

            if (!string.IsNullOrWhiteSpace(includeContentType) &&
                !result.Keys.Contains("Content-Type", StringComparer.InvariantCultureIgnoreCase))
                result.Add("Content-Type", includeContentType);

            return result;
        }

        /// <summary>
        /// Obter tempo considerando campo para configurar exceções de desenvolvimento do web.config
        /// </summary>
        /// <returns></returns>
        public int? GetTempo()
        {
            return Tempo ?? AppSettingsUtils.TempoPadraoParaReintegrarWebhooksComFalha;
        }

        public NotificacaoWebhookApiRequest CreateApiRequest(WebhookActionDependencies dependencies, string infoAdicional, object requisicao, int idEmpresa)
        {
            // Obtendo configurações para notificar setor responsável por e-mail caso falhe a integraçao da webhook
            //var email = new ParametrosService().GetEmailConfiguracoesRemetente(idEmpresa); //Properties.Resources.ATSEmail;
            //var password = new ParametrosService().GetEmailConfiguracoesSenha(idEmpresa); //Properties.Resources.Senha;
            //var porta = Convert.ToInt32(new ParametrosService().GetEmailConfiguracoesPorta(idEmpresa));
            //var smtp = new ParametrosService().GetEmailConfiguracoesSmtp(idEmpresa); //Properties.Resources.SMTPGoogle;

            /*var emailSustentacaoGlobal = dependencies.ParametrosGenericoService.GetParametro<string>(GLOBAL.EmailSustentacaoIntegracaoSistemas, 0);

            var emailSustentacaoEmpresa = dependencies.ParametrosEmpresaService.GetParametro<string>(EMPRESA_ID.EmailSustentacaoIntegracaoSistemas, 1);

            var emails = emailSustentacaoGlobal.SetEndChars(";") + emailSustentacaoEmpresa;

            var destinatariosEmailFalha = new ObservableCollection<NotificacaoEmailAddressApiRequest>();
            if (!string.IsNullOrWhiteSpace(emails) && emails != ";")
            {
                var emailsArray = emails.Split(';');
                foreach (var dest in emailsArray)
                    if (!string.IsNullOrWhiteSpace(dest))
                        destinatariosEmailFalha.Add(new NotificacaoEmailAddressApiRequest
                        {
                            Address = dest
                        });
            }*/
            
            // Request para integrar no serviço de infra
            var request = new NotificacaoWebhookApiRequest
            {
                Verbo = Verbo,
                Endpoint = Endpoint,
                AplicacaoDestino = Aplicacao,
                Aplicacao = AppSettingsUtils.NomeServicoLogs,
                Headers = GetHeadersAsDict(),
                InfoAdicional = infoAdicional,
                Requisicao = requisicao != null ? JsonConvert.SerializeObject(requisicao) : null,
                DataRequisicao = DateTime.Now                
            };

            /*if (email.HasValue() && smtp.HasValue() && destinatariosEmailFalha.Any())
                request.PoliticaFalha = new NotificacaoPoliticaFalhaApiRequest
                {
                    MinutosParaMonitorar = GetTempo(),
                    MinutosParaNovaTentativa = minutosParaNovaTentativa,
                    Alertas = new ObservableCollection<NotificacaoPoliticaFalhaAlertasApiRequest>
                    {
                        new NotificacaoPoliticaFalhaAlertasApiRequest
                        {
                            FalhasParaEnviarAlerta = 2,
                            Configuracao = new NotificacaoEmailApiRequest
                            {
                                Smtp = new NotificacaoEmailSmtpApiRequest
                                {
                                    Host = smtp,
                                    Port = porta,
                                    UserName = email,
                                    Password = password,
                                    EnableSsl = true,
                                    UseDefaultCredentials = false
                                },
                                Message = new NotificacaoEmailMessageApiRequest
                                {
                                    From = new NotificacaoEmailAddressApiRequest
                                    {
                                        Address = email,
                                        DisplayName = "Sustentacao ATS SOTRAN"
                                    },
                                    To = destinatariosEmailFalha,
                                    Subject = "[Webhook error] " + infoAdicional,
                                    Body = "ERRO AO EXECUTAR WEBHOOK\n\n" +
                                           $"Endpoint: {Endpoint}\n\n" +
                                           $"Verbo: {Verbo}\n\n" +
                                           $"Aplicação origem: {AppSettingsUtils.NomeServicoLogs}\n\n" +
                                           $"Aplicação destino: {Aplicacao}\n\n" +
                                           $"Data servidor ATS: {DateTime.Now.ParaFormatoBrasileiroStr()}\n\n" +
                                           $"Informações adicionais: {infoAdicional}\n\n\n\n" +
                                           $"OBS.: O ATS irá tentar reintegrar este cadastrado durante {GetTempo()} minutos, realizando uma tentativa a cada {minutosParaNovaTentativa} minuto(s)."
                                },
                                AplicacaoDestino = Aplicacao,
                                Aplicacao = AppSettingsUtils.NomeServicoLogs,
                                InfoAdicional = "[Webhook error] " + infoAdicional,
                                PoliticaFalha = new NotificacaoPoliticaFalhaApiRequest
                                {
                                    MinutosParaMonitorar = 10,
                                    MinutosParaNovaTentativa = 1
                                }
                            }
                        }
                    }
                };*/
            
            return request;
        }
        
        public void EnviarNotificacao(WebhookActionDependencies dependencies, string infoAdicional, object requisicao, string tokenEmpresa, int idEmpresa)
        {
            var request = CreateApiRequest(dependencies, infoAdicional, requisicao, idEmpresa);            
            EnviarNotificacao(request, tokenEmpresa);
        }
        
        public void EnviarNotificacao(NotificacaoWebhookApiRequest request, string tokenEmpresa)
        {
            var token = !string.IsNullOrWhiteSpace(tokenEmpresa)
                ? tokenEmpresa
                : SistemaInfoConsts.TokenAdministradora;
            var documentoUsuarioAudit = InfraExternalRepository.AuditDocIntegracao;
                     
            new WebhookExternalRepository(token, documentoUsuarioAudit, null).Webhook(request);
        }
    }

    /// <summary>
    /// ATENÇÃO: NÃO MODIFICAR NOME DOS ENUM!!
    /// Existem locais como a integração de viagem, onde o nome do enum representa o conteúdo a ser recebido no JSON de integração
    /// </summary>
    public enum WebhookTipoEvento
    {
        /// <summary>
        /// Notificar sistemas externos vinculados a viagem quando o ATS baixar o evento da viagem
        /// </summary>
        BaixarEventoViagem = 0,
        
        /// <summary>
        /// Notificar sistemas externos vinculados a viagem quando o ATS cancelar o evento da viagem
        /// </summary>
        CancelarEventoViagem = 1,
        
        /// <summary>
        /// Notificar sistemas externos vinculados a viagem quando o ATS receber a confirmação de carga ou descarga do cartão pedágio enviada pelo serviço de pedágio Sistema info (Moedeiro)
        /// </summary>
        CargaDescargaCartaoPedagio = 2,
        
        /// <summary>
        /// Configuração por id empresa - Link de TMS's para notificar ao aceitar protocolo na triagem
        /// </summary>
        ProtocoloAprovado = 3,
        
        /// <summary>
        /// Notificar administradoras de cartões de resgates de saldos residuais de cartões pedágio
        /// </summary>
        ResgateSaldoResidualCartaoPedagio = 4,

        /// <summary>
        /// Configuração por id empresa - Link de TMS's para integrar o estabelecimento cadastrado na plataforma
        /// </summary>
        IntegracaoEstabelecimento = 5,

        /// <summary>
        /// Configuração por id empresa - Link de TMS's para integrar o estabelecimento cadastrado na plataforma
        /// </summary>
        NotificacaoGR = 6
    }
}
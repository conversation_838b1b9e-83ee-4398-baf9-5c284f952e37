﻿using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class MotoristaPorCpfPamcardModel
    {
        public int IdMotorista { get; set; }
        public string Nome { get; set; }
        public string CNH { get; set; }
        public string CNHCategoria { get; set; }
        public DateTime? DataValidade { get; set; }
        public string Placa { get; set; }
        public string StatusRntrc { get; set; }
        public List<Cartao> Cartoes { get; set; }
        public List<Conta> Contas { get; set; }
    }

    public class Cartao
    {
        private const string Liberado = "LIBERADO";
        public int Quantidade { get; set; } = 1;
        public int Numero { get; set; }
        public string Tipo { get; set; }
        public string Status { get; set; } = Liberado;
    }

    public class Conta
    {
        private const string Pendente = "PENDENTE";
        public string Agencia { get; set; }
        public int AgenciaDigito { get; set; }
        public string Banco { get; set; }
        public string BancoNome { get; set; }
        public string Numero { get; set; }
        public string Status { get; set; } = Pendente;
        public string Tipo { get; set; }
    }
}
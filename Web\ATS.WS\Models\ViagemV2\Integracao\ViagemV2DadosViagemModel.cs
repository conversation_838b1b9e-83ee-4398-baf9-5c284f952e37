using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2DadosViagemModel
    {
        public ViagemV2DadosViagemModel()
        {
            ViagemEventos = new List<ViagemEventoIntegrarModel>();
        }

        /// <summary>
        /// Objeto com os dados iniciais da viagem
        /// </summary>
        public ViagemV2DadosIniciais DadosIniciais { get; set; }

        /// <summary>
        /// Objeto com os dados dos documentos
        /// </summary>
        public ViagemV2Documentos Documentos { get; set; }

        /// <summary>
        /// Objeto com os dados dos valores
        /// </summary>
        public ViagemV2ValoresViagemModel Valores { get; set; }

        /// <summary>
        /// Dados relacionados a forma de pagamento do frete
        /// </summary>
        public ViagemV2DadosPagamentoModel DadosPagamento { get; set; }

        /// <summary>
        /// Dados referentes as regras de negócio executada pela API da ANTTs
        /// </summary>
        public ViagemV2DadosAnttModel DadosAntt { get; set; }

        /// <summary>
        /// Objeto com os dados dos endereços
        /// </summary>
        public ViagemV2EnderecosModel Enderecos { get; set; }

        /// <summary>
        /// Objeto com os dados do veículo
        /// </summary>
        public ViagemV2VeiculoModel Veiculo { get; set; }
        /// <summary>
        /// Objeto com as datas da viagem
        /// </summary>
        public ViagemV2Datas Datas { get; set; }

        /// <summary>
        /// Objeto pai que contém uma lista de eventos relacionados a viagem
        /// </summary>
        public List<ViagemEventoIntegrarModel> ViagemEventos { get; set; }

        /// <summary>
        /// Objeto que contém uma lista dos documentos fiscais da viagem
        /// </summary>
        public List<ViagemDocumentoFiscalModel> DocumentosFiscais { get; set; }

        /// <summary>
        /// Objeto que contém os dados para o pedágio
        /// </summary>
        public PedagioModel Pedagio { get; set; }

        /// <summary>
        /// Webhook
        /// </summary>
        public Dictionary<string, WebHookIntegrarRequest> Webhook { get; set; }

        /// <summary>
        /// Objeto responsável por realizar os cadastros necessários para integrar a viagem
        /// </summary>
        public ViagemV2CadastrosPreViagemModel CadastrosPreViagem { get; set; }

        /// <summary>
        /// Regras Viagem, hoje não é utilizado nesta versão 2.0, mas para não travar o processo iniciamos esta lista
        /// </summary>
        public List<ViagemRegraIntegrarModel> ViagemRegra { get; set; }

        public List<ViagemV2AutorizacaoEstabelecimentoModel> AutorizacaoEstabelecimentos { get; set; }

        public ValidationResult ValidarEntrada(bool validarDocumentosComIntegracoes, bool validarViagemId)
        {
            if (DadosIniciais == null)
                return new ValidationResult().Add("Dados iniciais não foram informados.", EFaultType.Error);

            var validacaoDadosIniciais = DadosIniciais.ValidarEntrada();

            if (!validacaoDadosIniciais.IsValid)
                return validacaoDadosIniciais;

            if (Documentos == null)
                return new ValidationResult().Add("Documentos não foram informados", EFaultType.Error);

            var validacaoDocumentos = Documentos.ValidarEntrada();

            if (!validacaoDocumentos.IsValid)
                return validacaoDocumentos;

            if (validarDocumentosComIntegracoes && CadastrosPreViagem != null)
            {
                var validacaoDocumentosPreViagem = ValidarDocumentosComPreViagem();

                if (!validacaoDocumentosPreViagem.IsValid)
                    return validacaoDocumentos;
            }

            if (Valores == null)
                return new ValidationResult().Add("Valores não foram informados.", EFaultType.Error);

            var validacaoValores = Valores.ValidarEntrada();

            if (!validacaoValores.IsValid)
                return validacaoValores;

            if (DadosPagamento != null)
            {
                var validacaoFormaPagamento = DadosPagamento.ValidarEntrada();

                if (!validacaoFormaPagamento.IsValid)
                    return validacaoFormaPagamento;
            }

            if (DadosAntt != null)
            {
                var validacaoDadosAntt = DadosAntt.ValidarEntrada();

                if (!validacaoDadosAntt.IsValid)
                    return validacaoDadosAntt;
            }

            if (Enderecos != null)
            {
                var validacaoEndereco = Enderecos.ValidarEntrada();

                if (!validacaoEndereco.IsValid)
                    return validacaoEndereco;
            }

            if (Veiculo != null)
            {
                var validacaoVeiculo = Veiculo.ValidarEntrada();

                if (!validacaoVeiculo.IsValid)
                    return validacaoVeiculo;
            }

            if (CadastrosPreViagem?.Carretas != null)
            {
                var validacaoCarretas = new ValidationResult();

                foreach (var carreta in CadastrosPreViagem.Carretas)
                {
                    var carretaValida = carreta.ValidarEntrada();

                    if (!carretaValida.IsValid)
                        validacaoCarretas.Add(carretaValida.Errors.FirstOrDefault()?.Message, EFaultType.Error);
                }

                if (!validacaoCarretas.IsValid)
                    return validacaoCarretas;
            }

            if (Datas != null)
            {
                var validacaoDatas = Datas.ValidarEntrada();

                if (!validacaoDatas.IsValid)
                    return validacaoDatas;
            }

            if (Pedagio != null)
            {
                var validacaoPedagio = Pedagio.ValidarEntrada();

                if (!validacaoPedagio.IsValid)
                    return validacaoPedagio;
            }
            
            if (ViagemEventos.Any() && validarViagemId)
                foreach (var evento in ViagemEventos)
                    if (evento.IdViagemEvento.HasValue)
                        return new ValidationResult().Add("IdViagemEvento não pode estar preenchido para a integração.", EFaultType.Error);

            return new ValidationResult();
        }

        private ValidationResult ValidarDocumentosComPreViagem()
        {
            if (CadastrosPreViagem?.ClienteOrigem != null)
                if (CadastrosPreViagem?.ClienteOrigem?.CNPJCPF != Documentos.ClienteOrigemDocumento)
                    return new ValidationResult().Add(
                        "Documento do cliente de origem diverge do documento do cliente de origem presente na integração.",
                        EFaultType.Error);

            if (CadastrosPreViagem?.ClienteDestino != null)
                if (CadastrosPreViagem?.ClienteDestino?.CNPJCPF != Documentos.ClienteDestinoDocumento)
                    return new ValidationResult().Add(
                        "Documento do cliente de destino diverge do documento do cliente de destino presente na integração.",
                        EFaultType.Error);

            if (CadastrosPreViagem?.Motorista != null)
                if (CadastrosPreViagem?.Motorista?.Cpf != Documentos.MotoristaDocumento)
                    return new ValidationResult().Add(
                        "Documento do motorista diverge do documento informado na integração.", EFaultType.Error);

            if (CadastrosPreViagem?.Proprietario != null)
                if (CadastrosPreViagem?.Proprietario?.CnpjCpf != Documentos.ProprietarioDocumento)
                    return new ValidationResult().Add(
                        "Documento do proprietário diverge do documento informado na integração", EFaultType.Error);

            if (CadastrosPreViagem?.Veiculo != null)
            {
                if (CadastrosPreViagem?.Veiculo?.Placa != Veiculo.Placa)
                    return new ValidationResult().Add(
                        "Placa do veículo informado na integração, diverge da placa do veículo informado na viagem.",
                        EFaultType.Error);

                if (CadastrosPreViagem?.Veiculo?.CPFMotorista != Documentos.MotoristaDocumento)
                    return new ValidationResult().Add(
                        "Documento do motorista informado na integração do veículo diverge do documento do motorista informado na viagem.",
                        EFaultType.Error);

                if (CadastrosPreViagem.Veiculo?.CPFCNPJProprietario != Documentos.ProprietarioDocumento)
                    return new ValidationResult().Add(
                        "Documento do proprietário informado na integração do veículo diverge do documento do proprietário informado na viagem.",
                        EFaultType.Error);
            }
            
            return new ValidationResult(); 
        }
    }
}
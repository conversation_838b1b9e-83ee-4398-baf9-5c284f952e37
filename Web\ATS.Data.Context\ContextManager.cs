﻿using ATS.Data.Context.Interface;
using System;
using System.Web;

namespace ATS.Data.Context
{
    public class ContextManager<TContext> : IContextManager<TContext>
        where TContext : IDbContext, new()
    {
        private const string ContextKey = "ContextManager.Context";
        private static TContext Context { get; set; }

        public IDbContext GetContext()
        {
            if (HttpContext.Current == null)
            {
                if (Context != null) return Context;
                Context = new TContext();
                return Context;
            }

            if (HttpContext.Current.Items[ContextKey] == null)
                HttpContext.Current.Items[ContextKey] = new TContext();

            return HttpContext.Current.Items[ContextKey] as IDbContext;
        }

        public void Finish()
        {
            try
            {
                if (Context != null)
                {
                    Context.Dispose();
                    Context = default(TContext);
                }
            }
            catch (Exception)
            {
                //Ignorar erros que possam ocorrer ao dar dispose no contexto que não são http
            }
            if (HttpContext.Current == null)
                return;
            if (HttpContext.Current.Items[ContextKey] != null)
            {
                var Context = (IDbContext)HttpContext.Current.Items[ContextKey];

                (HttpContext.Current.Items[ContextKey] as IDbContext)?.Dispose();
                HttpContext.Current.Items[ContextKey] = null;
            }
        }
    }
}
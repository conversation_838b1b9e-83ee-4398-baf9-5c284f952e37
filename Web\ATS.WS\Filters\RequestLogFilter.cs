﻿using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using ATS.WS.Attributes;

namespace ATS.WS.Filters
{
    public class RequestLogFilter : ActionFilterAttribute, IActionFilter
    {
        private readonly InfraExternalRepository _infraRepository;

        public RequestLogFilter()
        {
            _infraRepository = new InfraExternalRepository();
        }

        // Never try to log an large object here it can create a memory leak.
        // Add exceptions to large objects..
        void IActionFilter.OnActionExecuted(ActionExecutedContext filterContext)
        {
            var attributes = filterContext.ActionDescriptor.GetCustomAttributes(true);
            
            if (attributes.All(o => o.GetType() != typeof(EnableLogRequest)))
                return;
            
            if (InfraExternalRepository.EnableLogService)
            {
                //Caso o atributo IgnoreRequestLog esteja listado no cabeçalho do método, então não permitimos que grave logs no ms
                if (attributes.Any(x => x.GetType() == typeof(IgnoreRequestLog)))
                    return;
                
                object sessionValue;
                if (HttpContext.Current?.Items != null &&
                    HttpContext.Current.Items.Contains(SistemaInfoConsts.CurrentLogIdSessionKey))
                {
                    sessionValue = HttpContext.Current.Items[SistemaInfoConsts.CurrentLogIdSessionKey];
                    if (sessionValue != null && !string.IsNullOrWhiteSpace(sessionValue.ToString()))
                    {
                        Guid requestId;
                        if (Guid.TryParse(sessionValue.ToString(), out requestId))
                            _infraRepository.LogConsumerResponse(filterContext, requestId);
                    }
                }
            }
        }

        void IActionFilter.OnActionExecuting(ActionExecutingContext filterContext)
        {
            var attributes = filterContext.ActionDescriptor.GetCustomAttributes(true);
            
            if (attributes.All(o => o.GetType() != typeof(EnableLogRequest)))
                return;
            
            if (InfraExternalRepository.EnableLogService)
            {
                if (attributes.Any(x => x.GetType() == typeof(IgnoreRequestLog)))
                    return;
                
                var logBus = _infraRepository.LogConsumerRequest(filterContext);
                if (HttpContext.Current?.Items != null && logBus?.Id != null && logBus.Id != default(Guid))
                {
                    HttpContext.Current.Items.Add(SistemaInfoConsts.CurrentLogIdSessionKey, logBus.Id);
                    HttpContext.Current.Items.Add(SistemaInfoConsts.CurrentLogNivelSessionKey, logBus.Nivel ?? 0);
                }
            }
        }
    }
}

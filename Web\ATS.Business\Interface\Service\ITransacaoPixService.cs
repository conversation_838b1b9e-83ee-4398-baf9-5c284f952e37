﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO.Pix;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ITransacaoPixService : IService<TransacaoPix>
    {
        BusinessResult<TransferenciaPixModelResponse> EfetuarTransferenciaPix(int idEmpresaOrigem, string cpfCnpjProprietarioDestino, decimal valor,
            ViagemEvento evento, string mensagem, string documentoUsuarioAudit, bool integracao = false);
        bool VerificarProprietario(int idEmpresa, int idProprietario);
        BusinessResult<GerarQrCodeEstaticoPixModelResponse> GerarQrCode(int idEmpresa);
        BusinessResult<ContaPixResponse> ConsultarContaPix(int idEmpresa);
        BusinessResult<TransferenciaPixGridResponse> ConsultarGrid(int idEmpresa, int page, int take, DateTime dataInicial, DateTime dataFinal, List<QueryFilters> filters);
        byte[] GerarRelatorioGrid(int idEmpresa, object dados, string tipoArquivo);
        BusinessResult<TransferenciaPixComprovanteResponse> ConsultarTransferencia(int idEmpresa, string endToEndId, ETipoTransferenciaPix tipo);
        BusinessResult<TransferenciaPixComprovanteResponse> ConsultarTransferenciaPorEvento(int idEmpresa, int idViagemEvento);
        BusinessResult CadastrarChave(int idEmpresa, string chave, ETipoChavePix tipo);
        BusinessResult DeletarChave(int idEmpresa, string chave);
        BusinessResult<ChavesPixGridResponse> ConsultarChaves(int idEmpresa);
        BusinessResult<LimitesPixAlterarModelResponse> AlterarLimite(int idEmpresa, AlterarLimitesPixRequest request);
        BusinessResult<LimitesPixConsultarModelResponse> ConsultarLimites(int idEmpresa);

        #region Gestão de alçadas

        BusinessResult EfetuarTransferenciaPixGestaoAlcadas(int idViagemEvento);
        
        BusinessResult CancelarTransferenciaPixGestaoAlcadas(int idViagemEvento);
        
        /// <summary>
        /// Solicitacao da Biz ter que validar a senha do cartao quando faz um Pix por questao de segurança.
        /// </summary>
        BusinessResult ValidarSenhaCartaoPixGestaoAlcadas(int idViagemEvento, string senha);

        BusinessResult<TransferenciaPixGridGestaoAlcadasResponse> ConsultarGridGestaoAlcadas(int page, int take, 
            DateTime dataInicial, DateTime dataFinal, OrderFilters order, List<QueryFilters> filters);


        #endregion
    }
}
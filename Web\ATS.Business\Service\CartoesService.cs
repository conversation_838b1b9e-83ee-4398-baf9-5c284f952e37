﻿using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Cartoes.SituacaoDosCartoes;
using ATS.CrossCutting.Reports.Cartoes.TransferenciaContasBancarias;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Cartao;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using ATS.Data.Repository.External.SistemaInfo.Pedagio;
using ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using Microsoft.Ajax.Utilities;
using NLog;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Threading.Tasks;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Validation;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Newtonsoft.Json;
using Sistema.Framework.Util.Enumerate;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using CancelarCartaoRemessaRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CancelarCartaoRemessaRequest;
using ConsultarEmpresaResponse = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.ConsultarEmpresaResponse;
using ConsultarExtratoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarExtratoRequest;
using ConsultarSaldoCartaoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoRequest;
using ConsultarSaldoCartaoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoResponse;
using CustomFilter = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.CustomFilter;
using Empresa = ATS.Domain.Entities.Empresa;
using IdentificadorCartao = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IdentificadorCartao;
using IntegrarEmpresaRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaRequest;
using IntegrarEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponse;
using IntegrarEmpresaResponseStatus = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponseStatus;
using ProdutoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ProdutoResponse;
using PedagioIntegrarEmpresaRequest = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.IntegrarEmpresaRequest;
using PedagioIntegrarEmpresaResponse = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.IntegrarEmpresaResponse;
using ConsultarSaldoEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoEmpresaResponse;
using Sistema.Framework.Util.Extension;
using ATS.Data.Repository.External.Extratta.Biz.Client;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.Extratta.Models;

namespace ATS.Domain.Service
{
    public class CartoesServiceArgs
    {
        public CartoesServiceArgs(IViagemRepository viagemRepository, IParametrosService parametrosService,
            IProprietarioRepository proprietarioRepository, IResgateCartaoAtendimentoService resgatarCartaoService, 
            IUserIdentity userIdentity, IViagemEventoService viagemEventoService, IEmpresaService empresaService, IPushService pushService,
            ILayoutCartaoService layoutCartaoService, ICidadeRepository cidadeRepository, IPaisRepository paisRepository, 
            IEstadoRepository estadoRepository, ITransacaoCartaoService transacaoCartaoService, IEmpresaRepository empresaRepository,
            IDeclaracaoCiotRepository declaracaoCiotRepository, IViagemEventoRepository viagemEventoRepository, 
            IParametrosGenericoService parametrosGenerico, IParametrosEmpresaService parametrosEmpresaService, 
            ICargaAvulsaRepository cargaAvulsaRepository)
        {
            ViagemRepository = viagemRepository;
            ParametrosService = parametrosService;
            ProprietarioRepository = proprietarioRepository;
            ResgatarCartaoService = resgatarCartaoService;
            UserIdentity = userIdentity;
            ViagemEventoService = viagemEventoService;
            EmpresaService = empresaService;
            PushService = pushService;
            LayoutCartaoService = layoutCartaoService;
            CidadeRepository = cidadeRepository;
            PaisRepository = paisRepository;
            EstadoRepository = estadoRepository;
            TransacaoCartaoService = transacaoCartaoService;
            EmpresaRepository = empresaRepository;
            DeclaracaoCiotRepository = declaracaoCiotRepository;
            ParametrosGenericoService = parametrosGenerico;
            ParametrosEmpresaService = parametrosEmpresaService;
            CargaAvulsaRepository = cargaAvulsaRepository;
        }

        public IViagemRepository ViagemRepository { get; }
        public IParametrosService ParametrosService { get; }
        public IProprietarioRepository ProprietarioRepository { get; }
        public IResgateCartaoAtendimentoService ResgatarCartaoService { get; }
        public IUserIdentity UserIdentity { get; }
        public IViagemEventoService ViagemEventoService { get; }
        public IEmpresaService EmpresaService { get; }
        public IEmpresaRepository EmpresaRepository { get; }
        public IPushService PushService { get; }
        public ILayoutCartaoService LayoutCartaoService { get; }
        public ICidadeRepository CidadeRepository { get; }
        public IPaisRepository PaisRepository { get; }
        public IEstadoRepository EstadoRepository { get; }
        public ITransacaoCartaoService TransacaoCartaoService { get; }
        public IDeclaracaoCiotRepository DeclaracaoCiotRepository { get; }
        public IViagemEventoRepository ViagemEventoRepository { get; }
        public IParametrosGenericoService ParametrosGenericoService { get; }
        public IParametrosEmpresaService ParametrosEmpresaService { get; }
        public ICargaAvulsaRepository CargaAvulsaRepository { get; }
    }

    public class CartoesService : ICartoesService
    {
        private int? _idEmpresa;

        // Utilizar a função "GetIdProdutoCartaoFretePadrao" para validar, inicializar, manter cache e retornar esta informação
        private int _idProdutoFrete;

        private readonly IViagemRepository _viagemRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IParametrosService _parametrosService;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IResgateCartaoAtendimentoService _resgatarCartaoService;
        private readonly IUserIdentity _userIdentity;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IPushService _pushService;
        private readonly CartaoExternalRepository _repository;
        private readonly PedagioExternalRepository _pedagioRepository;
        private readonly ExtrattaBizApiClient _extrattaBizApiClient;
        private readonly ILayoutCartaoService _layoutCartaoService;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IPaisRepository _paisRepository;
        private readonly IEstadoRepository _estadoRepository;
        private readonly ITransacaoCartaoService _transacaoCartaoService;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IParametrosGenericoService _parametrosGenerico;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ICargaAvulsaRepository _cargaAvulsaRepository;

        public const string TipoEventoConciliacaoPedagio = "Pedágio";
        private const string TipoEventoConciliacaoResgateSaldoResidualPedagio = "Pedágio";
        private const string TipoEventoDepositoEmpresa = "Depósito";
        private const string TipoEventoTransferenciaViaPixCredito = "PIX";
        private const string TipoEventoTransferenciaViaPixDebito = "PIX";
        private const string TipoEventoConciliacaoCargaAvulsa = "Avulsa";
        private const string TipoEventoConciliacaoPagamentoConta = "Pagamento de conta";
        private const string TipoEventoConciliacaoEstornoPagamentoConta = "Estorno pgto conta";
        private const string TipoEventoConciliacaoResgateValorDebito = "Resgate valor";
        private const string TipoEventoConciliacaoResgateValorCredito = "Resgate valor";
        private const string TipoEventoPedagioCargaAvulsaProvisionamento = "Pedágio";
        private const string TipoEventoValePedagioCargaAvulsaProvisionamento = "Vale Pedágio";
        public const string AuditDocIntegracao = "00000000000";

        public CartoesService(CartoesServiceArgs args, int? idEmpresa, string tokenEmpresa, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            _idEmpresa = idEmpresa;
            _viagemRepository = args.ViagemRepository;
            _parametrosService = args.ParametrosService;
            _proprietarioRepository = args.ProprietarioRepository;
            _resgatarCartaoService = args.ResgatarCartaoService;
            _userIdentity = args.UserIdentity;
            _viagemEventoService = args.ViagemEventoService;
            _empresaRepository = args.EmpresaRepository;
            _pushService = args.PushService;
            _layoutCartaoService = args.LayoutCartaoService;
            _cidadeRepository = args.CidadeRepository;
            _paisRepository = args.PaisRepository;
            _estadoRepository = args.EstadoRepository;
            _transacaoCartaoService = args.TransacaoCartaoService;
            _declaracaoCiotRepository = args.DeclaracaoCiotRepository;
            _viagemEventoRepository = args.ViagemEventoRepository;
            _parametrosGenerico = args.ParametrosGenericoService;
            _parametrosEmpresa = args.ParametrosEmpresaService;
            _cargaAvulsaRepository = args.CargaAvulsaRepository;

            _repository = new CartaoExternalRepository(tokenEmpresa, documentoUsuarioAudit, nomeUsuarioAudit);
            _pedagioRepository = new PedagioExternalRepository(tokenEmpresa, documentoUsuarioAudit, nomeUsuarioAudit);

            _extrattaBizApiClient = new ExtrattaBizApiClient(null);
        }

        public static CartoesService CreateByEmpresa(CartoesServiceArgs args, int id, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var serv = args.EmpresaRepository;
            var token = serv.GetTokenMicroServices(id);
            return new CartoesService(args, id, token, documentoUsuarioAudit, nomeUsuarioAudit);
        }

        #region Relatórios

        public object RelatorioSituacaoCartao(RelatorioCartaoApiRequest request, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var response = _repository.RelatorioSituacaoCartao(request);

            if (response.Sucesso == false)
                throw new Exception(response.Mensagem);

            var dados = response.Dados.AsQueryable();

            var produtosLista = dados.Select(x => x.ProdutoId).Distinct().ToList();

            var prodmestreLista = dados.Where(x => produtosLista.Contains(x.ProdutoMestreId));

            dados = dados.Where(x => x.ProdutoMestreId == null);

            dados = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? dados.OrderByDescending(x => x.CartaoId)
                : dados.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            dados = dados.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = dados.ToList().Count,

                items = dados.Skip((page - 1) * take).Take(take)
                    .ToList().Select(c => new
                    {
                        c.Identificador,
                        c.Produto,
                        c.ProdutoId,
                        DocumentoPortador = c.DocumentoPortador.FormatarCpfCnpj(),
                        c.Portador,
                        c.StatusText,
                        StatusPedagio = prodmestreLista.Where(x => x.CartaoMestreId == c.CartaoId).Select(x => x.StatusText).FirstOrDefault(),
                        ProdutoIdPedagio = prodmestreLista.Where(x => x.CartaoMestreId == c.CartaoId).Select(x => x.ProdutoId).FirstOrDefault(),
                        IdentificadorPedagio = prodmestreLista.Where(x => x.CartaoMestreId == c.CartaoId).Select(x => x.Identificador).FirstOrDefault(),
                        DataVinculo = c.DataVinculo?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataDesvinculo = c.DataDesvinculo?.ToString("dd/MM/yyyy HH:mm:ss"),
                        c.MotivoDesvinculo,
                        c.Administradora,
                        CnpjEmpresa = c.CnpjEmpresa.FormatarCpfCnpj(),
                        c.Empresa,
                        DocumentoPontoDistribuicao = c.DocumentoPontoDistribuicao.FormatarCpfCnpj(),
                        c.PontoDistribuicao,
                        DataRecepcaoPontoDistribuicao = c.DataRecepcaoPontoDistribuicao?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataCadastro = c.DataCadastro?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataAlteracao = c.DataAlteracao?.ToString("dd/MM/yyyy HH:mm:ss"),
                        HabilitarDesvincular = c.StatusText.Trim() == "Em trânsito - Para ponto de distribuição" || c.StatusText.Trim() == "Aguardando vinculação"
                    })
            };
        }

        public List<RelatorioSituacaoDosCartoesDataType> GetRelatorioSituacaoCartao(RelatorioCartaoApiRequest request, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var response = _repository.RelatorioSituacaoCartao(request);

            if (response.Sucesso.HasValue && !response.Sucesso.Value)
                throw new Exception($"Ocorreu um erro ao consultar o relatório de situação de cartões: {response.Mensagem}");

            var dados = response.Dados.AsQueryable();

            var produtosLista = dados.Select(x => x.ProdutoId).Distinct().ToList();

            var prodmestreLista = dados.Where(x => produtosLista.Contains(x.ProdutoMestreId));

            dados = dados.Where(x => x.ProdutoMestreId == null);

            dados = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? dados.OrderByDescending(x => x.CartaoId)
                : dados.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            dados = dados.AplicarFiltrosDinamicos(filters);



            return dados
                .ToList().Select(c => new RelatorioSituacaoDosCartoesDataType
                {
                    Identificador = c.Identificador,
                    Produto = c.Produto,
                    Documento = c.DocumentoPortador.FormatarCpfCnpj(),
                    Portador = c.Portador,
                    Status = c.StatusText,
                    StatusPedagio = prodmestreLista.Where(x => x.CartaoMestreId == c.CartaoId).Select(x => x.StatusText).FirstOrDefault(),
                    DataVinculo = c.DataVinculo?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataDesvinculo = c.DataDesvinculo?.ToString("dd/MM/yyyy HH:mm:ss"),
                    MotivoDesvinculo = c.MotivoDesvinculo,
                    Administradora = c.Administradora,
                    CnpjEmpresa = c.CnpjEmpresa.FormatarCpfCnpj(),
                    NomeEmpresa = c.Empresa,
                    DocumentoPontoDistribuicao = c.DocumentoPontoDistribuicao.FormatarCpfCnpj(),
                    PontoDistribuicao = c.PontoDistribuicao,
                    DataRecepcao = c.DataRecepcaoPontoDistribuicao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataCadastro = c.DataCadastro?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAlteracao = c.DataAlteracao?.ToString("dd/MM/yyyy HH:mm:ss")
                }).ToList();
        }

        public RelatorioConciliacaoAnaliticoDataType GetRelatorioConciliacaoAnaliticoDataType(RelatorioConciliacaoAnaliticoRequest request, bool somenteDivergencias, string pesquisa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var validationResult = new ValidationResult();
            // Buscar dados do serviço de cartões

            var response = _repository.RelatorioConciliacaoAnalitico(request);

            if (response.Status == RelatorioConciliacaoAnaliticoResponseStatus.Falha)
                validationResult.Add(response.Mensagem);

            if (!validationResult.IsValid)
                return new RelatorioConciliacaoAnaliticoDataType
                {
                    Sucesso = false,
                    Mensagem = validationResult.ToString()
                };

            if (_parametrosService.GetMetodoRefatoradoRelatorioConciliacaoAnalitico() && request.DataInicio.HasValue && request.DataFim.HasValue)
                return NovoRelatorioConciliacaoAnalitico(response, request.DataInicio.Value, request.DataFim.Value, somenteDivergencias, pesquisa, take, page, orderFilters, filters);

            if (response.Itens == null)
                response.Itens = new List<ConciliacaoItem>();

            var dadosMeioHomologado = response.Itens.AsQueryable();

            dadosMeioHomologado = dadosMeioHomologado.OrderBy(x => x.Data);

            // Aplicar filtros do usuário ao resultado do serviço de cartões
            var queryFiltersWithData = filters?.Where(f => f.CampoTipo == EFieldTipo.Date).ToList();
            if (queryFiltersWithData != null)
            {
                foreach (var item in queryFiltersWithData)
                {
                    item.Campo = item.Campo + ".Value";
                }

                dadosMeioHomologado = dadosMeioHomologado.AplicarFiltrosDinamicos(queryFiltersWithData.ToList());
                filters = filters.Where(f => f.CampoTipo != EFieldTipo.Date).ToList();
            }

            var queryableConciliacao = dadosMeioHomologado.ToList().Select(c =>
                    new RelatorioConciliacaoAnaliticoTransacaoDataType
                    {
                        ConciliacaoItem = c,
                        ProtocoloRequisicao = c.ProtocoloRequisicao,
                        ProtocoloRequisicaoPedagio = c.GetProtocoloRequisicaoPedagio(),
                        NumeroCartao = c.Cartao?.Identificador.ToString() ?? string.Empty,
                        DocumentoPortador = c.DocumentoPortador?.ToCpfOrCnpj(),
                        NomePortador = c.NomePortador ?? string.Empty,
                        Placa = GetPlacaCavalo(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0,
                            ExistenteAts(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0))?.ToPlacaFormato(),
                        NumeroRecibo = GetNumeroRecibo(c, Convert.ToInt32(c.ProtocoloRequisicao),
                            ExistenteAts(c, Convert.ToInt32(c.ProtocoloRequisicao))),
                        Ciot = GetCiot(c, Convert.ToInt32(c.ProtocoloRequisicao),
                            ExistenteAts(c, Convert.ToInt32(c.ProtocoloRequisicao))),
                        TipoEvento = GetTipoEvento(
                            c,
                            Convert.ToInt32(c.ProtocoloRequisicao),
                            ExistenteAts(c, Convert.ToInt32(c.ProtocoloRequisicao))),
                        Data = c.Data?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataDt = c.Data,
                        Valor = c.Valor?.ToString("C"),
                        ValorDecimal = c.Valor,
                        StatusAts = GetStatusNoAts(c),
                        StatusMeioHomologado = GetStatusMeioHomologado(c.StatusMeioHomologado),
                        StatusProcessadora = GetStatusProcessadora(c.StatusProcessadora),
                        StatusGeral = GetStatus(GetStatusProcessadora(c.StatusProcessadora), GetStatusMeioHomologado(c.StatusMeioHomologado), GetStatusNoAts(c)),
                        MensagemRetorno = string.IsNullOrWhiteSpace(c.MensagemRetorno)
                            ? GetMotivoAts(c, Convert.ToInt32(c.ProtocoloRequisicao),
                                ExistenteAts(c, Convert.ToInt32(c.ProtocoloRequisicao)))
                            : c.MensagemRetorno,
                        TipoTransacaoDescricao = GetTipoTransacaoDescricao(c, c.TipoTransacao),
                        TipoTransacao = c.TipoProcessadora,
                        Filial = GetFilial(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0,
                            ExistenteAts(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0)),
                        NomeUsuario = GetNomeUsuario(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0,
                            ExistenteAts(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0)),
                        DocumentoUsuario = GetDocumentoUsuario(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0,
                            ExistenteAts(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0)),
                        Informacoes = c.Informacoes,
                        IdParaCsv = GetIdViagemEventoTransacao(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0,
                            ExistenteAts(c, c.ProtocoloRequisicao.ToIntNullable(0) ?? 0)),
                        Conta = c.Cartao?.Conta
                    })
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(pesquisa))
            {
                queryableConciliacao = queryableConciliacao.Where(c => (c.Filial.Contains(pesquisa, StringComparison.OrdinalIgnoreCase))
                || (c.NomePortador.Contains(pesquisa, StringComparison.OrdinalIgnoreCase))
                || (c.Placa.Contains(pesquisa.RemoveSpecialCharacters(), StringComparison.OrdinalIgnoreCase))
                || (c.DocumentoPortador.Contains(pesquisa.OnlyNumbers(), StringComparison.OrdinalIgnoreCase))
                || (c.NumeroRecibo.Contains(pesquisa, StringComparison.OrdinalIgnoreCase))
                || (c.Ciot.Contains(pesquisa, StringComparison.OrdinalIgnoreCase))
                || (c.NumeroCartao.Contains(pesquisa, StringComparison.OrdinalIgnoreCase))
                || (c.DocumentoUsuario.Contains(pesquisa.OnlyNumbers(), StringComparison.OrdinalIgnoreCase))
                || (c.NomeUsuario.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)));
            }

            // Converter para DTO de apresentação
            var retorno = queryableConciliacao
                .AplicarOrderByDinamicos(orderFilters)
                .AplicarFiltrosDinamicos(filters)
                .ToList();

            if (somenteDivergencias)
            {
                retorno = retorno
                    .Where(c =>
                        c.StatusAts != EStatusTransacaoConciliacao.Existente ||
                        c.StatusMeioHomologado != EStatusTransacaoConciliacao.Existente ||
                        c.StatusProcessadora != EStatusTransacaoConciliacao.Existente)
                    .ToList();
            }

            // Totalizadores de valores
            var pedagioList = retorno.Where(r => r.TipoEvento == TipoEventoConciliacaoPedagio)
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var rpaList = retorno.Where(r => r.TipoEvento == ETipoEventoViagem.RPA.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var saldoList = retorno.Where(r => r.TipoEvento == ETipoEventoViagem.Saldo.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var adiantamentoList = retorno
                .Where(r => r.TipoEvento == ETipoEventoViagem.Adiantamento.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var estadiaList = retorno.Where(r => r.TipoEvento == ETipoEventoViagem.Estadia.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var anttList = retorno.Where(r => r.TipoEvento == ETipoEventoViagem.TarifaAntt.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            // Totalizadores por plataforma
            var valorAtsList = retorno.Where(r => r.StatusAts == EStatusTransacaoConciliacao.Existente)
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var meioHomologadoList = retorno
                .Where(r => r.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente)
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var processadoraList = retorno.Where(r => r.StatusProcessadora == EStatusTransacaoConciliacao.Existente)
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var diferencasList = retorno.Where(r =>
                    (r.StatusAts != EStatusTransacaoConciliacao.Existente ||
                    r.StatusMeioHomologado != EStatusTransacaoConciliacao.Existente ||
                    r.StatusProcessadora != EStatusTransacaoConciliacao.Existente)
                    && !r.TipoTransacao.In(ConciliacaoItemTipoProcessadora.ResgateValorDebito, ConciliacaoItemTipoProcessadora.ResgateValorCredito))
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            var abastecimentoList = retorno.Where(r => r.TipoEvento == ETipoEventoViagem.Abastecimento.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            // Toalizadores
            var resumo = new RelatorioConciliacaoAnaliticoResumoDataType
            {
                ValorTotalCargaAbastecimento = abastecimentoList.Any() ? abastecimentoList.Where(o => o.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador).Sum(o => o.Valor).GetValueOrDefault(0).ToString("C") : "R$ 0,00",
                ValorTotalEstornoAbastecimento = abastecimentoList.Any() ? abastecimentoList.Where(o => o.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador).Sum(c => c.Valor).GetValueOrDefault(0).ToString("C") : "R$ 0,00",

                ValorTotalCargaRpa = rpaList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(c => c.Valor)
                    .GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaSaldo = saldoList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaAdiantamento = adiantamentoList.Where(c =>
                        c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaEstadia = estadiaList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaTarifa = anttList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaPedagio = pedagioList.Where(c =>
                        c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoRpa = rpaList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoSaldo = saldoList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoAdiantamento = adiantamentoList.Where(c =>
                        c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoEstadia = estadiaList.Where(c =>
                        c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoTarifa = anttList
                    .Where(c => c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoPedagio = pedagioList.Where(c =>
                        c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalCargaAvulsa = retorno.Where(x => x.TipoEvento == TipoEventoConciliacaoCargaAvulsa && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C"),
                ValorTotalEstornoAvulsa = retorno.Where(x => x.TipoEvento == TipoEventoConciliacaoCargaAvulsa && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C"),
                ValorTotalAts = (valorAtsList
                                     .Where(c =>
                                         c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador ||
                                         c.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador)
                                     .Sum(c => c.Valor)
                                     .GetValueOrDefault(0) -
                                 valorAtsList.Where(c =>
                                         c.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador ||
                                         c.TipoProcessadora ==
                                         ConciliacaoItemTipoProcessadora.EstornoPedagioPortador)
                                     .Sum(c => c.Valor)
                                     .GetValueOrDefault(0)).ToString("C"),
                ValorTotalMeioHomologado = (meioHomologadoList
                                                .Where(c =>
                                                    c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                        .CargaPortador ||
                                                    c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                        .CargaPedagioPortador)
                                                .Sum(c => c.Valor)
                                                .GetValueOrDefault(0) -
                                            meioHomologadoList.Where(c =>
                                                    c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                        .EstornoPortador ||
                                                    c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                        .EstornoPedagioPortador)
                                                .Sum(c => c.Valor)
                                                .GetValueOrDefault(0)).ToString("C"),
                ValorTotalProcessadora = (processadoraList.Where(c =>
                                                  c.TipoProcessadora ==
                                                  ConciliacaoItemTipoProcessadora.CargaPortador ||
                                                  c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                      .CargaPedagioPortador)
                                              .Sum(c => c.Valor)
                                              .GetValueOrDefault(0) -
                                          processadoraList.Where(c =>
                                                  c.TipoProcessadora ==
                                                  ConciliacaoItemTipoProcessadora.EstornoPortador ||
                                                  c.TipoProcessadora == ConciliacaoItemTipoProcessadora
                                                      .EstornoPedagioPortador)
                                              .Sum(c => c.Valor)
                                              .GetValueOrDefault(0)).ToString("C"),
                ValorTotalDiferenca = diferencasList.Any()
                    ? diferencasList.Sum(c => c.Valor).GetValueOrDefault(0).ToString("C")
                    : "R$ 0,00",
                SaldoInicialConta = response.SaldoInicial?.SaldoConta.GetValueOrDefault(0).ToString("C"),
                SaldoFinalConta = response.SaldoFinal?.SaldoConta.GetValueOrDefault(0).ToString("C")
            };

            var totalItems = retorno.Count;
            if (take > 0)
                retorno = retorno.Skip((page - 1) * take).Take(take).ToList();

            // Resultado
            var retornoTotal = new RelatorioConciliacaoAnaliticoDataType
            {
                Sucesso = true,
                items = retorno,
                totalItems = totalItems,
                Resumo = resumo
            };

            return retornoTotal;
        }

        public RelatorioConciliacaoAnaliticoDataType NovoRelatorioConciliacaoAnalitico(
            RelatorioConciliacaoAnaliticoResponse dadosMeioHomologado, DateTime dataInicio, DateTime dataFim,
            bool somenteDivergencias, string pesquisa, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var relatorio = new RelatorioConciliacaoAnaliticoDataType
            {
                Sucesso = true,
                totalItems = 0,
                Resumo = new RelatorioConciliacaoAnaliticoResumoDataType
                {
                    SaldoInicialConta = dadosMeioHomologado.SaldoInicial?.SaldoConta.GetValueOrDefault(0).ToString("C"),
                    SaldoFinalConta = dadosMeioHomologado.SaldoFinal?.SaldoConta.GetValueOrDefault(0).ToString("C")
                }
            };

            var totalItemsDto = new List<RelatorioConciliacaoDto>();
            dadosMeioHomologado.Itens.ForEach(conciliacaoItem =>
                totalItemsDto.Add(new RelatorioConciliacaoDto(conciliacaoItem, GetStatusProcessadora(conciliacaoItem.StatusProcessadora), GetStatusMeioHomologado(conciliacaoItem.StatusMeioHomologado))));

            dadosMeioHomologado.Itens.Clear();

            var dadosCartao = BuscaDadosConciliacaoCartaoAts(
                totalItemsDto.Where(x => !x.DadosMeioHomologado.TipoProcessadora.HasValue || x.DadosMeioHomologado.TipoProcessadora.In(
                                         ConciliacaoItemTipoProcessadora.Inesperado, ConciliacaoItemTipoProcessadora.CargaPortador,
                                         ConciliacaoItemTipoProcessadora.EstornoPortador, ConciliacaoItemTipoProcessadora.OutrosCreditos,
                                         ConciliacaoItemTipoProcessadora.OutrosDebitos)
                                    ).ToList(), dataInicio, dataFim);

            var dadosPedagio = BuscaDadosConciliacaoPedagioAts(
                totalItemsDto.Where(x => x.DadosMeioHomologado.TipoProcessadora.HasValue && x.DadosMeioHomologado.TipoProcessadora.In(
                                             ConciliacaoItemTipoProcessadora.CargaPedagioPortador, ConciliacaoItemTipoProcessadora.EstornoPedagioPortador)
                                         ).ToList(), dataInicio, dataFim);

            AjustarItensConciliacao(totalItemsDto);

            Task.WaitAll(dadosCartao, dadosPedagio);
            var transacoesCartaoSomenteAts = dadosCartao.Result;
            var transacoesPedagioSomenteAts = dadosPedagio.Result;

            var dadosCartaoSemTransacao = BuscaDadosCartaoSemTransacao(dataInicio, dataFim);
            AdicionaDadosConciliacaoSomenteAts(totalItemsDto, transacoesCartaoSomenteAts, transacoesPedagioSomenteAts, dadosCartaoSemTransacao);

            var itensQuery = AplicarFiltrosEConversaoItensConciliacao(totalItemsDto, somenteDivergencias, pesquisa, take, page, orderFilters, filters);
            
            SomaValoresResumoConciliacao(relatorio, itensQuery);

            if (take > 0)
                relatorio.items = itensQuery
                    .Skip((page - 1) * take)
                    .Take(take)
                    .ToList();
            else
                relatorio.items = itensQuery.ToList();

            return relatorio;
        }

#pragma warning disable 1998
        private async Task<List<RelatorioConciliacaoAtsDto>> BuscaDadosConciliacaoCartaoAts(List<RelatorioConciliacaoDto> itensCartao, DateTime dataInicio, DateTime dataFim)
#pragma warning restore 1998
        {
            itensCartao.ForEach(x => x.DadosAts.IdTransacaoCartao = x.DadosMeioHomologado.ProtocoloRequisicao.ToIntSafe());

            var dadosTransacaoCartaoAts = _transacaoCartaoService.Find(x =>
                    (
                        (x.DataCriacao >= dataInicio && x.DataCriacao <= dataFim
                        ||
                        x.DataConfirmacaoMeioHomologado >= dataInicio && x.DataConfirmacaoMeioHomologado <= dataFim)
                        &&
                        (
                            x.IdCargaAvulsa.HasValue && x.CargaAvulsa.IdEmpresa == _idEmpresa ||
                            x.IdViagemEvento.HasValue && x.ViagemEvento.IdEmpresa == _idEmpresa &&
                            (
                                ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(x.TipoProcessamentoCartao) ||
                                ETipoProcessamentoCartaoUtils.EstornoCreditoProprietarioViagem.Contains(x.TipoProcessamentoCartao)
                            )
                        )
                    )
                ).ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            //Lista preenchida para fazer uma segunda busca por Id onde não conciliou o pedágio buscando no banco de dados por data
            var idsTransacaoCartao = new List<int>();

            foreach (var itemCartao in itensCartao)
                ConciliacaoItemCartao(itemCartao, dadosTransacaoCartaoAts, idsTransacaoCartao);

            var transacoesCartaoSomenteAts = new List<RelatorioConciliacaoAtsDto>();
            //As transações que foram conciliadas, ou não conciliadas mas a confirmação está fora da data pesquisada, são removidas da lista retornada
            transacoesCartaoSomenteAts.AddRange(dadosTransacaoCartaoAts
                .Where(x => !x.ItemConciliado &&
                     (!x.DataConfirmacaoCartao.HasValue
                     ||
                     (x.DataConfirmacaoCartao >= dataInicio && x.DataConfirmacaoCartao <= dataFim))
                 ).ToList());
            dadosTransacaoCartaoAts.Clear();

            //depois é feita uma nova consulta para buscar os ids não encontrados por data
            var transacoesCartaoPorIdTransacao = _transacaoCartaoService.Find(x => idsTransacaoCartao.Contains(x.IdTransacaoCartao))
                .ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            transacoesCartaoPorIdTransacao.ForEach(transacaoCartao =>
            {
                itensCartao.Where(y => y.DadosAts.IdTransacaoCartao == transacaoCartao.IdTransacaoCartao)
                    .ForEach(itemCartao =>
                    {
                        ConciliacaoItemCartao(itemCartao, new List<RelatorioConciliacaoAtsDto> { transacaoCartao });
                    });
            });

            return transacoesCartaoSomenteAts;
        }

        private void ConciliacaoItemCartao(RelatorioConciliacaoDto itemCartao, List<RelatorioConciliacaoAtsDto> transacoesCartaoAts, List<int> idsTransacaoCartao = null)
        {
            if (!itemCartao.DadosAts.IdTransacaoCartao.HasValue)
            {
                itemCartao.ExistenteAts = false;
                itemCartao.StatusAts = EStatusTransacaoConciliacao.Inexistente;
                itemCartao.DadosAts.MotivoAts = "Transação cartão sem informações de vinculo a Extratta";
                itemCartao.DadosAts.TipoEventoViagem = !string.IsNullOrWhiteSpace(itemCartao.TipoEvento)
                    ? EnumHelper.GetEnumByDescription<ETipoEventoViagem>(itemCartao.TipoEvento)
                    : (ETipoEventoViagem?)null;

                return;
            }

            var transacaoCartao = transacoesCartaoAts.FirstOrDefault(y => y.IdTransacaoCartao == itemCartao.DadosAts.IdTransacaoCartao);

            if (transacaoCartao == null)
            {
                itemCartao.ExistenteAts = false;
                itemCartao.StatusAts = EStatusTransacaoConciliacao.Inexistente;
                itemCartao.DadosAts.MotivoAts = "Transação cartão não localizado no ATS";
                itemCartao.DadosAts.TipoEventoViagem = !string.IsNullOrWhiteSpace(itemCartao.TipoEvento)
                    ? EnumHelper.GetEnumByDescription<ETipoEventoViagem>(itemCartao.TipoEvento)
                    : (ETipoEventoViagem?)null;

                //Lista preenchida para fazer uma segunda busca por Id onde não conciliou o pedágio buscando no banco de dados por data
                idsTransacaoCartao?.Add(itemCartao.DadosAts.IdTransacaoCartao ?? 0);

                return;
            }

            transacaoCartao.ItemConciliado = true;
            itemCartao.DadosAts = transacaoCartao;
            itemCartao.ExistenteAts = true;
            itemCartao.TipoEvento = itemCartao.DadosAts.IdCargaAvulsa.HasValue ? TipoEventoConciliacaoCargaAvulsa : itemCartao.DadosAts.TipoEventoViagem.GetDescription();

            itemCartao.StatusAts = GetStatusAts(itemCartao.DadosAts.StatusPagamento);
            itemCartao.DadosAts.MotivoAts = itemCartao.DadosAts.StatusPagamento != EStatusPagamentoCartao.Baixado ?
                itemCartao.DadosAts.MensagemProcessamentoWs : null;
        }

#pragma warning disable 1998
        private async Task<List<RelatorioConciliacaoAtsDto>> BuscaDadosConciliacaoPedagioAts(List<RelatorioConciliacaoDto> itensPedagio, DateTime dataInicio, DateTime dataFim)
#pragma warning restore 1998
        {
            //Obtém o IdViagem (ProtocoloRequisicaoPedagio) dos metadados da registro do meio homologado
            itensPedagio.ForEach(x =>
            {
                var idViagem = x.DadosMeioHomologado.GetProtocoloRequisicaoPedagio();

                //O ProtocoloRequisicaoPedagio no MH é o IdViagem, quando ocorre um cancelamento para não duplicar o protocolo é utilizado o valor negativo
                if (idViagem < 0)
                    x.DadosAts.IdViagem = idViagem * -1;
                else
                    x.DadosAts.IdViagem = idViagem;
            });

            //é realizada uma primeira consulta por data porque para trazer inclusive transações que podem existir apenas no ATS
            var transacoesCargaPedagio = _viagemRepository.Find(x => x.IdEmpresa == _idEmpresa && x.ResultadoCompraPedagio != EResultadoCompraPedagio.NaoRealizado &&
                    (
                        (x.DataConfirmacaoPedagio.HasValue && x.DataConfirmacaoPedagio >= dataInicio && x.DataConfirmacaoPedagio <= dataFim)
                        ||
                        (x.DataConfirmacaoCreditoPedagio.HasValue && x.DataConfirmacaoCreditoPedagio >= dataInicio && x.DataConfirmacaoCreditoPedagio <= dataFim)
                    )
                ).ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            var transacoesEstornoPedagio = _viagemRepository.Find(x => x.IdEmpresa == _idEmpresa &&
                  (x.ResultadoCompraPedagio == EResultadoCompraPedagio.CancelamentoSolicitado || x.ResultadoCompraPedagio == EResultadoCompraPedagio.CancelamentoConfirmado) &&
                  (
                      (x.DataCancelamentoPedagio.HasValue && x.DataCancelamentoPedagio >= dataInicio && x.DataCancelamentoPedagio <= dataFim)
                      ||
                      (x.DataConfirmacaoEstornoPedagio.HasValue && x.DataConfirmacaoEstornoPedagio >= dataInicio && x.DataConfirmacaoEstornoPedagio <= dataFim)
                  )
                ).ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            //Lista preenchida para fazer uma segunda busca por Id onde não conciliou o pedágio buscando no banco de dados por data
            var idsViagem = new List<int>();
            foreach (var itemPedagio in itensPedagio)
                ConciliacaoItemPedagio(itemPedagio, transacoesCargaPedagio, transacoesEstornoPedagio, idsViagem);

            //As transações que foram conciliadas, ou não conciliadas mas a confirmação está fora da data pesquisada, são removidas da lista retornada
            var transacoesPedagioSomenteAts = new List<RelatorioConciliacaoAtsDto>();
            transacoesPedagioSomenteAts.AddRange(transacoesCargaPedagio
                .Where(x => !x.ItemConciliado &&
                    (!x.DataConfirmacaoCompraPedagio.HasValue
                     ||
                    (x.DataConfirmacaoCompraPedagio >= dataInicio && x.DataConfirmacaoCompraPedagio <= dataFim))
                ).ToList());
            transacoesPedagioSomenteAts.AddRange(transacoesEstornoPedagio
                .Where(x => !x.ItemConciliado &&
                            (!x.DataConfirmacaoCancelamentoPedagio.HasValue
                             ||
                             (x.DataConfirmacaoCancelamentoPedagio >= dataInicio && x.DataConfirmacaoCancelamentoPedagio <= dataFim))
                ).ToList());
            transacoesCargaPedagio.Clear();
            transacoesEstornoPedagio.Clear();

            //depois é feita uma nova consulta para buscar os ids não encontrados por data
            var transacoesPedagioPorIdViagem = _viagemRepository.Find(x => idsViagem.Contains(x.IdViagem))
                .ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            transacoesPedagioPorIdViagem.ForEach(transacaoPedagio =>
            {
                itensPedagio.Where(y => y.DadosAts.IdViagem == transacaoPedagio.IdViagem)
                    .ForEach(itemPedagio =>
                {
                    if (itemPedagio.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador)
                        ConciliacaoItemPedagio(itemPedagio, new List<RelatorioConciliacaoAtsDto> { transacaoPedagio }, null);
                    else
                        ConciliacaoItemPedagio(itemPedagio, null, new List<RelatorioConciliacaoAtsDto> { transacaoPedagio });
                });
            });

            return transacoesPedagioSomenteAts;
        }

        private void ConciliacaoItemPedagio(RelatorioConciliacaoDto itemPedagio, List<RelatorioConciliacaoAtsDto> transacoesCargaPedagio, List<RelatorioConciliacaoAtsDto> transacoesEstornoPedagio, List<int> idsViagem = null)
        {
            itemPedagio.TipoEvento = TipoEventoConciliacaoPedagio;

            if (itemPedagio.DadosAts.IdViagem == null)
            {
                itemPedagio.ExistenteAts = false;
                itemPedagio.StatusAts = EStatusTransacaoConciliacao.Inexistente;
                itemPedagio.DadosAts.MotivoAts = "Carga de pedágio sem informações de vinculo a Extratta";
                return;
            }

            var transacaoPedagio = itemPedagio.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador
                ? transacoesCargaPedagio?.FirstOrDefault(y => y.IdViagem == itemPedagio.DadosAts.IdViagem)
                : transacoesEstornoPedagio?.FirstOrDefault(y => y.IdViagem == itemPedagio.DadosAts.IdViagem);

            if (transacaoPedagio == null)
            {
                itemPedagio.ExistenteAts = false;
                itemPedagio.StatusAts = EStatusTransacaoConciliacao.Inexistente;
                itemPedagio.DadosAts.MotivoAts = "Pedágio não localizado no ATS";

                //Lista preenchida para fazer uma segunda busca por Id onde não conciliou o pedágio buscando no banco de dados por data
                idsViagem?.Add(itemPedagio.DadosAts.IdViagem ?? 0);
                return;
            }

            transacaoPedagio.ItemConciliado = true;
            itemPedagio.DadosAts = transacaoPedagio;
            itemPedagio.ExistenteAts = true;

            if (itemPedagio.DadosAts.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CompraConfirmada,
                EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado))
            {
                itemPedagio.StatusAts = EStatusTransacaoConciliacao.Existente;

                if (itemPedagio.DadosAts.ValorPedagio != itemPedagio.DadosMeioHomologado.Valor)
                    itemPedagio.DadosAts.MotivoAts = $"Divegência de valor entre ATS ({itemPedagio.DadosAts.ValorPedagio.FormatMoney()}) e carga de pedágio ({itemPedagio.DadosMeioHomologado.Valor.FormatMoney()}).";
            }
            else //else duvidoso mas o código antigo estava assim, provavelmente está errado
                itemPedagio.StatusAts = EStatusTransacaoConciliacao.Pendente;
        }

        private void AjustarItensConciliacao(IEnumerable<RelatorioConciliacaoDto> itens)
        {
            // Resgate saldo residual
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
                .ForEach(x =>
                {
                    x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoConciliacaoResgateSaldoResidualPedagio;
                    x.StatusAts = EStatusTransacaoConciliacao.Existente;
                    x.DadosAts.MotivoAts = "Resgate de saldo residual de pedágio";
                });

            // Depósito empresa
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora.In(
                        ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                        ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa))
                .ForEach(x =>
                {
                    x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoDepositoEmpresa;
                    x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });

            // Transferência via PIX - Crédito
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.TransferenciaViaPixCredito)
                .ForEach(x =>
                {
                    x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoTransferenciaViaPixCredito;
                    x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });

            // Transferência via PIX - Débito
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.TipoEvento == null &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.TransferenciaViaPixDebito)
                .ForEach(x =>
                {
                    //x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoTransferenciaViaPixDebito;
                    //x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });

            // Pagamento de contas
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.PagamentoDeContas)
                .ForEach(x =>
                {
                    //x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoConciliacaoPagamentoConta;
                    //x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });

            // Estorno pagamento de contas
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPagamentoDeContas)
                .ForEach(x =>
                {
                    //x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoConciliacaoEstornoPagamentoConta;
                    //x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });


            // Resgate Valor Débito
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateValorDebito)
                .ForEach(x =>
                {
                    //x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoConciliacaoResgateValorDebito;
                    //x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });

            // Resgate Valor Crédito
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateValorCredito)
                .ForEach(x =>
                {
                    //x.ExistenteAts = true;
                    x.TipoEvento = TipoEventoConciliacaoResgateValorCredito;
                    //x.StatusAts = EStatusTransacaoConciliacao.Existente;
                });
            
            // Pedagio
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosAts.IdCargaAvulsa.HasValue &&
                    x.DadosAts.TipoCarga == ETipoCarga.Provisionamento &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                .ForEach(x =>
                {
                    x.TipoEvento = TipoEventoPedagioCargaAvulsaProvisionamento;
                });
            
            // Estorno Pedagio
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosAts.IdCargaAvulsa.HasValue &&
                    x.DadosAts.TipoCarga == ETipoCarga.Provisionamento &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .ForEach(x =>
                {
                    x.TipoEvento = TipoEventoPedagioCargaAvulsaProvisionamento;
                });
            
            // Vale Pedagio
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosAts.IdCargaAvulsa.HasValue &&
                    x.DadosAts.IdViagem.HasValue &&
                    x.DadosAts.TipoCarga == ETipoCarga.Provisionamento &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                .ForEach(x =>
                {
                    x.TipoEvento = TipoEventoValePedagioCargaAvulsaProvisionamento;
                });
            
            // Estorno Vale Pedagio
            itens.Where(x =>
                    x.DadosMeioHomologado.TipoProcessadora.HasValue &&
                    x.DadosAts.IdCargaAvulsa.HasValue &&
                    x.DadosAts.IdViagem.HasValue &&
                    x.DadosAts.TipoCarga == ETipoCarga.Provisionamento &&
                    x.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .ForEach(x =>
                {
                    x.TipoEvento = TipoEventoValePedagioCargaAvulsaProvisionamento;
                });
        }

        private List<RelatorioConciliacaoAtsDto> BuscaDadosCartaoSemTransacao(DateTime dataInicio, DateTime dataFim)
        {
            var eventosSemTransacaoBaixados = _viagemEventoService.Find(x =>
                    x.IdEmpresa == _idEmpresa && x.HabilitarPagamentoCartao && x.Status == EStatusViagemEvento.Baixado &&
                    x.DataHoraPagamento >= dataInicio && x.DataHoraPagamento <= dataFim &&
                    !x.TransacaoCartao.Any(t => ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(t.TipoProcessamentoCartao)))
                .ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            var eventosSemTransacaoCancelados = _viagemEventoService.Find(x =>
                   x.IdEmpresa == _idEmpresa && x.HabilitarPagamentoCartao && x.Status == EStatusViagemEvento.Cancelado &&
                   x.DataHoraCancelamento >= dataInicio && x.DataHoraCancelamento <= dataFim &&
                   x.TransacaoCartao.Any(t => ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(t.TipoProcessamentoCartao)) &&
                   !x.TransacaoCartao.Any(t => ETipoProcessamentoCartaoUtils.EstornoCreditoProprietarioViagem.Contains(t.TipoProcessamentoCartao)))
                .ProjectTo<RelatorioConciliacaoAtsDto>().ToList();

            var eventosSemTransacao = new List<RelatorioConciliacaoAtsDto>();
            eventosSemTransacao.AddRange(eventosSemTransacaoBaixados);
            eventosSemTransacaoBaixados.Clear();

            eventosSemTransacao.AddRange(eventosSemTransacaoCancelados);
            eventosSemTransacaoCancelados.Clear();

            return eventosSemTransacao;
        }

        private void AdicionaDadosConciliacaoSomenteAts(List<RelatorioConciliacaoDto> totalItemsDto, List<RelatorioConciliacaoAtsDto> transacoesCartaoSomenteAts, List<RelatorioConciliacaoAtsDto> transacoesPedagioSomenteAts, List<RelatorioConciliacaoAtsDto> dadosCartaoSemTransacao)
        {
            transacoesCartaoSomenteAts.ForEach(dadosAts =>
            {
                var statusAts = GetStatusAts(dadosAts.StatusPagamento);

                //Se tiver com status inexistente nos 3 lugares deve ser ignorado no relatório, não é um registro que precisa ser corrigido
                if (statusAts == EStatusTransacaoConciliacao.Inexistente)
                    return;

                var tipoProcessadora = dadosAts.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAvulsa
                                       || ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(dadosAts.TipoProcessamentoCartao)
                    ? ConciliacaoItemTipoProcessadora.CargaPortador
                    : ConciliacaoItemTipoProcessadora.EstornoPortador;

                var tipoEvento = dadosAts.IdCargaAvulsa.HasValue ? TipoEventoConciliacaoCargaAvulsa : dadosAts.TipoEventoViagem.GetDescription();
                dadosAts.MotivoAts = "Transação cartão sem informações de vinculo ao Meio Homologado";

                totalItemsDto.Add(new RelatorioConciliacaoDto(dadosAts, tipoProcessadora, tipoEvento, statusAts));
            });
            transacoesCartaoSomenteAts.Clear();

            transacoesPedagioSomenteAts.ForEach(dadosAts =>
            {
                EStatusTransacaoConciliacao? statusAtsCompra;
                ConciliacaoItemTipoProcessadora? tipoProcessadora;

                if (!dadosAts.DataCancelamentoPedagio.HasValue && !dadosAts.ResultadoCompraPedagio.In(
                        EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado))
                {
                    dadosAts.DataPedagio = dadosAts.DataCompraPedagio;
                    tipoProcessadora = ConciliacaoItemTipoProcessadora.CargaPedagioPortador;
                    statusAtsCompra = dadosAts.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CompraConfirmada,
                        EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado)
                        ? EStatusTransacaoConciliacao.Existente
                        : EStatusTransacaoConciliacao.Pendente;
                }
                else
                {
                    dadosAts.DataPedagio = dadosAts.DataCancelamentoPedagio;
                    tipoProcessadora = ConciliacaoItemTipoProcessadora.EstornoPedagioPortador;
                    statusAtsCompra = dadosAts.ResultadoCompraPedagio == EResultadoCompraPedagio.CancelamentoConfirmado
                        ? EStatusTransacaoConciliacao.Existente
                        : EStatusTransacaoConciliacao.Pendente;
                }
                dadosAts.MotivoAts = "Transação pedágio sem informações de vinculo ao Meio Homologado";

                totalItemsDto.Add(new RelatorioConciliacaoDto(dadosAts, tipoProcessadora.Value,
                    TipoEventoConciliacaoPedagio, statusAtsCompra.Value));

                if (!dadosAts.DataCancelamentoPedagio.HasValue && !dadosAts.ResultadoCompraPedagio.In(
                        EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado))
                    return;
            });
            transacoesPedagioSomenteAts.Clear();

            dadosCartaoSemTransacao.ForEach(dadosAts =>
            {
                //Neste caso vai mostrar no relatório porque é um bug ou um registro que deve ser corrigido
                var statusAts = GetStatusAts((EStatusPagamentoCartao?)null);
                var tipoEvento = dadosAts.TipoEventoViagem.GetDescription();
                dadosAts.MotivoAts = "Parcelas de viagem sem informações de transação no ATS";
                totalItemsDto.Add(dadosAts.StatusEvento == EStatusViagemEvento.Baixado
                    ? new RelatorioConciliacaoDto(dadosAts, ConciliacaoItemTipoProcessadora.CargaPortador, tipoEvento, statusAts)
                    : new RelatorioConciliacaoDto(dadosAts, ConciliacaoItemTipoProcessadora.EstornoPortador, tipoEvento, statusAts));
            });
            dadosCartaoSemTransacao.Clear();
        }

        private EStatusTransacaoConciliacao GetStatusAts(EStatusPagamentoCartao? statusPagamento)
        {
            switch (statusPagamento)
            {
                case EStatusPagamentoCartao.Aberto:
                    return EStatusTransacaoConciliacao.Inexistente;
                case EStatusPagamentoCartao.Erro:
                    return EStatusTransacaoConciliacao.Inexistente;
                case EStatusPagamentoCartao.Baixado:
                    return EStatusTransacaoConciliacao.Existente;
                case EStatusPagamentoCartao.Pendente:
                    return EStatusTransacaoConciliacao.Pendente;
                default:
                    return EStatusTransacaoConciliacao.Inexistente;
            }
            // return statusPagamento switch
            // {
            //     EStatusPagamentoCartao.Aberto => EStatusTransacaoConciliacao.Inexistente,
            //     EStatusPagamentoCartao.Erro => EStatusTransacaoConciliacao.Inexistente,
            //     EStatusPagamentoCartao.Baixado => EStatusTransacaoConciliacao.Existente,
            //     EStatusPagamentoCartao.Pendente => EStatusTransacaoConciliacao.Pendente,
            //     _ => EStatusTransacaoConciliacao.Inexistente
            // };
        }

        private class RelatorioConciliacaoInfosAdicionais
        {
            public int? IdTransacaoCartao { get; set; }
            public int? IdViagem { get; set; }
            public string DocumentoCliente { get; set; }
            public int? IdCargaAvulsa { get; set; }
            public ETipoCarga? TipoCarga { get; set; }
        }

        private IQueryable<RelatorioConciliacaoAnaliticoTransacaoDataType> AplicarFiltrosEConversaoItensConciliacao(
            List<RelatorioConciliacaoDto> totalItemsDto,
            bool somenteDivergencias,
            string pesquisa,
            int take,
            int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var ordemEfetiva = orderFilters != null && !string.IsNullOrWhiteSpace(orderFilters.Campo) ? Mapper.Map<OrderFilters>(orderFilters) : new OrderFilters(nameof(RelatorioConciliacaoAnaliticoTransacaoDataType.DataDt), EOperadorOrder.Ascending);
            if (ordemEfetiva?.Campo == nameof(RelatorioConciliacaoAnaliticoTransacaoDataType.Data))
                ordemEfetiva.Campo = nameof(RelatorioConciliacaoAnaliticoTransacaoDataType.DataDt);

            if (somenteDivergencias)
            {
                totalItemsDto = totalItemsDto.Where(c =>
                        c.StatusAts != EStatusTransacaoConciliacao.Existente ||
                        c.StatusMeioHomologado != EStatusTransacaoConciliacao.Existente ||
                        c.StatusProcessadora != EStatusTransacaoConciliacao.Existente)
                    .ToList();
            }

            var itensConciliacaoAnaliticoDataType = Mapper.Map<List<RelatorioConciliacaoDto>, List<RelatorioConciliacaoAnaliticoTransacaoDataType>>(totalItemsDto)
                .AsQueryable();

            totalItemsDto.Clear();

            if (!string.IsNullOrWhiteSpace(pesquisa))
            {
                itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType.Where(c =>
                    c.Filial.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)
                    || c.NomePortador.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)
                    || c.Placa.Contains(pesquisa.RemoveSpecialCharacters(), StringComparison.OrdinalIgnoreCase)
                    || c.DocumentoPortador.Contains(pesquisa.OnlyNumbers(), StringComparison.OrdinalIgnoreCase)
                    || c.NumeroRecibo.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)
                    || c.Ciot.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)
                    || c.NumeroCartao.Contains(pesquisa, StringComparison.OrdinalIgnoreCase)
                    || c.DocumentoUsuario.Contains(pesquisa.OnlyNumbers(), StringComparison.OrdinalIgnoreCase)
                    || c.NomeUsuario.Contains(pesquisa, StringComparison.OrdinalIgnoreCase));
            }

            var filtroValor = filters?.FirstOrDefault(f => f.Campo == "Valor");
            if (filtroValor != null)
            {
                if (decimal.TryParse(filtroValor.Valor, out var valorPesquisado))
                {
                    itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType.Where(x =>
                        (x.ValorDecimal.HasValue && Math.Floor(x.ValorDecimal.Value) == valorPesquisado) ||
                        (x.ValorDecimal.HasValue && x.ValorDecimal.Value == valorPesquisado));
                }
                filters.Remove(filtroValor);
            }

            itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType.AsQueryable();
            
            var filtroDocViagem = filters?.FirstOrDefault(f => f.Campo == "NumeroRecibo");
            if (filtroDocViagem != null)
            {
                itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType.Where(c => c.NumeroRecibo != null && c.NumeroRecibo.Contains(filtroDocViagem.Valor));
                filters.Remove(filtroDocViagem);
            }
            
            var filtroIdViagem = filters?.FirstOrDefault(f => f.Campo == "ProtocoloRequisicaoPedagio");
            if (filtroIdViagem != null)
            {
                var valorFiltroIdViagem = filtroIdViagem.Valor.OnlyNumbers().ToIntSafe() ?? 0;
                itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType.Where(c => c.ProtocoloRequisicaoPedagio == valorFiltroIdViagem);
                filters.Remove(filtroIdViagem);
            }
            
            itensConciliacaoAnaliticoDataType = itensConciliacaoAnaliticoDataType
                .AplicarOrderByDinamicos(ordemEfetiva)
                .AplicarFiltrosDinamicos(filters);

            return itensConciliacaoAnaliticoDataType;
        }

        private void SomaValoresResumoConciliacao(RelatorioConciliacaoAnaliticoDataType relatorio, IQueryable<RelatorioConciliacaoAnaliticoTransacaoDataType> itensQuery)
        {
            relatorio.totalItems = itensQuery.Count();

            relatorio.Resumo.ValorTotalCargaRpa = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.RPA
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoRpa = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.RPA
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaSaldo = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Saldo
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoSaldo = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Saldo
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaAdiantamento = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Adiantamento
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoAdiantamento = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Adiantamento
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaEstadia = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Estadia
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoEstadia = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.Estadia
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaTarifa = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.TarifaAntt
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoTarifa = itensQuery.Where(x => x.TipoEventoViagem.HasValue && x.TipoEventoViagem == (int)ETipoEventoViagem.TarifaAntt
                    && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaPedagio = itensQuery.Where(x => x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPedagioPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoPedagio = itensQuery.Where(x => x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalCargaAvulsa = itensQuery.Where(x => x.TipoEvento == TipoEventoConciliacaoCargaAvulsa
                                                                           && x.TipoTransacao == ConciliacaoItemTipoProcessadora.CargaPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalEstornoAvulsa = itensQuery.Where(x => x.TipoEvento == TipoEventoConciliacaoCargaAvulsa
                                                                             && x.TipoTransacao == ConciliacaoItemTipoProcessadora.EstornoPortador)
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            relatorio.Resumo.ValorTotalAts =
                (
                    itensQuery.Where(x => x.StatusAts == EStatusTransacaoConciliacao.Existente &&
                                             x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.CargaPortador, ConciliacaoItemTipoProcessadora.CargaPedagioPortador))
                        .Sum(x => x.ValorDecimal).GetValueOrDefault(0)
                    -
                    itensQuery.Where(x => x.StatusAts == EStatusTransacaoConciliacao.Existente &&
                                             x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.EstornoPortador, ConciliacaoItemTipoProcessadora.EstornoPedagioPortador))
                        .Sum(x => x.ValorDecimal).GetValueOrDefault(0)
                ).ToString("C");

            relatorio.Resumo.ValorTotalMeioHomologado =
            (
                itensQuery.Where(x => x.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente &&
                                         x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.CargaPortador, ConciliacaoItemTipoProcessadora.CargaPedagioPortador))
                    .Sum(x => x.ValorDecimal).GetValueOrDefault(0)
                -
                itensQuery.Where(x => x.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente &&
                                         x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.EstornoPortador, ConciliacaoItemTipoProcessadora.EstornoPedagioPortador))
                    .Sum(x => x.ValorDecimal).GetValueOrDefault(0)
            ).ToString("C");

            relatorio.Resumo.ValorTotalProcessadora =
            (
                itensQuery.Where(x => x.StatusProcessadora == EStatusTransacaoConciliacao.Existente &&
                                         x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.CargaPortador, ConciliacaoItemTipoProcessadora.CargaPedagioPortador))
                    .Sum(x => x.ValorDecimal).GetValueOrDefault(0)
                +
                itensQuery.Where(x => x.StatusProcessadora == EStatusTransacaoConciliacao.Existente &&
                                      x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.EstornoPortador, ConciliacaoItemTipoProcessadora.EstornoPedagioPortador))
                    .Sum(x => x.ValorDecimal).GetValueOrDefault(0)

            ).ToString("C");

            relatorio.Resumo.ValorTotalDiferenca = itensQuery.Where(x =>
                    (x.StatusAts != EStatusTransacaoConciliacao.Existente ||
                    x.StatusMeioHomologado != EStatusTransacaoConciliacao.Existente ||
                    x.StatusProcessadora != EStatusTransacaoConciliacao.Existente)
                    && !x.TipoTransacao.In(ConciliacaoItemTipoProcessadora.ResgateValorDebito, ConciliacaoItemTipoProcessadora.ResgateValorCredito))
                .Sum(x => x.ValorDecimal).GetValueOrDefault(0).ToString("C");

            var abastecimentoList = itensQuery.Where(r => r.TipoEvento == ETipoEventoViagem.Abastecimento.GetDescription())
                .Select(r => (ConciliacaoItem)r.ConciliacaoItem)
                .ToList();

            relatorio.Resumo.ValorTotalCargaAbastecimento = abastecimentoList.Any()
                ? abastecimentoList.Where(o => o.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPortador)
                    .Sum(o => o.Valor).GetValueOrDefault(0).ToString("C")
                : "R$ 0,00";

            relatorio.Resumo.ValorTotalEstornoAbastecimento = abastecimentoList.Any()
                ? abastecimentoList.Where(o => o.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPortador)
                    .Sum(c => c.Valor).GetValueOrDefault(0).ToString("C")
                : "R$ 0,00";
        }

        public object RelatorioTransferenciasContasBancarias(RelatorioTransferenciasContaBancariaRequest request,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var response = _repository.RelatorioTransferenciasContaBancaria(request);

            if (response.Status == RelatorioTransferenciasContaBancariaResponseStatus.Falha)
                throw new Exception($"Ocorreu um erro ao consultar o relatório de transferências: {response.Mensagem}");

            var dados = response.Itens.AsQueryable();
            if (filters != null)
            {
                var queryFiltersWithData = filters.Where(f => f.CampoTipo == EFieldTipo.Date).ToList();
                if (queryFiltersWithData.Any())
                {
                    foreach (var item in queryFiltersWithData)
                        item.Campo += ".Value";

                    dados = dados.AplicarFiltrosDinamicos(queryFiltersWithData);
                    filters = filters.Where(f => f.CampoTipo != EFieldTipo.Date).ToList();
                }
            }

            var retorno = new
            {
                totalItems = dados.ToList().Count,
                NumSucessos = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Sucesso).ToList().Count,
                NumErros = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Erro).ToList().Count,
                NumExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Exportado).ToList().Count,
                NumNaoExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado).ToList().Count,
                ValorTotalItems = dados.ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalSucessos = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Sucesso).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalErros = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Erro).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Exportado).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalNaoExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                items = dados.ToList().Select(c => new
                {
                    c.TransacaoId,
                    c.IdentificadorCartao,
                    c.CpfCnpjPortador,
                    c.NomePortador,
                    c.CpfCnpjConta,
                    c.Conta,
                    c.NomeDonoConta,
                    c.TipoTransacao,
                    c.ProtocoloRequisicao,
                    DataAlteracao = c.DataAlteracao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataCadastro = c.DataCadastro?.ToString("dd/MM/yyyy HH:mm:ss"),
                    c.MensagemExportacao,
                    Valor = c.Valor?.ToString("C"),
                    c.Status,
                    StatusExportacao = c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado
                                ? "Não exportado"
                                : c.StatusExportacao.ToString(),
                    c.NomeContaBancaria,
                    DataExportacao = c.DataExportacao?.Year < 1000 ? null : c.DataExportacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAlteracaoExportacao = c.DataAlteracaoExportacao?.Year < 1000 ? null : c.DataAlteracaoExportacao?.ToString("dd/MM/yyyy HH:mm:ss")
                }
                    ).AsQueryable().AplicarOrderByDinamicos(orderFilters).AplicarFiltrosDinamicos(filters).ToList()
                    .Skip((page - 1) * take).Take(take).ToList()
            };

            return retorno;
        }

        public RelatorioTransferenciaCotnasBancariasFull GetRelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaRequest request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var response = _repository.RelatorioTransferenciasContaBancaria(request);

            if (response.Status == RelatorioTransferenciasContaBancariaResponseStatus.Falha)
                throw new Exception($"Ocorreu um erro ao consultar o relatório de transferências: {response.Mensagem}");

            var dados = response.Itens.AsQueryable();
            var queryFiltersWithData = filters?.Where(f => f.CampoTipo == EFieldTipo.Date);
            if (queryFiltersWithData != null)
            {
                foreach (var item in queryFiltersWithData)
                {
                    item.Campo = item.Campo + ".Value";
                }

                dados = dados.AplicarFiltrosDinamicos(queryFiltersWithData.ToList());
                filters = filters.Where(f => f.CampoTipo != EFieldTipo.Date).ToList();
            }

            var retorno = dados.ToList().Select(c => new RelatorioTransferenciaContasBancariasDataType
            {
                TransacaoId = c.TransacaoId,
                IdentificadorCartao = c.IdentificadorCartao,
                CpfCnpjPortador = c.CpfCnpjPortador,
                NomePortador = c.NomePortador,
                Conta = c.Conta,
                TipoTransacao = c.TipoTransacao,
                ProtocoloRequisicao = c.ProtocoloRequisicao,
                DataAlteracao = c.DataAlteracao?.ToString("dd/MM/yyyy HH:mm:ss"),
                DataCadastro = c.DataCadastro?.ToString("dd/MM/yyyy HH:mm:ss"),
                MensagemExportacao = c.MensagemExportacao,
                Valor = c.Valor?.ToString("C"),
                Status = c.Status,
                StatusExportacao = c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado
                        ? "Não exportado"
                        : c.StatusExportacao.ToString(),
                NomeContaBancaria = c.NomeContaBancaria,
                DataExportacao = c.DataExportacao?.Year < 1000
                        ? null
                        : c.DataExportacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                DataAlteracaoExportacao = c.DataAlteracaoExportacao?.Year < 1000
                        ? null
                        : c.DataAlteracaoExportacao?.ToString("dd/MM/yyyy HH:mm:ss")

            }
            ).AsQueryable().AplicarOrderByDinamicos(order).AplicarFiltrosDinamicos(filters).ToList().ToList();

            var resumo = new ResumoTransferenciaContaBancaria
            {
                NumTotalItems = dados.ToList().Count.ToString(),
                NumSucessos = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Sucesso).ToList().Count.ToString(),
                NumErros = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Erro).ToList().Count.ToString(),
                NumExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Exportado).ToList().Count.ToString(),
                NumNaoExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado).ToList().Count.ToString(),
                ValorTotalItems = dados.ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalSucessos = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Sucesso).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalErros = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Erro).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.Exportado).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
                ValorTotalNaoExportados = dados.ToList().Where(c => c.StatusExportacao == ItemTransacaoStatusExportacao.NaoExportado).ToList().Sum(c => c.Valor).GetValueOrDefault(0).ToString("C"),
            };

            var retornoFull = new RelatorioTransferenciaCotnasBancariasFull
            {
                Resumo = resumo,
                DataType = retorno
            };
            return retornoFull;
        }

        private static string GetTipoTransacaoDescricao(ConciliacaoItem conciliacaoItem, int? enumValue)
        {
            if (conciliacaoItem == null)
                return "Inesperado";

            return conciliacaoItem.TipoProcessadora switch
            {
                ConciliacaoItemTipoProcessadora.CargaPortador => "Carga",
                ConciliacaoItemTipoProcessadora.EstornoPortador => "Estorno",
                ConciliacaoItemTipoProcessadora.CargaPedagioPortador => "Carga",
                ConciliacaoItemTipoProcessadora.EstornoPedagioPortador => "Estorno",
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa => "Estorno depósito",
                ConciliacaoItemTipoProcessadora.DepositoEmpresa => "Depósito",
                ConciliacaoItemTipoProcessadora.OutrosDebitos => "Débito",
                ConciliacaoItemTipoProcessadora.OutrosCreditos => "Crédito",
                ConciliacaoItemTipoProcessadora.Inesperado => "Inesperado",
                ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio => "Resgate",
                ConciliacaoItemTipoProcessadora.CargaPedagioManual => "Carga",
                ConciliacaoItemTipoProcessadora.EstornoPedagioManual => "Estorno",
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixDebito => "Débito",
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixCredito => "Crédito",
                ConciliacaoItemTipoProcessadora.PagamentoDeContas => "Débito",
                ConciliacaoItemTipoProcessadora.EstornoPagamentoDeContas => "Crédito",
                ConciliacaoItemTipoProcessadora.ResgateValorDebito => "Débito",
                ConciliacaoItemTipoProcessadora.ResgateValorCredito => "Crédito",
                _ => conciliacaoItem.TipoProcessadora.ToString()
            };
        }

        private string GetMotivoAts(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return "Carga de pedágio sem informações de vinculo a Extratta";

                protocoloRequisicao = Math.Abs(protocoloRequisicao.Value);

                var pedagio = _viagemRepository
                    .GetQuery(protocoloRequisicao.Value)
                    .Select(v => new
                    {
                        v.ResultadoCompraPedagio,
                        v.MensagemCompraPedagio,
                        v.ValorPedagio
                    })
                    .FirstOrDefault();

                if (pedagio == null)
                    return "Pedágio não localizado no ATS";

                if (conciliacaoItem.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
                    return "Resgate de saldo residual de pedágio";

                if (pedagio.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada ||
                    pedagio.ResultadoCompraPedagio == EResultadoCompraPedagio.CancelamentoConfirmado)
                {
                    if (pedagio.ValorPedagio != conciliacaoItem.Valor)
                        return
                            $"Divegência de valor entre ATS ({pedagio.ValorPedagio.FormatMoney()}) e carga de pedágio ({conciliacaoItem.Valor.FormatMoney()}).";
                    return string.Empty;
                }

                return pedagio.MensagemCompraPedagio;
            }

            if (!existente)
                return string.Empty;

            var transacaoService = _transacaoCartaoService;

            var transacao = transacaoService.GetQuery(idtransacao)
                .Select(t => new { t.StatusPagamento, t.MensagemProcessamentoWs })
                .First();

            switch (transacao.StatusPagamento)
            {
                case EStatusPagamentoCartao.Aberto:
                    return transacao.MensagemProcessamentoWs;
                case EStatusPagamentoCartao.Baixado:
                    return string.Empty;
                case EStatusPagamentoCartao.Erro:
                    return transacao.MensagemProcessamentoWs;
                case EStatusPagamentoCartao.Pendente:
                    return transacao.MensagemProcessamentoWs;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private string GetTipoEvento(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
                return TipoEventoConciliacaoPedagio;

            if (conciliacaoItem != null)
                switch (conciliacaoItem.TipoProcessadora)
                {
                    case ConciliacaoItemTipoProcessadora.CargaPedagioPortador:
                    case ConciliacaoItemTipoProcessadora.EstornoPedagioPortador:
                        return TipoEventoConciliacaoPedagio;
                    case ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio:
                        return TipoEventoConciliacaoResgateSaldoResidualPedagio;
                    case ConciliacaoItemTipoProcessadora.DepositoEmpresa:
                    case ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa:
                        return TipoEventoDepositoEmpresa;
                }

            if (!existente)
                return string.Empty;

            var transacaoService = _transacaoCartaoService;

            var transacao = transacaoService.GetQuery(idtransacao)
                .Select(t => new
                {
                    TipoEventoViagem = t.IdViagemEvento != null
                        ? t.ViagemEvento.TipoEventoViagem
                        : (ETipoEventoViagem?)null,
                    t.OrigemTransacaoCartao
                })
                .First();

            if (transacao.OrigemTransacaoCartao == EOrigemTransacaoCartao.Avulso)
                return TipoEventoConciliacaoCargaAvulsa;

            return transacao.TipoEventoViagem.GetDescription();
        }

        private string GetCiot(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            int idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            if (idViagem == 0)
            {
                if (!existente)
                    return string.Empty;

                var transacaoService = _transacaoCartaoService;
                var transacao = transacaoService.GetByIdIncludeViagem(idtransacao);
                if (transacao.IdViagemEvento == null)
                    return string.Empty;

                idViagem = transacao.ViagemEvento.IdViagem;
            }

            if (idViagem == 0)
                return string.Empty;

            var ciot = _declaracaoCiotRepository.GetCiot(idViagem);
            return ciot;
        }

        private string GetNumeroRecibo(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                var recibo = _viagemEventoRepository
                    .GetEventosQuery(Math.Abs(protocoloRequisicao.Value))
                    .Where(ve => ve.Viagem.DocumentoCliente != null)
                    .Select(ve => ve.Viagem.DocumentoCliente)
                    .FirstOrDefault();
                return recibo;
            }

            if (!existente)
                return string.Empty;

            var transacaoService = _transacaoCartaoService;

            var recibo2 = transacaoService.GetQuery(idtransacao)
                .Select(t => t.ViagemEvento.Viagem.DocumentoCliente)
                .FirstOrDefault();

            if (recibo2.IsNullOrWhiteSpace())
            {
                recibo2 = transacaoService.GetQuery(idtransacao).Select(c => c.CargaAvulsa.NroControleIntegracao).FirstOrDefault();
            }

            return recibo2;
        }

        private string GetNomeUsuario(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            var idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            if (idViagem == 0)
            {
                if (!existente)
                    return string.Empty;

                var transacaoService = _transacaoCartaoService;

                var transacao = transacaoService.GetQuery(idtransacao).Select(c => new
                {
                    c.IdCargaAvulsa,
                    c.IdViagemEvento
                }).FirstOrDefault();

                if (transacao == null)
                    return string.Empty;

                if (transacao.IdCargaAvulsa.HasValue)
                {
                    var nomeUsuario = transacaoService.GetQuery(idtransacao).Select(c => c.CargaAvulsa.NomeUsuario).FirstOrDefault();

                    return nomeUsuario;
                }
            }

            return string.Empty;
        }

        private string GetDocumentoUsuario(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            var idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            if (idViagem == 0)
            {
                if (!existente)
                    return string.Empty;

                var transacaoService = _transacaoCartaoService;

                var transacao = transacaoService.GetQuery(idtransacao).Select(c => new
                {
                    c.IdCargaAvulsa,
                    c.IdViagemEvento
                }).FirstOrDefault();

                if (transacao == null)
                    return string.Empty;

                if (transacao.IdCargaAvulsa.HasValue)
                {
                    var documentoUsuario = transacaoService.GetQuery(idtransacao).Select(c => c.CargaAvulsa.CPFCNPJUsuario).FirstOrDefault();

                    if (!string.IsNullOrWhiteSpace(documentoUsuario) && documentoUsuario.All(c => c == '0'))
                        return string.Empty;

                    return documentoUsuario;
                }
            }

            return string.Empty;
        }

        private int? GetIdViagemEventoTransacao(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return null;
            }

            var idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return null;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            if (idViagem == 0)
            {
                if (!existente)
                    return null;

                var transacaoService = _transacaoCartaoService;

                var transacao = transacaoService.GetQuery(idtransacao).Select(c => new
                {
                    c.IdViagemEvento
                }).FirstOrDefault();

                if (transacao == null)
                    return null;

                if (transacao.IdViagemEvento.HasValue)
                    return transacao.IdViagemEvento;
            }

            return null;
        }

        private string GetFilial(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            var idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            string filial;

            if (idViagem == 0)
            {
                if (!existente)
                    return string.Empty;

                var transacaoService = _transacaoCartaoService;

                var transacao = transacaoService.GetQuery(idtransacao).Select(c => new
                {
                    c.IdCargaAvulsa,
                    c.IdViagemEvento
                }).FirstOrDefault();

                if (transacao == null)
                    return string.Empty;

                if (transacao.IdCargaAvulsa.HasValue)
                {
                    filial = transacaoService.GetQuery(idtransacao).Select(c => c.CargaAvulsa.Filial.NomeFantasia).FirstOrDefault();
                    return filial;
                }

                if (!transacao.IdViagemEvento.HasValue)
                    return string.Empty;

                idViagem = transacaoService.GetQuery(idtransacao).Select(c => c.ViagemEvento.IdViagem).FirstOrDefault();
            }

            filial = _viagemRepository.GetQuery(idViagem).Select(c => c.Filial.NomeFantasia).FirstOrDefault();
            return filial;
        }

        private static EStatusTransacaoConciliacao GetStatusProcessadora(ConciliacaoItemStatusProcessadora? status)
        {
            if (!status.HasValue)
                return EStatusTransacaoConciliacao.Inexistente;

            switch (status)
            {
                case ConciliacaoItemStatusProcessadora.Existente:
                    return EStatusTransacaoConciliacao.Existente;
                case ConciliacaoItemStatusProcessadora.Pendente:
                    return EStatusTransacaoConciliacao.Pendente;
                case ConciliacaoItemStatusProcessadora.Erro:
                case ConciliacaoItemStatusProcessadora.Inexistente:
                    return EStatusTransacaoConciliacao.Inexistente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusTransacaoConciliacao GetStatusMeioHomologado(ConciliacaoItemStatusMeioHomologado? status)
        {
            if (!status.HasValue)
                return EStatusTransacaoConciliacao.Inexistente;

            switch (status)
            {
                case ConciliacaoItemStatusMeioHomologado.Existente:
                    return EStatusTransacaoConciliacao.Existente;
                case ConciliacaoItemStatusMeioHomologado.Pendente:
                    return EStatusTransacaoConciliacao.Pendente;
                case ConciliacaoItemStatusMeioHomologado.Erro:
                case ConciliacaoItemStatusMeioHomologado.Inexistente:
                    return EStatusTransacaoConciliacao.Inexistente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusTransacaoConciliacao GetStatus(EStatusTransacaoConciliacao statusProcessadora, EStatusTransacaoConciliacao statusMH, EStatusTransacaoConciliacao statusAts)
        {
            switch (statusAts)
            {
                case EStatusTransacaoConciliacao.Existente:
                    switch (statusMH)
                    {
                        case EStatusTransacaoConciliacao.Existente:
                            switch (statusProcessadora)
                            {
                                case EStatusTransacaoConciliacao.Existente:
                                    return EStatusTransacaoConciliacao.Existente;
                                case EStatusTransacaoConciliacao.Inexistente:
                                    return EStatusTransacaoConciliacao.Inexistente;
                                case EStatusTransacaoConciliacao.Pendente:
                                    return EStatusTransacaoConciliacao.Pendente;
                                default:
                                    throw new ArgumentOutOfRangeException(nameof(statusProcessadora), statusProcessadora, null);
                            }
                        case EStatusTransacaoConciliacao.Inexistente:
                            return EStatusTransacaoConciliacao.Inexistente;
                        case EStatusTransacaoConciliacao.Pendente:
                            return EStatusTransacaoConciliacao.Pendente;
                        default:
                            throw new ArgumentOutOfRangeException(nameof(statusMH), statusMH, null);
                    }
                case EStatusTransacaoConciliacao.Inexistente:
                    return EStatusTransacaoConciliacao.Inexistente;
                case EStatusTransacaoConciliacao.Pendente:
                    return EStatusTransacaoConciliacao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(statusAts), statusAts, null);
            }
        }

        private string GetPlacaCavalo(ConciliacaoItem conciliacaoItem, int idtransacao, bool existente)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return string.Empty;
            }

            var idViagem = 0;
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return string.Empty;

                idViagem = Math.Abs(protocoloRequisicao.Value);
            }

            string placa;

            if (idViagem == 0)
            {
                if (!existente)
                    return string.Empty;

                var transacaoService = _transacaoCartaoService;

                var transacao = transacaoService.GetQuery(idtransacao).Select(c => new
                {
                    c.IdCargaAvulsa,
                    c.IdViagemEvento
                }).FirstOrDefault();

                if (transacao == null)
                    return string.Empty;

                if (transacao.IdCargaAvulsa.HasValue)
                {
                    placa = transacaoService.GetQuery(idtransacao).Select(c => c.CargaAvulsa.PlacaCavalo).FirstOrDefault();
                    return placa.RemoveSpecialCharacters();
                }

                if (!transacao.IdViagemEvento.HasValue)
                    return string.Empty;

                idViagem = transacaoService.GetQuery(idtransacao).Select(c => c.ViagemEvento.IdViagem).FirstOrDefault();
            }

            placa = _viagemRepository.GetQuery(idViagem).Select(c => c.Placa).FirstOrDefault();
            return placa.RemoveSpecialCharacters();
        }

        private EStatusTransacaoConciliacao GetStatusNoAts(ConciliacaoItem conciliacaoItem)
        {
            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return EStatusTransacaoConciliacao.Existente;
            }

            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {

                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao != null)
                {
                    // Cancelamentos, são represetando pelo protocolo de requisição negativo
                    var pedagioLocal = _viagemRepository.GetQuery(Math.Abs(protocoloRequisicao.Value))
                        .Select(v => new { v.ResultadoCompraPedagio, v.ValorPedagio })
                        .FirstOrDefault();

                    if (pedagioLocal == null)
                        return EStatusTransacaoConciliacao.Inexistente;

                    if (conciliacaoItem.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
                        return EStatusTransacaoConciliacao.Existente;

                    // Registro do MH refrente a compra - Se a viagem do ATS estiver na situação "CancelamentoSolicitado/CancelamentoConfirmado", significa que a carga de crédito foir confirmada também, e o status da linha de crédito passa a ser OK.
                    if (protocoloRequisicao >= 0 &&
                        pedagioLocal.ResultadoCompraPedagio.In(
                            EResultadoCompraPedagio.CompraConfirmada,
                            EResultadoCompraPedagio.CancelamentoSolicitado,
                            EResultadoCompraPedagio.CancelamentoConfirmado) &&
                        pedagioLocal.ValorPedagio == conciliacaoItem.Valor)
                        return EStatusTransacaoConciliacao.Existente;

                    // Registro do MH referente a cancelamento - Status ATS OK
                    if (protocoloRequisicao < 0 &&
                        pedagioLocal.ResultadoCompraPedagio == EResultadoCompraPedagio.CancelamentoConfirmado &&
                        pedagioLocal.ValorPedagio == conciliacaoItem.Valor)
                        return EStatusTransacaoConciliacao.Existente;

                    //
                    return EStatusTransacaoConciliacao.Pendente;
                }

                return EStatusTransacaoConciliacao.Inexistente;
            }

            if (conciliacaoItem?.ProtocoloRequisicao == null)
                return EStatusTransacaoConciliacao.Inexistente;

            var transacaoService = _transacaoCartaoService;

            var transacao = transacaoService.GetQuery(Convert.ToInt32(conciliacaoItem.ProtocoloRequisicao))
                .Select(t => new { t.StatusPagamento })
                .FirstOrDefault();

            if (transacao == null)
                return EStatusTransacaoConciliacao.Inexistente;

            switch (transacao.StatusPagamento)
            {
                case EStatusPagamentoCartao.Aberto:
                case EStatusPagamentoCartao.Erro:
                    return EStatusTransacaoConciliacao.Inexistente;
                case EStatusPagamentoCartao.Baixado:
                    return EStatusTransacaoConciliacao.Existente;
                case EStatusPagamentoCartao.Pendente:
                    return EStatusTransacaoConciliacao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private bool ExistenteAts(ConciliacaoItem conciliacaoItem, int idtransacao)
        {
            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa)
            {
                Console.WriteLine("Teste");
            }

            var tiposExistentes = new List<ConciliacaoItemTipoProcessadora?>
            {
                ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa
            };

            if (tiposExistentes.Contains(conciliacaoItem?.TipoProcessadora))
            {
                return true;
            }

            if (conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.CargaPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador ||
                conciliacaoItem?.TipoProcessadora == ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio)
            {
                var protocoloRequisicao = conciliacaoItem.GetProtocoloRequisicaoPedagio();
                if (protocoloRequisicao == null)
                    return false;


                return _viagemRepository.GetQuery(protocoloRequisicao.Value).Any();
            }

            var transacaoService = _transacaoCartaoService;

            var anyByTransacao = transacaoService.AnyByTransacao(idtransacao);

            return anyByTransacao;
        }

        #endregion

        #region Validacao de Senha

        public ValidarSenhaCartaoResponseDto ValidarSenhaCartao(int identificador, int produto, string senha)
        {
            var requestValidacaoCartao = new ValidarSenhaCartaoRequest
            {
                Cartao = new IdentificadorCartao
                {
                    Identificador = identificador,
                    Produto = produto
                },
                Senha = senha //CriptografiaSenhaUtils.Encrypt(senha)
            };
            
            var response = _repository.ValidarSenhaCartao(requestValidacaoCartao);

            return Mapper.Map<ValidarSenhaCartaoResponseDto>(response);
        }

        public ValidarSenhaCartaoResponseDto ValidarSenhaCartao(CartaoVinculadoPessoaResponse request, string senha)
        {
            if (request?.Identificador == null || request?.Produto == null)
                throw new Exception("Identificador do cartão não informado!");

            return ValidarSenhaCartao(request.Identificador.Value, request.Produto.Id, senha);
        }

        #endregion

        #region Alteração da Senha do Cartão


        public AlterarSenhaCartaoResponse AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO requestDto)
        {
            if (requestDto?.Cartao.Identificador == null || requestDto?.Cartao.Produto == null)
                throw new Exception("Identificador do cartão não informado!");

            var requestAlterarcartao = new AlterarSenhaCartaoRequest
            {
                Cartao = new IdentificadorCartao
                {
                    Identificador = requestDto.Cartao.Identificador,
                    Produto = requestDto.Cartao.Produto
                },
                Senha = requestDto.Senha
            };

            return _repository.AlterarSenhaCartao(requestAlterarcartao);

        }


        #endregion

        #region Métodos para cartões

        /// <summary>
        ///
        /// </summary>
        /// <param name="documento"></param>
        /// <param name="idProdutos"></param>
        /// <param name="ativarCartaoVirtualCasoNaoPossuir">Caso o portador não possuir nenhum cartão, ativida automaticamente um virtual e retorna. Utilizado apenas na integração de viagem, caso o proprietário for diferente do motorista.</param>
        /// <param name="documentoUsuarioAudit"></param>
        /// <param name="nomeUsuarioAudit"></param>
        /// <param name="buscarCartoesBloqueados">Para integração de viagem, caso os cartões vinculados venham vazios, serão buscados também os cartões bloqueados para impedir que cartões virtuais sejam gerados para depósito</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public CartaoVinculadoPessoaListResponse GetCartoesVinculados(string documento, List<int> idProdutos,
            bool ativarCartaoVirtualCasoNaoPossuir, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null,
            bool buscarCartoesBloqueados = false)
        {
            documento = documento.OnlyNumbers2();
            documentoUsuarioAudit = documentoUsuarioAudit.OnlyNumbers2();

            var cartoes = _repository.GetCartoesVinculados(documento, idProdutos, documentoUsuarioAudit, nomeUsuarioAudit);

            if (cartoes.Status == CartaoVinculadoPessoaListResponseStatus.Falha)
                throw new Exception($"Ocorreu um erro ao consultar os cartões vinculados: {cartoes.Mensagem}");

            //na integração de viagem, para deposito,
            //se não foi encontrado nenhum cartão fisico vinculado (ativo)
            //é buscado um cartão bloqueado, e se não encontrar nenhum, segue o fluxo normal
            if (buscarCartoesBloqueados && cartoes.Status == CartaoVinculadoPessoaListResponseStatus.Sucesso && cartoes.Cartoes != null && !cartoes.Cartoes.Any())
            {
                var cartoesBloqueados = _repository.GetCartoesBloqueado(documento, idProdutos, documentoUsuarioAudit, nomeUsuarioAudit);

                if (cartoesBloqueados.Status == CartaoBloqueadoPessoaListResponseStatus.Falha)
                    throw new Exception($"Ocorreu um erro ao consultar os cartões bloqueados: {cartoesBloqueados.Mensagem}");

                cartoes = JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(JsonConvert.SerializeObject(cartoesBloqueados));
            }

            if (cartoes.Cartoes != null && !cartoes.Cartoes.Any() && ativarCartaoVirtualCasoNaoPossuir)
            {
                var cartao = VincularCartaoVirtual(documento);

                if (cartao.Status != VincularCartaoVirtualResponseStatus.Sucesso)
                    throw new Exception($"Ocorreu um erro ao vincular o cartão virtual: {cartao.Mensagem}");

                cartoes = _repository.GetCartoesVinculados(documento, idProdutos, documentoUsuarioAudit, nomeUsuarioAudit);

                if (cartoes.Status == CartaoVinculadoPessoaListResponseStatus.Falha)
                    throw new Exception($"Ocorreu um erro ao consultar os cartões vinculados: {cartoes.Mensagem}");
            }

            return cartoes;
        }

        public VincularCartaoVirtualResponse VincularCartaoVirtual(string documento)
        {
            documento = documento.OnlyNumbers2();

            var vincularRequest = new VincularCartaoVirtualRequest();
            var proprietarioService = _proprietarioRepository;

            var proprietario = proprietarioService.GetPorCpfCnpj(documento);
            if (proprietario == null)
                return new VincularCartaoVirtualResponse
                {
                    Status = VincularCartaoVirtualResponseStatus.Falha,
                    Mensagem = $"Proprietário com documento {documento} não cadastrado."
                };
            if (proprietario.Enderecos == null || !proprietario.Enderecos.Any())
                return new VincularCartaoVirtualResponse
                {
                    Status = VincularCartaoVirtualResponseStatus.Falha,
                    Mensagem = $"Proprietário com documento {documento} não possui endereço configurado para executar ativação de cartão virtual."
                };

            var enderecoProp = proprietario.Enderecos.First();
            var contatoProp = proprietario.Contatos.FirstOrDefault();
            var tipoPessoaJuridica = proprietario.CNPJCPF.Length > 11;

            vincularRequest.Produto = GetIdProdutoCartaoFretePadrao();
            vincularRequest.Pessoa = new IntegrarPessoaRequest
            {
                CpfCnpj = documento,
                Nome = proprietario.NomeFantasia,
                NomeFantasia = proprietario.NomeFantasia
            };
            vincularRequest.Pessoa.Info = new IntegrarPessoaInfoRequest
            {
                Sexo = "M", // //Fixo pois o ATS não possui um campo para o sexo do Proprietário.
                Rg = proprietario.RG,
                NomeMae = proprietario.NomeMae,
                NomePai = proprietario.NomePai,
                DataNascimento = tipoPessoaJuridica ? proprietario.DataNascimento : null,
                Telefone = contatoProp?.Celular ?? contatoProp?.Telefone,
                TacEquiparado = tipoPessoaJuridica ? proprietario.EquiparadoTac : true
            };
            vincularRequest.Pessoa.Endereco = new IntegrarPessoaEnderecoRequest
            {
                Cidade = enderecoProp.Cidade.IBGE,
                Bairro = enderecoProp.Bairro,
                Cep = enderecoProp.CEP,
                Complemento = enderecoProp.Complemento,
                Logradouro = enderecoProp.Endereco,
                Numero = enderecoProp.Numero.ToString()
            };

            var cartao = _repository.VincularCartaoVirtual(vincularRequest);

            return cartao;
        }

        public CartaoVinculadoPessoaListResponseDto GetCartoesVinculadosGrid(string documento, List<int> idProdutos)
        {
            var cartoesVinculados = _repository.GetCartoesVinculados(documento, idProdutos);
            return Mapper.Map<CartaoVinculadoPessoaListResponseDto>(cartoesVinculados);
        }

        public CartaoBloqueadoPessoaListResponseDto GetCartoesBloqueadoGrid(string documento, List<int> idProdutos)
        {
            var cartoesVinculados = _repository.GetCartoesBloqueado(documento, idProdutos);
            return Mapper.Map<CartaoBloqueadoPessoaListResponseDto>(cartoesVinculados);
        }

        public List<ProdutoResponse> GetCartaoProdutos()
        {
            var resposta = _repository.GetProdutos();
            return resposta;
        }

        public List<ProdutoResponse> FiltrarProdutosMestre()
        {
            var resposta = _repository.GetProdutos().Where(x => x.ProdutoMestreId == 0 || x.ProdutoMestreId == null);

            var lista = new List<ProdutoResponse>();

            foreach (var item in resposta)
            {
                lista.Add(item);
            }

            return lista;
        }

        public ProdutoResponse GetCartaoProduto(int id)
        {
            return _repository.GetProduto(id);
        }

        public ConsultaCartaoResponse GetCartaoProcessadora(int identificador, int produto)
        {
            return _repository.GetCartaoProcessadora(identificador, produto);
        }

        public ConsultaCartaoAdministradoraResponse AdministradoraCartao(int identificador, int idproduto)
        {
            return _repository.AdministradoraCartao(identificador, idproduto);
        }

        public ConsultaDetalhesEmpresaMeioHomologadoReponseDTO ConsultarEmpresa(string cnpj)
        {
            var response = _repository.ConsultarEmpresa(cnpj);

            return Mapper.Map<ConsultaDetalhesEmpresaMeioHomologadoReponseDTO>(response);
        }

        public EstornarResgateValorResponseDTO EstornarResgateValor(EstornarResgateValorDTO request, int usuario)
        {
            var resgate = _resgatarCartaoService.GetResgate(request.IdResgate);

            var transacao = _transacaoCartaoService;
            var resgatetransacao = transacao.GetByResgate(resgate.IdResgate);

            var cartao = Mapper.Map<ResgateCartaoAtendimento>(resgate);

            var empresaService = _empresaRepository;

            int? layoutPadraoCartao = null;
            if (request.Empresa.IsNullOrWhiteSpace())
                layoutPadraoCartao = _parametrosService.GetIdLayoutCartao();
            else
            {
                var empresaId = empresaService.GetIdPorCnpj(request.Empresa);
                if (empresaId == null)
                    throw new CartaoAtsException("Empresa não localizada com CNPJ " + request.Empresa);
                layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(empresaId.Value);
            }

            if (layoutPadraoCartao == null)
                return new EstornarResgateValorResponseDTO
                {
                    Status = EstornarResgateValorReponseStatus.Erro,
                    Mensagem = "Layout de cartão não localizado para realizar operação de estorno de resgate de valor"
                };

            var tipoProcessamentoCartao = ETipoProcessamentoCartao.EstornoResgateValor;

            var layoutHistorico =
                _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value, tipoProcessamentoCartao.ToString());

            var transacaoEstorno = AdicionarTransacaoViagemAvulso(layoutHistorico, "",
                "", resgatetransacao.ValorMovimentado, ETipoProcessamentoCartao.EstornoResgateValor,
                EOrigemTransacaoCartao.Atendimento, null);

            var estorno = new EstornarResgateValorRequest
            {
                Empresa = request.Empresa,
                Historico = transacaoEstorno.Historico,
                ProtocoloRequisicao = transacaoEstorno.IdTransacaoCartao,
                ProtocoloRequisicaoParaEstorno = resgatetransacao.IdTransacaoCartao
            };

            var res = _repository.EstornarResgateValor(estorno);

            var response = new EstornarResgateValorResponseDTO
            {
                Mensagem = res.Mensagem,
                Status = res.Status,
                ProtocoloProcessamento = res.ProtocoloProcessamento
            };

            transacaoEstorno.MensagemProcessamentoWs = response.Mensagem;
            transacaoEstorno.CnpjCpfDestino = request.Empresa;
            transacaoEstorno.IdResgate = resgate.IdResgate;
            transacaoEstorno.NumeroProtocoloWs = Convert.ToInt64(response.ProtocoloProcessamento);
            switch (response.Status)
            {
                case EstornarResgateValorReponseStatus.Erro:
                    transacaoEstorno.StatusPagamento = EStatusPagamentoCartao.Erro;
                    cartao.StatusResgate = EStatusResgate.Erro;
                    break;
                case EstornarResgateValorReponseStatus.Pendente:
                    transacaoEstorno.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    cartao.StatusResgate = EStatusResgate.NaoProcessado;
                    break;
                case EstornarResgateValorReponseStatus.Sucesso:
                    transacaoEstorno.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoEstorno.DataConfirmacaoMeioHomologado = DateTime.Now;
                    cartao.StatusResgate = EStatusResgate.Estornado;
                    break;
                case EstornarResgateValorReponseStatus.NaoProcessado:
                    transacaoEstorno.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    cartao.StatusResgate = EStatusResgate.Processado;
                    break;
                case EstornarResgateValorReponseStatus.ProtocoloExistente:
                    transacaoEstorno.StatusPagamento = EStatusPagamentoCartao.Erro;
                    cartao.StatusResgate = EStatusResgate.Erro;
                    break;
            }

            if (res.Status != EstornarResgateValorReponseStatus.Erro)
                _resgatarCartaoService.Alterar(cartao, request.MotivoEstorno, usuario);

            AtualizarTransacaoViagem(transacaoEstorno);


            return response;
        }

        public ResgatarValorResponseDTO ResgatarValor(ResgatarValorDTO request)
        {
            var empresaService = _empresaRepository;

            int? layoutPadraoCartao = null;
            if (request.Empresa.IsNullOrWhiteSpace())
                layoutPadraoCartao = _parametrosService.GetIdLayoutCartao();
            else
            {
                var empresaId = empresaService.GetIdPorCnpj(request.Empresa);
                if (empresaId == null)
                    throw new CartaoAtsException("Empresa não localizada com CNPJ " + request.Empresa);
                layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(empresaId.Value);
            }

            if (layoutPadraoCartao == null)
                return new ResgatarValorResponseDTO
                {
                    Status = ResgatarValorResponseStatus.Erro,
                    Mensagem = "Layout de cartão não localizado para realizar operação de resgate de valor"
                };

            var tipoProcessamentoCartao = ETipoProcessamentoCartao.ResgateValor;

            var layoutHistorico =
                _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value, tipoProcessamentoCartao.ToString());

            var transacaoCartao = AdicionarTransacaoViagemAvulso(layoutHistorico, "",
                "", request.Valor.GetValueOrDefault(0), ETipoProcessamentoCartao.ResgateValor,
                EOrigemTransacaoCartao.Atendimento, null);

            request.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;
            request.Historico = transacaoCartao.Historico;

            var resgate = new ResgateCartaoAtendimento
            {
                Valor = request.Valor.GetValueOrDefault(),
                CpfCnpjPortador = request.Documento.OnlyNumbers(),
                CnpjEmpresa = request.Empresa.OnlyNumbers(),
                NumeroCartao = request.Cartao,
                Produto = request.Produto,
                Motivo = request.Metadados,
                IdUsuarioCadastro = _userIdentity.IdUsuario,
                DataHoraCadastro = DateTime.Now,
                StatusResgate = EStatusResgate.NaoProcessado
            };

            _resgatarCartaoService.Add(resgate);

            var dicionario = new Dictionary<string, string>();
            dicionario.Add("Resgate", request.Especificacao);
            dicionario.Add("CPF", request.CpfUsuario);
            dicionario.Add("Motivo", request.Metadados);

            var req = new ResgatarValorRequest
            {
                Empresa = request.Empresa,
                Valor = request.Valor,
                Cartao = new IdentificadorCartao
                {
                    Identificador = request.Cartao,
                    Produto = request.Produto
                },
                Metadados = dicionario,
                InformacoesAdicionais = null,
                Historico = request.Historico,
                ProtocoloRequisicao = request.ProtocoloRequisicao
            };

            var response = _repository.ResgatarValor(req);

            var responseDto = new ResgatarValorResponseDTO
            {
                Mensagem = response.Mensagem,
                Status = response.Status,
                ProtocoloProcessamento = response.ProtocoloProcessamento
            };

            transacaoCartao.MensagemProcessamentoWs = response.Mensagem;
            transacaoCartao.CnpjCpfOrigem = request.Documento.OnlyNumbers();
            transacaoCartao.CnpjCpfDestino = request.Empresa.OnlyNumbers();
            transacaoCartao.IdResgate = resgate.IdResgate;
            transacaoCartao.NumeroProtocoloWs = Convert.ToInt64(responseDto.ProtocoloProcessamento);
            switch (responseDto.Status)
            {
                case ResgatarValorResponseStatus.Erro:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    resgate.StatusResgate = EStatusResgate.Erro;
                    break;
                case ResgatarValorResponseStatus.Pendente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    resgate.StatusResgate = EStatusResgate.NaoProcessado;
                    break;
                case ResgatarValorResponseStatus.Sucesso:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                    resgate.StatusResgate = EStatusResgate.Processado;
                    break;
                case ResgatarValorResponseStatus.NaoProcessado:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    resgate.StatusResgate = EStatusResgate.NaoProcessado;
                    break;
                case ResgatarValorResponseStatus.ProtocoloExistente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    resgate.StatusResgate = EStatusResgate.Erro;
                    break;
            }

            if (response.Status != ResgatarValorResponseStatus.Erro || response.Status != ResgatarValorResponseStatus.NaoProcessado)
                _resgatarCartaoService.Alterar(resgate, null, 0);

            AtualizarTransacaoViagem(transacaoCartao);

            return responseDto;
        }

        public HistoricoCartaoPessoaListResponse GetHistoricoCartoes(string documento, List<int> idProdutos)
        {
            return _repository.GetHistoricoCartoes(documento, idProdutos);
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartao(string documento)
        {
            var produtoId = new List<int> { GetIdProdutoCartaoFretePadrao() };

            var cartoes = GetCartoesVinculados(documento, produtoId, false, buscarCartoesBloqueados:true);
            var cartao = cartoes.Cartoes.LastOrDefault();

            if (cartao == null)
                throw new Exception($"Erro ao validar saldo: Nenhum cartão encontrado para o portador {documento.FormatarCpfCnpj()}");

            var consultaSaldoRequest = new ConsultarSaldoCartaoRequest()
            {
                Cartao = new IdentificadorCartao()
                {
                    Identificador = cartao.Identificador,
                    Produto = cartao.Produto.Id
                }
            };

            var saldo = _repository.ConsultarSaldoCartao(consultaSaldoRequest, AuditDocIntegracao, string.Empty);

            return saldo;
        }

        public ConsultarSaldoCartaoResponseDTO ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var saldo = _repository.ConsultarSaldoCartao(request, documentoUsuarioAudit, nomeUsuarioAudit);

            return Mapper.Map<ConsultarSaldoCartaoResponseDTO>(saldo);
        }

        public HistoricoCartaoPessoaListResponseDto GetHistoricoCartoesGrid(string documento, List<int> idProdutos)
        {
            var historicoCartao = _repository.GetHistoricoCartoes(documento, idProdutos);
            return Mapper.Map<HistoricoCartaoPessoaListResponseDto>(historicoCartao);
        }

        public VincularResponse VincularCartaoPortador(int identificador, int produtoId, AtsPortadorRequest atsPortadorRequest)
        {
            var vincularRequest = new VincularRequest();

            PopularVincularRequest(identificador, produtoId, atsPortadorRequest, vincularRequest);

            return _repository.VincularCartaoPortador(vincularRequest);
        }

        public DesvincularResponse DesvincularCartaoPortador(int identificador, int produtoId, string motivoDesvinculo, AtsPortadorRequest atsPortadorRequest, int? cartaoMestreId)
        {
            var desvincularRequest = new DesvincularRequest();

            PopularDesvincularRequest(identificador, produtoId, motivoDesvinculo, atsPortadorRequest,
                desvincularRequest);

            return _repository.DesvincularCartaoPortador(desvincularRequest);
        }

        #endregion

        #region Métodos de operacão

        public OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento, CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario)
        {
            var proprietarioService = _proprietarioRepository;
            var empresaService = _empresaRepository;
            var parametrosService = _parametrosService;

            try
            {
                var layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(viagem.IdEmpresa);

                if (!layoutPadraoCartao.HasValue)
                    return new OperacaoCartaoResponseDTO
                    {
                        Status = EStatusPagamentoCartao.Erro,
                        Mensagem = "Layout padrão da empresa não configurado.",
                        Protocolo = 0
                    };

                var cnpjEmpresa = empresaService.GetCnpj(viagem.IdEmpresa);

                var proprietario = proprietarioService.GetIdPorCpfCnpj(viagem.CPFCNPJProprietario, viagem.IdEmpresa);

                if (!proprietario.HasValue)
                {
                    proprietario = proprietarioService.GetById(viagem.IdProprietario ?? 0)?.IdProprietario;

                    if (!proprietario.HasValue)
                    {
                        return new OperacaoCartaoResponseDTO
                        {
                            Status = EStatusPagamentoCartao.Erro,
                            Mensagem = "Proprietário não encontrado.",
                            Protocolo = 0
                        };
                    }
                }

                var percentualTransferenciaFreteProprietarioMotorista = parametrosService.GetPercentualTransferenciaFreteProprietarioMotorista(viagem.CPFCNPJProprietario, viagem.CPFMotorista);
                var percentualTransferenciaFreteProprietario = parametrosService.GetPercentualTransferenciaFreteProprietario(viagem.CPFCNPJProprietario);
                var percentualTransferenciaFreteGenerico = parametrosService.GetPercentualTransferenciaFreteGenerico();

                decimal porcentagemTransferencia = 0;

                switch (viagemEvento.TipoEventoViagem)
                {
                    case ETipoEventoViagem.Adiantamento:

                        if (percentualTransferenciaFreteProprietarioMotorista?.Adiantamento != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.Adiantamento.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.Adiantamento.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.Adiantamento != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.Adiantamento.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.Adiantamento.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.Saldo:

                        if (percentualTransferenciaFreteProprietarioMotorista?.Saldo != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.Saldo.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.Saldo.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.Saldo != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.Saldo.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.Saldo.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.Estadia:

                        if (percentualTransferenciaFreteProprietarioMotorista?.Estadia != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.Estadia.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.Estadia.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.Estadia != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.Estadia.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.Estadia.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.RPA:

                        if (percentualTransferenciaFreteProprietarioMotorista?.RPA != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.RPA.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.RPA.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.RPA != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.RPA.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.RPA.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.TarifaAntt:

                        if (percentualTransferenciaFreteProprietarioMotorista?.TarifaAntt != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.TarifaAntt.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.TarifaAntt.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.TarifaAntt != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.TarifaAntt.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.TarifaAntt.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.Abastecimento:

                        if (percentualTransferenciaFreteProprietarioMotorista?.Abastecimento != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.Abastecimento.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.Abastecimento.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.Abastecimento != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.Abastecimento.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.Abastecimento.Value;
                            }
                        }

                        break;
                    case ETipoEventoViagem.Abono:

                        if (percentualTransferenciaFreteProprietarioMotorista?.Abono != null)
                        {
                            porcentagemTransferencia = percentualTransferenciaFreteProprietarioMotorista.Abono.Value;
                        }
                        else if (percentualTransferenciaFreteProprietario != null)
                        {
                            if (!percentualTransferenciaFreteProprietario.Abono.HasValue)
                            {
                                if (percentualTransferenciaFreteGenerico?.Abono != null)
                                {
                                    porcentagemTransferencia = percentualTransferenciaFreteGenerico.Abono.Value;
                                    parametrosService.SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono, porcentagemTransferencia, viagem.CPFCNPJProprietario);
                                }
                            }
                            else
                            {
                                porcentagemTransferencia = percentualTransferenciaFreteProprietario.Abono.Value;
                            }
                        }

                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(ETipoEventoViagem));
                }

                KeyValuePair<CarregarValorResponse, TransacaoCartao> cargaResponse;

                cargaResponse = CarregarValorCartao(viagemEvento, viagem.IdEmpresa, viagem.CPFCNPJProprietario, layoutPadraoCartao.Value, cnpjEmpresa, EOrigemTransacaoCartao.Automatico, cartaoVinculadoProprietario);

                var statusCarga = GetStatusSucesso(cargaResponse.Key.Status);

                if (porcentagemTransferencia > 0)
                {
                    if (viagem.CPFCNPJProprietario != viagem.CPFMotorista && cargaResponse.Key.Status == CarregarValorResponseStatus.Sucesso)
                    {
                        var transferenciaResponse = TransferirValorCartao(viagemEvento, viagem.IdEmpresa,
                                viagem.CPFCNPJProprietario, viagem.CPFMotorista, layoutPadraoCartao.Value, cnpjEmpresa,
                            porcentagemTransferencia, EOrigemTransacaoCartao.Automatico, cartaoVinculadoProprietario, cartaoVinculadoMotorista);

                        var statusTransferencia = GetStatusSucesso(transferenciaResponse.Key.Status);

                        if (statusTransferencia == EStatusPagamentoCartao.Erro)
                            return new OperacaoCartaoResponseDTO
                            {
                                Status = statusCarga,
                                Mensagem = "Transferência do Proprietário para o Motorista não teve sucesso.",
                                Protocolo = cargaResponse.Key.ProtocoloProcessamento
                            };

                        return new OperacaoCartaoResponseDTO
                        {
                            Status = statusTransferencia,
                            Mensagem = transferenciaResponse.Key.Mensagem,
                            Protocolo = transferenciaResponse.Key.ProtocoloProcessamento
                        };
                    }
                }

                return new OperacaoCartaoResponseDTO
                {
                    Status = statusCarga,
                    Mensagem = cargaResponse.Key.Mensagem,
                    Protocolo = cargaResponse.Key.ProtocoloProcessamento
                };
            }
            finally
            {
                //                proprietarioService.Dispose();
                //                empresaService.Dispose();
            }
        }

        public OperacaoCartaoResponseDTO RealizarEstornoCargaFrete(ViagemEvento viagemEvento, int idEmpresa, string cpfMotorista, string cnpjCpfProprietario, EOrigemTransacaoCartao origem)
        {
            var transacaoService = _transacaoCartaoService;
            var empresaService = _empresaRepository;

            try
            {
                var layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(idEmpresa);

                if (!layoutPadraoCartao.HasValue)
                    return new OperacaoCartaoResponseDTO
                    {
                        Status = EStatusPagamentoCartao.Erro,
                        Mensagem = "Layout padrão da empresa não configurado.",
                        Protocolo = 0
                    };

                var cnpjEmpresa = empresaService.GetCnpj(idEmpresa);

                var transacoes = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento)
                    .OrderByDescending(c => c.LineId);

                var resultado =
                    new OperacaoCartaoResponseDTO { Status = EStatusPagamentoCartao.Aberto, Mensagem = "Não processado no Web Service" };

                foreach (var transacao in transacoes)
                {
                    if (ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(transacao.TipoProcessamentoCartao)
                        && transacao.StatusPagamento != EStatusPagamentoCartao.Erro)
                    {
                        var resultCarga = EstornarCargaCartao(transacao, viagemEvento, idEmpresa,
                                                        cnpjCpfProprietario, layoutPadraoCartao.Value, cnpjEmpresa);

                        resultado = new OperacaoCartaoResponseDTO
                        {
                            Status = GetStatusSucesso(resultCarga.Key.Status),
                            Mensagem = resultCarga.Key.Mensagem,
                            Protocolo = resultCarga.Key.NumeroTransacao
                        };
                    }

                    if (ETipoProcessamentoCartaoUtils.TransferenciaProprietarioParaMotoristaViagem.Contains(transacao.TipoProcessamentoCartao)
                        && transacao.StatusPagamento != EStatusPagamentoCartao.Erro)
                    {
                        var resultTransferencia = EstornarTransferenciaCartao(transacao, viagemEvento, idEmpresa,
                            cpfMotorista, cnpjCpfProprietario, layoutPadraoCartao.Value, cnpjEmpresa, origem);

                        resultado = new OperacaoCartaoResponseDTO
                        {
                            Status = GetStatusSucesso(resultTransferencia.Key.Status),
                            Mensagem = resultTransferencia.Key.Mensagem,
                            Protocolo = resultTransferencia.Key.NumeroTransacao
                        };
                    }

                    /*switch (transacao.TipoProcessamentoCartao)
                    {
                        case ETipoProcessamentoCartao.CargaTarifaAntt:
                        case ETipoProcessamentoCartao.CargaAdiantamento:
                        case ETipoProcessamentoCartao.CargaAbastecimento:
                        case ETipoProcessamentoCartao.CargaSaldo:
                        case ETipoProcessamentoCartao.CargaAbono:
                        case ETipoProcessamentoCartao.CargaEstadia:
                        case ETipoProcessamentoCartao.CargaRpa:

                            var resultCarga = EstornarCargaCartao(transacao, viagemEvento, idEmpresa,
                                cnpjCpfProprietario, layoutPadraoCartao, cnpjEmpresa);

                            resultado = new OperacaoCartaoResponseDTO
                            {
                                Status = GetStatusSucesso(resultCarga.Key.Status),
                                Mensagem = resultCarga.Key.Mensagem,
                                Protocolo = resultCarga.Key.NumeroTransacao
                            };

                            break;

                        case ETipoProcessamentoCartao.TransferenciaTarifaAntt:
                        case ETipoProcessamentoCartao.TransferenciaAdiantamento:
                        case ETipoProcessamentoCartao.TransferenciaAbastecimento:
                        case ETipoProcessamentoCartao.TransferenciaSaldo:
                        case ETipoProcessamentoCartao.TransferenciaAbono:
                        case ETipoProcessamentoCartao.TransferenciaEstadia:
                        case ETipoProcessamentoCartao.TransferenciaRpa:

                            var resultTransferencia = EstornarTransferenciaCartao(transacao, viagemEvento, idEmpresa,
                                cpfMotorista, cnpjCpfProprietario, layoutPadraoCartao, cnpjEmpresa, origem);

                            resultado = new OperacaoCartaoResponseDTO
                            {
                                Status = GetStatusSucesso(resultTransferencia.Key.Status),
                                Mensagem = resultTransferencia.Key.Mensagem,
                                Protocolo = resultTransferencia.Key.NumeroTransacao
                            };

                            break;

                        default:
                            continue;
                    }*/
                }

                return resultado;
            }
            finally
            {
                //                transacaoService.Dispose();
            }
        }

        public KeyValuePair<CarregarValorResponse, TransacaoCartao> CarregarValorCartao(ViagemEvento viagemEvento, int idEmpresa, string documentoFavorecido, int layoutPadraoCartao, string cnpjEmpresa, EOrigemTransacaoCartao origem, CartaoVinculadoPessoaListResponse cartaoVinculadoPessoaList)
        {
            var transacaoService = _transacaoCartaoService;
            var empresaService = _empresaRepository;

            LogManager.GetCurrentClassLogger().Info($"Id da viagem evento inicial{viagemEvento.IdViagemEvento}.");
            var eventosexistentes = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);

            var tipoEventoAtual =
                GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.Carregamento);
            LogManager.GetCurrentClassLogger().Info($"Tipo de evento atual{tipoEventoAtual}.");

            if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Baixado))
            {
                var transacao = eventosexistentes.First(c =>
                    c.TipoProcessamentoCartao == tipoEventoAtual &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                var response = new CarregarValorResponse { Status = CarregarValorResponseStatus.Sucesso };

                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(response, transacao);
            }

            if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro))
            {
                var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro);

                var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                LogManager.GetCurrentClassLogger().Info($"Protocolo{protocolo.ProtocoloProcessamento} com status {protocolo.Status}");

                AtualizarTransacao(transacao, protocolo);

                var responseCarregar = new CarregarValorResponse { Status = GetStatusCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(responseCarregar, transacao);
            }
            else if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro))
            {
                var transacao = eventosexistentes.OrderByDescending(c => c.IdTransacaoCartao)
                    .First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro);

                var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                LogManager.GetCurrentClassLogger().Info($"Protocolo {protocolo.ProtocoloProcessamento} com status:{protocolo.Status}.");


                AtualizarTransacao(transacao, protocolo);

                if (protocolo.Status.In(ConsultarProtocoloResponseStatus.Pendente, ConsultarProtocoloResponseStatus.Sucesso))
                {
                    var responseCarregar = new CarregarValorResponse { Status = GetStatusCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                    return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(responseCarregar, transacao);
                }
            }

            var viagem = viagemEvento.Viagem ?? _viagemRepository.Get(viagemEvento.IdViagem);

            var empresa = empresaService.All().Where(e => e.IdEmpresa == viagem.IdEmpresa)
                                              .Select(e => new
                                              {
                                                  e.IdEmpresa,
                                                  e.RazaoSocial,
                                                  e.NomeFantasia
                                              })
                              .First();

            var tipoProcessamentoCartao = GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.Carregamento);
            LogManager.GetCurrentClassLogger().Info($"Tipo de processamento cartão{tipoProcessamentoCartao}");


            var layoutHistorico =
                _layoutCartaoService.GetIdItem(layoutPadraoCartao, tipoProcessamentoCartao.ToString());
            LogManager.GetCurrentClassLogger().Info($"Id layout historico {layoutHistorico}");


            var valorMovimentar = viagemEvento.ValorTotalPagamento ?? viagemEvento.ValorPagamento;

            var transacaoCartao = AdicionarTransacaoViagem(viagemEvento, ETipoOperacaoCartao.Carregamento,
                layoutHistorico, string.Empty, documentoFavorecido, valorMovimentar);
            LogManager.GetCurrentClassLogger().Info($"Id transação cartão {transacaoCartao.IdTransacaoCartao}");


            var cartaoVincular = cartaoVinculadoPessoaList.Cartoes.Last();

            var carregarRequest = new CarregarValorRequest();

            carregarRequest.PermitirTransacaoPendente = true;
            carregarRequest.Valor = valorMovimentar;

            carregarRequest.Cartao = new IdentificadorCartao();
            carregarRequest.Cartao.Identificador = cartaoVincular.Identificador;
            carregarRequest.Cartao.Produto = cartaoVincular.Produto.Id;

            carregarRequest.Empresa = cnpjEmpresa;
            carregarRequest.Historico = layoutHistorico;
            carregarRequest.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            var ciot = _declaracaoCiotRepository.GetDeclaracaoCiot(viagemEvento.IdViagem);

            carregarRequest.InformacoesAdicionais = new Dictionary<string, string>();
            if (!string.IsNullOrWhiteSpace(viagem.DocumentoCliente))
                carregarRequest.InformacoesAdicionais.Add("CT-e", viagem.DocumentoCliente);
            if (ciot != null)
            {
                if (long.TryParse(ciot.Ciot, out var ciotNumero))
                    carregarRequest.NumeroCIOT = ciotNumero;

                LogManager.GetCurrentClassLogger().Info($"Id da delacaracao ciot{ciot.IdDeclaracaoCiot}");
                carregarRequest.InformacoesAdicionais.Add("CIOT", $"{ciot.Ciot}/{ciot.Verificador}");
            }

            if (!AppSettingsUtils.IsInstanciaSotran)
                carregarRequest.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);

            try
            {
                var resultado = _repository.CarregarValorCartao(carregarRequest);
                transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                switch (resultado.Status)
                {
                    case CarregarValorResponseStatus.Pendente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                        break;
                    // Pendente
                    case CarregarValorResponseStatus.NaoProcessado:

                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                        break;

                    // Sucesso
                    case CarregarValorResponseStatus.Sucesso:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                        transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;

                        _pushService.EnviarPorDocumento(transacaoCartao.CnpjCpfDestino, "Crédito de " + transacaoCartao.ValorMovimentado.FormatMoney(),
                            $"Crédito transferido para sua conta: {transacaoCartao.ValorMovimentado.FormatMoney()}." +
                            $"\nTransportadora: {empresa.NomeFantasia}");
                        break;

                    // Erro e Protocolo Existente
                    case CarregarValorResponseStatus.Erro:
                    case CarregarValorResponseStatus.ProtocoloExistente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                        break;

                    default:
                        throw new ArgumentException(nameof(resultado.Status));
                }

                transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

                AtualizarTransacaoViagem(transacaoCartao);

                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(resultado, transacaoCartao);
            }
            catch (Exception e)
            {
                transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                transacaoCartao.MensagemProcessamentoWs = e.Message;
                transacaoCartao.NumeroProtocoloWs = 0;
                LogManager.GetCurrentClassLogger().Error(e);
                AtualizarTransacaoViagem(transacaoCartao);
                throw;
            }
        }

        private void AtualizarTransacao(TransacaoCartao transacao, ConsultarProtocoloResponse protocolo)
        {
            transacao.MensagemProcessamentoWs = protocolo.Mensagem;

            switch (protocolo.Status)
            {
                case ConsultarProtocoloResponseStatus.Pendente:
                    transacao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    break;
                // Sucesso
                case ConsultarProtocoloResponseStatus.Sucesso:
                    transacao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacao.DataConfirmacaoMeioHomologado = protocolo.DataConfirmacaoTransacao;
                    break;

                // Erro e Protocolo Existente
                case ConsultarProtocoloResponseStatus.NaoProcessado:
                case ConsultarProtocoloResponseStatus.Erro:
                case ConsultarProtocoloResponseStatus.ProtocoloExistente:
                    transacao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    break;

                default:
                    throw new ArgumentException(nameof(protocolo.Status));
            }

            transacao.NumeroProtocoloWs = protocolo.ProtocoloProcessamento ?? 0;

            AtualizarTransacaoViagem(transacao);
        }

        public KeyValuePair<CarregarValorResponse, TransacaoCartao> EfetuarCargaAvulsa(CargaAvulsa cargaAvulsa, int idEmpresa, 
            string documentoFavorecido, int layoutPadraoCartao, string cnpjEmpresa, EOrigemTransacaoCartao origem, 
            CartaoVinculadoPessoaResponse cartaoVinculadoPessoa)
        {
            var transacaoService = _transacaoCartaoService;
            var empresaService = _empresaRepository;

            var eventosexistentes = transacaoService.GetByIdCarga(cargaAvulsa.IdCargaAvulsa);
            var tipoEventoAtual = ETipoProcessamentoCartao.CargaAvulsa;

            if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Baixado))
            {
                var transacao = eventosexistentes.First(c =>
                    c.TipoProcessamentoCartao == tipoEventoAtual &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                var response = new CarregarValorResponse { Status = CarregarValorResponseStatus.Sucesso };

                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(response, transacao);
            }

            CarregarValorResponse resultado;

            if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual &&
                                           c.StatusPagamento == EStatusPagamentoCartao.Pendente))
            {
                var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoEventoAtual &&
                                                             c.StatusPagamento == EStatusPagamentoCartao.Pendente);

                var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                AtualizarTransacao(transacao, protocolo);

                var responseCarregar = new CarregarValorResponse { Status = GetStatusCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(responseCarregar, transacao);
            }

            var tipoProcessamentoCartao = ETipoProcessamentoCartao.CargaAvulsa;
            var empresa = empresaService.All().Where(e => e.IdEmpresa == idEmpresa)
                .Select(e => new
                {
                    e.IdEmpresa,
                    e.RazaoSocial,
                    e.NomeFantasia
                }).First();

            var layoutHistorico =
                _layoutCartaoService.GetIdItem(layoutPadraoCartao, tipoProcessamentoCartao.ToString());

            var valorMovimentar = cargaAvulsa.Valor;

            var transacaoCartao = AdicionarTransacaoViagem(cargaAvulsa, ETipoOperacaoCartao.Carregamento,
                layoutHistorico, string.Empty, documentoFavorecido, valorMovimentar, null, cartaoVinculadoPessoa.Identificador);

            var carregarRequest = new CarregarValorRequest();

            carregarRequest.PermitirTransacaoPendente = false;
            carregarRequest.Valor = valorMovimentar;

            carregarRequest.Cartao = new IdentificadorCartao();
            carregarRequest.Cartao.Identificador = cartaoVinculadoPessoa.Identificador;
            carregarRequest.Cartao.Produto = cartaoVinculadoPessoa.Produto.Id;

            carregarRequest.Empresa = cnpjEmpresa;
            carregarRequest.Historico = layoutHistorico;
            carregarRequest.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            carregarRequest.InformacoesAdicionais = new Dictionary<string, string>();

            if (!string.IsNullOrWhiteSpace(cargaAvulsa.NroControleIntegracao))
                carregarRequest.InformacoesAdicionais.Add("Documento", cargaAvulsa.NroControleIntegracao);

            if (!AppSettingsUtils.IsInstanciaSotran && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                carregarRequest.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);

            if (!string.IsNullOrEmpty(cargaAvulsa.Observacao))
                carregarRequest.InformacoesAdicionais.Add("Observação", cargaAvulsa.Observacao);

            try
            {
                resultado = _repository.CarregarValorCartao(carregarRequest);

                transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                switch (resultado.Status)
                {
                    case CarregarValorResponseStatus.Pendente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                        break;
                    // Pendente
                    case CarregarValorResponseStatus.NaoProcessado:

                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                        break;

                    // Sucesso
                    case CarregarValorResponseStatus.Sucesso:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                        transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                        break;

                    // Erro e Protocolo Existente
                    case CarregarValorResponseStatus.Erro:
                    case CarregarValorResponseStatus.ProtocoloExistente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                        break;

                    default:
                        throw new ArgumentException(nameof(resultado.Status));
                }

                transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

                AtualizarTransacaoViagem(transacaoCartao);

                return new KeyValuePair<CarregarValorResponse, TransacaoCartao>(resultado, transacaoCartao);
            }
            catch (Exception e)
            {
                transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                transacaoCartao.MensagemProcessamentoWs = e.Message;
                transacaoCartao.NumeroProtocoloWs = 0;

                AtualizarTransacaoViagem(transacaoCartao);

                throw new CartaoAtsException(e.Message);
            }
        }

        public KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao> EstornarCargaAvulsa(CargaAvulsa cargaAvulsa, TransacaoCartao transacaoCartaoOriginal, Empresa empresa, string documentoFavorecido, string cnpjEmpresa)
        {
            var empresaService = _empresaRepository;
            var transacaoService = _transacaoCartaoService;

            var tipoProcessamentoCartao = ETipoProcessamentoCartao.EstornoCargaAvulsa;
            try
            {
                var eventosexistentes = transacaoService.GetByIdCarga(cargaAvulsa.IdCargaAvulsa);

                // Double check
                if (eventosexistentes.Any(c =>
                    c.TipoProcessamentoCartao == tipoProcessamentoCartao &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado))
                {
                    var transacao = eventosexistentes.First(c =>
                        c.TipoProcessamentoCartao == tipoProcessamentoCartao &&
                        c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                    var response = new EstornarCargaCartaoResponse { Status = EstornarCargaCartaoResponseStatus.Sucesso };

                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(response, transacao);
                }

                if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoProcessamentoCartao &&
                                               c.StatusPagamento == EStatusPagamentoCartao.Pendente))
                {
                    var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoProcessamentoCartao && c.StatusPagamento == EStatusPagamentoCartao.Pendente);

                    var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                    AtualizarTransacao(transacao, protocolo);

                    var responseCarregar = new EstornarCargaCartaoResponse { Status = GetStatusEstornoCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                }

                // Efetivando
                var layoutHistorico = _layoutCartaoService.GetIdItem(empresa.IdLayoutCartao ?? 0, tipoProcessamentoCartao.ToString());

                var transacaoCartao = AdicionarTransacaoViagem(cargaAvulsa, ETipoOperacaoCartao.EstornoCarga,
                    layoutHistorico, documentoFavorecido, string.Empty, transacaoCartaoOriginal.ValorMovimentado, ETipoProcessamentoCartao.EstornoCargaAvulsa);

                EstornarCargaCartaoResponse resultado;

                var estornoRequest = new EstornarCargaCartaoRequest();
                estornoRequest.PermitirTransacaoPendente = false;
                estornoRequest.ProtocoloRequisicaoParaEstorno = transacaoCartaoOriginal.IdTransacaoCartao;
                estornoRequest.Empresa = cnpjEmpresa;
                estornoRequest.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

                estornoRequest.InformacoesAdicionais = new Dictionary<string, string>();

                if (!string.IsNullOrWhiteSpace(cargaAvulsa.NroControleIntegracao))
                    estornoRequest.InformacoesAdicionais.Add("Documento", cargaAvulsa.NroControleIntegracao);

                if (!AppSettingsUtils.IsInstanciaSotran && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                    estornoRequest.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);

                try
                {
                    resultado = _repository.EstornarCargaCartao(estornoRequest);

                    transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                    switch (resultado.Status)
                    {
                        // Aberta
                        case EstornarCargaCartaoResponseStatus.NaoProcessado:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                            break;

                        // Sucesso
                        case EstornarCargaCartaoResponseStatus.Sucesso:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                            transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                            break;

                        // Transacao pendente
                        case EstornarCargaCartaoResponseStatus.Pendente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                            break;

                        // Erro e Protocolo Existente
                        case EstornarCargaCartaoResponseStatus.Erro:
                        case EstornarCargaCartaoResponseStatus.ProtocoloExistente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                            break;

                        default:
                            throw new ArgumentException(nameof(resultado.Status));
                    }

                    transacaoCartao.NumeroProtocoloWs = resultado.NumeroTransacao ?? 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(resultado, transacaoCartao);
                }
                catch (Exception e)
                {
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    transacaoCartao.MensagemProcessamentoWs = e.Message;
                    transacaoCartao.NumeroProtocoloWs = 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    throw;
                }
            }
            finally
            {
                //                empresaService.Dispose();
            }
        }

        public KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao> TransferirValorCartao(ViagemEvento viagemEvento, int idEmpresa, string documentoOrigem, string documentoDestino, int layoutPadraoCartao, string cnpjEmpresa, decimal porcentagemTransferencia, EOrigemTransacaoCartao origem, CartaoVinculadoPessoaListResponse cartaoVinculadoOrigemList, CartaoVinculadoPessoaListResponse cartaoVinculadoDestinoList)
        {
            var transacaoService = _transacaoCartaoService;

            var empresaService = _empresaRepository;

            var eventosexistentes = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);
            var tipoEventoAtual =
                GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.Transferencia);

            if (eventosexistentes.Any(c =>
                c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Baixado))
            {
                var transacao = eventosexistentes.First(c =>
                    c.TipoProcessamentoCartao == tipoEventoAtual &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                var response = new TransferirValorCartaoResponse { Status = TransferirValorCartaoResponseStatus.Sucesso };

                return new KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao>(response, transacao);
            }

            if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro))
            {
                var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro);

                var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                AtualizarTransacao(transacao, protocolo);

                var responseCarregar = new TransferirValorCartaoResponse { Status = GetStatusTransferencia(protocolo.Status), Mensagem = protocolo.Mensagem };
                return new KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
            }
            else if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro))
            {
                var transacao = eventosexistentes.OrderByDescending(c => c.IdTransacaoCartao)
                    .First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro);

                var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                AtualizarTransacao(transacao, protocolo);

                if (protocolo.Status.In(ConsultarProtocoloResponseStatus.Pendente, ConsultarProtocoloResponseStatus.Sucesso))
                {
                    var responseCarregar = new TransferirValorCartaoResponse { Status = GetStatusTransferencia(protocolo.Status), Mensagem = protocolo.Mensagem };
                    return new KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                }
            }

            TransferirValorCartaoResponse resultado;

            if (porcentagemTransferencia <= 0)
            {
                resultado = new TransferirValorCartaoResponse
                {
                    ProtocoloProcessamento = 0,
                    Status = TransferirValorCartaoResponseStatus.Erro,
                    Mensagem = $"Porcentagem de Transferência do proprietário para motorista deve ser maior que 0."
                };

                return new KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao>(resultado, null);
            }

            var tipoProcessamentoCartao = GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.Transferencia);

            var layoutHistorico = _layoutCartaoService.GetIdItem(layoutPadraoCartao, tipoProcessamentoCartao.ToString());

            var valorTransferir = viagemEvento.ValorTotalPagamento ?? viagemEvento.ValorPagamento;

            var valorMovimentar = Math.Round(valorTransferir * (porcentagemTransferencia / 100), 2, MidpointRounding.AwayFromZero);

            var transacaoCartao = AdicionarTransacaoViagem(viagemEvento, ETipoOperacaoCartao.Transferencia, layoutHistorico, documentoOrigem, documentoDestino, valorMovimentar);

            var viagem = viagemEvento.Viagem ?? _viagemRepository.Get(viagemEvento.IdViagem);

            var empresa = empresaService.All().Where(e => e.IdEmpresa == viagem.IdEmpresa)
                .Select(e => new
                {
                    e.IdEmpresa,
                    e.RazaoSocial,
                    e.NomeFantasia
                })
                              .First();

            var cartaoVincularOrigem = cartaoVinculadoOrigemList.Cartoes.Last();
            var cartaoVincularDestino = cartaoVinculadoDestinoList.Cartoes.Last();

            var transferenciaRequet = new TransferirValorCartaoRequest();

            transferenciaRequet.PermitirTransacaoPendente = origem == EOrigemTransacaoCartao.Automatico;
            transferenciaRequet.Valor = valorMovimentar;

            transferenciaRequet.CartaoOrigem = new IdentificadorCartao();
            transferenciaRequet.CartaoOrigem.Identificador = cartaoVincularOrigem.Identificador;
            transferenciaRequet.CartaoOrigem.Produto = cartaoVincularOrigem.Produto.Id;

            transferenciaRequet.CartaoDestino = new IdentificadorCartao();
            transferenciaRequet.CartaoDestino.Identificador = cartaoVincularDestino.Identificador;
            transferenciaRequet.CartaoDestino.Produto = cartaoVincularDestino.Produto.Id;

            transferenciaRequet.Empresa = cnpjEmpresa;
            transferenciaRequet.Historico = layoutHistorico;
            transferenciaRequet.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            var ciot = _declaracaoCiotRepository.GetDeclaracaoCiot(viagemEvento.IdViagem);

            transferenciaRequet.InformacoesAdicionais = new Dictionary<string, string>();
            if (!string.IsNullOrWhiteSpace(viagem.DocumentoCliente))
                transferenciaRequet.InformacoesAdicionais.Add("CT-e", viagem.DocumentoCliente);
            if (ciot != null)
                transferenciaRequet.InformacoesAdicionais.Add("CIOT", $"{ciot.Ciot}/{ciot.Verificador}");
            transferenciaRequet.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes());
            transferenciaRequet.InformacoesAdicionais.Add("Movimentação", $"{viagem.NomeProprietario} -> {viagem.NomeMotorista}");
            if (!AppSettingsUtils.IsInstanciaSotran)
                transferenciaRequet.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);

            try
            {
                resultado = _repository.TransferirValorCartao(transferenciaRequet);

                transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                switch (resultado.Status)
                {
                    // Pendente
                    case TransferirValorCartaoResponseStatus.Pendente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                        break;

                    // Pendente
                    case TransferirValorCartaoResponseStatus.NaoProcessado:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                        break;

                    // Sucesso
                    case TransferirValorCartaoResponseStatus.Sucesso:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                        transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;

                        _pushService.EnviarPorDocumento(transacaoCartao.CnpjCpfDestino, "Crédito de " + transacaoCartao.ValorMovimentado.FormatMoney(),
                            $"Crédito transferido para sua conta: {transacaoCartao.ValorMovimentado.FormatMoney()}." +
                                $"\nOrigem: {viagem.NomeProprietario} - {viagem.CPFCNPJProprietario.FormatarCpfCnpj(false)}" +
                                $"\nTransportadora: {empresa.NomeFantasia}");
                        break;

                    // Erro e Protocolo Existente
                    case TransferirValorCartaoResponseStatus.Erro:
                    case TransferirValorCartaoResponseStatus.ProtocoloExistente:
                        transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                        break;
                }

                transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

                AtualizarTransacaoViagem(transacaoCartao);

                return new KeyValuePair<TransferirValorCartaoResponse, TransacaoCartao>(resultado, transacaoCartao);
            }
            catch (Exception e)
            {
                transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                transacaoCartao.MensagemProcessamentoWs = e.Message;
                transacaoCartao.NumeroProtocoloWs = 0;

                AtualizarTransacaoViagem(transacaoCartao);

                throw;
            }
        }

        public KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao> EstornarCargaCartao(TransacaoCartao transacaoCartaoOriginal, ViagemEvento viagemEvento, int idEmpresa, string documentoFavorecido, int layoutPadraoCartao, string cnpjEmpresa)
        {
            var empresaService = _empresaRepository;
            var transacaoService = _transacaoCartaoService;

            try
            {
                var eventosexistentes = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);
                var tipoEventoAtual =
                    GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.EstornoCarga);

                if (eventosexistentes.Any(c =>
                    c.TipoProcessamentoCartao == tipoEventoAtual &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado))
                {
                    var transacao = eventosexistentes.First(c =>
                        c.TipoProcessamentoCartao == tipoEventoAtual &&
                        c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                    var response = new EstornarCargaCartaoResponse { Status = EstornarCargaCartaoResponseStatus.Sucesso };

                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(response, transacao);
                }

                if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro))
                {
                    var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro);

                    var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                    AtualizarTransacao(transacao, protocolo);

                    var responseCarregar = new EstornarCargaCartaoResponse { Status = GetStatusEstornoCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                }
                else if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro))
                {
                    var transacao = eventosexistentes.OrderByDescending(c => c.IdTransacaoCartao)
                        .First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro);

                    var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                    AtualizarTransacao(transacao, protocolo);

                    if (protocolo.Status.In(ConsultarProtocoloResponseStatus.Pendente, ConsultarProtocoloResponseStatus.Sucesso))
                    {
                        var responseCarregar = new EstornarCargaCartaoResponse { Status = GetStatusEstornoCargaValor(protocolo.Status), Mensagem = protocolo.Mensagem };
                        return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                    }
                }

                var tipoProcessamentoCartao = GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, ETipoOperacaoCartao.EstornoCarga);
                var layoutHistorico = _layoutCartaoService.GetIdItem(layoutPadraoCartao, tipoProcessamentoCartao.ToString());

                var transacaoCartao = AdicionarTransacaoViagem(viagemEvento, ETipoOperacaoCartao.EstornoCarga,
                    layoutHistorico, documentoFavorecido, string.Empty, transacaoCartaoOriginal.ValorMovimentado);

                var viagem = viagemEvento.Viagem ?? _viagemRepository.Get(viagemEvento.IdViagem);

                var empresa = empresaService.All().Where(e => e.IdEmpresa == viagem.IdEmpresa)
                    .Select(e => new
                    {
                        e.IdEmpresa,
                        e.RazaoSocial,
                        e.NomeFantasia
                    })
                                  .First();

                EstornarCargaCartaoResponse resultado;

                var estornoRequest = new EstornarCargaCartaoRequest();
                estornoRequest.PermitirTransacaoPendente = true;
                estornoRequest.ProtocoloRequisicaoParaEstorno = transacaoCartaoOriginal.IdTransacaoCartao;
                estornoRequest.Empresa = cnpjEmpresa;
                estornoRequest.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

                var ciot = _declaracaoCiotRepository.GetDeclaracaoCiot(viagemEvento.IdViagem);

                estornoRequest.InformacoesAdicionais = new Dictionary<string, string>();
                if (!string.IsNullOrWhiteSpace(viagem.DocumentoCliente))
                    estornoRequest.InformacoesAdicionais.Add("CT-e", viagem.DocumentoCliente);
                if (ciot != null)
                    estornoRequest.InformacoesAdicionais.Add("CIOT", $"{ciot.Ciot}/{ciot.Verificador}");

                if (!AppSettingsUtils.IsInstanciaSotran)
                    estornoRequest.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);

                try
                {
                    resultado = _repository.EstornarCargaCartao(estornoRequest);

                    transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                    switch (resultado.Status)
                    {
                        // Aberta
                        case EstornarCargaCartaoResponseStatus.NaoProcessado:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                            break;

                        // Sucesso
                        case EstornarCargaCartaoResponseStatus.Sucesso:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                            transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                            break;

                        // Transacao pendente
                        case EstornarCargaCartaoResponseStatus.Pendente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                            break;

                        // Erro e Protocolo Existente
                        case EstornarCargaCartaoResponseStatus.Erro:
                        case EstornarCargaCartaoResponseStatus.ProtocoloExistente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                            break;

                        default:
                            throw new ArgumentException(nameof(resultado.Status));
                    }

                    transacaoCartao.NumeroProtocoloWs = resultado.NumeroTransacao ?? 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    return new KeyValuePair<EstornarCargaCartaoResponse, TransacaoCartao>(resultado, transacaoCartao);
                }
                catch (Exception e)
                {
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    transacaoCartao.MensagemProcessamentoWs = e.Message;
                    transacaoCartao.NumeroProtocoloWs = 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    throw;
                }
            }
            finally
            {
                //                empresaService.Dispose();
            }
        }

        public KeyValuePair<EstornarTransferenciaCartaoResponse, TransacaoCartao> EstornarTransferenciaCartao(TransacaoCartao transacaoCartaoOriginal, ViagemEvento viagemEvento, int idEmpresa, string documentoOrigem, string documentoDestino, int layoutPadraoCartao, string cnpjEmpresa, EOrigemTransacaoCartao origem)
        {
            var empresaService = _empresaRepository;
            var transacaoService = _transacaoCartaoService;


            try
            {
                var eventosexistentes = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);
                var tipoEventoAtual = GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem,
                    ETipoOperacaoCartao.EstornoTransferencia);

                if (eventosexistentes.Any(c =>
                    c.TipoProcessamentoCartao == tipoEventoAtual &&
                    c.StatusPagamento == EStatusPagamentoCartao.Baixado))
                {
                    var transacao = eventosexistentes.First(c =>
                        c.TipoProcessamentoCartao == tipoEventoAtual &&
                        c.StatusPagamento == EStatusPagamentoCartao.Baixado);
                    var response = new EstornarTransferenciaCartaoResponse
                    {
                        Status = EstornarTransferenciaCartaoResponseStatus.Sucesso
                    };

                    return new KeyValuePair<EstornarTransferenciaCartaoResponse, TransacaoCartao>(response, transacao);
                }

                if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro))
                {
                    var transacao = eventosexistentes.First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento != EStatusPagamentoCartao.Erro);

                    var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                    AtualizarTransacao(transacao, protocolo);

                    var responseCarregar = new EstornarTransferenciaCartaoResponse { Status = GetStatusEstornoTransferencia(protocolo.Status), Mensagem = protocolo.Mensagem };
                    return new KeyValuePair<EstornarTransferenciaCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                }
                else if (eventosexistentes.Any(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro))
                {
                    var transacao = eventosexistentes.OrderByDescending(c => c.IdTransacaoCartao)
                        .First(c => c.TipoProcessamentoCartao == tipoEventoAtual && c.StatusPagamento == EStatusPagamentoCartao.Erro);

                    var protocolo = ConsultarProcotolo(transacao.IdTransacaoCartao);
                    AtualizarTransacao(transacao, protocolo);

                    if (protocolo.Status.In(ConsultarProtocoloResponseStatus.Pendente, ConsultarProtocoloResponseStatus.Sucesso))
                    {
                        var responseCarregar = new EstornarTransferenciaCartaoResponse { Status = GetStatusEstornoTransferencia(protocolo.Status), Mensagem = protocolo.Mensagem };
                        return new KeyValuePair<EstornarTransferenciaCartaoResponse, TransacaoCartao>(responseCarregar, transacao);
                    }
                }

                EstornarTransferenciaCartaoResponse resultado;

                var tipoProcessamentoCartao = GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem,
                    ETipoOperacaoCartao.EstornoTransferencia);

                var layoutHistorico =
                    _layoutCartaoService.GetIdItem(layoutPadraoCartao, tipoProcessamentoCartao.ToString());

                var transacaoCartao = AdicionarTransacaoViagem(viagemEvento, ETipoOperacaoCartao.EstornoTransferencia,
                    layoutHistorico, documentoOrigem, documentoDestino, transacaoCartaoOriginal.ValorMovimentado);

                var viagem = viagemEvento.Viagem ?? _viagemRepository.Get(viagemEvento.IdViagem);
                var empresa = empresaService.All().Where(e => e.IdEmpresa == viagem.IdEmpresa)
                    .Select(e => new
                    {
                        e.IdEmpresa,
                        e.RazaoSocial,
                        e.NomeFantasia
                    })
                                  .First();

                var estornoRequest = new EstornarTransferenciaCartaoRequest();
                estornoRequest.PermitirTransacaoPendente = true;
                estornoRequest.ProtocoloRequisicaoParaEstorno = transacaoCartaoOriginal.IdTransacaoCartao;
                estornoRequest.Empresa = cnpjEmpresa;
                estornoRequest.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

                var ciot = _declaracaoCiotRepository.GetDeclaracaoCiot(viagemEvento.IdViagem);

                estornoRequest.InformacoesAdicionais = new Dictionary<string, string>();
                if (!string.IsNullOrWhiteSpace(viagem.DocumentoCliente))
                    estornoRequest.InformacoesAdicionais.Add("CT-e", viagem.DocumentoCliente);
                if (ciot != null)
                    estornoRequest.InformacoesAdicionais.Add("CIOT", $"{ciot.Ciot}/{ciot.Verificador}");
                estornoRequest.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes());
                estornoRequest.InformacoesAdicionais.Add("Movimentação", $"{viagem.NomeMotorista} -> {viagem.NomeProprietario}");
                if (!AppSettingsUtils.IsInstanciaSotran)
                    estornoRequest.InformacoesAdicionais.Add("Transportador", empresa.NomeFantasia);
                try
                {
                    resultado = _repository.EstornarTransferencia(estornoRequest);

                    transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

                    switch (resultado.Status)
                    {
                        // Transacao pendente
                        case EstornarTransferenciaCartaoResponseStatus.Pendente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                            break;

                        // Pendente
                        case EstornarTransferenciaCartaoResponseStatus.NaoProcessado:

                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                            break;

                        // Sucesso
                        case EstornarTransferenciaCartaoResponseStatus.Sucesso:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                            transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                            break;

                        // Erro e Protocolo Existente
                        case EstornarTransferenciaCartaoResponseStatus.Erro:
                        case EstornarTransferenciaCartaoResponseStatus.ProtocoloExistente:
                            transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                            break;
                    }

                    transacaoCartao.NumeroProtocoloWs = resultado.NumeroTransacao ?? 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    return new KeyValuePair<EstornarTransferenciaCartaoResponse, TransacaoCartao>(resultado, transacaoCartao);
                }
                catch (Exception e)
                {
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    transacaoCartao.MensagemProcessamentoWs = e.Message;
                    transacaoCartao.NumeroProtocoloWs = 0;

                    AtualizarTransacaoViagem(transacaoCartao);

                    throw;
                }
            }
            finally
            {
                //                empresaService.Dispose();
                //                transacaoService.Dispose();
            }
        }

        public ETipoProcessamentoCartao GetTipoProcessamentoCartaoCarga(ETipoEventoViagem tipoEventoViagem, ETipoOperacaoCartao tipoOperacao)
        {
            switch (tipoOperacao)
            {
                case ETipoOperacaoCartao.Carregamento:

                    switch (tipoEventoViagem)
                    {
                        case ETipoEventoViagem.TarifaAntt:
                            return ETipoProcessamentoCartao.CargaTarifaAntt;

                        case ETipoEventoViagem.Abastecimento:
                            return ETipoProcessamentoCartao.CargaAbastecimento;

                        case ETipoEventoViagem.Adiantamento:
                            return ETipoProcessamentoCartao.CargaAdiantamento;

                        case ETipoEventoViagem.Saldo:
                            return ETipoProcessamentoCartao.CargaSaldo;

                        case ETipoEventoViagem.Abono:
                            return ETipoProcessamentoCartao.CargaAbono;

                        case ETipoEventoViagem.RPA:
                            return ETipoProcessamentoCartao.CargaRpa;

                        case ETipoEventoViagem.Estadia:
                            return ETipoProcessamentoCartao.CargaEstadia;
                        default:
                            throw new ArgumentException(nameof(tipoEventoViagem));
                    }

                case ETipoOperacaoCartao.Transferencia:

                    switch (tipoEventoViagem)
                    {
                        case ETipoEventoViagem.TarifaAntt:
                            return ETipoProcessamentoCartao.TransferenciaTarifaAntt;

                        case ETipoEventoViagem.Abastecimento:
                            return ETipoProcessamentoCartao.TransferenciaAbastecimento;

                        case ETipoEventoViagem.Adiantamento:
                            return ETipoProcessamentoCartao.TransferenciaAdiantamento;

                        case ETipoEventoViagem.Saldo:
                            return ETipoProcessamentoCartao.TransferenciaSaldo;

                        case ETipoEventoViagem.RPA:
                            return ETipoProcessamentoCartao.TransferenciaRpa;

                        case ETipoEventoViagem.Estadia:
                            return ETipoProcessamentoCartao.TransferenciaEstadia;

                        case ETipoEventoViagem.Abono:
                            return ETipoProcessamentoCartao.TransferenciaAbono;

                        default:
                            throw new ArgumentException(nameof(tipoEventoViagem));
                    }

                case ETipoOperacaoCartao.EstornoCarga:

                    switch (tipoEventoViagem)
                    {
                        case ETipoEventoViagem.TarifaAntt:
                            return ETipoProcessamentoCartao.EstornoTarifaAntt;

                        case ETipoEventoViagem.Abastecimento:
                            return ETipoProcessamentoCartao.EstornoAbastecimento;

                        case ETipoEventoViagem.Adiantamento:
                            return ETipoProcessamentoCartao.EstornoAdiantamento;

                        case ETipoEventoViagem.Saldo:
                            return ETipoProcessamentoCartao.EstornoSaldo;

                        case ETipoEventoViagem.RPA:
                            return ETipoProcessamentoCartao.EstornoRpa;

                        case ETipoEventoViagem.Estadia:
                            return ETipoProcessamentoCartao.EstornoEstadia;

                        case ETipoEventoViagem.Abono:
                            return ETipoProcessamentoCartao.EstornoAbono;

                        default:
                            throw new ArgumentException(nameof(tipoEventoViagem));
                    }

                case ETipoOperacaoCartao.EstornoTransferencia:

                    switch (tipoEventoViagem)
                    {
                        case ETipoEventoViagem.TarifaAntt:
                            return ETipoProcessamentoCartao.EstornoTransferenciaTarifaAntt;

                        case ETipoEventoViagem.Abastecimento:
                            return ETipoProcessamentoCartao.EstornoTransferenciaAbastecimento;

                        case ETipoEventoViagem.Adiantamento:
                            return ETipoProcessamentoCartao.EstornoTransferenciaAdiantamento;

                        case ETipoEventoViagem.Saldo:
                            return ETipoProcessamentoCartao.EstornoTransferenciaSaldo;

                        case ETipoEventoViagem.RPA:
                            return ETipoProcessamentoCartao.EstornoTransferenciaRpa;

                        case ETipoEventoViagem.Estadia:
                            return ETipoProcessamentoCartao.EstornoTransferenciaEstadia;

                        case ETipoEventoViagem.Abono:
                            return ETipoProcessamentoCartao.EstornoTransferenciaAbono;

                        default:
                            throw new ArgumentException(nameof(tipoEventoViagem));
                    }

                case ETipoOperacaoCartao.TransferenciaContaBancaria:

                    return ETipoProcessamentoCartao.TransferenciaContaBancaria;

                case ETipoOperacaoCartao.EstornoTransferenciaContaBancaria:

                    return ETipoProcessamentoCartao.EstornoTransferenciaContaBancaria;

                case ETipoOperacaoCartao.TransferenciaParaRecuperarSaldoMotoristaNoCancelamentoViagem:
                    return ETipoProcessamentoCartao.TransferenciaParaRecuperarSaldoMotoristaNoCancelamentoViagem;

                default:
                    throw new ArgumentException(nameof(tipoOperacao));
            }
        }

        #endregion

        #region Métodos de cartão avulsos (Sem integracão necessária com viagem)

        public OperacaoCartaoResponseDTO TransferirValorCartao(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem, string senha, int? administradoraId = null)
        {
            int? empresaId = 0;
            List<int> produtoId = new List<int>(0);
            if (origem != EOrigemTransacaoCartao.Atendimento)
                produtoId = new List<int> { GetIdProdutoCartaoFretePadrao() };
            else
            {
                var prodPadrao = _parametrosService.GetProdutoIdPadrao(administradoraId.GetValueOrDefault(0));
                produtoId.Add(Convert.ToInt32(prodPadrao));
            }

            var empresaService = _empresaRepository;

            int? layoutPadraoCartao = null;
            if (cnpjEmpresa.IsNullOrWhiteSpace())
                layoutPadraoCartao = _parametrosService.GetIdLayoutCartao();
            else
            {
                empresaId = empresaService.GetIdPorCnpj(cnpjEmpresa);
                if (empresaId == null)
                    throw new CartaoAtsException("Empresa não localizada com CNPJ " + cnpjEmpresa);
                layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(empresaId.Value);
            }

            if (layoutPadraoCartao == null)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = "Layout de cartão não localizado para realizar operação de transferência de valores"
                };

            var layoutHistorico = _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value,
                ETipoProcessamentoCartao.TransferenciaSaldo.ToString());

            #region Cartao origem

            var cartoesVinculadosOrigem = GetCartoesVinculados(documentoOrigem, produtoId, false);

            if (cartoesVinculadosOrigem?.Cartoes == null || cartoesVinculadosOrigem.Cartoes.Count == 0)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = $"Nenhum cartão foi encontrado para o favorecido de documento {documentoOrigem}",
                    Protocolo = 0
                };

            var cartaoVincularOrigem = cartoesVinculadosOrigem.Cartoes.Last();

            var senhaValidada = ValidarSenhaCartao(cartaoVincularOrigem, senha);

            if (!senhaValidada.Sucesso)
            {
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = senhaValidada.Mensagem,
                    Protocolo = 0
                };
            }

            #endregion

            #region Cartao destino

            var cartoesVinculadosDestino = GetCartoesVinculados(documentoDestino, produtoId, false);

            if (cartoesVinculadosDestino.Cartoes.Count == 0)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = $"Nenhum cartão foi encontrado para o favorecido de documento {documentoDestino}",
                    Protocolo = 0
                };

            var cartaoVincularDestino = cartoesVinculadosDestino.Cartoes.Last();

            #endregion

            var transacaoCartao = AdicionarTransacaoViagemAvulso(layoutHistorico, documentoOrigem, documentoDestino,
                valor, ETipoProcessamentoCartao.TransferenciaSaldo, origem, null);

            var transferenciaRequet = new TransferirValorCartaoRequest();

            transferenciaRequet.PermitirTransacaoPendente = origem == EOrigemTransacaoCartao.Atendimento;
            transferenciaRequet.Valor = valor;

            transferenciaRequet.CartaoOrigem = new IdentificadorCartao();
            transferenciaRequet.CartaoOrigem.Identificador = cartaoVincularOrigem.Identificador;
            transferenciaRequet.CartaoOrigem.Produto = cartaoVincularOrigem.Produto.Id;

            transferenciaRequet.CartaoDestino = new IdentificadorCartao();
            transferenciaRequet.CartaoDestino.Identificador = cartaoVincularDestino.Identificador;
            transferenciaRequet.CartaoDestino.Produto = cartaoVincularDestino.Produto.Id;

            transferenciaRequet.Empresa = StringExtension.OnlyNumbers(cnpjEmpresa);
            transferenciaRequet.Historico = layoutHistorico;
            transferenciaRequet.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            transferenciaRequet.InformacoesAdicionais = new Dictionary<string, string>();
            transferenciaRequet.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes()); // Automática/Aplicativo
            transferenciaRequet.InformacoesAdicionais.Add("Documento Favorecido", documentoDestino.FormatarCpfCnpj()); // Automática/Aplicativo

            var resultado = _repository.TransferirValorCartao(transferenciaRequet);

            transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

            switch (resultado.Status)
            {
                // Pendente
                case TransferirValorCartaoResponseStatus.Pendente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    break;

                // Pendente
                case TransferirValorCartaoResponseStatus.NaoProcessado:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    break;

                // Sucesso
                case TransferirValorCartaoResponseStatus.Sucesso:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;

                    EnviarPushTransferencia(empresaId, transacaoCartao.CnpjCpfOrigem, transacaoCartao.CnpjCpfDestino,
                        transacaoCartao.ValorMovimentado);
                    break;

                // Erro e Protocolo Existente
                case TransferirValorCartaoResponseStatus.Erro:
                case TransferirValorCartaoResponseStatus.ProtocoloExistente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    break;
            }

            transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

            AtualizarTransacaoViagem(transacaoCartao);

            return new OperacaoCartaoResponseDTO
            {
                Status = GetStatusSucesso(resultado.Status),
                Mensagem = resultado.Mensagem,
                Protocolo = resultado.ProtocoloProcessamento
            };
        }

        public OperacaoCartaoResponseDTO TransferirValorCartao(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem)
        {
            var produtoId = new List<int> { GetIdProdutoCartaoFretePadrao() };

            var empresaService = _empresaRepository;

            var empresaId = empresaService.GetIdPorCnpj(cnpjEmpresa);

            int? layoutPadraoCartao = null;
            if (empresaId != null)
                layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(empresaId.Value);

            if (layoutPadraoCartao == null)
                layoutPadraoCartao = SistemaInfoConsts.ProdutoFretePadraoAdministradora;

            if (layoutPadraoCartao == null)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = "Layout de cartão não localizado para realizar operação de transferência de valores"
                };

            var layoutHistorico = _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value, ETipoProcessamentoCartao.TransferenciaParaRecuperarSaldoMotoristaNoCancelamentoViagem.ToString());

            #region Cartao origem

            var cartoesVinculadosOrigem = GetCartoesVinculados(documentoOrigem, produtoId, documentoDestino != documentoOrigem);

            if (cartoesVinculadosOrigem.Cartoes.Count == 0)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = $"Nenhum cartão foi encontrado para o favorecido de documento {documentoOrigem}",
                    Protocolo = 0
                };

            var cartaoVincularOrigem = cartoesVinculadosOrigem.Cartoes.Last();

            #endregion

            #region Cartao destino

            var cartoesVinculadosDestino = GetCartoesVinculados(documentoDestino, produtoId, false);

            if (cartoesVinculadosDestino.Cartoes.Count == 0)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = $"Nenhum cartão foi encontrado para o favorecido de documento {documentoDestino}",
                    Protocolo = 0
                };

            var cartaoVincularDestino = cartoesVinculadosDestino.Cartoes.Last();

            #endregion

            var transacaoCartao = AdicionarTransacaoViagemAvulso(layoutHistorico, documentoOrigem, documentoDestino,
                valor, ETipoProcessamentoCartao.TransferenciaAdiantamento, origem, null);

            var transferenciaRequet = new TransferirValorCartaoRequest();

            transferenciaRequet.PermitirTransacaoPendente = origem == EOrigemTransacaoCartao.Automatico;
            transferenciaRequet.Valor = valor;

            transferenciaRequet.CartaoOrigem = new IdentificadorCartao();
            transferenciaRequet.CartaoOrigem.Identificador = cartaoVincularOrigem.Identificador;
            transferenciaRequet.CartaoOrigem.Produto = cartaoVincularOrigem.Produto.Id;

            transferenciaRequet.CartaoDestino = new IdentificadorCartao();
            transferenciaRequet.CartaoDestino.Identificador = cartaoVincularDestino.Identificador;
            transferenciaRequet.CartaoDestino.Produto = cartaoVincularDestino.Produto.Id;

            transferenciaRequet.Empresa = StringExtension.OnlyNumbers(cnpjEmpresa);
            transferenciaRequet.Historico = layoutHistorico;
            transferenciaRequet.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            transferenciaRequet.InformacoesAdicionais = new Dictionary<string, string>();
            transferenciaRequet.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes()); // Automática/Aplicativo
            transferenciaRequet.InformacoesAdicionais.Add("Documento Favorecido", documentoDestino.FormatarCpfCnpj()); // Automática/Aplicativo

            var resultado = _repository.TransferirValorCartao(transferenciaRequet);

            transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

            switch (resultado.Status)
            {
                // Pendente
                case TransferirValorCartaoResponseStatus.Pendente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    break;

                // Pendente
                case TransferirValorCartaoResponseStatus.NaoProcessado:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    break;

                // Sucesso
                case TransferirValorCartaoResponseStatus.Sucesso:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;

                    EnviarPushTransferencia(empresaId, transacaoCartao.CnpjCpfOrigem, transacaoCartao.CnpjCpfDestino,
                        transacaoCartao.ValorMovimentado);
                    break;

                // Erro e Protocolo Existente
                case TransferirValorCartaoResponseStatus.Erro:
                case TransferirValorCartaoResponseStatus.ProtocoloExistente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    break;
            }

            transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

            AtualizarTransacaoViagem(transacaoCartao);

            return new OperacaoCartaoResponseDTO
            {
                Status = GetStatusSucesso(resultado.Status),
                Mensagem = resultado.Mensagem,
                Protocolo = resultado.ProtocoloProcessamento
            };
        }

        public void EnviarPushTransferencia(int? empresaId, string cpfCnpjOrigem, string cpfCnpjDestino, decimal valor)
        {
            var empresaService = _empresaRepository;

            var nomeEmpresa = empresaId.HasValue
                ? empresaService.All()
                    .Where(e => e.IdEmpresa == empresaId)
                    .Select(e => e.NomeFantasia ?? e.RazaoSocial)
                    .FirstOrDefault()
                : null;

            var propQuery = _proprietarioRepository.AllAtivos().Where(p => p.CNPJCPF == cpfCnpjOrigem);
            if (empresaId.HasValue)
                propQuery = propQuery.Where(p => p.IdEmpresa == empresaId);

            var nomeProp = propQuery.Select(p => p.NomeFantasia ?? p.RazaoSocial).FirstOrDefault();

            _pushService.EnviarPorDocumento(cpfCnpjDestino, "Crédito de " + valor.FormatMoney(),
                $"Crédito transferido para sua conta: {valor.FormatMoney()}." +
                $"\nOrigem: " + (!string.IsNullOrWhiteSpace(nomeProp) ? nomeProp + " - " : string.Empty) + cpfCnpjOrigem.FormatarCpfCnpj(false) +
                $"\nTransportadora: {nomeEmpresa}");
        }

        public OperacaoCartaoResponseDTO TransferirValorContaBancaria(TransferirContaBancariaRequestDTO request, EOrigemTransacaoCartao origem)
        {
            var empresaId = string.IsNullOrWhiteSpace(request.CnpjEmpresa) ? null : _empresaRepository.GetIdPorCnpj(request.CnpjEmpresa);
            var layoutPadraoCartao = empresaId.HasValue ? _empresaRepository.GetLayoutCartaoPadrao(empresaId.Value) : _parametrosService.GetIdLayoutCartao();
            if (layoutPadraoCartao == null) throw new CartaoAtsException("Empresa não possui layout de cartão padrão");

            var produtoId = new List<int> { GetIdProdutoCartaoFretePadrao() };
            var cartoes = GetCartoesVinculados(request.DocumentoFavorecido, produtoId, false);
            if (cartoes?.Cartoes == null || !cartoes.Cartoes.Any())
            {
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = $"Nenhum cartão foi encontrado para o favorecido de documento {request.DocumentoFavorecido}",
                    Protocolo = 0
                };
            }

            int identificadorCartao;
            int produtoCartao;

            if (request.CartaoOrigem != null && request.CartaoOrigem.Identificador != 0)
            {
                if (!cartoes.Cartoes.Any(c => c.Identificador == request.CartaoOrigem.Identificador && c.Produto.Id == request.CartaoOrigem.Produto))
                {
                    return new OperacaoCartaoResponseDTO
                    {
                        Status = EStatusPagamentoCartao.Erro,
                        Mensagem = $"Não foi possível validar os dados informados.",
                        Protocolo = 0
                    };
                }
                identificadorCartao = request.CartaoOrigem.Identificador;
                produtoCartao = request.CartaoOrigem.Produto;
            }
            else
            {
                var cartao = cartoes.Cartoes.LastOrDefault();
                if (cartao?.Identificador == null || cartao.Produto == null)
                    throw new InvalidOperationException("Não foi possivel identificar o cartão do portador " + request.DocumentoFavorecido);
                identificadorCartao = cartao.Identificador.Value;
                produtoCartao = cartao.Produto.Id;
            }

            var senhaValidada = ValidarSenhaCartao(identificadorCartao, produtoCartao, request.Senha);

            if (!senhaValidada.Sucesso)
            {
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = senhaValidada.Mensagem,
                    Protocolo = 0
                };
            }

            var tipoProcessamentoCartao = GetTipoProcessamentoCartaoCarga(ETipoEventoViagem.Adiantamento,
                ETipoOperacaoCartao.TransferenciaContaBancaria);

            var layoutHistorico =
                _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value, tipoProcessamentoCartao.ToString());

            var transacaoCartao = AdicionarTransacaoViagemAvulso(layoutHistorico, request.DocumentoFavorecido,
                request.DocumentoFavorecido, request.Valor, ETipoProcessamentoCartao.TransferenciaContaBancaria,
                origem, null);

            var transferencia = new TransferirValorContaBancariaRequest();

            transferencia.Valor = request.Valor;
            transferencia.ContaBancaria = request.ContaBancaria;
            transferencia.DocumentoFavorecido = request.DocumentoFavorecido;

            transferencia.CartaoOrigem = new IdentificadorCartao();
            transferencia.CartaoOrigem.Identificador = identificadorCartao;
            transferencia.CartaoOrigem.Produto = produtoCartao;
            transferencia.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            transferencia.Historico = layoutHistorico;

            transferencia.InformacoesAdicionais = new Dictionary<string, string>();
            transferencia.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes());

            transferencia.InformacoesAdicionais.Add("Código do banco", transferencia.ContaBancaria.CodigoBacenBanco);
            transferencia.InformacoesAdicionais.Add("Agência", transferencia.ContaBancaria.Agencia);
            transferencia.InformacoesAdicionais.Add("Conta bancária", transferencia.ContaBancaria.Conta);
            transferencia.InformacoesAdicionais.Add("Digito da conta", transferencia.ContaBancaria.DigitoConta.ToString());
            transferencia.InformacoesAdicionais.Add("Tipo da conta", transferencia.ContaBancaria.TipoConta == IdentificadorContaBancariaTipoConta.Poupanca ? "Poupança" : "Corrente");

            var resultado = _repository.TransferirValorContaBancaria(transferencia);

            transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

            switch (resultado.Status)
            {
                // Pendente
                case TransferirValorContaBancariaResponseStatus.Pendente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    break;

                // Pendente
                case TransferirValorContaBancariaResponseStatus.NaoProcessado:

                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    break;

                // Sucesso
                case TransferirValorContaBancariaResponseStatus.Sucesso:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                    break;

                // Erro e Protocolo Existente
                case TransferirValorContaBancariaResponseStatus.Erro:
                case TransferirValorContaBancariaResponseStatus.ProtocoloExistente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    break;
            }

            transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

            AtualizarTransacaoViagem(transacaoCartao);

            return new OperacaoCartaoResponseDTO
            {
                Status = GetStatusSucesso(resultado.Status),
                Mensagem = resultado.Mensagem,
                Protocolo = resultado.ProtocoloProcessamento ?? 0
            };
        }

        #endregion

        #region Métodos privados

        private TransacaoCartao AdicionarTransacaoViagem(CargaAvulsa cargaAvulsa, ETipoOperacaoCartao tipoOperacaoCartao, 
            int historico, string cnpjOrigem, string cnpjDestino, decimal valorMovimentado, 
            ETipoProcessamentoCartao? tipoProcessamentoCartao = null, int? identificadorCartao = null)
        {
            var transacaoService = _transacaoCartaoService;

            if (!tipoProcessamentoCartao.HasValue)
                tipoProcessamentoCartao = ETipoProcessamentoCartao.CargaAvulsa;

            var transacao =
                transacaoService.GetByIdCargaAndTipoProcesso(cargaAvulsa.IdCargaAvulsa, tipoProcessamentoCartao.Value);

            if (transacao != null)
                return transacao;

            transacao = new TransacaoCartao();

            transacao.IdViagemEvento = null;
            transacao.IdCargaAvulsa = cargaAvulsa.IdCargaAvulsa;
            transacao.DataCriacao = DateTime.Now;
            transacao.LineId = transacaoService.GetCountByIdCarga(cargaAvulsa.IdCargaAvulsa) + 1;
            transacao.StatusPagamento = EStatusPagamentoCartao.Aberto;
            transacao.MensagemProcessamentoWs = string.Empty;
            transacao.NumeroProtocoloWs = 0;
            transacao.ValorMovimentado = valorMovimentado;
            transacao.Historico = historico;
            transacao.CnpjCpfOrigem = cnpjOrigem;
            transacao.CnpjCpfDestino = cnpjDestino;
            transacao.OrigemTransacaoCartao = EOrigemTransacaoCartao.Avulso;
            transacao.TipoProcessamentoCartao = tipoProcessamentoCartao.Value;
            if (identificadorCartao != null) transacao.IdentificadorCartao = identificadorCartao;

            return transacaoService.Add(transacao);
        }

        private TransacaoCartao AdicionarTransacaoViagem(ViagemEvento viagemEvento, ETipoOperacaoCartao tipoOperacaoCartao, int historico, string cnpjOrigem, string cnpjDestino, decimal valorMovimentado)
        {
            try
            {
                var transacaoService = _transacaoCartaoService;

                var tipoProcessamentoCartao =
                    GetTipoProcessamentoCartaoCarga(viagemEvento.TipoEventoViagem, tipoOperacaoCartao);

                var transacao =
                    transacaoService.GetByIdEventoAndTipoProcesso(viagemEvento.IdViagemEvento, tipoProcessamentoCartao);


                if (transacao != null && transacao.StatusPagamento != EStatusPagamentoCartao.Erro)
                    return transacao;

                transacao = new TransacaoCartao();

                transacao.IdViagemEvento = viagemEvento.IdViagemEvento;
                transacao.DataCriacao = DateTime.Now;
                transacao.LineId = transacaoService.GetCountByIdEvento(transacao.IdViagemEvento.Value) + 1;
                transacao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                transacao.MensagemProcessamentoWs = string.Empty;
                transacao.NumeroProtocoloWs = 0;
                transacao.ValorMovimentado = valorMovimentado;
                transacao.Historico = historico;
                transacao.CnpjCpfOrigem = cnpjOrigem;
                transacao.CnpjCpfDestino = cnpjDestino;
                transacao.OrigemTransacaoCartao = EOrigemTransacaoCartao.Automatico;

                transacao.TipoProcessamentoCartao = tipoProcessamentoCartao;

                return transacaoService.Add(transacao);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="historico"></param>
        /// <param name="cnpjOrigem"></param>
        /// <param name="cnpjDestino"></param>
        /// <param name="valorMovimentado"></param>
        /// <param name="tipoProcessamentoCartao"></param>
        /// <param name="origemTransacaoCartao"></param>
        /// <param name="idViagemEvento">Vincular transferência a uma parcela de viagem, para que o estorno desfaça as transferência manuais também em busca de reaver o saldo da empresa</param>
        /// <returns></returns>
        private TransacaoCartao AdicionarTransacaoViagemAvulso(int historico, string cnpjOrigem, string cnpjDestino,
            decimal valorMovimentado, ETipoProcessamentoCartao tipoProcessamentoCartao,
            EOrigemTransacaoCartao origemTransacaoCartao, int? idViagemEvento)
        {
            var transacaoService = _transacaoCartaoService;

            var transacao = new TransacaoCartao();

            transacao.IdViagemEvento = idViagemEvento;
            transacao.DataCriacao = DateTime.Now;
            transacao.LineId = transacaoService.GetCountByIdEvento(transacao.IdViagemEvento ?? 0) + 1;
            transacao.StatusPagamento = EStatusPagamentoCartao.Aberto;
            transacao.MensagemProcessamentoWs = string.Empty;
            transacao.NumeroProtocoloWs = 0;
            transacao.ValorMovimentado = valorMovimentado;
            transacao.Historico = historico;
            transacao.CnpjCpfOrigem = cnpjOrigem;
            transacao.CnpjCpfDestino = cnpjDestino;
            transacao.OrigemTransacaoCartao = origemTransacaoCartao;
            transacao.TipoProcessamentoCartao = tipoProcessamentoCartao;

            return transacaoService.Add(transacao);
        }

        private void AtualizarTransacaoViagem(TransacaoCartao transacaoCartao)
        {
            try
            {
                var transacaoService = _transacaoCartaoService;
                transacaoCartao.MensagemProcessamentoWs = transacaoCartao.MensagemProcessamentoWs.ValueLimited(300);
                transacaoService.Update(transacaoCartao);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                throw;
            }
        }

        private static void PopularVincularRequest(int identificador, int produtoId, AtsPortadorRequest atsPortadorRequest, VincularRequest vincularRequest)
        {
            var cartao = new IdentificadorCartao
            {
                Identificador = identificador,
                Produto = produtoId
            };
            vincularRequest.Cartao = cartao;

            var pessoaRequest = new IntegrarPessoaRequest
            {
                Nome = atsPortadorRequest.Nome,
                NomeFantasia = atsPortadorRequest.NomeFantasia,
                CpfCnpj = atsPortadorRequest.Documento.OnlyNumbers(),
                Endereco = new IntegrarPessoaEnderecoRequest
                {
                    Cidade = atsPortadorRequest.CodigoIbge,
                    Numero = atsPortadorRequest.Numero.ToString(),
                    Logradouro = atsPortadorRequest.Logradouro,
                    Bairro = atsPortadorRequest.Bairro,
                    Cep = atsPortadorRequest.Cep.OnlyNumbers(),
                    Complemento = atsPortadorRequest.Complemento
                },
                Info = new IntegrarPessoaInfoRequest
                {
                    Sexo = atsPortadorRequest.Sexo,
                    Rg = atsPortadorRequest.RG,
                    NomeMae = atsPortadorRequest.NomeMae,
                    NomePai = atsPortadorRequest.NomePai,
                    DataNascimento = atsPortadorRequest.DataNascimento,
                    Telefone = atsPortadorRequest.Telefone.OnlyNumbers(),
                    TacEquiparado = atsPortadorRequest.EquiparadoTac
                }
            };
            vincularRequest.Pessoa = pessoaRequest;
        }

        private static void PopularDesvincularRequest(int identificador, int produtoId, string motivoDesvinculo, AtsPortadorRequest atsPortadorRequest, DesvincularRequest desvincularRequest)
        {
            var cartao = new IdentificadorCartao
            {
                Identificador = identificador,
                Produto = produtoId
            };

            desvincularRequest.Cartao = cartao;

            desvincularRequest.MotivoDesvinculo = motivoDesvinculo;
        }

        private static EStatusPagamentoCartao GetStatusSucesso(CarregarValorResponseStatus status)
        {
            switch (status)
            {
                case CarregarValorResponseStatus.NaoProcessado:
                    return EStatusPagamentoCartao.Aberto;

                case CarregarValorResponseStatus.Sucesso:
                    return EStatusPagamentoCartao.Baixado;

                case CarregarValorResponseStatus.Erro:
                    return EStatusPagamentoCartao.Erro;

                case CarregarValorResponseStatus.ProtocoloExistente:
                    return EStatusPagamentoCartao.Erro;

                case CarregarValorResponseStatus.Pendente:
                    return EStatusPagamentoCartao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusPagamentoCartao GetStatusSucesso(TransferirValorCartaoResponseStatus status)
        {
            switch (status)
            {
                case TransferirValorCartaoResponseStatus.NaoProcessado:
                    return EStatusPagamentoCartao.Aberto;

                case TransferirValorCartaoResponseStatus.Sucesso:
                    return EStatusPagamentoCartao.Baixado;

                case TransferirValorCartaoResponseStatus.Erro:
                    return EStatusPagamentoCartao.Erro;

                case TransferirValorCartaoResponseStatus.ProtocoloExistente:
                    return EStatusPagamentoCartao.Erro;

                case TransferirValorCartaoResponseStatus.Pendente:
                    return EStatusPagamentoCartao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusPagamentoCartao GetStatusSucesso(EstornarCargaCartaoResponseStatus status)
        {
            switch (status)
            {
                case EstornarCargaCartaoResponseStatus.NaoProcessado:
                    return EStatusPagamentoCartao.Aberto;

                case EstornarCargaCartaoResponseStatus.Sucesso:
                    return EStatusPagamentoCartao.Baixado;

                case EstornarCargaCartaoResponseStatus.Erro:
                    return EStatusPagamentoCartao.Erro;

                case EstornarCargaCartaoResponseStatus.ProtocoloExistente:
                    return EStatusPagamentoCartao.Erro;

                case EstornarCargaCartaoResponseStatus.Pendente:
                    return EStatusPagamentoCartao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusPagamentoCartao GetStatusSucesso(EstornarTransferenciaCartaoResponseStatus status)
        {
            switch (status)
            {
                case EstornarTransferenciaCartaoResponseStatus.NaoProcessado:
                    return EStatusPagamentoCartao.Aberto;

                case EstornarTransferenciaCartaoResponseStatus.Sucesso:
                    return EStatusPagamentoCartao.Baixado;

                case EstornarTransferenciaCartaoResponseStatus.Erro:
                    return EStatusPagamentoCartao.Erro;

                case EstornarTransferenciaCartaoResponseStatus.ProtocoloExistente:
                    return EStatusPagamentoCartao.Erro;

                case EstornarTransferenciaCartaoResponseStatus.Pendente:
                    return EStatusPagamentoCartao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static EStatusPagamentoCartao GetStatusSucesso(TransferirValorContaBancariaResponseStatus status)
        {
            switch (status)
            {
                case TransferirValorContaBancariaResponseStatus.NaoProcessado:
                    return EStatusPagamentoCartao.Aberto;

                case TransferirValorContaBancariaResponseStatus.Sucesso:
                    return EStatusPagamentoCartao.Baixado;

                case TransferirValorContaBancariaResponseStatus.Erro:
                    return EStatusPagamentoCartao.Erro;

                case TransferirValorContaBancariaResponseStatus.ProtocoloExistente:
                    return EStatusPagamentoCartao.Erro;

                case TransferirValorContaBancariaResponseStatus.Pendente:
                    return EStatusPagamentoCartao.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(status), status, null);
            }
        }

        private static TransferirValorCartaoResponseStatus GetStatusTransferencia(ConsultarProtocoloResponseStatus statusConsulta)
        {
            switch (statusConsulta)
            {
                case ConsultarProtocoloResponseStatus.NaoProcessado:
                    return TransferirValorCartaoResponseStatus.NaoProcessado;
                case ConsultarProtocoloResponseStatus.Sucesso:
                    return TransferirValorCartaoResponseStatus.Sucesso;
                case ConsultarProtocoloResponseStatus.Erro:
                    return TransferirValorCartaoResponseStatus.Erro;
                case ConsultarProtocoloResponseStatus.ProtocoloExistente:
                    return TransferirValorCartaoResponseStatus.ProtocoloExistente;
                case ConsultarProtocoloResponseStatus.Pendente:
                    return TransferirValorCartaoResponseStatus.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(statusConsulta), statusConsulta, null);
            }
        }

        private static CarregarValorResponseStatus GetStatusCargaValor(ConsultarProtocoloResponseStatus statusConsulta)
        {
            switch (statusConsulta)
            {
                case ConsultarProtocoloResponseStatus.NaoProcessado:
                    return CarregarValorResponseStatus.NaoProcessado;
                case ConsultarProtocoloResponseStatus.Sucesso:
                    return CarregarValorResponseStatus.Sucesso;
                case ConsultarProtocoloResponseStatus.Erro:
                    return CarregarValorResponseStatus.Erro;
                case ConsultarProtocoloResponseStatus.ProtocoloExistente:
                    return CarregarValorResponseStatus.ProtocoloExistente;
                case ConsultarProtocoloResponseStatus.Pendente:
                    return CarregarValorResponseStatus.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(statusConsulta), statusConsulta, null);
            }
        }

        private static EstornarCargaCartaoResponseStatus GetStatusEstornoCargaValor(ConsultarProtocoloResponseStatus statusConsulta)
        {
            switch (statusConsulta)
            {
                case ConsultarProtocoloResponseStatus.NaoProcessado:
                    return EstornarCargaCartaoResponseStatus.NaoProcessado;
                case ConsultarProtocoloResponseStatus.Sucesso:
                    return EstornarCargaCartaoResponseStatus.Sucesso;
                case ConsultarProtocoloResponseStatus.Erro:
                    return EstornarCargaCartaoResponseStatus.Erro;
                case ConsultarProtocoloResponseStatus.ProtocoloExistente:
                    return EstornarCargaCartaoResponseStatus.ProtocoloExistente;
                case ConsultarProtocoloResponseStatus.Pendente:
                    return EstornarCargaCartaoResponseStatus.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(statusConsulta), statusConsulta, null);
            }
        }

        private static EstornarTransferenciaCartaoResponseStatus GetStatusEstornoTransferencia(ConsultarProtocoloResponseStatus statusConsulta)
        {
            switch (statusConsulta)
            {
                case ConsultarProtocoloResponseStatus.NaoProcessado:
                    return EstornarTransferenciaCartaoResponseStatus.NaoProcessado;
                case ConsultarProtocoloResponseStatus.Sucesso:
                    return EstornarTransferenciaCartaoResponseStatus.Sucesso;
                case ConsultarProtocoloResponseStatus.Erro:
                    return EstornarTransferenciaCartaoResponseStatus.Erro;
                case ConsultarProtocoloResponseStatus.ProtocoloExistente:
                    return EstornarTransferenciaCartaoResponseStatus.ProtocoloExistente;
                case ConsultarProtocoloResponseStatus.Pendente:
                    return EstornarTransferenciaCartaoResponseStatus.Pendente;
                default:
                    throw new ArgumentOutOfRangeException(nameof(statusConsulta), statusConsulta, null);
            }
        }

        public int GetIdProdutoCartaoFretePadrao()
        {
            if (_idProdutoFrete != 0)
                return _idProdutoFrete;

            //                throw new CartaoAtsException("Serviço de cartão inicializado sem empresa! Não é possivel realizar a operação.");
            int? prod = null;
            if (_idEmpresa.HasValue)
            {
                prod = _empresaRepository.GetIdProdutoCartaoFretePadrao(_idEmpresa.Value);
                if (prod == null || prod.Value == 0)
                    throw new CartaoAtsException("Cartão padrão para operações de frete não configurado para empresa: " + _idEmpresa);
            }
            else
            {
                prod = _parametrosService.GetIdProdutoCartaoFrete();
                if (prod.Value == 0)
                    throw new CartaoAtsException("Cartão padrão para operações de frete não configurado nos parâmetros");
            }


            _idProdutoFrete = prod.Value;
            return _idProdutoFrete;
        }

        #endregion

        #region Integrar pessoa no micro serviço

        public IntegrarPessoaResponse IntegrarPessoaMicroServico(Estabelecimento estabelecimento)
        {
            var cidadeRepository = _cidadeRepository;
            var cidade = cidadeRepository.Get(estabelecimento.IdCidade);
            if (cidade.IBGE == null)
                throw new Exception($"IBGE não configurado para cidade {cidade.IdCidade}-{cidade.Nome}.");

            var request = new IntegrarPessoaRequest
            {
                Nome = estabelecimento.Descricao,
                NomeFantasia = estabelecimento.Descricao,
                CpfCnpj = estabelecimento.CNPJEstabelecimento,
                Info = new IntegrarPessoaInfoRequest
                {
                    Sexo = SistemaInfoConsts.Masculino,
                    Telefone = estabelecimento.Telefone
                },
                Endereco = new IntegrarPessoaEnderecoRequest
                {
                    Cidade = estabelecimento.Cidade.IBGE,
                    Logradouro = estabelecimento.Logradouro,
                    Numero = estabelecimento.Numero.ToString(),
                    Bairro = estabelecimento.Bairro,
                    Cep = estabelecimento.CEP.OnlyNumbers(),
                    Complemento = estabelecimento.Complemento
                },
                Flags = new IntegrarPessoaTipoFlagsRequest
                {
                    PontoCargaMoedeiro = true
                }
            };

            var result = _repository.IntegrarPessoa(request);

            if (result.Status != IntegrarPessoaResponseStatus.Sucesso)
                LogManager.GetCurrentClassLogger()
                    .Error($"Erro ao integrar estabelecimento id {estabelecimento.IdEstabelecimento}-{estabelecimento.CNPJEstabelecimento} como ponto de distribuição de cartão: " +
                        result.Mensagens.FirstOrDefault()?.Message);

            return result;
        }

        public IntegrarPessoaResponse IntegrarPessoaMicroServico(Filial filial)
        {
            var cidadeRepository = _cidadeRepository;
            var cidade = cidadeRepository.Get(filial.IdCidade);
            if (cidade.IBGE == null)
                throw new Exception($"IBGE não configurado para cidade {cidade.IdCidade}-{cidade.Nome}.");

            var request = new IntegrarPessoaRequest
            {
                Nome = filial.RazaoSocial,
                NomeFantasia = filial.NomeFantasia,
                CpfCnpj = filial.CNPJ,
                Endereco = new IntegrarPessoaEnderecoRequest
                {
                    Cidade = filial.Cidade.IBGE,
                    Logradouro = filial.Endereco,
                    Numero = filial.Numero.ToString(),
                    Bairro = filial.Bairro,
                    Cep = filial.CEP.OnlyNumbers(),
                    Complemento = filial.Complemento
                },
                Info = new IntegrarPessoaInfoRequest
                {
                    Sexo = SistemaInfoConsts.Masculino,
                    Telefone = filial.Telefone
                },
                Flags = new IntegrarPessoaTipoFlagsRequest
                {
                    PontoDistribuicaoCartao = true,
                    PontoCargaMoedeiro = true
                }
            };

            // Obrigado a possuir 2 nomes para integrar na plataforma de cartões
            if (!request.Nome.Contains(' '))
            {
                var emp = filial.Empresa ?? _empresaRepository.Get(filial.IdEmpresa);
                request.Nome = emp.NomeFantasia + " - " + filial.RazaoSocial;
            }

            var result = _repository.IntegrarPessoa(request);

            if (result.Status != IntegrarPessoaResponseStatus.Sucesso)
                LogManager.GetCurrentClassLogger()
                    .Error(
                        $"Erro ao integrar filial id {filial.IdFilial}-{filial.CNPJ} como ponto de distribuição de cartão: " +
                        result.Mensagens.FirstOrDefault()?.Message);

            return result;
        }

        public IntegrarPessoaResponse IntegrarPessoaMicroServico(Empresa empresa)
        {
            var cidadeRepository = _cidadeRepository;
            var cidade = cidadeRepository.Get(empresa.IdCidade);
            if (cidade.IBGE == null)
                throw new Exception($"IBGE não configurado para cidade {cidade.IdCidade}-{cidade.Nome}.");

            var request = new IntegrarPessoaRequest
            {
                Nome = empresa.RazaoSocial,
                NomeFantasia = empresa.NomeFantasia,
                CpfCnpj = empresa.CNPJ,
                Endereco = new IntegrarPessoaEnderecoRequest
                {
                    Cidade = empresa.Cidade.IBGE,
                    Logradouro = empresa.Endereco,
                    Numero = empresa.Numero.ToString(),
                    Bairro = empresa.Bairro,
                    Cep = empresa.CEP.OnlyNumbers(),
                    Complemento = empresa.Complemento
                },
                Info = new IntegrarPessoaInfoRequest
                {
                    Sexo = SistemaInfoConsts.Masculino,
                    Telefone = empresa.Telefone
                },
                Flags = new IntegrarPessoaTipoFlagsRequest
                {
                    PontoDistribuicaoCartao = true,
                    PontoCargaMoedeiro = true
                }
            };

            var result = _repository.IntegrarPessoa(request);

            if (result.Status != IntegrarPessoaResponseStatus.Sucesso)
                LogManager.GetCurrentClassLogger()
                    .Error(
                        $"Erro ao integrar empresa id {empresa.IdEmpresa}-{empresa.CNPJ} como ponto de distribuição de cartão: " +
                        result.Mensagens.FirstOrDefault()?.Message);

            return result;
        }

        #endregion

        #region Remessa de cartões

        public List<ConsultarPontoDistribuicaoResponse> GetPontoDistribuicao(List<string> cnpjList)
        {
            return _repository.GetPontoDistribuicao(cnpjList);
        }

        public PessoasCartoesSaldoResponse ConsultarListaPortadorCartaoComSaldo(CartoesVinculadosListaSaldoRequest request)
        {
            return _repository.ConsultarListaPortadorCartaoComSaldo(request);
        }
        
        public RelatorioPortadorCartaoResponse RelatorioPortadorCartaoPorListaDocumento(RelatorioPortadorCartaoRequest request)
        {
            return _repository.RelatorioPortadorCartaoPorListaDocumento(request);
        }

        public List<PessoasCartoesVinculadosResponse> CartoesVinculadosLista(List<string> listaCpf)
        {
            return _repository.CartoesVinculadosLista(listaCpf);
        }

        public EnvioRemessaResponse EnviarRemessaCartoes(EnvioRemessaRequest request)
        {
            return _repository.EnviarRemessaCartoes(request);
        }

        public EnvioRemessaResponse ValidarCartaoRemessa(ValidarCartaoRequest request)
        {
            return _repository.ValidarCartaoRemessa(request);
        }

        public EnvioRemessaResponse ValidarLoteCartaoRemessa(ValidarCartoesLoteRequest request)
        {
            return _repository.ValidarLoteCartaoRemessa(request);
        }

        public BaixaRemessaResponse ReceberRemessaCartoes(BaixaRemessaRequest request)
        {
            return _repository.ReceberRemessaCartoes(request);
        }

        public ConsultarRemessaResponse ConsultarCartoesLote(int loteId)
        {
            return _repository.ConsultarCartoesLote(loteId);
        }

        public ConsultarRemessaResponseDTO CarregarRemessaEmpresa(List<string> cnpjList, bool filtrarEmpresaOrigem, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim)
        {
            var response = _repository.CarregarRemessaEmpresa(cnpjList, filtrarEmpresaOrigem, dataInicio, dataFim);
            if (response?.ConsultarRemessaResponseList == null || response.ConsultarRemessaResponseList.Count == 0)
            {
                return new ConsultarRemessaResponseDTO
                {
                    Status = response?.Status ?? ConsultarRemessaEmpresaResponseStatus.Erro,
                    Mensagem = response == null ? "Erro ao realizar consulta." : response.Mensagem,
                    items = new List<ConsultarRemessaResponseItemDTO>(),
                    totalItems = 0
                };
            }

            var dados = response.ConsultarRemessaResponseList.AsQueryable();

            if (filters != null)
            {
                var queryFiltersWithData = filters.Where(f => f.CampoTipo == EFieldTipo.Date).ToList();
                if (queryFiltersWithData.Any())
                {
                    foreach (var item in queryFiltersWithData)
                        item.Campo += ".Value";

                    dados = dados.AplicarFiltrosDinamicos(queryFiltersWithData);
                    filters = filters.Where(f => f.CampoTipo != EFieldTipo.Date).ToList();
                }
            }

            var defaultDate = new DateTime(0001, 01, 01, 00, 00, 00);
            var consultarRemessaResponseDtos = new ConsultarRemessaResponseDTO
            {
                Status = response.Status,
                Mensagem = response.Mensagem,
                items = dados.ToList().Select(c => new ConsultarRemessaResponseItemDTO
                {
                    Lote = Convert.ToInt32(c.Lote),
                    DocumentoOrigem = c.DocumentoOrigem,
                    DocumentoDestino = c.DocumentoDestino,
                    DocumentoOrigemFormatado = c.DocumentoOrigem.FormatarCpfCnpj(),
                    DocumentoDestinoFormatado = c.DocumentoDestino.FormatarCpfCnpj(),
                    NomeOrigem = string.IsNullOrEmpty(c.NomeOrigem) ? @WebConfigurationManager.AppSettings["TITULO_SISTEMA"] : c.NomeOrigem,
                    NomeDestino = c.NomeDestino,
                    DataCadastro = c.DataCadastro?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAlteracao = c.DataAlteracao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataBaixa = c.DataBaixa != defaultDate ? c.DataBaixa?.ToString("dd/MM/yyyy HH:mm:ss") : null,
                    StatusBaixa = c.DataBaixa == defaultDate ? "Em trânsito" : "Baixado",
                    RemessaOriginalId = Convert.ToInt32(c.RemessaOriginalId) == 0 ? "" : Convert.ToInt32(c.RemessaOriginalId).ToString(),
                    Status = c.Status,
                    NomeUsuarioBaixa = c.NomeUsuarioBaixa,
                    NomeUsuarioCadastro = c.NomeUsuarioCadastro
                }).AsQueryable().AplicarFiltrosDinamicos(filters).ToList()
                    .Skip((page - 1) * take).Take(take).OrderByDescending(x => x.DataCadastro).ToList(),
                totalItems = dados.ToList().Count
            };

            consultarRemessaResponseDtos.items = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? consultarRemessaResponseDtos.items.OrderByDescending(x => x.DataCadastro).ToList()
                : consultarRemessaResponseDtos.items.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}").ToList();


            return consultarRemessaResponseDtos;
        }

        #endregion

        #region Integrar Empresa

        public IntegrarEmpresaResponse IntegrarEmpresa(Empresa empresa)
        {
            var cidadeEmpresa = _cidadeRepository.GetAll().FirstOrDefault(c => c.IdCidade == empresa.IdCidade);
            var paisEmpresa = _paisRepository.GetAll().FirstOrDefault(c => c.IdPais == empresa.IdPais);
            var estadoEmpresa = _estadoRepository.GetAll().FirstOrDefault(c => c.IdEstado == empresa.IdEstado);

            if (cidadeEmpresa == null || paisEmpresa == null || estadoEmpresa == null)
            {
                var failResult = new IntegrarEmpresaResponse
                {
                    Status = IntegrarEmpresaResponseStatus.Falha,
                    Mensagens = new List<ApiResponseValidation>()
                };
                failResult.Mensagens.Add(new ApiResponseValidation
                {
                    Type = ApiResponseValidationType.Error,
                    Message = "Cidade, estado ou pais não definido para empresa " + empresa.CNPJ
                });

                return failResult;
            }


            var request = new IntegrarEmpresaRequest
            {
                CpfCnpj = empresa.CNPJ,
                Endereco = new IntegrarPessoaEnderecoRequest
                {
                    Bairro = empresa.Bairro,
                    Cep = empresa.CEP,
                    Cidade = cidadeEmpresa.IBGE,
                    //CidadeIbge = cidadeEmpresa.IBGE.ToString(),
                    Complemento = empresa.Complemento,
                    Logradouro = empresa.Endereco,
                    Numero = empresa.Numero.ToString(),
                    //PaisSigla = paisEmpresa.Sigla,
                    //UfSigla = estadoEmpresa.Sigla
                },
                NomeFantasia = empresa.NomeFantasia,
                Nome = empresa.RazaoSocial,
                //Sexo = "F",
                Flags = new IntegrarPessoaTipoFlagsRequest
                {
                    PontoCargaMoedeiro = true,
                    PontoDistribuicaoCartao = true
                }
            };

            var result = _repository.EmpresaIntegrar(request);
            return result;
        }

        #endregion

        public SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponse IntegrarEmpresa(SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaRequest request)
        {
            return _repository.EmpresaIntegrar(request);
        }

        public ConsultaRotaResponseDto ConsultarCustoRota(ConsultaRotaRequest request)
        {
            return _pedagioRepository.CalcularRota(request);
        }

        public async Task<IntegrarParametroResponse> IntegrarParametrosPedagio(string loginAcessoMoveMais, 
            string senhaAcessoMoveMais, bool utilizaCredenciaisExtrattaMoveMais, ParametrosConectCarDto parametrosConectCar,
            ParametrosVeloeDto parametrosVeloe, ParametrosTagExtrattaDto parametrosTagExtratta, ParametrosTaggyEdenredDto parametrosTaggyEdenred, int? diasDataExpiracaoPedagioViaFacil)
        {
            var request = new IntegrarParametroRequest
            {
                MoveMais = new ParametrosMoveMaisDto
                {
                    LoginAcesso = loginAcessoMoveMais,
                    SenhaAcesso = senhaAcessoMoveMais,
                    UtilizaCredenciaisExtrattaMoveMais = utilizaCredenciaisExtrattaMoveMais
                },
                Veloe = parametrosVeloe,
                ConectCar = parametrosConectCar,
                TagExtratta = parametrosTagExtratta,
                TaggyEdenred = parametrosTaggyEdenred,
                ViaFacil = new ParametrosViaFacilDto
                {
                    DiasDataExpiracaoPedagio = diasDataExpiracaoPedagioViaFacil
                }
            };

            return await _pedagioRepository.IntegrarParametros(request);
        }

        public async Task<PedagioIntegrarEmpresaResponse> IntegrarEmpresaPedagio(string codigoViaFacil,
                    string loginViaFacil, string senhaViaFacil,bool utilizaCredenciaisExtrattaCompraViaFacil)
        {

            var requestEmpresa = new PedagioIntegrarEmpresaRequest
            {
                CodigoAcessoViaFacil = codigoViaFacil,
                LoginAcessoViaFacil = loginViaFacil,
                SenhaAcessoViaFacil = senhaViaFacil,
                UtilizaCredenciaisExtrattaCompraViaFacil = utilizaCredenciaisExtrattaCompraViaFacil
            };

            return await _pedagioRepository.IntegrarEmpresa(requestEmpresa);
        }

        public async Task<ConsultarParametroResponse> ConsultarParametrosPedagio()
        {
            return await _pedagioRepository.ConsultarParametrosPedagio();
        }

        public async Task<ConsultarEmpresaResponse> ConsultarEmpresaPedagio()
        {
            return await _pedagioRepository.ConsultarEmpresaPedagio();
        }

        public SolicitarCompraPedagioResponse SolicitarCompraPedagio(SolicitarCompraPedagioRequest request)
        {
            return _pedagioRepository.SolicitarCompraPedagio(request);
        }

        public ConsultaCompraPedagioResponse ConsultarCompraPedagio(int pageSize, int pageIndex, IEnumerable<object> orderBy, List<CustomFilter> customFilter, ConsultaCompraPedagioRequest request)
        {
            return _pedagioRepository.ConsultarCompraPedagio(pageSize, pageIndex, orderBy, customFilter, request);
        }

        public CancelarCompraPedagioResponse CancelarCompraPedagio(CancelarCompraPedagioRequest request)
        {
            return _pedagioRepository.CancelarCompraPedagio(request);
        }

        public ConsultaHistoricoRotaResponse ConsultaHistoricoRota(ConsultaHistoricoRotaRequest request)
        {
            return _pedagioRepository.ConsultaHistoricoRota(request);
        }

        public GetStatusPedagioResponse ValoresCompraStatusTipoDictionary()
        {
            return _pedagioRepository.ValoresCompraStatusTipoDictionary();
        }

        public List<PessoaContaBancariaResponse> GetContaBancaria(string documento)
        {
            return _repository.GetContaBancaria(documento);
        }

        public ConsultarExtratoResponseDTO ConsultarExtrato(ConsultarExtratoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var extrato = _repository.ConsultarExtrato(request, documentoUsuarioAudit, nomeUsuarioAudit);

            return Mapper.Map<ConsultarExtratoResponseDTO>(extrato);
        }

        public IntegracaoResult<GetExtratoBizResponse> ConsultarExtratoV2(ConsultaExtratoV2DTORequest request)
        {
            return _extrattaBizApiClient.GetExtrato(request.Identificador, request.Produto, request.DataInicio.ToString("s"), request.DataFim.ToString("s"), request.Page, request.Take);
        }

        public ConsultarPessoaDetalhadaResponse ConsultarPortadorDetalhado(string documento)
        {
            return _repository.ConsultarPortadorDetalhado(documento);
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartaoAtendimento(ConsultarSaldoCartaoRequest request)
        {
            return _repository.ConsultarSaldoCartaoAtendimento(request);
        }

        public FilteredResultOfMotivoBloqueioModel BuscarMotivosBloquearCartao(int take, int page, OrderFilters order, List<SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilter> filters)
        {
            return _repository.BuscarMotivosBloquearCartao(take, page, order, filters);
        }

        public BloquearCartaoResponse BloquearCartao(BloquearCartaoRequest request)
        {
            return _repository.BloquearCartao(request);
        }

        public BloquearCartaoResponse BloquearCartaoParametrizacaoEmpresa(BloquearCartaoRequest request)
        {
            return _repository.BloquearCartaoParametrizacaoEmpresa(request);
        }

        public DesbloquearCartaoResponse DesbloquearCartao(DesbloquearCartaoRequest request)
        {
            return _repository.DesbloquearCartao(request);
        }

        public ConsultarContasBancariasResponseDTO ContasBancarias(string documento, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var contasBancarias = _repository.ContasBancarias(documento, documentoUsuarioAudit, nomeUsuarioAudit);

            return Mapper.Map<ConsultarContasBancariasResponseDTO>(contasBancarias);
        }

        public InativarContaBancariaAtsResponseDTO InativarContaBancaria(InativarContaBancariaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var response = _repository.InativarContaBancaria(request, documentoUsuarioAudit, nomeUsuarioAudit);

            return Mapper.Map<InativarContaBancariaAtsResponseDTO>(response);
        }

        public OperacaoCartaoResponseDTO TransferirValorCartaoAtendimento(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem, string senha, IdentificadorCartao cartaoVinculadoOrigem, IdentificadorCartao cartaoVinculadoDestino, int? administradoraId = null)
        {
            int? empresaId = 0;
            var empresaService = _empresaRepository;

            int? layoutPadraoCartao = null;
            if (cnpjEmpresa.IsNullOrWhiteSpace())
                layoutPadraoCartao = _parametrosService.GetIdLayoutCartao();
            else
            {
                empresaId = empresaService.GetIdPorCnpj(cnpjEmpresa);
                if (empresaId == null)
                    throw new CartaoAtsException("Empresa não localizada com CNPJ " + cnpjEmpresa);
                layoutPadraoCartao = empresaService.GetLayoutCartaoPadrao(empresaId.Value);
            }

            if (layoutPadraoCartao == null)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = "Layout de cartão não localizado para realizar operação de transferência de valores"
                };

            var validarSenhaCartao = ValidarSenhaCartao(cartaoVinculadoOrigem.Identificador ?? 0, cartaoVinculadoOrigem.Produto ?? 0, senha);

            if (!validarSenhaCartao.Sucesso)
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = validarSenhaCartao.Mensagem
                };

            var layoutHistorico = _layoutCartaoService.GetIdItem(layoutPadraoCartao.Value,
                ETipoProcessamentoCartao.TransferenciaSaldo.ToString());

            var transacaoCartao = AdicionarTransacaoViagemAvulso(layoutHistorico, documentoOrigem, documentoDestino,
                valor, ETipoProcessamentoCartao.TransferenciaSaldo, origem, null);

            var senhaValidada = ValidarSenhaCartao(cartaoVinculadoOrigem.Identificador ?? 0, cartaoVinculadoOrigem.Produto ?? 0, senha);

            if (!senhaValidada.Sucesso)
            {
                return new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = senhaValidada.Mensagem,
                    Protocolo = 0
                };
            }

            var transferenciaRequet = new TransferirValorCartaoRequest();

            transferenciaRequet.PermitirTransacaoPendente = origem == EOrigemTransacaoCartao.Atendimento;
            transferenciaRequet.Valor = valor;

            transferenciaRequet.CartaoOrigem = new IdentificadorCartao();
            transferenciaRequet.CartaoOrigem.Identificador = cartaoVinculadoOrigem.Identificador;
            transferenciaRequet.CartaoOrigem.Produto = cartaoVinculadoOrigem.Produto;

            transferenciaRequet.CartaoDestino = new IdentificadorCartao();
            transferenciaRequet.CartaoDestino.Identificador = cartaoVinculadoDestino.Identificador;
            transferenciaRequet.CartaoDestino.Produto = cartaoVinculadoDestino.Produto;

            transferenciaRequet.Empresa = StringExtension.OnlyNumbers(cnpjEmpresa);
            transferenciaRequet.Historico = layoutHistorico;
            transferenciaRequet.ProtocoloRequisicao = transacaoCartao.IdTransacaoCartao;

            transferenciaRequet.InformacoesAdicionais = new Dictionary<string, string>();
            transferenciaRequet.InformacoesAdicionais.Add("Tipo", origem.GetTextoIntegracaoPlataformaCartoes()); // Automática/Aplicativo
            transferenciaRequet.InformacoesAdicionais.Add("Documento Favorecido", documentoDestino.FormatarCpfCnpj()); // Automática/Aplicativo

            var resultado = _repository.TransferirValorCartao(transferenciaRequet);

            transacaoCartao.MensagemProcessamentoWs = resultado.Mensagem;

            switch (resultado.Status)
            {
                // Pendente
                case TransferirValorCartaoResponseStatus.Pendente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Pendente;
                    break;

                // Pendente
                case TransferirValorCartaoResponseStatus.NaoProcessado:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Aberto;
                    break;

                // Sucesso
                case TransferirValorCartaoResponseStatus.Sucesso:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Baixado;
                    transacaoCartao.DataConfirmacaoMeioHomologado = DateTime.Now;
                    break;

                // Erro e Protocolo Existente
                case TransferirValorCartaoResponseStatus.Erro:
                case TransferirValorCartaoResponseStatus.ProtocoloExistente:
                    transacaoCartao.StatusPagamento = EStatusPagamentoCartao.Erro;
                    break;
            }

            transacaoCartao.NumeroProtocoloWs = resultado.ProtocoloProcessamento ?? 0;

            AtualizarTransacaoViagem(transacaoCartao);

            if (resultado.Status == TransferirValorCartaoResponseStatus.Sucesso)
                EnviarPushTransferencia(empresaId, transacaoCartao.CnpjCpfOrigem, transacaoCartao.CnpjCpfDestino,
                    transacaoCartao.ValorMovimentado);

            return new OperacaoCartaoResponseDTO
            {
                Status = GetStatusSucesso(resultado.Status),
                Mensagem = resultado.Mensagem,
                Protocolo = resultado.ProtocoloProcessamento
            };
        }

        public IntegrarPessoaResponse IntegrarPessoaMicroServico(IntegrarPessoaRequestDTO requestDto)
        {
            var request = Mapper.Map<IntegrarPessoaRequest>(requestDto);

            return _repository.IntegrarPessoa(request);
        }

        public ConsultarProtocoloResponse ConsultarProcotolo(ConsultarProtocoloRequest request)
        {
            return _repository.ConsultarProcotolo(request);
        }

        public ConsultarPrimeiraTransacaoResponse ConsultarPrimeiraTransacao(int identificador, int produto)
        {
            return _repository.ConsultarPrimeiraTransacao(identificador, produto);
        }

        public ConsultarProtocoloResponse ConsultarProcotolo(int idTransacaoCartao)
        {
            return _repository.ConsultarProcotolo(new ConsultarProtocoloRequest { ProtocoloRequisicao = idTransacaoCartao });
        }

        public EmpresaTokenMicroServicoDto GetOrGenerateTokenEmpresa(string cnpjEmpresa, string appName, int grupoContabilizacao)
        {
            var response = _repository.GetOrGenerateTokenEmpresa(cnpjEmpresa, appName, grupoContabilizacao);

            var empresa = Mapper.Map<EmpresaTokenMicroServicoDto>(response);
            empresa.IdEmpresa = _empresaRepository.GetIdPorCnpj(cnpjEmpresa);

            return empresa;
        }

        public byte[] GerarRelatorioExtratoGrid(ConsultarExtratoRequest request, RelatorioGridExtratoDTO requestGrid)
        {
            var response = ConsultarExtrato(request, null, null);
            var csvBuilder = new CsvBuilderHelper(";", false);

            var listaExtrato = response.Objeto.Detalhes.Select(x => new
            {
                ValorFormatado = x.Valor.FormatMoney(),
                Descricao = x.Historico.DefaultIfNullOrWhiteSpace(x.DescricaoProcessadora),
                x.InformacoesAdicionais,
                x.Metadados,
                x.ProtocoloProcessamento,
                x.ProtocoloRequisicao,
                DataTransacao = x.Data.FormatDateTimeBr(),
                Informacoes = string.Join(" / ",
                    x.InformacoesAdicionais.Select(i => i.Key + ": " + i.Value)),
                SaldoFinal = x.SaldoFinal.FormatMoney(),
                Tipo = x.Tipo == "D" ? "Débito" : "Crédito",
                Valor = x.Valor.FormatMoney()
            }).AsQueryable().AplicarFiltrosDinamicos(requestGrid.filters).AplicarOrderByDinamicos(requestGrid.Order).ToList();

            // Header
            if (requestGrid.DocumentoPortador != null && requestGrid.NomePortador != null)
                csvBuilder.SetTopHeader($"Documento: {requestGrid.DocumentoPortador} / {requestGrid.NomePortador} / Cartão: {requestGrid.Identificador}");
            
            //Segmento
            foreach (var item in listaExtrato)
            {
                csvBuilder.AddRow();
                csvBuilder["DATA DA TRANSAÇÂO"] = item.DataTransacao;
                csvBuilder["DESCRIÇÃO"] = item.Descricao;
                csvBuilder["TIPO"] = item.Tipo;
                csvBuilder["VALOR"] = item.Valor;
                csvBuilder["SALDO"] = item.SaldoFinal;
                csvBuilder["INFORMAÇÔES"] = item.Informacoes;
            }

            return csvBuilder.ExportToBytes();
        }

        public ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpjEmpresa)
        {
            return _repository.ConsultarSaldoEmpresa(cnpjEmpresa);
        }

        public CancelarCartaoRemessaResponse CancelarCartaoRemessa(CancelarCartaoRemessaRequest cancelarCartaoRemessaRequest)
        {
            return _repository.CancelarCartaoRemessa(cancelarCartaoRemessaRequest);
        }
        
         public PessoaCartaoResponse GetCartaoFretePorVinculoDaEmpresa(string documento)
         {
             return _repository.GetCartaoFretePorVinculoDaEmpresa(documento);
         }

         public ConsultarInformacoesPorDadosContaResponse ConsultarInformacoesPorDadosConta(int conta, int produto, int emissor, int sucursal, int grupoAfinidade)
         {
             return _repository.ConsultarInformacoesPorDadosConta(conta, produto, emissor, sucursal, grupoAfinidade);
         }
         
         public CadastrarTransportadorViaFacilResponse CadastrarTransportadorRntrcViaFacil(CadastrarTransportadorViaFacilRequest request)
         {
             return _pedagioRepository.CadastrarTransportadorRntrcViaFacil(request);
         }
         
         public EditarTransportadorViaFacilResponse EditarTransportadorRntrcViaFacil(EditarTransportadorViaFacilRequest request)
         {
             return _pedagioRepository.EditarTransportadorRntrcViaFacil(request);
         }
         
         public ConsultaTransportadorViaFacilResponse ConsultarTransportadorRntrcViaFacil(string cnpjEmbarcador)
         {
             return _pedagioRepository.ConsultarTransportadorRntrcViaFacil(cnpjEmbarcador);
         }
         
         public DeletarTransportadorViaFacilResponse DeletarTransportadorRntrcViaFacil(DeletarTransportadorViaFacilRequest request)
         {
             return _pedagioRepository.DeletarTransportadorRntrcViaFacil(request);
         }
         
         public CadastrarEmbarcadorViaFacilSTPResponse CadastrarEmbarcadorViaFacilSTP(EmbarcadorEmpresa request)
         {
             return _pedagioRepository.CadastrarEmbarcadorViaFacilSTP(request);
         }
    }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Dapper
{
    public interface IEmpresaDapper : IRepositoryDapper<Empresa>
    {
        bool AtualizaUltimaExecucao(int idEmpresa);
        List<Empresa> GetCompanies();
        List<string> GetEmailsFinanceiro(int idempresa);
    }
}
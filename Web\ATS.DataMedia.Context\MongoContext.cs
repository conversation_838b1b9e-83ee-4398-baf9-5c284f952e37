﻿using ATS.MongoDB.Context.Entities;
using ATS.MongoDB.Context.Entities.Base;
using ATS.MongoDB.Enum;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Threading.Tasks;

namespace ATS.MongoDB.Context
{
    public class MongoContext
    {
        private readonly string _connStr;
        private readonly string _dbName;

        public MongoContext()
        {
            _connStr = ConfigurationManager.AppSettings["MediaContextConnStr"];
            _dbName = ConfigurationManager.AppSettings["MediaDataBaseName"];
        }

        public ObjectId Add(int type, string base64Data, string fileName, string mimeType)
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<Media>("Media");

            var novoRegistro = new Media()
            {
                Data = base64Data,
                FileName = fileName,
                MimeType = mimeType,
                Type = (EMediaType)type
            };

            mediaTbl.InsertOne(novoRegistro);

            return novoRegistro._id;
        }

        public ObjectId AddCache<TType>(TType model, string tableName)
            where TType : CacheEntity
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<TType>(tableName);

            mediaTbl.InsertOne(model);

            return model._id;
        }

        public long CountFromCache<TType>(string tableName, string requestID)
            where TType : CacheEntity
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<TType>(tableName);

#pragma warning disable 618
            return mediaTbl.Count(x => x.RequestID == requestID);
#pragma warning restore 618

        }

        public List<TType> GetFromCache<TType>(string tableName, string requestID, int skip = 1, int take = 10, Task proccess = null)
           where TType : CacheEntity
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<TType>(tableName);
            
            return mediaTbl.Find(x => x.RequestID == requestID)
                    .Skip((skip - 1) * take)
                    .Limit(take)
                    .ToList();

        }

        public void DeleteCacheFromTable<TType>(string table, int time) 
            where TType : CacheEntity
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<TType>(table);

            mediaTbl.DeleteMany(x => (x.DateTimeIntegration <= DateTime.UtcNow.AddHours(time *-1)) || x.DateTimeIntegration == null);
        }

        public void DeleteByToken(string _id)
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<Media>("Media");
            
            var record = mediaTbl.Find(x => x._id == new ObjectId(_id)).FirstOrDefault();
            if (record == null)
                throw new Exception("Não foi possível encontrar o anexo á ser excluído!");

            mediaTbl.DeleteOne(x => x._id == new ObjectId(_id));
        }

        public Media GetMedia(string _id)
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<Media>("Media");

            return mediaTbl.Find(x => x._id == new ObjectId(_id)).FirstOrDefault();
        }
        
        public List<Media> GetListMedia(IEnumerable<ObjectId> listId)
        {
            var db = getConnection();
            var mediaTbl = db.GetCollection<Media>("Media");

            var filtro = Builders<Media>.Filter.In("_id", listId);

            return mediaTbl.Find(filtro).ToList();
        }

        private IMongoDatabase getConnection()
        {
            MongoClient client = new MongoClient(_connStr);
            IMongoDatabase db = client.GetDatabase(_dbName);
            return db;
        }
    }
}

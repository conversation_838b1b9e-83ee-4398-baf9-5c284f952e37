﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using NLog;
using Sistema.Framework.Util.Enumerate;

namespace ATS.Domain.Service
{
    public class LimiteTransacaoPortadorService : BaseService<ILimiteTransacaoPortadorRepository>, ILimiteTransacaoPortadorService
    {
        private readonly ILogger _logger;
        private readonly IUserIdentity _userIdentity;

        public LimiteTransacaoPortadorService(ILimiteTransacaoPortadorRepository repository, IUserIdentity sessionUser, IUserIdentity userIdentity) : base(repository, sessionUser)
        {
            _userIdentity = userIdentity;
            _logger = LogManager.GetCurrentClassLogger();
        }

        public BusinessResult LimitarValor(string documento, ETipoLimiteTransacaoPortador tipo, decimal valor)
        {
            try
            {
                var entity = Repository.Where(c => c.Documento == documento && c.Tipo == (short) tipo).FirstOrDefault();

                if (entity != null)
                {
                    entity.Valor = valor;
                    entity.DataAtualizacao = DateTime.Now;
                    if (_userIdentity.IdUsuario != 0) entity.IdUsuarioAtualizacao = _userIdentity.IdUsuario;

                    Repository.Update(entity);

                    return BusinessResult.Valid();
                }

                entity = new LimiteTransacaoPortador
                {
                    Documento = documento,
                    Tipo = (short) tipo,
                    Valor = valor,
                    DataCadastro = DateTime.Now,
                    DataAtualizacao = DateTime.Now
                };

                if (_userIdentity.IdUsuario != 0)
                {
                    entity.IdUsuarioCriacao = _userIdentity.IdUsuario;
                    entity.IdUsuarioAtualizacao = _userIdentity.IdUsuario;
                }

                Repository.Add(entity);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult.Error(message);
            }
        }

        public decimal GetLimite(string documento, ETipoLimiteTransacaoPortador tipo)
        {
            return Repository.Where(c => c.Documento == documento && c.Tipo == (short) tipo)
                .Select(c => c.Valor).FirstOrDefault();
        }

        public IList<PortadorLimitesValor> GetLimites(string documento)
        {
            var limites = new List<PortadorLimitesValor>();

            foreach (ETipoLimiteTransacaoPortador tipo in System.Enum.GetValues(typeof(ETipoLimiteTransacaoPortador)))
            {
                limites.Add(new PortadorLimitesValor
                {
                    Id = (int) tipo,
                    Descricao = EnumHelper.GetDescriptionToString(tipo),
                    Valor = GetLimite(documento, tipo)
                });
            }

            return limites;
        }
    }
}
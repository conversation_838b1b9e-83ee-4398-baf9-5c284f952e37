﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Models.PagamentoFrete;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IPagamentoFreteApp
    {
        void AtualizarPagamentoSemChave(bool liberarPagamento, string token, int? estabId, string observacao, int idUsuarioLibSemChave);
        byte[] GerarRelatorioGrid(DateTime dataInicial, DateTime dataFinal, string uf, double? a, double? b, double? c, string tipoArquivo, string logo, int idEmpresa);
        byte[] ConsultarPagamentosEstabelecimentoReport(DateTime? dataInicio, DateTime? dataFim,
            int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo,
            string logo);
        byte[] GerarRelatorioDetalhesProvisaoAberto(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa);
        byte[] GerarRelatorioDetalhesProvisaoBaixado(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa);
        PagamentoFreteModel ConsultarPorToken(string token, string cpfCnpjUsuario, string nomeUsuario, List<int> idsEstabelecimentosBaseUsuario = null, int? idEstabelecimento = null);
        ValidationResult EfetuarPagamento(PagamentoFreteModel pagamentoFreteModel, Usuario usuarioLogado, bool integracaoAts, int administradoraPlataforma,
            List<int> idsEstabelecimentosUsuario = null);
        PagamentoFreteEventoModel CalcularValoresProtocolo(string token, decimal? pesoChegada, int? numeroSacas);
        PagamentoFreteEventoModel CalcularValoresViagem(string token, bool habilitaPagamentoCartao, decimal? pesoChegada, int? numeroSacas, EUnidadeMedida unidadeInformada, bool? quebraAbono);
        object ConsultarTotalPagamentosCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double A, double B, double C, int page_, int take_, int idEmpresa);
        byte[] GerarRecibo(string token, int? idViagemEvento, string usuario);
        List<PagamentoFreteValorAdicionalModel> ConsultarOutrosDescontos(string token);
        List<PagamentoFreteValorAdicionalModel> ConsultarOutrosAcrescimos(string token);
        string EnviarSMSValidacao(string token, string cpf, string cnpj, string celular, bool isPessoaJuridica);
        void RemoverAnexo(int idViagemDocumento);
        void AbonarViagemEvento(string tokenEvento, string tokenDocumento);
        ValidationResult AlterarSaldo(decimal saldo, decimal pesoChegada, decimal quebraMercadoria, decimal difFreteMotorista, string token);
        ValidationResult AnalisarAbonoCartaFrete(int idViagemEventoSaldo, int idProtocolo, int? idMotivoRejeicao, string descricaoMotivo, IUserIdentity usuarioLogado, EStatusAnaliseAbono statusAnalise);
        void CancelarAbono(string token);
        CheckTokenModel CheckToken(string token);
        List<PagamentoFreteAnexoModel> ConsultarAnexos(string token, int? idUsuarioLogado = null);
        PagamentoFreteEventoModel ConsultarEvento(string token);
        object ConsultarGraficosSumPorEstabelecimento(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int idEmpresa);
        object ConsultarGridDetalhesAberto(DateTime dataInicio_, DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);
        object ConsultarGridDetalhesBaixado(DateTime dataInicio_, DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);
        object ConsultarMotivoAbono(int idViagemEvento);
        object ConsultarPagamentosEstabelecimentoGrid(DateTime dataInicio_, DateTime dataFim_, int idEstabelecimentoBase, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ValidationResult CreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, IUserIdentity usuarioLogado, decimal? valor);
        ValidationResult DeletarEventoAbono(int idViagem);
        List<ViagemEventoByCiotModel> GetByCiot(string numeroCiot);
        object GetTotaisPagamentosPorTipo(DateTime dataInicio, DateTime dataFinal, int? UF_, EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimentoBase, int? idEmpresa);
        ValidationResult NaoCreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, int? idMotivo = null, string descricao = null, int? idUsuario = null);
        bool RemoverAnexo(string token);
        ValidationResult SetMotivoRejeicaoAbono(int idViagemEvento, int? idMotivo, string detalhamentoMotivo, int? idUsuario);
        void SolicitarAbono(string token, int? idMotivo, string detalhamento, int idUsuario);
        ValidationResult ValidarChavePagamento(int empresaId, string documento, string chave, string tokenPagamento);
        bool VerificarCartaoVinculado(int idEmpresa, string cpfCnpj);
        void VincularAnexo(string tokenMidia, int idViagemDocumento);
        BusinessResult<FaturamentoGridModelResponse> RelatorioFaturamento(DateTime dataInicial,DateTime datafinal,int? empresaFiltro, int take, int page, OrderFilters order, List<QueryFilters> filters);
        byte[] GerarRelatorioFaturamento(DateTime dataInicial,DateTime datafinal,int? empresaFiltro, int take, int page, OrderFilters order, List<QueryFilters> filters,string extensao);
    }
}
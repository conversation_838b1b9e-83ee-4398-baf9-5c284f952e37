using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class BloqueioFinanceiroTipoMap : EntityTypeConfiguration<BloqueioFinanceiroTipo>
    {
        public BloqueioFinanceiroTipoMap()
        {
            ToTable("BLOQUEIO_FINANCEIRO_TIPO");

            HasKey(t => t.IdBloqueioFinanceiroTipo);
            Property(t => t.IdBloqueioFinanceiroTipo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            
            Property(t => t.IdBloqueioFinanceiroTipo).IsRequired();
        }
    }
}
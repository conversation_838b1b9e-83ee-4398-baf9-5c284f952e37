﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping

{
    public class PlanoEmpresaMap : EntityTypeConfiguration<PlanoEmpresa>
    {
        public PlanoEmpresaMap()
        {
            ToTable("PLANO_EMPRESA");

            HasKey(x => x.IdPlanoEmpresa);

            Property(t => t.IdPlanoEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(x => x.Empresa)
               .WithMany(x => x.PlanosEmpresaCollection)
               .HasForeignKey(x => x.IdEmpresa);

            HasRequired(x => x.Plano)
                .WithMany(x => x.PlanosEmpresa)
                .HasForeignKey(x => x.IdPlano);

            Property(x => x.DataCadastro)
                .HasColumnName("datacadastro")
                .HasColumnType("datetime2");

            Property(x => x.DataAtualizacao)
                .HasColumnName("dataatualizacao")
                .IsOptional()
                .HasColumnType("datetime2");

            HasRequired(x => x.Usuario)
                .WithMany(x => x.PlanosEmpresa)
                .HasForeignKey(x => x.IdUsuarioCadastro);

            HasRequired(x => x.Usuario)
                .WithMany(x => x.PlanosEmpresa)
                .HasForeignKey(x => x.IdUsuarioAtualizacao);

            Property(t => t.Ativo)
                .HasColumnName("ativo")
                .IsRequired()
                .HasColumnType("bit");
        }
    }
}

﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEstabelecimentoBaseDocumentoService : IService<EstabelecimentoBaseDocumento>
    {
        IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null);
        
        string Upload(string dataBase64, string fileName);
        
        object Download(string token);
        
        ValidationResult AddOrUpdateDocumentos(ICollection<EstabelecimentoBaseDocumento> estabelecimentoDocumentos, int idEstabelecimento, int administradoraPlataforma);
        List<EstabelecimentoBaseDocumento> GetDocumentos(int idEstabelecimentoBase);
    }
}

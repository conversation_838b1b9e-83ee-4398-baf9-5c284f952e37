﻿using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Models.CidadeModels;

namespace ATS.Domain.Interface.Service
{
    public interface ICidadeService
    {
        Cidade Get(int id);
        Cidade GetWithAllChilds(int id);
        ValidationResult Add(Cidade entity);
        ValidationResult Update(Cidade entity);
        IQueryable<CidadeGrid> Consultar(string nome);
        Cidade GetCidadeFromIBGE(int nIBGE);
        List<Cidade> GetTodos();
        IQueryable<Cidade> GetCidades(int idEstado);
        IQueryable<Cidade> GetCidadesAtualizadas(string uf, DateTime dataBase);
        IQueryable<Cidade> All();
        List<Cidade> WhereNomeLike(string nome);
        ValidationResult UpdateLocalizao(int idCidade, decimal latitude, decimal longitude);
        LocalizacaoModel GetLocalizacao(int idCidade, EOrigemConsumoServicoExterno origemConsumoServicoExterno);
        int? GetIdCidadePorNome(int idEstado, string nome);
        ValidationResult Inativar(int idCidade);
        ValidationResult Reativar(int idCidade);
        CidadeDetalhesResponse GetDetalhes(int idcidade);

        object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);

        int? GetIdCidade(int nIBGE);

        IQueryable<Cidade> GetQueryByIBGE(int ibge);

        Cidade WhereNomeIs(string nome);
        Cidade VerificaString(string nome);

        List<Cidade> ConsultarPaginadas(int? IBGEEstado, int? ibge, DateTime? dataBase, int? take, int? skip);
        Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude);
        int GetIdPorIBGE(int ibge);
        Cidade getByNameState(string nome, string state);
        List<int> GetIbgeCidade(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel,bool errorCidadeNotFound = false);
        int GetIbgeCidade(CidadeIbgeRequestModel cidadesIbgeRequestModel);
        bool ValidarIbgeCadastrado(int ibge);
    }
}
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Mail;
using System.Text;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using Autofac;

namespace ATS.WS.Services.ViagemServices
{
    public static class EmailViagem
    {
        public static ValidationResult EnviarEmailTokensBloqueados(Empresa empresa, List<ViagemEvento> eventosBloqueados)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var emailService = scope.Resolve<IEmailService>();
                var validationResult = new ValidationResult();

                try
                {
                    var aplicativo = "ATS";
                    var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                    var emailModel = new EmailModel { Assunto = $"Eventos de viagem bloqueados - {aplicativo}" };

                    using (var ms = new StreamReader(caminhoAplicacao + (@"\Content\Email\tokens-bloqueados.html")))
                    {
                        var logoHeader = empresa.Logo != null
                            ? new LinkedResource(new MemoryStream(empresa.Logo))
                            {
                                ContentId = Guid.NewGuid().ToString()
                            }
                            : new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                            {
                                ContentId = Guid.NewGuid().ToString()
                            };

                        var logoAts = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                        var bannerReprovacao =
                            new LinkedResource(caminhoAplicacao + @"\Content\Image\header-tokens-bloqueados.png")
                            {
                                ContentId = Guid.NewGuid().ToString()
                            };
                        var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                        var html = ms.ReadToEnd();
                        html = html.Replace("{0}", logoHeader.ContentId);
                        html = html.Replace("{1}", bannerReprovacao.ContentId);
                        html = html.Replace("{2}", facebook.ContentId);
                        html = html.Replace("{logoATS}", logoAts.ContentId);
                        html = html.Replace("{EMPRESA}", empresa.RazaoSocial);
                        html = html.Replace("{DATA}", DateTime.Now.ToString("dd/MM/yyyy"));
                        html = html.Replace("{DIAS}", empresa.DiasParaBloquearPagto.ToString());
                        html = html.Replace("{TABELA}", MontaTabelaEventosBloqueados(eventosBloqueados));
                        html = html.Replace("{NomeAplicativo}", aplicativo);

                        var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                        view.LinkedResources.Add(logoHeader);
                        view.LinkedResources.Add(bannerReprovacao);
                        view.LinkedResources.Add(facebook);
                        view.LinkedResources.Add(logoAts);
                        emailModel.AlternateView = view;
                    }

                    emailModel.Destinatarios = new List<string> { empresa.EmailCartaFrete };
                    emailModel.NomeVisualizacao = aplicativo;
                    emailModel.Prioridade = MailPriority.High;

                    emailService.EnviarEmail(emailModel);

                    return validationResult;
                }
                catch (Exception)
                {
                    return validationResult.Add(
                        $"Não foi possível enviar o e-mail dos eventos da empresa {empresa.RazaoSocial}. Verifique as configurações de e-mail");
                }
            }
        }
        
        private static string MontaTabelaEventosBloqueados(List<ViagemEvento> eventos)
        {
            var tabela = new StringBuilder();
            tabela.AppendLine(
                "<thead><tr style=\"color: #fff; background: #000;\"><th style=\"border: 1px solid black; border-collapse: collapse;\">Token</th><th style=\"border: 1px solid black; border-collapse: collapse;\">Tipo</th><th style=\"border: 1px solid black; border-collapse: collapse;\">Data Emissão</th><th style=\"border: 1px solid black; border-collapse: collapse;\">Valor</th></tr></thead><tbody>");

            for (var i = 0; i < eventos.Count; i++)
            {
                var linhaCinza = i % 2 != 0 ? " style=\"background: #ccc;\"" : string.Empty;
                tabela.Append(
                    $"<tr{linhaCinza}><td style=\"border: 1px solid black; border-collapse: collapse;\">{eventos[i].Token}</td><td style=\"border: 1px solid black; border-collapse: collapse;\">{eventos[i].TipoEventoViagem.ToString()}</td><td style=\"border: 1px solid black; border-collapse: collapse;\">{eventos[i].DataEmissaoViagem?.ToString("dd/MM/yyyy HH:mm")}</td><td style=\"border: 1px solid black; border-collapse: collapse;\">{eventos[i].ValorPagamento}</td></tr>");
            }

            return "<table style=\"border: 1px solid black; border-collapse: collapse;\">" + tabela +
                   "</tbody></table>";
        }
    }
}
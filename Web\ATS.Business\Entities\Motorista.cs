using System;
using System.Collections;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Motorista
    {
        /// <summary>
        /// Código do motorista
        /// </summary>
        public int IdMotorista { get; set; }

        /// <summary>
        /// Código do empresa
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Nome
        /// </summary>
        public string Nome { get; set; }

        /// <summary>
        /// Número do RG
        /// </summary>
        public string RG { get; set; }

        /// <summary>
        /// Orgão Expedidor do RG
        /// </summary>
        public string RGOrgaoExpedidor { get; set; }

        /// <summary>
        /// Referencia 1
        /// </summary>
        public string Referencia1 { get; set; }

        /// <summary>
        /// Referencia 2
        /// </summary>
        public string Referencia2 { get; set; }

        /// <summary>
        /// Valida da CNH do motorista
        /// </summary>
        public DateTime? ValidadeCNH { get; set; }

        /// <summary>
        /// Número do CPF
        /// </summary>
        public string CPF { get; set; }

        /// <summary>
        /// Sexo
        /// </summary>
        public string Sexo { get; set; }

        /// <summary>
        /// Número da CNH
        /// </summary>
        public string CNH { get; set; }

        /// <summary>
        /// Categoria da CNH
        /// </summary>
        public string CNHCategoria { get; set; }

        /// <summary>
        /// Número do celular
        /// </summary>
        public string Celular { get; set; }

        /// <summary>
        /// Tipo de contrato
        /// </summary>
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Frota;

        /// <summary>
        /// Endereço de e-mail
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Foto
        /// </summary>
        [SkipTracking]
        public byte[] Foto { get; set; }

        /// <summary>
        /// Indica se o registro esta ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Número do CEP
        /// </summary>
        public string CEP { get; set; }

        /// <summary>
        /// Endereço
        /// </summary>
        public string Endereco { get; set; }

        /// <summary>
        /// Complemento do endereço
        /// </summary>
        public string Complemento { get; set; }

        /// <summary>
        /// Número
        /// </summary>
        public string Numero { get; set; }

        /// <summary>
        /// Nome do bairro
        /// </summary>
        public string Bairro { get; set; }

        /// <summary>
        /// Código do País
        /// </summary>
        public int IdPais { get; set; }

        /// <summary>
        /// Código do Estado
        /// </summary>
        public int IdEstado { get; set; }

        /// <summary>
        /// Código da Cidade
        /// </summary>
        public int IdCidade { get; set; }
        
        /// <summary>
        /// Usuario de cadastro
        /// </summary>
        public int? IdUsuarioCadastro  { get; set; }

        /// <summary>
        /// Data e hora em que o registro foi atualizado pela ultima vez
        /// </summary>
        [SkipTracking]
        public DateTime? DataHoraUltimaAtualizacao { get; set; }

        public DateTime? AvisoValidadeCNH { get; set; }
        public DateTime? DataContatado { get; set; }
        
        public string NomeMae { get; set; }
        
        public string NomePai { get; set; }
        
        public DateTime? DataNascimento { get; set; }

	    /// <summary>
        /// Formulário CNH
        /// </summary>
        public string FormularioCnh { get; set; } // <-- Campo novo para adicionar a informação do número do formulário da cnh do motorista. Campo obrigatório. Dia 16-11-2018.

        #region Referências

        [ForeignKey("IdEmpresa")]
        public virtual Empresa Empresa { get; set; }

        [ForeignKey("IdPais")]
        public virtual Pais Pais { get; set; }

        [ForeignKey("IdEstado")]
        public virtual Estado Estado { get; set; }

        [ForeignKey("IdCidade")]
        public virtual Cidade Cidade { get; set; }

        [ForeignKey("IdUsuarioCadastro")]
        public virtual Usuario UsuarioCadastro { get; set; } 

        /// <summary>
        /// Veículos
        /// </summary>
        public virtual ICollection<Veiculo> Veiculos { get; set; }

        /// <summary>
        /// CheckIns realizados pelo motorista
        /// </summary>
        public virtual ICollection<CheckIn> CheckIns { get; set; }

        public virtual ICollection<CheckinResumo> CheckinResumos { get; set; }
        
        #endregion

        #region Tabelas Filhas

        /// <summary>
        /// Dispositivos moveis vinculados ao motorista
        /// </summary>
        public virtual ICollection<MotoristaMovel> Moveis { get; set; }

        public virtual ICollection<ConjuntoEmpresa> ConjuntosEmpresa { get; set; }
        #endregion

    }
}

using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Request.Cartoes
{
    public class ConsultarSaldoCartaoAtsRequest : RequestBase
    {
        public int? Identificador { get; set; }
        public int? Produto { get; set; }
        public string Documento
        {
            get { return _documento; }
            set {_documento = value.OnlyNumbers();}
        }
        private string _documento { get; set; }
        
        public ValidationResult ValidaRequest()
        {
            ValidationResult validationResult = ValidaRequestBase();
            
            if (Identificador == null && Produto == null && string.IsNullOrWhiteSpace(Documento))
                validationResult.Add("É obrigatório o envio dos campos do Cartao (Identificador e Produto) ou Documento");
            else
            {
                if (!string.IsNullOrWhiteSpace(Documento) && (Documento.Length != 11 && Documento.Length != 14))
                    validationResult.Add("O campo Documento deve conter 11 ou 14 dígitos");
                if (string.IsNullOrWhiteSpace(Documento) && (!Identificador.HasValue || !Produto.HasValue))
                    validationResult.Add("Os campos do Cartao devem ser informados: Identificador, Produto");
            }

            return validationResult;
        }
    }
}
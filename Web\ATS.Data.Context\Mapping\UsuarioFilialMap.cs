﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioFilialMap : EntityTypeConfiguration<UsuarioFilial>
    {
        public UsuarioFilialMap()
        {
            ToTable("USUARIO_FILIAL");

            HasKey(t => new {t.IdUsuario, t.IdFilial});

            Property(t => t.IdFilial)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            HasRequired(x => x.Filial)
                .WithMany(x => x.UsuarioFilial)
                .HasForeignKey(x => x.IdFilial);

            HasRequired(x => x.Usuario)
                .WithMany(x => x.Filiais)
                .HasForeignKey(x => x.IdUsuario);
        }
    }
}
using System;

namespace ATS.Domain.Entities
{
    public class ResgateCartaoAtendimento
    {
        public int IdResgate { get; set; }
        public decimal Valor { get; set; }
        public string CpfCnpjPortador { get; set; }
        public string CnpjEmpresa { get; set; }
        public int NumeroCartao { get; set; }
        public int Produto { get; set; }
        public string Motivo { get; set; }
        public int IdUsuarioCadastro { get; set; }
        public DateTime DataHoraCadastro { get; set; }
        public EStatusResgate StatusResgate { get; set; }
        public int? IdUsuarioEstorno { get; set; }
        public DateTime? DataHoraEstorno { get; set; }
        public string MotivoEstorno { get; set; }
    }

    public enum EStatusResgate
    {
        NaoProcessado = 0,
        Processado = 1,
        Estornado = 2,
        Erro = 3
    }
}
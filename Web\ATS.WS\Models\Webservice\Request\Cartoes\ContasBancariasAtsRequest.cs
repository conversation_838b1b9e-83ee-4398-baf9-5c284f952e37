using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Request.Cartoes
{
    public class ContasBancariasAtsRequest : RequestBase
    {
        public string Documento
        {
            get { return _documento; }
            set {_documento = value.OnlyNumbers();}
        }
        private string _documento { get; set; }
        
        public ValidationResult ValidaRequest()
        {
            ValidationResult validationResult = ValidaRequestBase();
            
            if (string.IsNullOrWhiteSpace(Documento))
                validationResult.Add("É obrigatório o envio do campo Documento");
            else if (Documento.Length != 11 && Documento.Length != 14)
                validationResult.Add("O campo Documento deve conter 11 ou 14 dígitos");

            return validationResult;
        }
    }
}
﻿namespace ATS.Domain.Entities
{
    public class TipoMotivo
    {
        public int IdTipoMotivo { get; set; }
        public int IdMotivo { get; set; }
        public ETipoMotivo Tipo { get; set; }

        #region Relacionamentos
        public virtual Motivo Motivo { get; set; }
        #endregion
    }
    
    public enum ETipoMotivo
    {
        Todos = 0,
        OfertaCarga_RemocaoFilaLote = 1,
        Credenciamento_RejeicaoSolicitacao = 2,
        PgtoFrete_TriagemProtocolo_CorrecaoEvento = 3,
        PgtoFrete_TriagemProtocolo_Ocorrencia = 4,
        PgtoFrete_TriagemProtocolo_Abono = 5,
        PgtoFrete_TriagemProtocolo_RejeicaoEvento = 6,
        PgtoFrete_TriagemProtocolo_Rejeicao = 7,
        Credenciamento_Descredenciamento = 8,
        Atendimento_FinalizarAtendimento = 10
    }
}


using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Text;
using System.Web.Http;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.AuthMobile;

namespace ATS.WS.Controllers
{
    public class AuthMobileController : BaseController
    {
        private readonly IRsaCryptoService _rsaCryptoService;
        private readonly IUsuarioService _usuarioService;
        private readonly KeycloakHelper _keycloak;
        
        public AuthMobileController(BaseControllerArgs baseArgs, KeycloakHelper keycloak, 
            IRsaCryptoService rsaCryptoService, IUsuarioService usuarioService) : base(baseArgs)
        {
            _keycloak = keycloak;
            _rsaCryptoService = rsaCryptoService;
            _usuarioService = usuarioService;
        }

        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string Login(AuthMobileLoginRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Login))
                    throw new InvalidOperationException("Login é obrigatório.");

                if (string.IsNullOrEmpty(request.Senha) && string.IsNullOrEmpty(request.SenhaCrypt))
                    throw new InvalidOperationException("Senha é obrigatória.");

                var senha = request.SenhaCrypt ?? request.Senha;
                
                if(!string.IsNullOrWhiteSpace(senha))
                {
                    try
                    {
                        senha = _rsaCryptoService.Decrypt(senha);
                        if (string.IsNullOrEmpty(senha)) throw new Exception();
                    }
                    catch (Exception)
                    {
                        throw new InvalidOperationException("Senha inválida.");
                    }
                }

                var token = _keycloak.GetUserAccessToken(request.Login, senha, "");
                
                var usuario = _usuarioService.Find(c => c.Login == request.Login).Include(c => c.Contatos).FirstOrDefault();
                if (usuario == null) throw new InvalidOperationException("Erro ao efetuar login. Verifique seus dados e tente novamente.");
                
                if (token.StatusCode == HttpStatusCode.OK)
                {
                    _usuarioService.ResetarErroSenhaLoginMobile(usuario.IdUsuario);
                    return new JsonResult().Responde(new
                    {
                        access_token = token.AccessToken,
                        refresh_token = token.RefreshToken,
                        expires_in = token.ExpiresIn,
                    });
                }

                if (token.StatusCode == HttpStatusCode.Unauthorized)
                {
                    var bloquear = _usuarioService.IncrementarErroSenhaLoginMobile(usuario.IdUsuario);
                    if (!bloquear) throw new InvalidOperationException("Erro ao efetuar login. Verifique seus dados e tente novamente.");
                    var contato = usuario.Contatos?.FirstOrDefault();
                    var email = contato?.Email ?? string.Empty;
                    _keycloak.CreateOrUpdateUser(usuario.LoginDesabilitado, usuario.Nome, email, "", false, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CpfCnpjDesabilitado } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome } },
                        { "Modified", new List<string> { "disabled automatically at " + DateTime.Now.ToString() + " for reaching password mistake limit"} },
                    }, false);
                    
                    throw new InvalidOperationException("Usuário bloqueado após repetidos erros de senha. Entre em contato com o suporte da Extratta.");
                }

                if (token.StatusCode == HttpStatusCode.InternalServerError)
                {
                    throw new InvalidOperationException("Erro inesperado de comunicação ao efetuar login.");
                }
                
                throw new InvalidOperationException("Erro ao efetuar login. Aguarde um momento e tente novamente. Caso o erro persista entre em contato com o suporte da Extratta.");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoBaseMap : EntityTypeConfiguration<EstabelecimentoBase>
    {
        public EstabelecimentoBaseMap()
        {
            ToTable("ESTABELECIMENTO_BASE");

            HasKey(x => x.IdEstabelecimento);

            Property(x => x.DataUltimaAtualizacao)
                .IsOptional();

            Property(x => x.CEP)
                .HasMaxLength(20);

            Property(x => x.Associacao);
            Property(x => x.PertenceRedeJsl);
            
            HasRequired(x => x.TipoEstabelecimento)
                .WithMany(x => x.EstabelecimentosBase)
                .HasForeignKey(x => x.IdTipoEstabelecimento);

            HasRequired(x => x.Pais)
                .WithMany(x => x.EstabelecimentosBase)
                .HasForeignKey(x => x.IdPais);

            HasRequired(x => x.Estado)
                .WithMany(x => x.EstabelecimentosBase)
                .HasForeignKey(x => x.IdEstado);

            HasRequired(x => x.Cidade)
                .WithMany(x => x.EstabelecimentosBase)
                .HasForeignKey(x => x.IdCidade);
            
            HasOptional(x => x.Filial)
                .WithMany()
                .HasForeignKey(x => x.IdFilial);
        }
    }
}

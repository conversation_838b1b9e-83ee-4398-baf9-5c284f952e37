using System;
using ATS.Domain.Entities;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class ResgatarCartaoResponseDTO
    {
        public int IdResgate { get; set; }
        public decimal Valor { get; set; }
        public string CpfPortador { get; set; }
        public string CnpjEmpresa { get; set; }
        public int NumeroCartao { get; set; }
        public int Produto { get; set; }
        public string Motivo { get; set; }
        public int IdUsuarioCadastro { get; set; }
        public DateTime DataHoraCadastro { get; set; }
        public EStatusResgate StatusResgate { get; set; }
        public int? IdUsuarioEstorno { get; set; }
        public DateTime? DataHoraEstorno { get; set; }
        public string MotivoEstorno { get; set; }
    }
}
﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class NotificacaoPushItemRepository : Repository<NotificacaoPushItem>, INotificacaoPushItemRepository
    {
        public NotificacaoPushItemRepository(AtsContext context) : base(context)
        {
        }

        public void Delete(NotificacaoPushItem item)
        {
            base.Delete(item);
        }
    }
}
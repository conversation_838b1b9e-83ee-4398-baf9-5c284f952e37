﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Domain.Models.Ciot
{
    public class DeclararOperacaoTransporteModel : DeclararOperacaoTransporteReponse
    {
        public int TipoViagem { get; set; }
        public List<VeiculoRequest> Veiculos { get; set; }
        
        /// <summary>
        /// Registro no sistema da declaração de CIOT efetuada
        /// </summary>
        public DeclaracaoCiot DeclaracaoCiot { get; set; }
    }
}
﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Validation;
using Autofac;

namespace ATS.WS.Models.Common.Request
{
    public class MotoristaIntegrarRequestModel : RequestBase
    {
        public string CpfcnpjUsuario { get; set; } = null;
        public string Nome { get; set; }
        public string Rg { get; set; }
        public string RgOrgaoExpedidor { get; set; }
        public string Referencia1 { get; set; }
        public string Referencia2 { get; set; }
        public string Cpf { get; set; }
        public string Sexo { get; set; }
        public string Cnh { get; set; }
        public string CnhCategoria { get; set; }
        public DateTime? ValidadeCnh { get; set; }
        public string Celular { get; set; }
        public ETipoContrato TipoContrato { get; set; }
        public string Email { get; set; }
        public byte[] Foto { get; set; }
        public bool Ativo { get; set; } = true;
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public DateTime? DataNascimento { get; set; }


        // Endereço                         
        public string Cep { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IbgeCidade { get; set; }
        public int IbgeEstado { get; set; }
        public int BacenPais { get; set; }

        public string FormularioCnh { get; set; }

        public MotoristaCartaoIntegrarRequestModel Cartao { get; set; }

        public ValidationResult ValidarEntrada()
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var cidadeApp = scope.Resolve<ICidadeApp>();
                var validation = new ValidationResult();

                if (string.IsNullOrEmpty(Cpf))
                    validation.Add("CPF não informado no cadastro do motorista.", EFaultType.Error);
                else if (Cpf.Length > 11)
                    validation.Add("CPF não pode ter mais de 11 caracteres no cadastro de motorista.", EFaultType.Error);
                else if (!Cpf.ValidateDocument())
                    validation.Add("CPF inválido no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Nome))
                    validation.Add("Nome não informado no cadastro do motorista.", EFaultType.Error);
                else if (Nome.Length > 150)
                    validation.Add("Nome não pode ter mais de 150 caracteres no cadastro de motorista.", EFaultType.Error);

                if (IbgeCidade == 0)
                    validation.Add("Código IBGE da cidade não informado no cadastro de motorista.", EFaultType.Error);
                else if (!cidadeApp.ValidarIbgeCadastrado(IbgeCidade))
                    validation.Add("Código IBGE da cidade inválido no cadastro de motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Cnh))
                    validation.Add("Número da CNH não informado no cadastro do motorista.", EFaultType.Error);
                else if (Cnh.Length > 14)
                    validation.Add("Número da CNH não pode ter mais de 14 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(CnhCategoria))
                    validation.Add("Categoria da CNH não informado no cadastro do motorista.", EFaultType.Error);
                else if (CnhCategoria.Length > 2)
                    validation.Add("Categoria da CNH não pode ter mais de 2 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Rg))
                    validation.Add("Número do RG não informado no cadastro do motorista.", EFaultType.Error);
                else if (Rg.Length > 100)
                    validation.Add("Número do RG não pode ter mais de 100 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(RgOrgaoExpedidor))
                    validation.Add("Órgão expedidor do RG não informado no cadastro do motorista.", EFaultType.Error);
                else if (RgOrgaoExpedidor.Length > 10)
                    validation.Add("Órgão expedidor RG não pode ter mais de 10 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Cep))
                    validation.Add("CEP não informado no cadastro do motorista.", EFaultType.Error);
                else if (Cep.Length > 8)
                    validation.Add("CEP não pode ter mais de 8 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Endereco))
                    validation.Add("Endereço não informado no cadastro do motorista.", EFaultType.Error);
                else if (Endereco.Length > 100)
                    validation.Add("Endereço não pode ter mais de 100 caracteres no cadastro do motorista.", EFaultType.Error);

                if (string.IsNullOrEmpty(Bairro))
                    validation.Add("Bairro não informado no cadastro do motorista.", EFaultType.Error);
                else if (Bairro.Length > 50)
                    validation.Add("Bairro não pode ter mais de 50 caracteres no cadastro do motorista.", EFaultType.Error);

                if (Cartao != null)
                    if (Cartao.NumeroCartao == 0)
                        validation.Add("Número do cartão não informado no cadastro do proprietário.", EFaultType.Error);

                return validation;
            }
        }
    }
    
    public class MotoristaCartaoIntegrarRequestModel
    {
        public int NumeroCartao { get; set; }
        public bool RealizarTrocaCartao { get; set; }
    }
}
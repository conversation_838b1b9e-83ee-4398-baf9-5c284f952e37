﻿using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Filial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.CrossCutting.Reports.Empresa.RelatorioListaEmpresa
{
    public class RelatorioFilial
    {
        public byte[] GetReport(List<RelatorioFiliaisDataType> listaDados, string tipoArquivo, string logo)
        {
            try
            {
                var path = ReportUtils.CreateLogo(logo);

                // A classe ReportParameter espera por 3 argumentos: o nome do parâmetro (string), o valor do parâmetro (string), e se esse parâmetro vai ser visível (true), por isso passamos um Tupple com esses três tipos, para se tornar genérico a passagem dos parâmetros
                var parametros = new Tuple<string, string, bool>[1];
                parametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + path, true);

                var bytes = new Base.Reports().GetReport(listaDados, parametros, true, "DtsFilial",
                    "ATS.CrossCutting.Reports.Filial.RelatorioFilial.rdlc", tipoArquivo);

                return bytes;
            }
            catch (Exception)
            {
                return new byte[0];
            }
        }
    }
}

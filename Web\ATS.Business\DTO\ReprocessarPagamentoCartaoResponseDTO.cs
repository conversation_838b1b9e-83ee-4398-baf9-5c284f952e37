using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class ReprocessarPagamentoCartaoResponseDTO
    {
        public List<ReprocessamentoPagamentoCartaoDTO> ReprocessamentoPagamentoCartoes { get; set; } = new List<ReprocessamentoPagamentoCartaoDTO>();
    }

    public class ReprocessamentoPagamentoCartaoDTO
    {
        public int IdViagemEvento { get; set; }
        public string TipoEvento { get; set; }
        public OperacaoCartaoResponseDTO RetornoOperacaoCartao { get; set; }
    }
}
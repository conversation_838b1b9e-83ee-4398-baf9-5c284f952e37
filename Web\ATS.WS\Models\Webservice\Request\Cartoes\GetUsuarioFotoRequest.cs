using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Request.Cartoes
{
    public class GetUsuarioFotoRequest : RequestBase
    {
        public string Cpfcnpj
        {
            get { return _cpfcnpj; }
            set {_cpfcnpj = value.OnlyNumbers();}
        }
        private string _cpfcnpj { get; set; }

        public ValidationResult ValidaRequest()
        {
            ValidationResult validacao = ValidaRequestBase(false);
            
            if (string.IsNullOrWhiteSpace(Cpfcnpj))
                validacao.Add("É obrigatório o envio do campo Cpfcnpj");
            else if (Cpfcnpj.Length != 11 && Cpfcnpj.Length != 14)
                validacao.Add("O campo Cpfcnpj deve conter 11 ou 14 dígitos");

            return validacao;
        }
    }
}
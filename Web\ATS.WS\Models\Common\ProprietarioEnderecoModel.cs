﻿using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class ProprietarioEnderecoModel
    {
        public int IdProprietario   { get; set; }
        public int IdEmpresa  { get; set; }
        public int IdEndereco       { get; set; }
        public string CEP           { get; set; }
        public string Endereco      { get; set; }
        public string Complemento   { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Numero          { get; set; }
        public string Bairro        { get; set; }
        public int IdCidade         { get; set; }
        public int IdEstado         { get; set; }
        public int IdPais           { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual PaisModel Pais { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual EstadoModel Estado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual CidadeModel Cidade { get; set; }
    }
}
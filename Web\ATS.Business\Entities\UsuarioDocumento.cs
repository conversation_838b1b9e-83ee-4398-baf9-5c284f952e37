﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class UsuarioDocumento
    {
        public int IdUsuarioDocumento { get; set; }

        public int IdUsuario { get; set; }
        public int IdTipoDocumento { get; set; }
        public DateTime? Validade { get; set; }
        public DateTime? AvisoValidade { get; set; }

        public virtual Usuario Usuario { get; set; }
        public virtual TipoDocumento TipoDocumento { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Data.Context.Mapping
{
    public class PlanoMap : EntityTypeConfiguration<Plano>
    {
        public PlanoMap()
        {
            ToTable("PLANO");

            HasKey(x => new { x.IdPlano });

            Property(t => t.IdPlano)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.DataCriacao)
                .HasColumnName("datacriacao")
                .HasColumnType("datetime2");

            Property(x => x.DataAtualizacao)
                .HasColumnName("dataatualizacao")
                .IsOptional()
                .HasColumnType("datetime2");

            HasRequired(x => x.Usuario)
                .WithMany(x => x.Planos)
                .HasForeignKey(x => x.IdUsuarioCadastro);

            HasRequired(x => x.Usuario)
                .WithMany(x => x.Planos)
                .HasForeignKey(x => x.IdUsuarioAtualizacao);

            Property(x => x.Descricao)
              .HasColumnName("descricao")
              .IsOptional()
              .HasColumnType("varchar")
              .HasMaxLength(100);

            Property(t => t.Ativo)
                .HasColumnName("ativo")
                .IsRequired()
                .HasColumnType("bit");

        }
    }
}

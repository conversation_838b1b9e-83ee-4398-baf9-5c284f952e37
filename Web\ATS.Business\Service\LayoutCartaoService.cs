﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class LayoutCartaoService : ILayoutCartaoService
    {
        private readonly ILayoutCartaoRepository _layoutCartaoRepository;

        public LayoutCartaoService(ILayoutCartaoRepository layoutCartaoRepository)
        {
            _layoutCartaoRepository = layoutCartaoRepository;
        }

        public int GetIdItem(int id, string key)
        {
            return _layoutCartaoRepository.GetIdItemCartao(id, key);
        }

        public IList<LayoutCartao> GetAll()
        {
            return _layoutCartaoRepository.GetAll().ToList();
        }
    }
}
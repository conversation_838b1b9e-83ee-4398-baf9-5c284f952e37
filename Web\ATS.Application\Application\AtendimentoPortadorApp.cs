﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento;
using ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.AtendimentoPortador;
using AutoMapper;
using Sistema.Framework.Util.Extension;

namespace ATS.Application.Application
{
    public class AtendimentoPortadorApp : AppBase, IAtendimentoPortadorApp
    {
        private readonly IAtendimentoPortadorService _portadorService;
        private readonly IEmpresaApp _empresaApp;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosMobileGlobalService _parametrosMobileService;
        private readonly IUsuarioApp _usuarioApp;

        public AtendimentoPortadorApp(IAtendimentoPortadorService portadorService, IEmpresaApp empresaApp, 
            IUserIdentity userIdentity, IParametrosMobileGlobalService parametrosMobileService, IUsuarioApp usuarioApp)
        {
            _portadorService = portadorService;
            _empresaApp = empresaApp;
            _userIdentity = userIdentity;
            _parametrosMobileService = parametrosMobileService;
            _usuarioApp = usuarioApp;
        }

        public AtendimentoPortadorResultModel IniciarAtendimento(int idUsuario, int? idEmpresa)
        {
            return _portadorService.IniciarAtendimento(idUsuario, idEmpresa);
        }
        public void AtualizaDocumento(string documento, int idUsuario)
        {
            _portadorService.AtualizaDocumento(documento, idUsuario);
        }
        public AtendimentoPortadorResultModel Finalizar(FinalizarAtendimentoDTORequest request, int idUsuario)
        {
            return _portadorService.Finalizar(request, idUsuario);
        }

        public AtendimentoPortadorResultModel ConsultaAtendimentoPendente(int idUsuario, int? idEmpresa)
        {
            return _portadorService.ConsultaAtendimentoPendente(idUsuario, idEmpresa);
        }
        
        public void InserirTramite(AtendimentoPortadorTramiteRequest atendimentoTramitePortadorDto)
        {
            _portadorService.InserirTramite(atendimentoTramitePortadorDto);
        }

        public List<ItensGraficoPorEmpresa> ConsultaNumeroGraficoAtendimentoPortador(DateTime dataInicial, DateTime dataFinal, DateTime? dataSelecinada)
        {
            return _portadorService.ConsultaQuantidadeAtendimentoPortador(dataInicial, dataFinal, dataSelecinada);
        } 

        public IQueryable<AtendimentoPortador> Find(Expression<Func<AtendimentoPortador, bool>> predicate, bool @readonly = false)
        {
            return _portadorService.Find(predicate, @readonly);
        }

        public ConsultarAtendimentoResponseDTO ConsultarAtendimento(ConsultarAtendimentoRequestDTO request, int take, int page,
            OrderFilters order, List<QueryFilters> filters, int idEmpresa)
        {
            return _portadorService.ConsultarAtendimento(request, take, page, order, filters, idEmpresa);
        }
        
        public ConsultarHistoricoAtendimentoResponseDTO ConsultarHistoricoAtendimento(ConsultarHistoricoAtendimentoRequestDTO request, int take, int page,
            OrderFilters order, List<QueryFilters> filters, int idEmpresa)
        {
            return _portadorService.ConsultarHistoricoAtendimento(request, take, page, order, filters, idEmpresa);
        }

        public byte[] RelatorioConsultaAtendimento(RelatorioAtendimentoRequestDTO request, int take, int page, OrderFilters order,
            List<QueryFilters> filters, int idEmpresa, string extensao)
        {
            var logoEmpresa = _empresaApp.Get(request.IdEmpresa ?? 0, null)?.Logo;
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

            var requestDto = new ConsultaRelatorioAtendimentoDTO
            {
                Protocolo = request.Protocolo,
                DataInicio = request.DataInicio,
                DataFim = request.DataFim,
                Atendente = request.Atendente,
                Portador = request.Portador.GetValueOrDefault(0)
            };

            var list = _portadorService.RelatorioConsultaAtendimentos(requestDto, take, page, order, filters);

            var dados = list.Select(c => new RelatorioConsultaAtendimentoItemDataType
            {
                IdAtendimentoPortador = c.IdAtendimentoPortador,
                Cnpjcpf = c.Cnpjcpf.FormatarCpfCnpj(ignorarErro: true),
                Observacao = c.Observacao,
                DataInicio = c.DataInicio,
                DataFinal = c.DataFinal,
                IdUsuario = c.IdUsuario,
                UsuarioNome = c.UsuarioNome,
                Status = c.Status.GetDescription(),
                Protocolo = c.Protocolo,
                IdMotivoFinalizacaoAtendimento = c.IdMotivoFinalizacaoAtendimento,
                DescricaoMotivoAtendimento = c.DescricaoMotivoAtendimento,
                TotalData = c.TotalData
            }).ToList();

            var requestRelatorio = new RelatorioConsultaAtendimentoDataType();

            requestRelatorio.items = dados;
            requestRelatorio.totalItems = dados.Count;

            var relatorio = new RelatorioConsultaAtendimento().GetReport(extensao, requestRelatorio, logo);
            return relatorio;
        }
        
        public PermissoesEmpresaAtendimentoPortadorResultModel ConsultaPermissoesUsuario(int idUsuario, EPerfil perfil, int? idEmpresa)
        {
            return _portadorService.ConsultaPermissoesUsuario(idUsuario, perfil, idEmpresa);
        }

        public byte[] GerarReciboTransacoes(ReciboTransferenciaDto dadosRecibo)
        {
            var dadosReciboDataType = Mapper.Map<ReciboTransferenciaDataType>(dadosRecibo);

            return new ReciboTransferencia()
                .GetReport(dadosReciboDataType);
        }

        public AtendimentoInformacoesContatoWhatsappModel ConsultarInformacoesContatoWhatsapp()
        {
            var nomes = _usuarioApp
                .Find(c => c.IdUsuario == _userIdentity.IdUsuario)
                .Select(c => new
                {
                    Nome = c.Nome,
                    Empresa = c.Empresa.RazaoSocial
                })
                .FirstOrDefault();

            if (nomes == null)
                throw new InvalidOperationException("Não foi possível consultar o nome do usuário e da empresa.");

            var numero = _parametrosMobileService.GetTelefoneAtendimento();

            if(string.IsNullOrWhiteSpace(numero))  
                throw new InvalidOperationException("Não foi possível consultar o número de telefone do atendimento.");

            return new AtendimentoInformacoesContatoWhatsappModel()
            {
                Empresa = nomes.Empresa.Replace(' ', '+'),
                Nome = nomes.Nome.Replace(' ', '+'),
                Numero = "55"+numero.OnlyNumbers()
            };
        }
    }
}
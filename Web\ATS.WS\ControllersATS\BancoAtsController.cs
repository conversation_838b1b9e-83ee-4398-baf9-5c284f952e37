﻿using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class BancoAtsController : DefaultController
    {
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IBancoApp _bancoApp;


        public BancoAtsController(ICadastrosApp cadastrosApp,IBancoApp bancoApp)
        {
            _cadastrosApp = cadastrosApp;
            _bancoApp = bancoApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var bancos = _cadastrosApp.Bancos();

                if (!bancos.Sucesso)
                    return ResponderSucesso(new object());

                var retorno = _bancoApp.ConsultaGrid(take, page, order, filters,bancos);
                
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTodos()
        {
            try
            {
                var bancos = _bancoApp.ConsultarTodos(true);
                return ResponderSucesso(bancos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
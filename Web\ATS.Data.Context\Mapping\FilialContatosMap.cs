﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class FilialContatosMap : EntityTypeConfiguration<FilialContatos>
    {
        public FilialContatosMap()
        {
            ToTable("FILIAL_CONTATOS");

            HasKey(t => new  { t.IdFilialContato, t.IdFilial });

            Property(t => t.IdFilial)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdFilialContato)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(t => t.Filial)
                .WithMany(t => t.FilialContatos)
                .HasForeignKey(t => t.IdFilial);

        }
    }
}

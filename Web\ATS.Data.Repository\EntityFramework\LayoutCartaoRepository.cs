﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using Sistema.Framework.Util.Extension;
using System;
using System.Data.Entity;
using System.Linq;

namespace ATS.Data.Repository.EntityFramework
{
    public class LayoutCartaoRepository : Repository<LayoutCartao>, ILayoutCartaoRepository
    {
        public LayoutCartaoRepository(AtsContext context) : base(context)
        {
        }
        
        public int GetIdItemCartao(int id, string key)
        {
            var itens = (from layoutcartao in All()
                    .Include(x => x.IdLayout)
                    .Include(x => x.Itens)
                    .Include(x => x.Nome)
                where layoutcartao.IdLayout == id
                select layoutcartao.Itens).FirstOrDefault();

            var iditemCartao = itens.Where(c => c.Key == key).Select(c => c.ItemCartao).LastOrDefault();

            return iditemCartao;

            //return Where(c => c.IdLayout == id).Include(c => c.Itens).Select(c => c.Itens.First(a => a.Key == key).ItemCartao).First();
        }
    }
}
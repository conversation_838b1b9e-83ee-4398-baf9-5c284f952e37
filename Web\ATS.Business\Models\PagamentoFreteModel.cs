﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class PagamentoFreteModel
    {
        public string Token { get; set; }
        public string NomeFantasiaEmpresa { get; set; }
        public string Filial { get; set; }
        public string NomeMotorista { get; set; }
        public string CPFMotorista { get; set; }
        public string ValidadeCNH { get; set; }
        public string CNH { get; set; }
        public string RG { get; set; }
        public string PlacaVeiculo { get; set; }
		public string AbonoDocument { get; set; }
        public decimal PesoSaida { get; set; }
        public decimal PesoChegada { get; set; }
        public decimal ValorMercadoria { get; set; }
        public decimal? TarifaTonelada { get; set; }
        public bool Rejeitado { get; set; }
        public string Origem { get; set; }
        public string Destino { get; set; }
        public string Produto { get; set; }
        public int? IdProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string CPFCNPJ { get; set; }
        public string NumeroCTE { get; set; }
        public object Unidade { get; set; }
        public string UnidadeMedida { get; set; }
        public string DataEmissao { get; set; }
        public bool CreditarAbono { get; set; }
        public decimal? Quantidade { get; set; }
        public decimal? TotalFrete { get; set; }
        public decimal? FreteUnitario { get; set; }
        public decimal? ValorINSS { get; set; }
        public decimal? ValorSaca { get; set; }
        public decimal? ValorSESTSENAT { get; set; }
        public decimal? ValorIRRF { get; set; }
        public decimal? Diferenca { get; set; }
        public decimal? Adiantamento { get; set; }
        public decimal? TotalPago { get; set; }
        public decimal? TotalPagar { get; set; }
        public decimal? ValorPedagio { get; set; }
        public decimal? AdiantamentoSemPedagio { get; set; }
        public decimal? ValorQuebra { get; set; }
        public bool AbonoRequisitado { get; set; }
        public string EstabelecimentoDePagamento { get; set; }
        public bool PedagioBaixado { get; set; }
        public string Remetente { get; set; }
        public string Destinatario { get; set; }
        public string Tomador { get; set; }
        public string Instrucao { get; set; }
        public string Coleta { get; set; }
        public string Entrega { get; set; }
        public bool? HasDesconto { get; set; }
        public bool HasRPA { get; set; }
        public string DataPagamentoRPA { get; set; }
        public string DataPagamentoAdiantamento { get; set; }
        public EStatusViagemEvento? StatusAdiantamento { get; set; }
        public EStatusViagemEvento? StatusTarifa { get; set; }
        public decimal? ValorTarifaANTT { get; set; }
        public decimal? ValorAbastecimento { get; set; }
        public string TipoQuebraMercadoria { get; set; }
        public bool FreteLotacao { get; set; }
        public bool HasProtocolo { get; set; }
        public decimal ValorEvento { get; set; }
        public decimal ValorPedagioParaIncluirPagamento { get; set; }
        public bool HabilitarPagamentoCartao { get; set; }
        public string FormaPagamentoDescricao { get; set; }
        public int? IdViagemEVentoAbono { get; set; }
        public bool? PagamentoAntecipado { get; set; } = false;
        public int? IdMotivoOcorrencia { get; set; }
        public string DetalhamentoOcorrencia { get; set; }
        public string DescricaoOcorrencia { get; set; }

        public decimal? Estadia { get; set; }
        public string DataPagamentoEstadia { get; set; }
        public EStatusViagemEvento? StatusEstadia { get; set; }
        public decimal? Saldo { get; set; }
        public string DataPagamentoSaldo { get; set; }
        public EStatusViagemEvento? StatusSaldo { get; set; }
        public decimal? Abastecimento { get; set; }
        public string DataPagamentoAbastecimento { get; set; }
        public EStatusViagemEvento? StatusAbastecimento { get; set; }
        public decimal QuantidadeSacas { get; set; }
        public string NumeroCartao { get; set; }
        public string Celular { get; set; }
        public object ParamsEmpPagFrete { get; set; }
        public string QRCodeEvento { get; set; }
        public int? IdProtocolo { get; set; }
        public PagamentoFreteEventoModel RPA { get; set; }
        public EStatusViagemEvento? StatusRPA { get; set; }
        public PagamentoFreteTipoEventoModel TipoEvento { get; set; }
        public PagamentoFreteEventoModel Evento { get; set; }
        public List<PagamentoFreteAnexoModel> Anexos { get; set; }
        public List<string> Carretas { get; set; }
        public EStatusProtocoloEvento StatusProtocoloEvento { get; set; }
        public int? IdEstabelecimento { get; set; }
        public EStatusViagemEvento? StatusViagemEvento { get; set; }
        public int? TempoValidadeChave { get; internal set; }
        public int?  MinutosValidadeHash { get; internal set; }
        public bool EmpresaValidaChaveBaixaEvento { get; internal set; }
        public bool EmpresaValidaChaveMHBaixaEvento { get; internal set; }
        public string Status { get; internal set; }
        public bool LiberarPagtoSemChave { get; internal set; }
        public ETipoEventoViagem TipoEventoViagem { get; internal set; }
        public int IdViagem { get; set; }
        public bool EventoAnalisado { get; set; }
        public string DataHoraPagamnto { get; set; }
        public string EstabelecimentoPagamento { get; set; }
        public string NumeroCiot { get; set; }
        public decimal? PesoChegadaTriagem { get; set; }

        public bool? AbonoAnalisado { get; set; }
        public DateTime? DataDescarga { get; set; }
        public string DataDescargaStr { get; set; }        
        public int AdministradoraPlataforma { get; set; }
    }

    public class PagamentoFreteValorAdicionalModel
    {
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
    }

    public class PagamentoFreteTipoEventoModel
    {
        public string Descricao { get; set; }
        public ETipoEventoViagem Valor { get; set; }
    }

    public class PagamentoFreteEventoModel
    {
        public decimal PesoChegada { get; set; }
        public decimal? PesoChegadaOriginal { get; set; }
        public bool PesoChegadaAlterado { get; set; }
        public decimal? QuantidadeSacas { get; set; }
        public string DataEmissao { get; set; }
        public decimal DifFreteMotorista { get; set; }
        public decimal QuebraMercadoria { get; set; }
        public decimal QuebraMercadoriaCalculada { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal ValorTotalPagamento { get; set; }
        public decimal ValorTotalPagamentoCalculado { get; set; }
        public decimal? ValorBruto { get; set; }
        public decimal? ValorIRRF { get; set; }
        public decimal? ValorINSS { get; set; }
        public decimal? ValorLiquido { get; set; }
        public decimal? ValorPedagio { get; set; }
        public decimal? ValorSESTSENAT { get; set; }
        public int? IdProtocoloEvento { get; set; }

        public bool AbonoRequisitado { get; set; }
        public string AbonoDocument { get; set; }
        public bool ValorPagamentoBloqueado { get; set; }
        public bool ValorTotalPagamentoBaixado { get; set; }
        public string DataValidade { get; set; }
        public bool PossuiOutrosValores { get; set; }
        public bool Valido { get; set; } = true;
        public string ValidationMessage { get; set; }
        public int IdViagemEvento { get; internal set; }
        public decimal TotalOutrosDescontos { get; set; }
        public decimal TotalOutrosAcrescimos { get; set; }
        public decimal? TotalPago { get; set; }
        public decimal? TotalPagar { get; set; }
        public bool DifFreteMotoristaPassivo { get; set; }
        public string DescricaoMotivo { get; set; }
        public PagamentoParametrosCalculo PagamentoParametrosCalculo { get; set; }
        public PagamentoSolicitacaoAbono PagamentoSolicitacaoAbono { get; set; }
        public int? TipoEvento { get; set; }
        public DateTime? DataHoraLimiteValidadeToken { get; internal set; }
        public string ChaveToken { get; internal set; }
        public ETipoEventoViagem TipoEventoViagem { get; internal set; }
        public decimal ValorPedagioParaIncluirPagamento { get; set; }
        public decimal? ValorTotalPagamentoComDesconto { get; internal set; }
        public bool? ModificouFormaPagamentoCartaFreteParaCartao { get; set; }
        public bool? ModificouFormaPagamentoCartaoParaCartaFrete { get; set; }
    }

    public class PagamentoSolicitacaoAbono
    {
        public int IdViagemSolicitacaoAbono { get; set; }
        public EStatusAbono Status { get; set; }

    }

    public class PagamentoParametrosCalculo
    {
        public decimal? Tolerancia { get; set; }
        public decimal? ValorKG { get; set; }
        public decimal? PesoDiferencaTolerada { get; set; }
        public decimal? Falta { get; set; }
        public decimal? Tarifa { get; set; }
        public decimal? Diferenca { get; set; }
        public decimal? PesoSaida { get; set; }
    }

}
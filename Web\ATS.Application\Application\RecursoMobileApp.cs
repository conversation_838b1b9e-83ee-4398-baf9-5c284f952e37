﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class RecursoMobileApp : BaseApp<IParametrosMobileGlobalService>, IRecursoMobileApp
    {
        public RecursoMobileApp(IParametrosMobileGlobalService service) : base(service)
        {
        }

        public BusinessResult<RecursosMobileItemModel> GetRecursoMobileData()
        {
            try
            {
                var rsponse = new RecursosMobileItemModel
                {
                    ValidarSenhaIgualCPF = Service.GetValidarSenhaIgualCPF() ?? false,
                    TelefoneAtendimento = Service.GetTelefoneAtendimento(),
                    UtilizaKeycloack = Service.GetUtilizaKeycloack() ?? false
                };

                return BusinessResult<RecursosMobileItemModel>.Valid(rsponse);
            }
            catch (System.Exception e)
            {
                return BusinessResult<RecursosMobileItemModel>.Error(e.GetBaseException().Message);
            }
        }
    }
}

using System.Collections.Generic;

namespace ATS.Domain.Models.DespesasViagem
{
    public class DespesasViagemGridResponse : DespesasViagemReportGridResponse
    {
       //Permissão por usuário
       public bool DesabilitarBotoesResgate { get; set; }
       public bool DesabilitarBotoesExtrato { get; set; }
       
       //Permissão por empresa
       public bool PermiteResgate { get; set; }
       public bool AcessarExtrato { get; set; }
       public bool AcessarExtratoDetalhado { get; set; }
    }
    
    public class DespesasViagemReportGridResponse 
    {
        public string Saldo { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public int? Identificador { get; set; }
        public int? Produto { get; set; }
        public string Status { get; set; }
    }

    public class DespesasViagemGridSaldoResponse
    {
        public int totalItems { get; set; }
        public List<DespesasViagemGridResponse> items { get; set; }
        public string saldoTotal { get; set; } = "R$ 0,00";
    }
}
﻿using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class DespesaUsuarioApp : BaseApp<IDespesaUsuarioService>, IDespesaUsuarioApp
    {
        private readonly IDataMediaServerRepository _mongoRepository;
        public DespesaUsuarioApp(IDespesaUsuarioService service, IDataMediaServerRepository mongoRepository) : base(service)
        {
            _mongoRepository = mongoRepository;
        }

        public BusinessResult<DespesaUsuarioAddModelResponse> Add(DespesaUsuarioAddModel model)
        {
            return Service.Add(model);
        }
        
        public IQueryable<DespesaUsuario> GetAll()
        {
            return Service.GetAll();
        }

        public List<string> GetListHashId(int? idUsuario)
        {
            return Service.GetDespesaUsuario(idUsuario).Select(x => x.HashId).Distinct().ToList();
        }
        
        public DespesaUsuarioAnexoModelResponse GetLastAnexo(int? idUsuario,string hashId)
        {
            var retorno =  Service
                .GetDespesaUsuario(idUsuario)
                .Where(x => x.IdUsuario == idUsuario && x.HashId == hashId)
                .OrderByDescending(x => x.DataCadastro)
                .Select(x => new 
                {
                    x.Descricao,
                    Categoria = x.Categoria.Descricao,
                    Link = x.URL,
                    x.ImageToken
                })
                .FirstOrDefault();
            
            return new DespesaUsuarioAnexoModelResponse()
            {
                Descricao = retorno?.Descricao,
                Categoria = retorno?.Categoria,
                Link = retorno?.Link,
                Imagem = _mongoRepository.GetMedia(retorno?.ImageToken)?.Data
            };
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Dapper
{
    public interface ICidadeDapper : IRepositoryDapper<Cidade>
    {
        Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude);
        List<Cidade> GetCidadesPorNome(string nome);

        IQueryable<Cidade> GetCidades(string siglaEstado, DateTime dataAtualizacao);
    }
}

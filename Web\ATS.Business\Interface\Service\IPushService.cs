﻿using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{

    public interface IPushService
    { 
        ValidationResult EnviarPorDocumento(string documento, string titulo, string mensagem, object data = null, ETipoMensagemPush? tipoMensagem = ETipoMensagemPush.MessageGeneric);
        ValidationResult Enviar(List<string> idsPush, string title, string message, object data = null, ETipoMensagemPush? tipoMensagem = ETipoMensagemPush.MessageGeneric);
    }
   
}
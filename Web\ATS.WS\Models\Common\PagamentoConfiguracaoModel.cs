﻿using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class PagamentoConfiguracaoModel
    {
        public int IdPagamentoConfiguracao { get; set; }
        
        public int IdEmpresa { get; set; }

        public int? IdFilial { get; set; }

        public string RazaoSocialEmpresa { get; set; }

        public string RazaoSocialFilial { get; set; }

        public double TaxaAntecipacao { get; set; } = 0;

        public bool BonificarMotorista { get; set; } = false;

        public List<DocumentoModel> DoctosCredenciamento { get; set; }
        public List<DocumentoModel> DoctosPgtoFrete { get; set; }
        public List<DocumentoModel> DoctosAntecipacao { get; set; }
        public List<DocumentoModel> DoctosProtocolo { get; set; }
    }

    public class DocumentoModel
    {
        public int IdDocumento { get; set; }
        public string Descricao { get; set; }
    }

}
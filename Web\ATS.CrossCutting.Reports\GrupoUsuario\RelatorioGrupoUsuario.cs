﻿using ATS.CrossCutting.IoC.Utils;
using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.GrupoUsuario
{
    public class RelatorioGrupoUsuario
    {
        public byte[] GetReport(object listaDados, string tipoArquivo, string logo)
        {
            try
            {
                var path = ReportUtils.CreateLogo(logo);
                var parametros = new Tuple<string, string, bool>[1];
                parametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + path, true);

                var dataSources = new Tuple<object, string>(listaDados, "DtsGrupoUsuario");

                var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                    "ATS.CrossCutting.Reports.GrupoUsuario.RelatorioGrupoUsuario.rdlc", tipoArquivo);

                return bytes;
            }
            catch (Exception)
            {
                return new byte[0];
            }
        }
    }
}

﻿using ATS.Data.Context.Mapping.Common;
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class ProprietarioEnderecoMap : EnderecoBaseMap<ProprietarioEndereco>
    {
        public ProprietarioEnderecoMap()
        {
            ToTable("PROPRIETARIO_ENDERECO");

            HasKey(t => new { t.IdProprietario, IdEmpresa = t.IdEmpresa, t.IdEndereco });

            Property(t => t.IdProprietario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEndereco)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(a => a.Proprietario)
                .WithMany(b => b.<PERSON><PERSON>)
                .HasForeignKey(c => new { c.IdProprietario, IdEmpresa = c.IdEmpresa});
        }
    }
}
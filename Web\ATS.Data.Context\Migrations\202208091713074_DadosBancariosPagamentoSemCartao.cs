﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class DadosBancariosPagamentoSemCartao : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.VIAGEM", "contacorrente", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.VIAGEM", "agencia", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.VIAGEM", "formapagamentosemcartao", c => c.Int());
            AddColumn("dbo.VIAGEM", "idbanco", c => c.Int());
            AddColumn("dbo.VIAGEM", "tipoconta", c => c.Int());
        }
        
        public override void Down()
        {
            DropColumn("dbo.VIAGEM", "tipoconta");
            DropColumn("dbo.VIAGEM", "idbanco");
            DropColumn("dbo.VIAGEM", "formapagamentosemcartao");
            DropColumn("dbo.VIAGEM", "agencia");
            DropColumn("dbo.VIAGEM", "contacorrente");
        }
    }
}

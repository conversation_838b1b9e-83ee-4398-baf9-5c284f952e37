﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddColumns_LatLngDestino : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 2));
            AddColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 2));
            AddColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 2));
            AddColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 2));
            DropColumn("dbo.ROTA_MODELO", "latitude");
            DropColumn("dbo.ROTA_MODELO", "longitude");
        }
        
        public override void Down()
        {
            AddColumn("dbo.ROTA_MODELO", "longitude", c => c.Decimal(precision: 10, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "latitude", c => c.Decimal(precision: 10, scale: 7));
            DropColumn("dbo.ROTA_MODELO", "destinolongitude");
            DropColumn("dbo.ROTA_MODELO", "destinolatitude");
            DropColumn("dbo.ROTA_MODELO", "origemlongitude");
            DropColumn("dbo.ROTA_MODELO", "origemlatitude");
        }
    }
}

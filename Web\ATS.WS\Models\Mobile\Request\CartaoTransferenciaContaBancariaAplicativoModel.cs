﻿using ATS.WS.Models.Common.Request.Base;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Models.Mobile.Request
{
    public class CartaoTransferenciaContaBancariaAplicativoModel : RequestBase
    {
        /// <summary>
        /// Cartão origem da transferência.
        /// Para o ATS Global, onde não há vinculo com a empresa, o app necessita obrigatoriamente informar esta informação.
        /// Em aplicativos específicos para empresa, como a SOTRAN, não é obrigado esta informação porque é possivel identificar o cartão automaticamente pelo CPF x Produto padrão de frete da empresa.
        /// </summary>
        public CartaoIdentificacaoAplicativoModel CartaoOrigem { get; set; }

        /// <summary>
        /// Documento do favorecido da transferencia
        /// </summary>
        public string DocumentoFavorecido { get; set; }

        /// <summary>
        /// Identificador da conta bancária
        /// </summary>
        public IdentificadorContaBancaria ContaBancaria { get; set; }

        /// <summary>
        /// Valor para efetuar carga
        /// </summary>
        public decimal Valor { get; set; }

        public string Senha { get; set; }
        
        public string SenhaCrypt { get; set; }
    }
}
﻿using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class LayoutApp : ILayoutApp
    {
        private readonly ILayoutService _layoutService;

        public LayoutApp(ILayoutService layoutService)
        {
            _layoutService = layoutService;
        }

        public Layout GetPorEmpresa(int idEmpresa)
        {
            return _layoutService.GetPorEmpresa(idEmpresa);
        }

        public Layout GetConfiguracaoLayoutPorDominio(string href)
        {
            return _layoutService.GetConfiguracaoLayoutPorDominio(href);
        }
    }
}

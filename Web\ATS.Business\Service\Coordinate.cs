﻿using System.Collections.Generic;

namespace ATS.Domain.Service
{
    public class Coordinate
    {
        public bool Status { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
    }


    #region Google Distance Response
    public class GoogleMatrixRespose
    {
        public decimal? Distancia { get; set; }
        public decimal? TempoDuracao { get; set; }
    }

    public class GoogleMatrixResponse
    {
        public string Status { get; set; }
        public List<Result> Results { get; set; }
        public List<string> destination_addresses { get; set; }
        public List<string> origin_addresses { get; set; }
        public List<Row> rows { get; set; }

    }

    public class Row
    {
        public List<Element> elements { get; set; }
    }

    public class Element
    {
        public TextProperty distance { get; set; }
        public TextProperty duration { get; set; }
        public string status { get; set; }
    }

    public class TextProperty
    {
        public decimal? value { get; set; }
        public string text { get; set; }
    }
    #endregion


    #region Google place response

    public class GooglePlaceResponse
    {
        public List<Results> Results { get; set; }
        public string Status { get; set; }
    }

    public class Results
    {
        public Geometry Geometry { get; set; }
        public string Name { get; set; }
        public string Vicinity { get; set; }
        public List<string> Types { get; set; }
    }

    #endregion
}
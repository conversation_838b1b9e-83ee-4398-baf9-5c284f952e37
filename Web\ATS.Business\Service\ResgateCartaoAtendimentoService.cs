using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class ResgateCartaoAtendimentoService : ServiceBase, IResgateCartaoAtendimentoService
    {
        private readonly IResgateCartaoAtendimentoRepository _resgateCartaoAtendimentoRepository;

        public ResgateCartaoAtendimentoService(IResgateCartaoAtendimentoRepository resgateCartaoAtendimentoRepository)
        {
            _resgateCartaoAtendimentoRepository = resgateCartaoAtendimentoRepository;
        }

        public ValidationResult Add(ResgateCartaoAtendimento resgatarCartao)
        {
            try
            {
                if (resgatarCartao.Valor <= 0)
                {
                    throw new Exception("Valor não pode ser menor que 0!");
                }
                if (resgatarCartao.Motivo == null)
                {
                    throw new Exception("Campo Motivo é obrigatório");
                }

                _resgateCartaoAtendimentoRepository.Add(resgatarCartao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        public ValidationResult Update(ResgateCartaoAtendimento resgatarCartao)
        {
            try
            {
                _resgateCartaoAtendimentoRepository.Update(resgatarCartao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        public object ConsultarGrid(ConsultarResgateValorDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var resgate = _resgateCartaoAtendimentoRepository.All();

            resgate = resgate.Where(x =>
                x.CpfCnpjPortador == request.Cnpj && x.NumeroCartao == request.NumeroCartao &&
                x.Produto == request.Produto && x.StatusResgate != EStatusResgate.Estornado).OrderByDescending(x=>x.IdResgate);
            
            resgate = resgate.AplicarFiltrosDinamicos(filters);
            
            return new
            {
                totalItens = resgate.Count(),
                items = resgate.Skip((page - 1) * take).Take(take).ToList()
            };
        }

        public ValidationResult Alterar(ResgateCartaoAtendimento resgatar, string motivoEstorno, int usuario)
        {
            try
            {
                var repository = _resgateCartaoAtendimentoRepository;
                var resgate = repository.FirstOrDefault(o => o.IdResgate == resgatar.IdResgate);

                if (resgate == null)
                    return new ValidationResult().Add("Resgate não encontrado.");
                
                resgate.MotivoEstorno = motivoEstorno;
                resgate.IdUsuarioEstorno = usuario;
                resgate.StatusResgate = resgatar.StatusResgate;
                if (usuario != 0)
                    resgate.DataHoraEstorno = DateTime.Now;

                repository.Update(resgate);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ResgateCartaoAtendimento GetResgate(int idResgate)
        {
            var repository = _resgateCartaoAtendimentoRepository;
            var resgate = repository.Where(x => x.IdResgate == idResgate).FirstOrDefault();
            
            return new ResgateCartaoAtendimento
            {
                IdResgate = resgate.IdResgate,
                Valor = resgate.Valor,
                CpfCnpjPortador = resgate.CpfCnpjPortador.OnlyNumbers(),
                CnpjEmpresa = resgate.CnpjEmpresa.OnlyNumbers(),
                NumeroCartao = resgate.NumeroCartao,
                Produto = resgate.Produto,
                Motivo = resgate.Motivo,
                IdUsuarioCadastro = resgate.IdUsuarioCadastro,
                DataHoraCadastro = resgate.DataHoraCadastro,
                StatusResgate = resgate.StatusResgate,
                IdUsuarioEstorno = resgate.IdUsuarioEstorno,
                DataHoraEstorno = resgate.DataHoraEstorno,
                MotivoEstorno = resgate.MotivoEstorno
            };
        }
    }
}
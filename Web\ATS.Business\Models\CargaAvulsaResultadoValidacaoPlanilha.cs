using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class CargaAvulsaResultadoValidacaoPlanilha
    {
        public int Total { get; set; }
        
        public int TotalInvalidos { get; set; }
        
        public int TotalValidos { get; set; }

        public decimal SomatorioValoresCargaAvulsa { get; set; }

        public string CodigoPlanilhaImportada { get; set; }

        public List<CargaAvulsaValidacaoPlanilha> Registros { get; set; }
    }

    public class CargaAvulsaValidacaoPlanilha
    {
        public bool Valido { get; set; }
        
        public string NomeSheet { get; set; }
        
        public string CnpjFilial { get; set; }

        public int Linha { get; set; }

        public string Cpf { get; set; }

        public string Nome { get; set; }

        public decimal Valor { get; set; }

        public string MensagemValidacao { get; set; }

        public string CodigoPlanilhaImportada { get; set; }

        public int? IdEmpresa { get; set; }

        public int UsuarioCadastroId { get; set; }

        public string UsuarioCadastroCpf { get; set; }
        public string Observacao { get; set; }
        public bool UltimoRegistro { get; set; }
        public int? IdMotivo { get; set; }
    }

    public class CargaAvulsaValidacaoPlanilhaItem
    {
        public string Cpf { get; set; }
        public string PlacaCavalo { get; set; }
        public string Nome { get; set; }
        public string CnpjFilial { get; set; }
        public decimal Valor { get; set; }
        public string Observacao { get; set; }
        public string Mensagem { get; set; }
        public int? IdMotivo { get; set; }
    }
}
﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Estado;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class EstadoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IEstadoApp _estadoApp;

        public EstadoAtsController(IUserIdentity userIdentity, IEstadoApp estadoApp)
        {
            _userIdentity = userIdentity;
            _estadoApp = estadoApp;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idPais)
        {
            try
            {
                var estados = _estadoApp
                    .Consultar()
                    .Where(x => x.IdPais == idPais)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { x.IdEstado, x.Nome });

                return ResponderSucesso(estados);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ConsultarPorPais(int idPais)
        {
            try
            {
                var estados = _estadoApp
                    .Consultar()
                    .Where(x => x.IdPais == idPais)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { Codigo = x.IdEstado, Descricao = x.Nome });

                return ResponderSucesso(estados);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorBacenPais(int bacen)
        {
            try
            {
                var estados = _estadoApp
                    .Consultar()
                    .Where(x => x.Pais.BACEN == bacen)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { x.IdEstado, x.Nome });

                return ResponderSucesso(estados);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var estados = _estadoApp.ConsultaGrid(take, page, order, filters);

                return ResponderSucesso(estados);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idEstado)
        {
            try
            {
                var validationResult = _estadoApp.Inativar(idEstado);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estado inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idEstado)
        {
            try
            {
                var validationResult = _estadoApp.Reativar(idEstado);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estado reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(CadastroEstadoRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                var validationResult = new ValidationResult();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Apenas usuários administradores podem editar um estado.");

                if (@params.Nome == null || @params.Nome.Length < 1)
                    validationResult.Add("Descrição é obrigatória");
                if (@params.IdPais < 1)
                    validationResult.Add("País é obrigatório");


                if (!validationResult.IsValid)
                    ResponderErro(validationResult.ToString());

                var estado = new Estado
                {
                    IdPais = @params.IdPais,
                    Nome = @params.Nome,
                    Ativo = true,
                    DataAlteracao = DateTime.Now,
                    IBGE = @params.IBGE,
                    Sigla = @params.Sigla
                };

                validationResult = _estadoApp.Add(estado);

                return !validationResult.IsValid ? ResponderErro(validationResult.ToString()) : ResponderSucesso("Dados incluídos com sucesso.");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(CadastroEstadoRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                var validationResult = new ValidationResult();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                var estado = _estadoApp.Get(@params.IdEstado);
                if (estado == null)
                    validationResult.Add("Não foi possível localizar o estado.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                ReflectionHelper.CopyProperties(@params, estado);
                validationResult = _estadoApp.Update(estado);

                if (validationResult.IsValid)
                    return ResponderPadrao(true, "Dados incluídos com sucesso.");

                return ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idEstado)
        {
            try
            {
                var estado = _estadoApp.Get(idEstado);
                var retorno = new
                {
                    estado.IdEstado,
                    estado.Nome,
                    estado.IBGE,
                    estado.IdPais,
                    estado.Sigla
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetIdPorDescricao(string estado)
        {
            try
            {
                var ret = _estadoApp.Consultar(estado).FirstOrDefault();

                return ResponderSucesso(ret?.IdEstado);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
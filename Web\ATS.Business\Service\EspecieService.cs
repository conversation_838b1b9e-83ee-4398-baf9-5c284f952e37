﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class EspecieService : ServiceBase, IEspecieService
    {
        private readonly IEspecieRepository _especieRepository;

        public EspecieService(IEspecieRepository especieRepository)
        {
            _especieRepository = especieRepository;
        }

        /// <summary>
        /// Buscar espécie
        /// </summary>
        /// <param name="id">Código da espécie</param>
        /// <returns>Objeto Especie</returns>
        public Especie Get(int id)
        {
            return _especieRepository.Get(id);
        }

        /// <summary>
        /// Adicionar a espécie a base de dados
        /// </summary>
        /// <param name="especie">Dados da espécie</param>
        /// <returns></returns>
        public ValidationResult Add(Especie especie)
        {
            try
            {
                especie.DataHoraUltimaAtualizacao = DateTime.Now;

                var especieRepository = _especieRepository;
                especieRepository.Add(especie);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Especie AddReturningObject(Especie especie)
        {
            _especieRepository.Add(especie);
            return especie;
        }

        /// <summary>
        /// Atualizar o registro da espécie
        /// </summary>
        /// <param name="especie">Dados da espécie</param>
        /// <returns></returns>
        public ValidationResult Update(Especie especie)
        {
            try
            {
                especie.DataHoraUltimaAtualizacao = DateTime.Now;

                IEspecieRepository especieRepository = _especieRepository;
                especieRepository.Update(especie);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Especie UpdateReturningObject(Especie especie)
        {
            _especieRepository.Update(especie);
            return especie;
        }

        /// <summary>
        /// Retorna as espécies a partir dos dados de filtro
        /// </summary>
        /// <param name="descricao">Descrição da espécie</param>
        /// <returns>IQueryable de Especie</returns>
        public IQueryable<Especie> Consultar(string descricao)
        {
            if (string.IsNullOrEmpty(descricao))
                descricao = string.Empty;

            return _especieRepository.Consultar(descricao);
        }

        /// <summary>
        /// Inativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idEspecie)
        {
            try
            {
                IEspecieRepository especieRepository = _especieRepository;

                Especie especie = especieRepository.Get(idEspecie);
                if (!especie.Ativo)
                    return new ValidationResult().Add($"Espécie já desativado na base de dados.");

                especie.Ativo = false;

                especieRepository.Update(especie);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idEspecie)
        {
            try
            {
                IEspecieRepository especieRepository = _especieRepository;

                Especie especie = especieRepository.Get(idEspecie);
                if (especie.Ativo)
                    return new ValidationResult().Add($"Espécie já ativado na base de dados.");

                especie.Ativo = true;

                especieRepository.Update(especie);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna todas as especies ativas
        /// </summary>
        /// <returns>IQueryable de Especie</returns>
        public IQueryable<Especie> All()
        {
            return _especieRepository.All().Where(x => x.Ativo);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="dataBase"></param>
        /// <returns></returns>
        public List<Especie> GetEspeciesAtualizadas(DateTime dataBase)
        {
            return _especieRepository.GetIdsEspeciesAtualizadas(dataBase).ToList();
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var especies = _especieRepository.GetAll();

            especies = string.IsNullOrWhiteSpace(order?.Campo)
                    ? especies.OrderBy(x => x.IdEspecie)
                    : especies.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            especies = especies.AplicarFiltrosDinamicos<Especie>(filters);

            return new
            {
                totalItems = especies.Count(),
                items = especies.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.Ativo,
                    x.IdEspecie,
                    x.Descricao
                })
            };
        }

        public Especie GetPorDescricao(string especie)
        {
            return _especieRepository.FirstOrDefault(x => x.Descricao.ToLower() == especie.ToLower());
        }

        public ValidationResult AlterarStatus(int idEspecie)
        {
            try
            {
                var repository = _especieRepository;
                var especie = repository.Get(idEspecie);

                if (especie == null)
                    return new ValidationResult().Add("Registro não encontrado.");

                especie.Ativo = !especie.Ativo;
                repository.Update(especie);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}
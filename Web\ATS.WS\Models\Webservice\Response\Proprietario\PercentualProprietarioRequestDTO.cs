﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using System.Collections.Generic;
using System.Linq;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class PercentualProprietarioRequestDTO : RequestBase
    {
        public string DocumentoProprietario { get; set; }
        public List<PercentuaisTransferenciaDTO> PercentuaisTransferencias { get; set; }

        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);

            if (string.IsNullOrWhiteSpace(DocumentoProprietario))
                validacao.Add("É obrigatório o envio do campo DocumentoProprietario");
            else if (DocumentoProprietario.IsValidCPF() && DocumentoProprietario.IsValidCNPJ())
                validacao.Add("O campo DocumentoProprietario deve conter um documento válido");

            return validacao;
        }
    }

    public class PercentualProprietarioMotoristaRequestDTO : RequestBase
    {
        public string DocumentoProprietario { get; set; }
        public List<MotoristaProprietarioDTO> Motoristas { get; set; }

        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);

            if (string.IsNullOrWhiteSpace(DocumentoProprietario))
                validacao.Add("É obrigatório o envio do campo DocumentoProprietario");
            else if (DocumentoProprietario.IsValidCPF() && DocumentoProprietario.IsValidCNPJ())
                validacao.Add("O campo DocumentoProprietario deve conter um documento válido");

            if (Motoristas == null || !Motoristas.Any())
                validacao.Add("É obrigatório o envio de ao menos um registro no campo Motoristas");

            if (Motoristas.Any(c => !c.DocumentoMotorista.IsValidCPF()))
                validacao.Add("Os campos DocumentoMotorista devem conter apenas documentos válidos");

            return validacao;
        }
    }

    public class MotoristaProprietarioDTO
    {
        public string DocumentoMotorista { get; set; }
        public List<PercentuaisTransferenciaDTO> PercentuaisTransferencias { get; set; }
    }

    public class PercentuaisTransferenciaDTO
    {
        public ETipoEventoViagem TipoEventoViagem { get; set; }
        public decimal Percentual { get; set; }
    }

    public class PercentualReturnDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
    }
}

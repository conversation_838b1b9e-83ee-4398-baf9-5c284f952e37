﻿using System;
using System.Globalization;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class TransferenciaPixComprovanteResponse
    {
        public DateTime? TransactionDate { get; set; }
        
        public string TransactionDateFormatted => TransactionDate?.ToString("dd/MM/yyyy HH:mm:ss");

        public string EndToEndId { get; set; }

        public double? Value { get; set; }
        
        public string ValueFormatted => (Value ?? 0).ToString("C2", new CultureInfo("pt-br"));

        public string RemittanceInformation { get; set; }
        
        public int? IdTransacaoPixPortal { get; set; }
        
        public string DocumentoCliente { get; set; }
        
        public string TipoPagamento { get; set; }

        public string Ciot { get; set; }
        
        public int? IdViagem { get; set; }
        
        public int? IdViagemEvento { get; set; }

        public TransferenciaPixComprovanteResponseOrigem Origin { get; set; }

        public TransferenciaPixComprovanteResponseDestino Destiny { get; set; }
    }

    public class TransferenciaPixComprovanteResponseDestino
    {
        public string Name { get; set; }

        public string Identification { get; set; }

        public string IdentificationFormatted => Identification.FormatarCpfCnpjSafe();

        public string IspbName { get; set; }

        public string BankAgency { get; set; }

        public string BankAccount { get; set; }

        public string AccountType { get; set; }

        public string Key { get; set; }
    }

    public class TransferenciaPixComprovanteResponseOrigem
    {
        public string Name { get; set; }

        public string Identification { get; set; }

        public string IdentificationFormatted => Identification.FormatarCpfCnpjSafe();

        public string IspbName { get; set; }

        public string BankAgency { get; set; }

        public string BankAccount { get; set; }

        public string AccountType { get; set; }
    }
}
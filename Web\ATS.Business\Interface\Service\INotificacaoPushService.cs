﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface INotificacaoPushService
    {
        void EnviarRequest(IEnumerable<string> idsPush, string title, ETipoMensagemPush messageType, string message);
        MesagemPushModel EnviarPush(string query, List<string> imeis, string imei, int idEmpresa, string idFilial, int idGrupoUsuario, string modeloMenssagem, string apelido, int idTipoNotificacao, bool isTeste);
        MesagemPushModel EnviarPushGrupoUsuarios(string query, List<string> imeis, string imei, int idEmpresa, string idFilial, List<int> idGrupoUsuarios, string modeloMenssagem, string apelido, int idTipoNotificacao, bool isTeste);
        void DeletarFilhos(int idNotificacaoPush);
        string PreparaConsulta(string query, List<string> imeis, string imei, string idEmpresa, string idFilial);
        MesagemPushModel ValidarRegra(string query, string modeloMenssagem, int? idEmpresa, string apelido);
        NotificacaoPushModel ExecutarRegra(string query);
        IQueryable<NotificacaoPush> GetNotificacoesAtivas();
        ValidationResult Add(NotificacaoPush NotificacaoPush);
        ValidationResult Update(NotificacaoPush NotificacaoPush);
        ValidationResult Reativar(int idNotificacaoPush);
        ValidationResult Inativar(int idNotificacaoPush);
        NotificacaoPush Get(int id);

        object ConsultaGrid(int? idEmpresa,
            int? idFilial,
            string descricao,
            int Take,
            int Page,
            OrderFilters Order,
            List<QueryFilters> Filters);

        IQueryable<NotificacaoPush> Consultar(List<QueryFilters> queryFilters, OrderFilters orderFilters);
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Base
{
    [Serializable]
    public class FullTrustReportviewer : MarshalByRefObject
    {
        private readonly ReportViewer _fullTrust;
        public SubreportProcessingEventHandler SubreportProcessing { get; set; }

        public FullTrustReportviewer()
        {
            _fullTrust = new ReportViewer
            {
                ShowExportControls = false,
                ShowPrintButton = true,
                ShowZoomControl = true,
                SizeToReportContent = false,
                ShowReportBody = true,
                ShowDocumentMapButton = false,
                ShowFindControls = true
            };

            _fullTrust.LocalReport.SubreportProcessing += LocalReport_SubreportProcessing;
        }

        public void Initialize(string displayName, string reportPath, bool visible, ReportParameter[] reportParam, string reportRenderFormat, string deviceInfo, string repMainContent, List<string[]> repSubContent)
        {
            _fullTrust.LocalReport.EnableExternalImages = true;
            _fullTrust.LocalReport.DisplayName = displayName;
            _fullTrust.LocalReport.ReportEmbeddedResource = reportPath;
            _fullTrust.Visible = visible;
            _fullTrust.LocalReport.LoadReportDefinition(new StringReader(repMainContent));
            _fullTrust.LocalReport.SetParameters(reportParam);

            repSubContent.ForEach(x =>
            {
                _fullTrust.LocalReport.LoadSubreportDefinition(x[0], new StringReader(x[1]));
            });

            _fullTrust.LocalReport.DataSources.Clear();
        }

        public byte[] Render(string reportRenderFormat, string deviceInfo)
        {
            return _fullTrust.LocalReport.Render(reportRenderFormat, deviceInfo);
        }

        public void AddDataSources<T>(string p, IEnumerable<T> datatable)
        {
            _fullTrust.LocalReport.DataSources.Add(new ReportDataSource(p, datatable));
        }

        public static void LocalReport_SubreportProcessing(object sender, SubreportProcessingEventArgs e)
        {
            var lr = (LocalReport)sender;

            e.DataSources.Clear();

            if (!e.ReportPath.Contains("DataTable2"))
                return;
            
            var dt = (DataTable)lr.DataSources["DataTable2"].Value;
            var dv = new DataView(dt) {RowFilter = $"Id={e.Parameters["Id"].Values[0]}"};
            var rds = new ReportDataSource("DataTable2", dv.ToTable());
            e.DataSources.Add(rds);
        }
    }
}

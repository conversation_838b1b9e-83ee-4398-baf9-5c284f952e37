﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Web.Http;
using ATS.Application.Interface;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class CheckInController : BaseController
    {
        private readonly SrvCheckIn _srvCheckIn;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public CheckInController(BaseControllerArgs baseArgs, SrvCheckIn srvCheckIn, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _srvCheckIn = srvCheckIn;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        /// <summary>
        /// Integrar checkin
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string Integrar(IntegrarCheckInRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvCheckIn.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }


        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(string token, string cnpjAplicacao, string cnpjEmpresa, DateTime? dataInicial, DateTime? dataFinal, string placa, string cpf = "")
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    throw new Exception("Token inválido");

                var checkins = _srvCheckIn.Consultar(token, cnpjAplicacao, cnpjEmpresa, dataInicial, dataFinal, placa, cpf);

                return  new JsonResult().Responde(new Retorno<List<CheckInModel>>(true, checkins));
            }
            catch(Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarPorEmpresa(string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    throw new Exception("Token inválido");

                var checkins = _srvCheckIn.ConsultarPorEmpresa(token, cnpjAplicacao, cnpjEmpresa);

                return new JsonResult().Responde(new Retorno<List<CheckInMobileModel>>(true, checkins));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
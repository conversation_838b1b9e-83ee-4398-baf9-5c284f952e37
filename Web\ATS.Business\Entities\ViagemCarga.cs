﻿using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    /// <summary>
    /// Realiza o vinculo entre as Cargas relacionadas a determinada Viagem
    /// </summary>
    [TrackChanges]
    public class ViagemCarga
    {
        /// <summary>
        /// Código da Viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código da Carga
        /// </summary>
        public int IdCarga { get; set; }

        /// <summary>
        /// Quantidade carregada na viagem.. 
        /// </summary>
        public decimal  QuantidadeCarregada{ get; set; }
        
        // Referência
        public virtual Viagem Viagem { get; set; }
    }
}
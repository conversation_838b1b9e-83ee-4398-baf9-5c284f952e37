﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using System.Collections.Generic;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IMotoristaService : IService<Motorista>
    {
        bool Any(string cpf, int idempresa, bool? ativo = null);
        Motorista Get(int id);
        Motorista GetWithChilds(int id, int? idUsuarioLogOn);
        Motorista GetWithChilds(string cpf, int? idUsuarioLogOn);
        ValidationResult Add(Motorista motorista, int idUsuario, bool ignorePermissaoPerfil = false);
        ValidationResult Update(Motorista motorista, int idUsuario, bool ignorePermissaoPerfil = false);
        Motorista GetPorCpf(string cpf, bool withIncludes = true);
        int? GetIdPorCpf(string cpf, int? idEmpresa = null, bool? ativo = true);
        byte[] GetFoto(int id);
        IQueryable<Motorista> GetAllByIdEmpresa(int idEmpresa);
        IQueryable<Motorista> QueryById(int id);
        Motorista Get(string cpf);
        List<Veiculo> GetVeiculosMotorista(string cpfMotorista, int? idEmpresa);
        ValidationResult DesvincularMotoristaVeiculo(int idEmpresa, string cpfMotorista, string placa);
        IQueryable<Motorista> GetPorCpfQueryable(string cpf);
        ValidationResult AlterarStatus(int idMotorista);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters,bool ativo);
        byte[] GerarRelatorioGridMotoristas(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string logo, string extensao);
        UsuarioDocumento GetDocumentoCnhPorMot(Motorista mot);
        int GetQtdMotoristasCadastradosByUser(int idUsuario);
        bool Any(string cpf, bool? ativo = null);
        Motorista GetFromAllTables(string cpfCnpj);
        List<Motorista> GetMotoristasAtualizados(int idEmpresa, DateTime dataAtualizacao);
    }
}
﻿using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;

namespace ATS.WS.Controllers
{
    public class VeiculoController : BaseController
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly SrvVeiculo _srvVeiculo;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public VeiculoController(BaseControllerArgs baseArgs, IParametrosApp parametrosApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IVersaoAnttLazyLoadService versaoAntt, SrvVeiculo srvVeiculo, ITagExtrattaApp tagExtrattaApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
            _srvVeiculo = srvVeiculo;
            _tagExtrattaApp = tagExtrattaApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Integrar(VeiculoIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                    @params.CNPJEmpresa = @params.CNPJAplicacao;

                return new JsonResult().Responde(_srvVeiculo.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string ConsultarTipoCarreta(TipoCarretaConsultarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvVeiculo.ConsultarTipoCarreta(@params.DataBase, @params.CNPJEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string ConsultarTipoCavalo(TipoCavaloConsultarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvVeiculo.ConsultarTipoCavalo(@params.DataBase, @params.CNPJEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarNumeroPlacas(EmpresaRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvVeiculo.ConsultarNumeroPlacas(string.IsNullOrEmpty(@params.CNPJEmpresa) ? @params.CNPJ : @params.CNPJEmpresa));
            }
            catch (Exception ex)
            {
                Logger.Error(ex);
                return new JsonResult().Mensagem(ex.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarVeiculo(string token, string cnpjAplicacao, string placa, long? numeroFrota, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                if (numeroFrota != null)
                    return
                        new JsonResult().Responde(_srvVeiculo.ConsultarVeiculoPorNumeroFrota(token, cnpjAplicacao,
                            numeroFrota.Value));


                return new JsonResult().Responde(_srvVeiculo.ConsultarVeiculoPorPlaca(token, cnpjAplicacao, placa, cnpjEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Alterar(VeiculoIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                    @params.CNPJEmpresa = @params.CNPJAplicacao;

                return new JsonResult().Responde(_srvVeiculo.Alterar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var request = Mapper.Map<ConsultarFrotaTransportadorRequestModel, ConsultarFrotaTransportadorRequest>(@params);
                var response = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.ConsultarFrotaTransportador(request)
                    : _ciotV3App.ConsultarFrotaTransportador(request);

                return new JsonResult().Responde(response);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public string IntegrarVeiculoFila(VeiculoIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();
                
                return new JsonResult().Responde(_srvVeiculo.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(true)]
        public string ConsultarPlacaFornecedor(string token, string cnpjAplicacao, string cnpjEmpresa, string placa, FornecedorEnum fornecedor)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_tagExtrattaApp.ConsultarPlacasFornecedor(placa, fornecedor));
            }
            catch (Exception ex)
            {
                Logger.Error(ex);
                return new JsonResult().Mensagem(ex.Message);
            }
        }
    }
}
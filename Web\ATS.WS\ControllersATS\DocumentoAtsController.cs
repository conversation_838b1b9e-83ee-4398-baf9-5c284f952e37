﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class DocumentoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IDocumentoService _documentoService;
        private readonly IDocumentoApp _documentoApp;
        private readonly SrvDocumento _srvDocumento;

        public DocumentoAtsController(IUserIdentity userIdentity, IDocumentoService documentoService, IDocumentoApp documentoApp, SrvDocumento srvDocumento)
        {
            _userIdentity = userIdentity;
            _documentoService = documentoService;
            _documentoApp = documentoApp;
            _srvDocumento = srvDocumento;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idFilial, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {

            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;
                
                if (idFilial.HasValue)
                {
                    if (filters == null)
                        filters = new List<QueryFilters>();

                    filters.Add(new QueryFilters
                    {
                        Campo = "IdFilial",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = idFilial.Value.ToString()
                    });
                }

                var documentos = _documentoService.ConsultaGrid(idEmpresa, descricao, take, page, order, filters);

                return ResponderSucesso(documentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idEmpresa, int? idFilial)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;

                var documentos = _documentoApp.GetPorEmpresa(idEmpresa, idFilial);

                return ResponderSucesso(documentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idDocumento)
        {
            try
            {
                var validationResult = _documentoApp.Inativar(idDocumento);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Documento inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idDocumento)
        {
            try
            {
                var validationResult = _documentoApp.Reativar(idDocumento);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Documento reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(int? idDocumento, int? idEmpresa, int? idFilial, bool possuirValidade, string descricao, bool obrigaDocOriginal, int? diasValidade)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                if (!idEmpresa.HasValue)
                    return ResponderErro("A Empresa é obrigatória");

                if (string.IsNullOrWhiteSpace(descricao))
                    return ResponderErro("A descrição é obrigatória");

                var documento = new Documento();

                if (idDocumento.HasValue)
                    documento = _documentoApp.Get(idDocumento.Value);

                documento.Descricao = descricao;
                documento.IdEmpresa = idEmpresa.Value;
                documento.IdFilial = idFilial;
                documento.PossuirValidade = possuirValidade;
                documento.Ativo = true;
                documento.ObrigaDocOriginal = obrigaDocOriginal;
                documento.DiasValidade = diasValidade;

                var cadastrar = idDocumento.HasValue
                    ? _documentoApp.Update(documento)
                    : _documentoApp.Add(documento);

                return cadastrar.IsValid
                    ? (idDocumento.HasValue ? ResponderSucesso("Dados atualizados com sucesso.") : ResponderSucesso("Dados cadastrados com sucesso."))
                    : ResponderErro(cadastrar.ToFormatedMessage());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idDocumento)
        {
            try
            {
                var documento = _documentoApp.Get(idDocumento);

                var retorno = new
                {
                    documento.IdDocumento,
                    documento.PossuirValidade,
                    documento.Descricao,
                    documento.Ativo,
                    RazaoSocialEmpresa = documento.Empresa?.RazaoSocial,
                    RazaoSocialFilial = documento.Filial?.RazaoSocial,
                    documento.IdEmpresa,
                    documento.IdFilial,
                    documento.ObrigaDocOriginal,
                    documento.DiasValidade
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridDocumento(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            var report = _srvDocumento.GerarRelatorioGridDocumentos(filtrosGridModel.IdEmpresa, string.Empty, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de documentos.{filtrosGridModel.Extensao}");
        }
    }
}
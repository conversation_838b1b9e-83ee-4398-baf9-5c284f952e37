﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Linq;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class MensagemGrupoUsuarioService : ServiceBase, IMensagemGrupoUsuarioService
    {
        private readonly IMensagemGrupoUsuarioRepository _mensagemGrupoUsuarioRepository;
        private readonly IMensagemGrupoDestinatarioRepository _mensagemGrupoDestinatarioRepository;
        private readonly IGruposUsuariosRepository _gruposUsuariosRepository;

        public MensagemGrupoUsuarioService(IMensagemGrupoUsuarioRepository mensagemGrupoUsuarioRepository, IMensagemGrupoDestinatarioRepository mensagemGrupoDestinatarioRepository,
            IGruposUsuariosRepository gruposUsuariosRepository)
        {
            _mensagemGrupoUsuarioRepository = mensagemGrupoUsuarioRepository;
            _mensagemGrupoDestinatarioRepository = mensagemGrupoDestinatarioRepository;
            _gruposUsuariosRepository = gruposUsuariosRepository;
        }

        public IQueryable<MensagemGrupoUsuario> GetGruposDoUsuario(int idUsuario)
        {
            return _mensagemGrupoUsuarioRepository.GetGruposDoUsuario(idUsuario);
        }

        /// <summary>
        /// Retorna os usuários membros de um grupo
        /// </summary>
        /// <param name="idGrupo"></param>
        /// <returns></returns>
        public IEnumerable<object> GetUsuariosPeloGrupo(int idGrupo)
        {
            var retorno = _mensagemGrupoDestinatarioRepository
                    .Include(x => x.UsuarioMembroGrupo)
                    .Where(x => x.IdGrupoUsuario == idGrupo)
                    .Select(x => new
                    {
                        IdUsuario = x.UsuarioMembroGrupo.IdUsuario,
                        Nome = x.UsuarioMembroGrupo.Nome,
                        IdGrupoUsuario = x.IdGrupoUsuario
                    }).ToList();

            return retorno;
        }

        public IEnumerable<int> GetIdsUsuariosPeloGrupo(int idGrupo)
        {
            var retorno =
                _mensagemGrupoDestinatarioRepository
                    .Include(x => x.UsuarioMembroGrupo)
                    .Where(x => x.IdGrupoUsuario == idGrupo).Select(x => x.IdUsuarioDestinatario);

            return retorno;
        }

        public void DeletarGrupo(int idGrupo)
        {
            var membrosGrupo = GetIdsUsuariosPeloGrupo(idGrupo);

            foreach (var membro in membrosGrupo)
            {
                _mensagemGrupoDestinatarioRepository.DeletarUsuarios(membro);
            }

            _mensagemGrupoUsuarioRepository.DeletarGrupo(idGrupo);
        }

        public MensagemGrupoUsuario AddGrupo(MensagemGrupoUsuario grupoUsuarios)
        {
            return _mensagemGrupoUsuarioRepository.AddGrupo(grupoUsuarios);
        }

        public ValidationResult AddUsuarioParaGrupo(int idGrupo, int idUsuario)
        {

            try
            {
                var grupo = _mensagemGrupoUsuarioRepository
                    .Include(x => x.MensagemGrupoDestinatario)    
                    .Where(x => x.IdGrupoUsuario == idGrupo).FirstOrDefault();
                //var usuariosGrupo = _mensagemGrupoUsuarioRepository
                //                    .Include(p => p.MensagemGrupoDestinatario).Where(x => x.IdGrupoUsuario == idGrupo);

                if (!grupo.MensagemGrupoDestinatario.Any(x => x.IdUsuarioDestinatario == idUsuario))
                {
                    var destinatario = new MensagemGrupoDestinatario
                    {
                        IdGrupoUsuario = idGrupo,
                        IdUsuarioDestinatario = idUsuario
                    };
                    _mensagemGrupoUsuarioRepository.AddUsuarioParaGrupo(destinatario);
                }
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }

            return new ValidationResult();
        }

        public void RemoverUsuarioDoGrupo(int idGrupo, int idUsuario)
        {
            var gruposUsuarioDestinatario =
                _mensagemGrupoDestinatarioRepository
                    .Find(x => x.IdGrupoUsuario == idGrupo && x.IdUsuarioDestinatario == idUsuario).ToList();
            foreach (var grupoUsuarioDestinatario in gruposUsuarioDestinatario)
            {
                _mensagemGrupoUsuarioRepository.RemoverUsuarioDoGrupo(grupoUsuarioDestinatario);
            }
        }

        public IEnumerable<MensagemGrupoUsuario> GetGruposUsuarioPorIdUsuario(int idUsuario)
        {
            return _mensagemGrupoUsuarioRepository.GetGruposUsuarioPorIdUsuario(idUsuario);
        }

        public GruposUsuarios AddMensagemParaGrupo(GruposUsuarios grupoMensagem)
        {
            return _gruposUsuariosRepository.Add(grupoMensagem);
        }


    }
}
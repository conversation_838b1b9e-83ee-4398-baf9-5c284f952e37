﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using Newtonsoft.Json;

namespace ATS.WS.ControllersATS
{
    public class TriagemProtocoloAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IViagemApp _viagemApp;
        private readonly SrvCredenciamento _srvCredenciamento;
        private readonly ICredenciamentoApp _credenciamentoApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly ICredenciamentoService _credenciamentoService;
        private readonly SrvTriagemProtocolo _srvTriagemProtocolo;
        private readonly SrvProtocolo _srvProtocolo;

        public TriagemProtocoloAtsController(IUserIdentity userIdentity, IViagemApp viagemApp, SrvCredenciamento srvCredenciamento, ICredenciamentoApp credenciamentoApp, IUsuarioApp usuarioApp, 
            IEstabelecimentoApp estabelecimentoApp, IProtocoloApp protocoloApp, ICredenciamentoService credenciamentoService, SrvTriagemProtocolo srvTriagemProtocolo, SrvProtocolo srvProtocolo)
        {
            _userIdentity = userIdentity;
            _viagemApp = viagemApp;
            _srvCredenciamento = srvCredenciamento;
            _credenciamentoApp = credenciamentoApp;
            _usuarioApp = usuarioApp;
            _estabelecimentoApp = estabelecimentoApp;
            _protocoloApp = protocoloApp;
            _credenciamentoService = credenciamentoService;
            _srvTriagemProtocolo = srvTriagemProtocolo;
            _srvProtocolo = srvProtocolo;
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetCredenciamento(int idProtocolo)
        {
            try
            {
                var credenciamento = _srvCredenciamento.GetCredenciamentoPorProtocolo(idProtocolo);
                var associacoes = credenciamento?.Estabelecimento?.EstabelecimentoAssociacoesEstabelecimento;
                var usuarioAssociado = false;
                var permiteAlterarDocumentosPesoChegada = false;
                if (associacoes != null)
                    foreach (var item in associacoes)
                    {
                        var credenciamentoAssociacao = _credenciamentoApp.Get(credenciamento.IdEmpresa, item.IdAssociacao);
                        if (credenciamentoAssociacao != null)
                        {
                            var hasEstabelecimento = _usuarioApp.HasEstabelecimento(_userIdentity.IdUsuario,
                                credenciamentoAssociacao.IdEstabelecimentoBase);
                            if (hasEstabelecimento)
                                usuarioAssociado = true;
                        }

                        permiteAlterarDocumentosPesoChegada =
                            usuarioAssociado && associacoes.Any(x => x.Associacao.PermiteAlterarDocumentosPesoChegada);
                    }

                var retorno = new
                {
                    UsuarioLogadoAssociado = usuarioAssociado,
                    credenciamento?.IdEstabelecimentoBase,
                    credenciamento?.IdEmpresa,
                    PermiteAlterarDocumentosPesoChegada = permiteAlterarDocumentosPesoChegada
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, int take, int page, 
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                if (filters == null)
                    filters = new List<QueryFilters>();

                var idEstabelecimentoBase = new List<int>();
                var associacoes = new List<KeyValuePair<int, int>>();

                var estabelecimentosUsuarioLogado = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario);

                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                {
                    idEstabelecimentoBase = estabelecimentosUsuarioLogado?.Select(x => x.IdEstabelecimento)
                        .ToList();
                }

                //Funcionalidade Associacao
                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento && estabelecimentosUsuarioLogado != null)
                {
                    var estabelecimentos = estabelecimentosUsuarioLogado;
                    foreach (var item in estabelecimentos)
                    {
                        var estabelecimentosEmpresas =
                            _estabelecimentoApp.GetIdEstabelecimentosAssociadosLiberacaoProtocolo(
                                item.IdEstabelecimento);
                        if (estabelecimentosEmpresas != null && estabelecimentosEmpresas.Any())
                            associacoes.AddRange(estabelecimentosEmpresas);
                    }
                }

                var protocolos = _protocoloApp.ConsultarTriagemProtocolo(idEmpresa, idEstabelecimentoBase,
                    idEstabelecimento, idAssociacao, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial, 
                    dataPagamentoFinal, associacoes, take, page, order, filters);

                return ResponderSucesso(protocolos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPagamentos(int? idProtocolo)
        {
            try
            {
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado. ");

                var pagamentos = _protocoloApp.ConsultarPagamentos(idProtocolo.Value, null, null, null, null);

                return ResponderSucesso(pagamentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridPagamentos(int? idProtocolo, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            try
            {
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado. ");

                var pagamentos = _protocoloApp.ConsultarPagamentos(idProtocolo.Value, take, page, order, filters);

                return ResponderSucesso(pagamentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ReverterRejeicao(int idProtocolo)
        {
            try
            {
                _protocoloApp.ReverterRejeicao(idProtocolo);

                return ResponderSucesso("Protocolo revertido com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public void DefinirEmAnalise(int idProtocolo, int perfilUsuario)
        {
            var protocolo = _protocoloApp.Get(idProtocolo);
            if (protocolo == null)
                throw new Exception($"Nenhum protocolo encontrado para o id {idProtocolo}!");

            if (((protocolo.StatusProtocolo == EStatusProtocolo.EmTransito && protocolo.DataRecebidoEmpresa != null) || protocolo.StatusProtocolo == EStatusProtocolo.Recebido) && perfilUsuario == (int)EPerfilSotran.Empresa)
                _protocoloApp.EmAnalise(idProtocolo);
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(EStatusProtocoloEvento? status, int? idProtocolo, int? idMotivo,
            DateTime? dataPrevisaoPagamento, string detalhamento)
        {
            try
            {
                var usuario = new Usuario
                    {IdUsuario = _userIdentity.IdUsuario, Nome = _userIdentity.Nome, CPFCNPJ = _userIdentity.CpfCnpj};
                
                System.Web.HttpContext.Current.Items["IdUsuarioLogado"] = _userIdentity.IdUsuario;
                System.Web.HttpContext.Current.Items["CpfUsuarioLogado"] = _usuarioApp.Get(_userIdentity.IdUsuario).CPFCNPJ;

                if (!status.HasValue)
                    return ResponderErro("Status não informado. ");
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado. ");

                switch (status)
                {
                    case EStatusProtocoloEvento.Aprovado:
                        var validationResult = _protocoloApp.Aprovar(idProtocolo.Value, dataPrevisaoPagamento, _userIdentity.IdUsuario);
                        return validationResult.IsValid
                            ? ResponderSucesso("Protocolo aprovado com sucesso!")
                            : ResponderErro(validationResult.ToString());

                    case EStatusProtocoloEvento.Rejeitado:
                        if (!idMotivo.HasValue)
                            return ResponderErro("Motivo deve ser informado para rejeição de um protocolo");

                        var validationResultReprovado =
                            _protocoloApp.Rejeitar(idProtocolo.Value, idMotivo.Value, detalhamento, usuario);
                        return validationResultReprovado.IsValid
                            ? ResponderSucesso("Protocolo rejeitado com sucesso!")
                            : ResponderErro(validationResultReprovado.ToString());

                    //case EStatusProtocoloEvento.Liberado:
                    //    var validationResultLiberacao = _protocoloApp.Liberar(idProtocolo.Value);
                    //    return validationResultLiberacao.IsValid
                    //        ? ResponderSucesso("Protocolo liberado com sucesso!")
                    //        : ResponderErro(validationResultLiberacao.ToString());
                    default:
                        return ResponderErro("Ação não informada para   a alteração de status.");
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult RejeitarPagamento(int? idProtocoloEvento, int? idMotivo, string detalhamento)
        {
            try
            {
                var usuario = new Usuario
                    {IdUsuario = _userIdentity.IdUsuario, Nome = _userIdentity.Nome, CPFCNPJ = _userIdentity.CpfCnpj};
                
                if (!idProtocoloEvento.HasValue)
                    return ResponderErro("Pagamento não informado.");

                var validationResult =
                    _protocoloApp.RejeitarPagamento(idProtocoloEvento.Value, idMotivo, detalhamento, usuario);
                return validationResult.IsValid
                    ? ResponderSucesso("Rejeição do pagamento realizada com sucesso!")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult VerificarEmpresa(int? idEstabelecimento, int? idUsuario)
        {
            try
            {
                var estabelecimentos = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario);
                var idEstabelecimentoUs = estabelecimentos?.FirstOrDefault()?.IdEstabelecimento;

                var credenciamento = _credenciamentoService.GetByIdEstabelecimentoBase(idEstabelecimentoUs).Where(x => x.Status == EStatusCredenciamento.Aprovado).ToList();

                if (credenciamento.Count == 1)
                    return ResponderSucesso(string.Empty, new { credenciamento.FirstOrDefault()?.Empresa?.IdEmpresa, credenciamento.FirstOrDefault()?.Empresa?.RazaoSocial });

                return ResponderErro(string.Empty);

            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAnexoProtocolo(int? idProtocolo)
        {
            try
            {
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado.");

                var anexos = _protocoloApp.ConsultarAnexos(idProtocolo.Value);

                return ResponderSucesso(anexos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAnexoViagem(int? idViagemEvento)
        {
            try
            {
                if (!idViagemEvento.HasValue)
                    return ResponderErro("Viagem não informada.");

                var anexos = _viagemApp.ConsultarDocumentos(idViagemEvento.Value);

                return ResponderSucesso(anexos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult CalcularTaxaAntecipacao(int? idProtocolo, DateTime dataAntecipacao)
        {
            try
            {
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado.");

                return ResponderSucesso(_protocoloApp.CalcularTaxaAntecipacao(idProtocolo.Value, dataAntecipacao));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult EnviarSolicitacaoAntecipacao(int idProtocolo, DateTime dataPagamentoAntecipado,
            decimal valorPagamentoAntecipado)
        {
            try
            {
                var validationResult =
                    _protocoloApp.EnviarSolicitacaoAntecipacao(idProtocolo, valorPagamentoAntecipado,
                        dataPagamentoAntecipado);
                return validationResult.IsValid
                    ? ResponderSucesso("Solicitação enviada com sucesso!")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderSucesso(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GetRelatorio(string sessionKey, string idsProtocolo)
        {
            var listaIdsProtocolo = idsProtocolo?.Split(',').Select(x => Convert.ToInt32(x)).ToList();

            var report = _srvTriagemProtocolo.GerarRelatorio(sessionKey, listaIdsProtocolo);

            return File(report, ConstantesUtils.PdfMimeType, $"Relaório de protocolos.pdf");
            }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarPesoChegada(int idProtocoloEvento, decimal? pesoChegada, int? numeroSacas)
        {
            try
            {
                var validationResult =
                    _protocoloApp.AlterarPesoChegada(idProtocoloEvento, pesoChegada, numeroSacas);

                return validationResult.IsValid
                    ? ResponderSucesso("Alteração realizada com sucesso!")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public object ConsultarDescontoPorId(int idProtocoloEvento)
        {
            try
            {
                var ret = _protocoloApp.ConsultarDescontoPorId(idProtocoloEvento);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarUsuarioAssociado(int idEstabelecimentoBase)
        {
            try
            {
                return ResponderSucesso(_credenciamentoApp.VerificarEstabelecimentoBaseAssociacao(idEstabelecimentoBase));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GerarQRCodeViagemEvento(int idViagemEvento)
        {
            try
            {
                var retorno = _srvTriagemProtocolo.GerarQRCodeViagemEvento(idViagemEvento);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AddOcorrencia(int IdProtocoloEvento, int IdMotivo, string Detalhamento)
        {
            try
            {
                var usuario = new Usuario
                    {IdUsuario = _userIdentity.IdUsuario, Nome = _userIdentity.Nome, CPFCNPJ = _userIdentity.CpfCnpj};
                
                var retorno = _srvTriagemProtocolo.AddOcorrencia(IdProtocoloEvento, IdMotivo, Detalhamento, usuario);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public new JsonResult Resolver(int idProtocoloEvento)
        {
            try
            {
                _protocoloApp.Resolver(idProtocoloEvento);
                return ResponderSucesso("Resolvido com sucesso!", null);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ResolverProtocolo(int idProtocolo)
        {
            try
            {
                var usuario = new Usuario
                    {IdUsuario = _userIdentity.IdUsuario, Nome = _userIdentity.Nome, CPFCNPJ = _userIdentity.CpfCnpj};
                
                _protocoloApp.ResolverProtocolo(idProtocolo, usuario);
                return ResponderSucesso("Resolvido com sucesso", null);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]        
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorToken(string token, int? idProtocolo, bool analisado = false)
        {
            try
            {
                var usuario = new Usuario
                    {IdUsuario = _userIdentity.IdUsuario, Nome = _userIdentity.Nome, CPFCNPJ = _userIdentity.CpfCnpj};
                
                var evento = _protocoloApp.ConsultarPorToken(token, usuario, idProtocolo, null, analisado);
                return ResponderSucesso(evento);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult IniciarTransito(int IdProtocolo, string CodigoRastreamento)
        {
            try
            {
                _srvProtocolo.IniciarTransito(IdProtocolo, CodigoRastreamento, (EPerfil)_userIdentity.Perfil);

                return ResponderSucesso("Protocolo atualizado para em trânsito");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult SetAnalisado(int? IdProtocoloEvento, bool analisado)
        {
            try
            {
                _protocoloApp.SetAnalisado(IdProtocoloEvento, analisado);

                return ResponderSucesso("Protocolo atualizado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridTriagem(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioGridTriagemDTO>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            var idEstabelecimentoBase = new List<int>();
            var associacoes = new List<KeyValuePair<int, int>>();
            
            if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                idEstabelecimentoBase = _usuarioApp.GetUsuariosEstabelecimento(_userIdentity.IdUsuario)?.Select(x => x.IdEstabelecimento)
                    .ToList();

            if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento && idEstabelecimentoBase != null && idEstabelecimentoBase.Any())
            {
                foreach (var item in idEstabelecimentoBase)
                {
                    var estabelecimentosEmpresas =
                        _estabelecimentoApp.GetIdEstabelecimentosAssociadosLiberacaoProtocolo(
                            item);
                    if (estabelecimentosEmpresas != null && estabelecimentosEmpresas.Any())
                        associacoes.AddRange(estabelecimentosEmpresas);
                }
            }

            var relatorio = _srvProtocolo.GerarRelatorioTriagemProtocolo(filtroGridModel.IdEmpresa, idEstabelecimentoBase,
                filtroGridModel.IdEstabelecimento, filtroGridModel.IdAssociacao, filtroGridModel.DataGeracaoInicial, filtroGridModel.DataGeracaoFinal, filtroGridModel.DataPagamentoInicial,
                filtroGridModel.DataPagamentoFinal, associacoes, filtroGridModel.Order, filtroGridModel.Filters, filtroGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(relatorio, mimeType, $"Triagem de protocolo.{filtroGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetDadosAnaliseAbono(int idProtocolo, int idViagemEvento)
        {
            try
            {
                var response = _protocoloApp.GetDadosAnaliseAbono(idProtocolo, idViagemEvento);
                return ResponderSucesso(string.Empty, response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Common.Request;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services.ViagemServices
{
    public class IntegracaoEventoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly SrvViagem _srvViagem;

        public IntegracaoEventoViagem(IVersaoAnttLazyLoadService versaoAntt, IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaRepository empresaRepository, SrvViagem srvViagem)
        {
            _versaoAntt = versaoAntt;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaRepository = empresaRepository;
            _srvViagem = srvViagem;
        }

        public AdicionarEventosViagemResponseModel AdicionarEventosViagem(AdicionarEventosViagemRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return AdicionarEventosViagemV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return AdicionarEventosViagemV3(@params);
                default:
                    return AdicionarEventosViagemV2(@params, isApi);
            }
        }
        
        private AdicionarEventosViagemResponseModel AdicionarEventosViagemV2(AdicionarEventosViagemRequestModel @params, bool isApi = false)
        {
            var viagemQuery = !string.IsNullOrWhiteSpace(@params.NumeroControle)
                ? _viagemApp.Find(v => v.NumeroControle == @params.NumeroControle)
                : _viagemApp.Find(v => v.IdViagem == @params.IdViagem);

            var viagem = viagemQuery
                .Select(v => new
                {
                    v.IdViagem,
                    v.NumeroControle,
                    v.PesoSaida,
                    v.PedagioBaixado,
                    v.HabilitarDeclaracaoCiot,
                    v.NaturezaCarga
                })
                .FirstOrDefault();

            if (viagem == null)
                return new AdicionarEventosViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma viagem encontrado com a IdViagem/NumeroControle informado."
                };

            if (!string.IsNullOrWhiteSpace(@params.NumeroControle) && @params.IdViagem.HasValue &&
                (@params.NumeroControle != viagem.NumeroControle || @params.IdViagem != viagem.IdViagem))
                return new AdicionarEventosViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Numero de controle {0} e id viagem {1} não pertencem ao mesmo registro."
                        .FormatEx(@params.NumeroControle, @params.IdViagem)
                };

            //
            var alterarViagem = new ViagemIntegrarRequestModel
            {
                CNPJEmpresa = @params.CNPJEmpresa,
                CNPJAplicacao = @params.CNPJAplicacao,
                Token = @params.Token,
                DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                NomeUsuarioAudit = @params.NomeUsuarioAudit,
                IdViagem = viagem.IdViagem,
                NumeroControle = @params.NumeroControle,
                PesoSaida = viagem.PesoSaida,
                PedagioBaixado = viagem.PedagioBaixado,
                HabilitarDeclaracaoCiot = viagem.HabilitarDeclaracaoCiot,
                NaturezaCarga = viagem.NaturezaCarga
            };

            alterarViagem.ViagemEventos = new List<ViagemEventoIntegrarModel>();

            foreach (var viagemEventoParams in @params.ViagemEventos)
            {
                if (viagemEventoParams.IdViagemEvento.HasValue)
                    continue;
                
                var evento = new ViagemEventoIntegrarModel
                {
                    IdViagemEvento = viagemEventoParams.IdViagemEvento,
                    NumeroControle = viagemEventoParams.NumeroControle,
                    ValorPagamento = viagemEventoParams.ValorPagamento,
                    ValorBruto = viagemEventoParams.ValorPagamento,
                    TipoEvento = viagemEventoParams.TipoEvento,
                    HabilitarPagamentoCartao = viagemEventoParams.HabilitarPagamentoCartao ?? false,
                    Status = viagemEventoParams.Status ?? EStatusViagemEvento.Aberto
                };

                alterarViagem.ViagemEventos.Add(evento);
            }

            var retorno = _srvViagem.Alterar(alterarViagem, isApi);

            return new AdicionarEventosViagemResponseModel
            {
                Sucesso = retorno.Sucesso,
                Mensagem = retorno.Mensagem
            };
        }
        
        private AdicionarEventosViagemResponseModel AdicionarEventosViagemV3(AdicionarEventosViagemRequestModel @params)
        {
            var viagemQuery = !string.IsNullOrWhiteSpace(@params.NumeroControle)
                ? _viagemApp.Find(v => v.NumeroControle == @params.NumeroControle)
                : _viagemApp.Find(v => v.IdViagem == @params.IdViagem);

            var viagem = viagemQuery
                .Select(v => new
                {
                    v.IdViagem,
                    v.NumeroControle,
                    v.PesoSaida,
                    v.PedagioBaixado,
                    v.HabilitarDeclaracaoCiot,
                    v.NaturezaCarga
                })
                .FirstOrDefault();

            if (viagem == null)
                return new AdicionarEventosViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma viagem encontrado com a IdViagem/NumeroControle informado."
                };

            if (string.IsNullOrWhiteSpace(@params.NumeroControle) && @params.IdViagem.HasValue &&
                (@params.NumeroControle != viagem.NumeroControle || @params.IdViagem != viagem.IdViagem))
                return new AdicionarEventosViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Numero de controle {0} e id viagem {1} não pertencem ao mesmo registro."
                        .FormatEx(@params.NumeroControle, @params.IdViagem)
                };

            //
            var alterarViagem = new ViagemIntegrarRequestModel
            {
                CNPJEmpresa = @params.CNPJEmpresa,
                CNPJAplicacao = @params.CNPJAplicacao,
                Token = @params.Token,
                DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                NomeUsuarioAudit = @params.NomeUsuarioAudit,
                IdViagem = viagem.IdViagem,
                NumeroControle = @params.NumeroControle,
                PesoSaida = viagem.PesoSaida,
                PedagioBaixado = viagem.PedagioBaixado,
                HabilitarDeclaracaoCiot = viagem.HabilitarDeclaracaoCiot,
                NaturezaCarga = viagem.NaturezaCarga
            };


            alterarViagem.ViagemEventos = new List<ViagemEventoIntegrarModel>();

            foreach (var viagemEventoParams in @params.ViagemEventos)
            {
                if (viagemEventoParams.IdViagemEvento.HasValue)
                    continue;

                var evento = new ViagemEventoIntegrarModel
                {
                    IdViagemEvento = viagemEventoParams.IdViagemEvento,
                    NumeroControle = viagemEventoParams.NumeroControle,
                    ValorPagamento = viagemEventoParams.ValorPagamento,
                    ValorBruto = viagemEventoParams.ValorPagamento,
                    TipoEvento = viagemEventoParams.TipoEvento,
                    Status = viagemEventoParams.Status ?? EStatusViagemEvento.Aberto
                };

                alterarViagem.ViagemEventos.Add(evento);
            }

            var retorno = _srvViagem.Alterar(alterarViagem);

            return new AdicionarEventosViagemResponseModel
            {
                Sucesso = retorno.Sucesso,
                Mensagem = retorno.Mensagem
            };
        }
    }
}
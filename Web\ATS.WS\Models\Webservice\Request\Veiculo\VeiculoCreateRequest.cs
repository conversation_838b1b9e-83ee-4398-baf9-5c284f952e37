﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Request.Veiculo
{
    public class VeiculoCreateRequest
    {
        public int? IdVeiculo { get; set; }

        public int? IdEmpresa { get; set; }

        public int? IdFilial { get; set; }

        public int? IdProprietario { get; set; }

        public int? IdMotorista { get; set; }

        public int? IdUsuario { get; set; }

        public int? IdPais { get; set; }

        public int? IdEstado { get; set; }

        public int? IdCidade { get; set; }

        public long? NumeroFrota { get; set; }

        public ETipoContrato TipoContrato { get; set; }

        public int QuantidadeEixos { get; set; }

        public DateTime? DataUltimaAtualizacao { get; set; }

        public string Municipio { get; set; }

        public string Placa { get; set; }

        public string Chassi { get; set; }

        public int? AnoFabricacao { get; set; }

        public int? AnoModelo { get; set; }

        public string Renavam { get; set; }

        public string Marca { get; set; }

        public string Modelo { get; set; }

        public bool ComTracao { get; set; }

        public ETipoRodagem TipoRodagem { get; set; }

        public int? IdTipoCavalo { get; set; }
        
        public string TipoCavaloNome { get; set; }

        public int? IdTipoCarreta { get; set; }

        public bool Ativo { get; set; } = true;
        
        public bool? HabilitarContratoCiotAgregado { get; set; }

        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Pendente;

        public List<VeiculoTipoCombustivelCreateRequest> TipoCombustiveis { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public int? IdOperacao { get; set; }
        public string CorVeiculo { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class DeclaracaoCiotService : ServiceBase, IDeclaracaoCiotService
    {
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IEmpresaRepository _empresaRepository;

        public DeclaracaoCiotService(IDeclaracaoCiotRepository declaracaoCiotRepository, IEmpresaRepository empresaRepository)
        {
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _empresaRepository = empresaRepository;
        }

//        public DeclaracaoCiot ConsultarCiot(int viagemid)
//        {
//            return _declaracaoCiotRepository.ConsultarCiot(viagemid);
//        }

        public List<ItensGraficoPorEmpresa> ConsultaNumeroGraficoDeclaracaoCiot(int? idEmpresa, DateTime dataInicial,
            DateTime dataFinal, DateTime? dataSelecinada)
        {
            var primeiraHoraDoDiaSelecionado = dataSelecinada;
            var ultimaHoraDoDiaSelecionado = dataSelecinada;
            
            //Datas para os indicadores com filtro do dia selecinado.
            var dataSelecionadaDia = Convert.ToDateTime(dataSelecinada);

            if (dataSelecinada != null)
            {
                primeiraHoraDoDiaSelecionado = new DateTime(dataSelecionadaDia.Year, dataSelecionadaDia.Month, dataSelecionadaDia.Day, 0, 0, 0);
                ultimaHoraDoDiaSelecionado = new DateTime(dataSelecionadaDia.Year, dataSelecionadaDia.Month, dataSelecionadaDia.Day, 23, 59, 59);
            }
     
            //Quantia de Ciot do dia do filtro
            var declaracaoCiotDia = _declaracaoCiotRepository
                .Find(x => x.DataDeclaracao >= (primeiraHoraDoDiaSelecionado ?? DateTime.Today) && x.DataDeclaracao <= (ultimaHoraDoDiaSelecionado ?? DateTime.Now));
 
            var declaracaoCiot = _declaracaoCiotRepository
                .Find(x => x.DataDeclaracao >= dataInicial && x.DataDeclaracao <= dataFinal);

            if (idEmpresa != null)
            {
                declaracaoCiotDia = declaracaoCiotDia.Where(x => x.IdEmpresa == idEmpresa);
                declaracaoCiot = declaracaoCiot.Where(x => x.IdEmpresa == idEmpresa);
            }

            var arrayCiot = declaracaoCiot.GroupBy(a => a.IdEmpresa).ToList()
                .Select(c => new ItensGraficoPorEmpresa
                {
                    IdEmpresa = c.Key,
                    QuantidadeItensPorEmpresa = c.Count(),
                    QuantidadeTotalItens = declaracaoCiot.Count(),
                    QuantidadeItensPorDia = declaracaoCiotDia.Count(),
                    
                    NomeEmpresa = _empresaRepository.FirstOrDefault(a => a.IdEmpresa == c.Key)
                        .NomeFantasia
                }).ToList();
            return arrayCiot;
        }
    }
}
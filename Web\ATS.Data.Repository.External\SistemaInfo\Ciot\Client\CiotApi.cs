﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

namespace ATS.Data.Repository.External.SistemaInfo.Ciot.Client
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class AnttClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AnttClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSituacaoTransportadorReponse> ConsultarSituacaoTransportadorAsync(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarSituacaoTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarSituacaoTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSituacaoTransportadorReponse> ConsultarSituacaoTransportadorAsync(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ConsultarSituacaoTransportador");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSituacaoTransportadorReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoTransportadorReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSituacaoTransportadorReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarFrotaTransportadorReponse> ConsultarFrotaTransportadorAsync(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarFrotaTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarFrotaTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarFrotaTransportadorReponse> ConsultarFrotaTransportadorAsync(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ConsultarFrotaTransportador");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarFrotaTransportadorReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarFrotaTransportadorReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarFrotaTransportadorReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<DeclararOperacaoTransporteReponse> DeclararOperacaoTransporteAsync(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return DeclararOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public DeclararOperacaoTransporteReponse DeclararOperacaoTransporte(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await DeclararOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<DeclararOperacaoTransporteReponse> DeclararOperacaoTransporteAsync(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/DeclararOperacaoTransporte");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(DeclararOperacaoTransporteReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<DeclararOperacaoTransporteReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(DeclararOperacaoTransporteReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CancelarOperacaoTransporteReponse> CancelarOperacaoTransporteAsync(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CancelarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CancelarOperacaoTransporteReponse CancelarOperacaoTransporte(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CancelarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CancelarOperacaoTransporteReponse> CancelarOperacaoTransporteAsync(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/CancelarOperacaoTransporte");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CancelarOperacaoTransporteReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarOperacaoTransporteReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CancelarOperacaoTransporteReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<RetificarOperacaoTransporteReponse> RetificarOperacaoTransporteAsync(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return RetificarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public RetificarOperacaoTransporteReponse RetificarOperacaoTransporte(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await RetificarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<RetificarOperacaoTransporteReponse> RetificarOperacaoTransporteAsync(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/RetificarOperacaoTransporte");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(RetificarOperacaoTransporteReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<RetificarOperacaoTransporteReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(RetificarOperacaoTransporteReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EncerrarOperacaoTransporteReponse> EncerrarOperacaoTransporteAsync(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EncerrarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EncerrarOperacaoTransporteReponse EncerrarOperacaoTransporte(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EncerrarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<EncerrarOperacaoTransporteReponse> EncerrarOperacaoTransporteAsync(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/EncerrarOperacaoTransporte");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(EncerrarOperacaoTransporteReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(EncerrarOperacaoTransporteReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class ConsultasClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public ConsultasClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSituacaoCiotReponse> SituacaoCiotAsync(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return SituacaoCiotAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSituacaoCiotReponse SituacaoCiot(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await SituacaoCiotAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSituacaoCiotReponse> SituacaoCiotAsync(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Consultas/SituacaoCiot");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSituacaoCiotReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoCiotReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSituacaoCiotReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarOperacaoTacAgregadoReponse> EncerramentosPendentesAsync(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EncerramentosPendentesAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarOperacaoTacAgregadoReponse EncerramentosPendentes(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EncerramentosPendentesAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -])</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarOperacaoTacAgregadoReponse> EncerramentosPendentesAsync(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Consultas/EncerramentosPendentes");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarOperacaoTacAgregadoReponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarOperacaoTacAgregadoReponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarOperacaoTacAgregadoReponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSituacaoTransportadorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjInteressado;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjInteressado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjInteressado
        {
            get { return _cpfCnpjInteressado; }
            set 
            {
                if (_cpfCnpjInteressado != value)
                {
                    _cpfCnpjInteressado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set 
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set 
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSituacaoTransportadorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoTransportadorRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSituacaoTransportadorReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private string _nomeRazaoSocialTransportador;
        private bool? _rntrcAtivo;
        private System.DateTime? _dataValidadeRNTRC;
        private string _tipoTransportador;
        private bool? _equiparadoTAC;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set 
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set 
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocialTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocialTransportador
        {
            get { return _nomeRazaoSocialTransportador; }
            set 
            {
                if (_nomeRazaoSocialTransportador != value)
                {
                    _nomeRazaoSocialTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcAtivo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? RntrcAtivo
        {
            get { return _rntrcAtivo; }
            set 
            {
                if (_rntrcAtivo != value)
                {
                    _rntrcAtivo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataValidadeRNTRC", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataValidadeRNTRC
        {
            get { return _dataValidadeRNTRC; }
            set 
            {
                if (_dataValidadeRNTRC != value)
                {
                    _dataValidadeRNTRC = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoTransportador
        {
            get { return _tipoTransportador; }
            set 
            {
                if (_tipoTransportador != value)
                {
                    _tipoTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("equiparadoTAC", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EquiparadoTAC
        {
            get { return _equiparadoTAC; }
            set 
            {
                if (_equiparadoTAC != value)
                {
                    _equiparadoTAC = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSituacaoTransportadorReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoTransportadorReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ExcecaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ExcecaoResponseTipo _tipo;
        private string _codigo;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ExcecaoResponseTipo Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigo", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public string Codigo
        {
            get { return _codigo; }
            set 
            {
                if (_codigo != value)
                {
                    _codigo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ExcecaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ExcecaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarFrotaTransportadorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjInteressado;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private System.Collections.ObjectModel.ObservableCollection<string> _placa;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjInteressado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjInteressado
        {
            get { return _cpfCnpjInteressado; }
            set 
            {
                if (_cpfCnpjInteressado != value)
                {
                    _cpfCnpjInteressado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set 
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set 
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<string> Placa
        {
            get { return _placa; }
            set 
            {
                if (_placa != value)
                {
                    _placa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarFrotaTransportadorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarFrotaTransportadorRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarFrotaTransportadorReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private string _nomeRazaoSocialTransportador;
        private bool? _rntrcAtivo;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoFrotaTransportadorResponse> _veiculoTransportador;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set 
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set 
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocialTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocialTransportador
        {
            get { return _nomeRazaoSocialTransportador; }
            set 
            {
                if (_nomeRazaoSocialTransportador != value)
                {
                    _nomeRazaoSocialTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcAtivo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? RntrcAtivo
        {
            get { return _rntrcAtivo; }
            set 
            {
                if (_rntrcAtivo != value)
                {
                    _rntrcAtivo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoFrotaTransportadorResponse> VeiculoTransportador
        {
            get { return _veiculoTransportador; }
            set 
            {
                if (_veiculoTransportador != value)
                {
                    _veiculoTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarFrotaTransportadorReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarFrotaTransportadorReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VeiculoFrotaTransportadorResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _placaVeiculo;
        private bool? _situacaoVeiculoFrotaTransportador;
    
        [Newtonsoft.Json.JsonProperty("placaVeiculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PlacaVeiculo
        {
            get { return _placaVeiculo; }
            set 
            {
                if (_placaVeiculo != value)
                {
                    _placaVeiculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("situacaoVeiculoFrotaTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? SituacaoVeiculoFrotaTransportador
        {
            get { return _situacaoVeiculoFrotaTransportador; }
            set 
            {
                if (_situacaoVeiculoFrotaTransportador != value)
                {
                    _situacaoVeiculoFrotaTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VeiculoFrotaTransportadorResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VeiculoFrotaTransportadorResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DeclararOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private FreteRequest _frete;
        private ContratanteRequest _contratante;
        private DestinatarioRequest _destinatario;
        private ConsignatarioRequest _consignatario;
        private RemetenteRequest _remetente;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> _veiculos;
        private ValoresFreteRequest _valores;
        private PagamentoRequest _pagamento;
        private string _ciotAjuste;
    
        [Newtonsoft.Json.JsonProperty("frete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public FreteRequest Frete
        {
            get { return _frete; }
            set 
            {
                if (_frete != value)
                {
                    _frete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ContratanteRequest Contratante
        {
            get { return _contratante; }
            set 
            {
                if (_contratante != value)
                {
                    _contratante = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("destinatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DestinatarioRequest Destinatario
        {
            get { return _destinatario; }
            set 
            {
                if (_destinatario != value)
                {
                    _destinatario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("consignatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ConsignatarioRequest Consignatario
        {
            get { return _consignatario; }
            set 
            {
                if (_consignatario != value)
                {
                    _consignatario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("remetente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RemetenteRequest Remetente
        {
            get { return _remetente; }
            set 
            {
                if (_remetente != value)
                {
                    _remetente = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> Veiculos
        {
            get { return _veiculos; }
            set 
            {
                if (_veiculos != value)
                {
                    _veiculos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ValoresFreteRequest Valores
        {
            get { return _valores; }
            set 
            {
                if (_valores != value)
                {
                    _valores = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PagamentoRequest Pagamento
        {
            get { return _pagamento; }
            set 
            {
                if (_pagamento != value)
                {
                    _pagamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotAjuste", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CiotAjuste
        {
            get { return _ciotAjuste; }
            set 
            {
                if (_ciotAjuste != value)
                {
                    _ciotAjuste = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DeclararOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DeclararOperacaoTransporteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class FreteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
        private System.DateTime? _dataInicioFrete;
        private System.DateTime? _dataTerminoFrete;
        private string _dadosComplementares;
        private ProprietarioRequest _proprietario;
        private MotoristaRequest _motorista;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private int? _tipoViagem;
        private bool? _subContratacao;
        private string _ciotPrincipal;
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set 
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set 
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioFrete
        {
            get { return _dataInicioFrete; }
            set 
            {
                if (_dataInicioFrete != value)
                {
                    _dataInicioFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoFrete
        {
            get { return _dataTerminoFrete; }
            set 
            {
                if (_dataTerminoFrete != value)
                {
                    _dataTerminoFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dadosComplementares", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DadosComplementares
        {
            get { return _dadosComplementares; }
            set 
            {
                if (_dadosComplementares != value)
                {
                    _dadosComplementares = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("proprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProprietarioRequest Proprietario
        {
            get { return _proprietario; }
            set 
            {
                if (_proprietario != value)
                {
                    _proprietario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motorista", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public MotoristaRequest Motorista
        {
            get { return _motorista; }
            set 
            {
                if (_motorista != value)
                {
                    _motorista = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set 
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set 
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoViagem
        {
            get { return _tipoViagem; }
            set 
            {
                if (_tipoViagem != value)
                {
                    _tipoViagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("subContratacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? SubContratacao
        {
            get { return _subContratacao; }
            set 
            {
                if (_subContratacao != value)
                {
                    _subContratacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotPrincipal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CiotPrincipal
        {
            get { return _ciotPrincipal; }
            set 
            {
                if (_ciotPrincipal != value)
                {
                    _ciotPrincipal = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static FreteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<FreteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ContratanteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _rntrc;
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set 
            {
                if (_rntrc != value)
                {
                    _rntrc = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set 
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ContratanteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ContratanteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DestinatarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set 
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DestinatarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DestinatarioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsignatarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set 
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsignatarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsignatarioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class RemetenteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set 
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static RemetenteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RemetenteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VeiculoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _placa;
        private string _rntrc;
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Placa
        {
            get { return _placa; }
            set 
            {
                if (_placa != value)
                {
                    _placa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set 
            {
                if (_rntrc != value)
                {
                    _rntrc = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VeiculoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VeiculoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ValoresFreteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _valorFrete;
        private decimal? _valorCombustivel;
        private decimal? _valorDespesas;
        private decimal? _totalImposto;
        private decimal? _totalPegadio;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set 
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorCombustivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorCombustivel
        {
            get { return _valorCombustivel; }
            set 
            {
                if (_valorCombustivel != value)
                {
                    _valorCombustivel = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorDespesas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorDespesas
        {
            get { return _valorDespesas; }
            set 
            {
                if (_valorDespesas != value)
                {
                    _valorDespesas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalImposto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalImposto
        {
            get { return _totalImposto; }
            set 
            {
                if (_totalImposto != value)
                {
                    _totalImposto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalPegadio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalPegadio
        {
            get { return _totalPegadio; }
            set 
            {
                if (_totalPegadio != value)
                {
                    _totalPegadio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set 
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set 
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ValoresFreteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ValoresFreteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PagamentoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _formaPagmento;
        private System.Collections.ObjectModel.ObservableCollection<ParcelaPagamentoRequest> _parcelas;
        private bool? _parcelaUnica;
        private string _infoPagamento;
        private string _bancoPagamento;
        private string _agenciaPagamento;
        private string _contaPagamento;
    
        [Newtonsoft.Json.JsonProperty("formaPagmento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? FormaPagmento
        {
            get { return _formaPagmento; }
            set 
            {
                if (_formaPagmento != value)
                {
                    _formaPagmento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parcelas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<ParcelaPagamentoRequest> Parcelas
        {
            get { return _parcelas; }
            set 
            {
                if (_parcelas != value)
                {
                    _parcelas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parcelaUnica", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ParcelaUnica
        {
            get { return _parcelaUnica; }
            set 
            {
                if (_parcelaUnica != value)
                {
                    _parcelaUnica = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("infoPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoPagamento
        {
            get { return _infoPagamento; }
            set 
            {
                if (_infoPagamento != value)
                {
                    _infoPagamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bancoPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BancoPagamento
        {
            get { return _bancoPagamento; }
            set 
            {
                if (_bancoPagamento != value)
                {
                    _bancoPagamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agenciaPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AgenciaPagamento
        {
            get { return _agenciaPagamento; }
            set 
            {
                if (_agenciaPagamento != value)
                {
                    _agenciaPagamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contaPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContaPagamento
        {
            get { return _contaPagamento; }
            set 
            {
                if (_contaPagamento != value)
                {
                    _contaPagamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PagamentoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PagamentoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProprietarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _rntrc;
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set 
            {
                if (_rntrc != value)
                {
                    _rntrc = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set 
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProprietarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProprietarioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class MotoristaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cpfCnpj;
        private string _numeroCNH;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroCNH", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NumeroCNH
        {
            get { return _numeroCNH; }
            set 
            {
                if (_numeroCNH != value)
                {
                    _numeroCNH = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static MotoristaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<MotoristaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaEnderecoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cep;
        private int? _codigoMunicipio;
        private string _logradouro;
        private string _numero;
        private string _complemento;
        private string _bairro;
        private string _telefone;
        private string _email;
    
        [Newtonsoft.Json.JsonProperty("cep", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Cep
        {
            get { return _cep; }
            set 
            {
                if (_cep != value)
                {
                    _cep = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipio
        {
            get { return _codigoMunicipio; }
            set 
            {
                if (_codigoMunicipio != value)
                {
                    _codigoMunicipio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("logradouro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Logradouro
        {
            get { return _logradouro; }
            set 
            {
                if (_logradouro != value)
                {
                    _logradouro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numero", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Numero
        {
            get { return _numero; }
            set 
            {
                if (_numero != value)
                {
                    _numero = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("complemento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Complemento
        {
            get { return _complemento; }
            set 
            {
                if (_complemento != value)
                {
                    _complemento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bairro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Bairro
        {
            get { return _bairro; }
            set 
            {
                if (_bairro != value)
                {
                    _bairro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("telefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Telefone
        {
            get { return _telefone; }
            set 
            {
                if (_telefone != value)
                {
                    _telefone = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Email
        {
            get { return _email; }
            set 
            {
                if (_email != value)
                {
                    _email = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaEnderecoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaEnderecoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ParcelaPagamentoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _codigoParcela;
        private decimal? _valorParcela;
        private System.DateTime? _vencimento;
    
        [Newtonsoft.Json.JsonProperty("codigoParcela", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoParcela
        {
            get { return _codigoParcela; }
            set 
            {
                if (_codigoParcela != value)
                {
                    _codigoParcela = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorParcela", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorParcela
        {
            get { return _valorParcela; }
            set 
            {
                if (_valorParcela != value)
                {
                    _valorParcela = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("vencimento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? Vencimento
        {
            get { return _vencimento; }
            set 
            {
                if (_vencimento != value)
                {
                    _vencimento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ParcelaPagamentoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ParcelaPagamentoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DeclararOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _codigoVerificador;
        private string _avisoTransportador;
        private bool? _emContingencia;
        private string _protocoloErro;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set 
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("avisoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AvisoTransportador
        {
            get { return _avisoTransportador; }
            set 
            {
                if (_avisoTransportador != value)
                {
                    _avisoTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("emContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EmContingencia
        {
            get { return _emContingencia; }
            set 
            {
                if (_emContingencia != value)
                {
                    _emContingencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloErro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloErro
        {
            get { return _protocoloErro; }
            set 
            {
                if (_protocoloErro != value)
                {
                    _protocoloErro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set 
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DeclararOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DeclararOperacaoTransporteReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CancelarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _motivoCancelamento;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoCancelamento
        {
            get { return _motivoCancelamento; }
            set 
            {
                if (_motivoCancelamento != value)
                {
                    _motivoCancelamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set 
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CancelarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarOperacaoTransporteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CancelarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private System.DateTime? _dataCancelamento;
        private string _protocoloCancelamento;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCancelamento
        {
            get { return _dataCancelamento; }
            set 
            {
                if (_dataCancelamento != value)
                {
                    _dataCancelamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloCancelamento
        {
            get { return _protocoloCancelamento; }
            set 
            {
                if (_protocoloCancelamento != value)
                {
                    _protocoloCancelamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CancelarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarOperacaoTransporteReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class RetificarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> _veiculos;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private System.DateTime? _dataInicioViagem;
        private System.DateTime? _dataFimViagem;
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
        private string _senhaAlteracao;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> Veiculos
        {
            get { return _veiculos; }
            set 
            {
                if (_veiculos != value)
                {
                    _veiculos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set 
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set 
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioViagem
        {
            get { return _dataInicioViagem; }
            set 
            {
                if (_dataInicioViagem != value)
                {
                    _dataInicioViagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimViagem
        {
            get { return _dataFimViagem; }
            set 
            {
                if (_dataFimViagem != value)
                {
                    _dataFimViagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set 
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set 
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set 
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set 
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set 
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static RetificarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RetificarOperacaoTransporteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class RetificarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private System.DateTime? _dataRetificacao;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataRetificacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRetificacao
        {
            get { return _dataRetificacao; }
            set 
            {
                if (_dataRetificacao != value)
                {
                    _dataRetificacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static RetificarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RetificarOperacaoTransporteReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EncerrarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private decimal? _pesoCarga;
        private System.Collections.ObjectModel.ObservableCollection<EncerrarOperacaoTransporteViagemRequest> _viagensOperacaoTransporte;
        private ValoresFreteRequest _valoresEfetivos;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set 
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("viagensOperacaoTransporte", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<EncerrarOperacaoTransporteViagemRequest> ViagensOperacaoTransporte
        {
            get { return _viagensOperacaoTransporte; }
            set 
            {
                if (_viagensOperacaoTransporte != value)
                {
                    _viagensOperacaoTransporte = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valoresEfetivos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ValoresFreteRequest ValoresEfetivos
        {
            get { return _valoresEfetivos; }
            set 
            {
                if (_valoresEfetivos != value)
                {
                    _valoresEfetivos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set 
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EncerrarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EncerrarOperacaoTransporteViagemRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private int? _quantidadeViagens;
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set 
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set 
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set 
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set 
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeViagens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeViagens
        {
            get { return _quantidadeViagens; }
            set 
            {
                if (_quantidadeViagens != value)
                {
                    _quantidadeViagens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EncerrarOperacaoTransporteViagemRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteViagemRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EncerrarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _protocoloEncerramento;
        private System.DateTime? _dataEncerramento;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloEncerramento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloEncerramento
        {
            get { return _protocoloEncerramento; }
            set 
            {
                if (_protocoloEncerramento != value)
                {
                    _protocoloEncerramento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataEncerramento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataEncerramento
        {
            get { return _dataEncerramento; }
            set 
            {
                if (_dataEncerramento != value)
                {
                    _dataEncerramento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EncerrarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSituacaoCiotRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set 
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSituacaoCiotRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoCiotRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSituacaoCiotReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _codigoVerificador;
        private int? _situacao;
        private bool? _enviadoEmContingencia;
        private ExcecaoResponse _erroContingencia;
        private string _nomeProprietario;
        private string _cpfCnpjProprietario;
        private string _rntrcProprietario;
        private System.DateTime? _dataInicioFrete;
        private System.DateTime? _dataTerminoFrete;
        private decimal? _valorFrete;
        private decimal? _valorEfetivoFrete;
        private decimal? _valorDespesas;
        private decimal? _totalImposto;
        private decimal? _totalPegadio;
        private int? _tipoViagem;
        private bool? _encerrado;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set 
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("situacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Situacao
        {
            get { return _situacao; }
            set 
            {
                if (_situacao != value)
                {
                    _situacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("enviadoEmContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EnviadoEmContingencia
        {
            get { return _enviadoEmContingencia; }
            set 
            {
                if (_enviadoEmContingencia != value)
                {
                    _enviadoEmContingencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("erroContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse ErroContingencia
        {
            get { return _erroContingencia; }
            set 
            {
                if (_erroContingencia != value)
                {
                    _erroContingencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeProprietario
        {
            get { return _nomeProprietario; }
            set 
            {
                if (_nomeProprietario != value)
                {
                    _nomeProprietario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjProprietario
        {
            get { return _cpfCnpjProprietario; }
            set 
            {
                if (_cpfCnpjProprietario != value)
                {
                    _cpfCnpjProprietario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcProprietario
        {
            get { return _rntrcProprietario; }
            set 
            {
                if (_rntrcProprietario != value)
                {
                    _rntrcProprietario = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioFrete
        {
            get { return _dataInicioFrete; }
            set 
            {
                if (_dataInicioFrete != value)
                {
                    _dataInicioFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoFrete
        {
            get { return _dataTerminoFrete; }
            set 
            {
                if (_dataTerminoFrete != value)
                {
                    _dataTerminoFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set 
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorEfetivoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorEfetivoFrete
        {
            get { return _valorEfetivoFrete; }
            set 
            {
                if (_valorEfetivoFrete != value)
                {
                    _valorEfetivoFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorDespesas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorDespesas
        {
            get { return _valorDespesas; }
            set 
            {
                if (_valorDespesas != value)
                {
                    _valorDespesas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalImposto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalImposto
        {
            get { return _totalImposto; }
            set 
            {
                if (_totalImposto != value)
                {
                    _totalImposto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalPegadio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalPegadio
        {
            get { return _totalPegadio; }
            set 
            {
                if (_totalPegadio != value)
                {
                    _totalPegadio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoViagem
        {
            get { return _tipoViagem; }
            set 
            {
                if (_tipoViagem != value)
                {
                    _tipoViagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("encerrado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Encerrado
        {
            get { return _encerrado; }
            set 
            {
                if (_encerrado != value)
                {
                    _encerrado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSituacaoCiotReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoCiotReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarOperacaoTacAgregadoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjContratante;
        private string _cpfCnpjTransportador;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjContratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjContratante
        {
            get { return _cpfCnpjContratante; }
            set 
            {
                if (_cpfCnpjContratante != value)
                {
                    _cpfCnpjContratante = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set 
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarOperacaoTacAgregadoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarOperacaoTacAgregadoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarOperacaoTacAgregadoReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private System.Collections.ObjectModel.ObservableCollection<ConsultaOperacaoTacAgregadoViagemResponse> _viagensPendentes;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set 
            {
                if (_excecao != value)
                {
                    _excecao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("viagensPendentes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<ConsultaOperacaoTacAgregadoViagemResponse> ViagensPendentes
        {
            get { return _viagensPendentes; }
            set 
            {
                if (_viagensPendentes != value)
                {
                    _viagensPendentes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarOperacaoTacAgregadoReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarOperacaoTacAgregadoReponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaOperacaoTacAgregadoViagemResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _codigoVerificador;
        private System.DateTime? _dataTerminoViagem;
        private System.DateTime? _dataDeclaracao;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set 
            {
                if (_ciot != value)
                {
                    _ciot = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set 
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoViagem
        {
            get { return _dataTerminoViagem; }
            set 
            {
                if (_dataTerminoViagem != value)
                {
                    _dataTerminoViagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDeclaracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDeclaracao
        {
            get { return _dataDeclaracao; }
            set 
            {
                if (_dataDeclaracao != value)
                {
                    _dataDeclaracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set 
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set 
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaOperacaoTacAgregadoViagemResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaOperacaoTacAgregadoViagemResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ExcecaoResponseTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Autorizacao")]
        Autorizacao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Validacao")]
        Validacao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Negocio")]
        Negocio = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Aplicacao")]
        Aplicacao = 3,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public string StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class LayoutService : ServiceBase, ILayoutService
    {
        private readonly ILayoutRepository _layoutRepository;

        public LayoutService(ILayoutRepository layoutRepository)
        {
            _layoutRepository = layoutRepository;
        }

        public Layout GetConfiguracaoLayoutPorDominio(string href)
        {
            if (string.IsNullOrWhiteSpace(href)) return null;

            if (href.Contains("?"))
                href = href.Split('?')[0];

            return _layoutRepository
                .Find(x => href.Contains(x.Href))
                .Include(x => x.Empresa)
                .OrderByDescending(x => x.IdLayout)
                .FirstOrDefault();
        }

        public Layout GetPorEmpresa(int idEmpresa)
        {
            return _layoutRepository
                .Find(x => x.IdEmpresa == idEmpresa)
                .Include(x => x.Empresa)
                .OrderByDescending(x => x.IdLayout)
                .FirstOrDefault();
        }
        
        public string GetEmpresaNomeAplicativo(int idEmpresa)
        {
            return _layoutRepository
                .Find(x => x.IdEmpresa == idEmpresa)
                .OrderByDescending(x => x.IdLayout)
                .Select(x => x.NomeAplicativo).FirstOrDefault();
        }
    }
}

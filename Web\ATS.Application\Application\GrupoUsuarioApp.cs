﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Linq;
using System.Transactions;
using ATS.Domain.Grid;
using System;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class GrupoUsuarioApp : AppBase, IGrupoUsuarioApp
    {
        private readonly IGrupoUsuarioService _grupoUsuarioService;

        public GrupoUsuarioApp(IGrupoUsuarioService grupoUsuarioService)
        {
            _grupoUsuarioService = grupoUsuarioService;
        }

        /// <summary>
        /// Consultar os grupos de usuários existentes
        /// </summary>
        /// <param name="nome"></param>
        /// <param name="idEmpresa"></param>
        /// /// <param name="idEstabelecimentoBase"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase)
        {
            return _grupoUsuarioService.Consultar(nome, idEmpresa, idEstabelecimentoBase);
        }

        /// <summary>
        /// Adiciona um novo grupo de usuário
        /// </summary>
        /// <param name="grupoUsuario"></param>
        /// <returns></returns>
        public ValidationResult Add(GrupoUsuario grupoUsuario)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _grupoUsuarioService.Add(grupoUsuario);
                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Retorna o grupo de usuário
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GrupoUsuario Get(int id)
        {
            return _grupoUsuarioService.Get(id);
        }

        /// <summary>
        /// Retorna o grupo de usuário contendo todos os elementos de referência
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GrupoUsuario GetChilds(int id)
        {
            return _grupoUsuarioService.GetChilds(id);
        }

        /// <summary>
        /// Atualizar um registro de grupo de usuário
        /// </summary>
        /// <param name="grupoUsuario"></param>
        /// <returns></returns>
        public ValidationResult Update(GrupoUsuario grupoUsuario, List<GrupoUsuarioMenu> grupoUsuarioMenu)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _grupoUsuarioService.Update(grupoUsuario, grupoUsuarioMenu);
                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public void Atualizar(int idGrupoUsuario, int idEmpresa, string descricao, List<int> idMenusSelecionados)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _grupoUsuarioService.Atualizar(idGrupoUsuario, idEmpresa, descricao, idMenusSelecionados);
                transaction.Complete();
            }
        }

        public void Inserir(int idEmpresa, string descricao, List<int> idMenusSelecionados, int? idEstabelecimentoBase)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _grupoUsuarioService.Inserir(idEmpresa, descricao, idMenusSelecionados, idEstabelecimentoBase);
                transaction.Complete();
            }
        }

        public object ConsultarGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _grupoUsuarioService.ConsultarGrid(idEmpresa, idEstabelecimentoBase,  take, page, order, filters);
        }

        public bool PertenceAEmpresa(int idEmpresa, int idGrupoUsuario)
        {
            return _grupoUsuarioService.GetQuery().Any(c => c.IdEmpresa == idEmpresa && c.IdGrupoUsuario == idGrupoUsuario);
        }

        /// <summary>
        /// Ativar o registro
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <param name="idUsuarioLogOn">Código do usuário que esta executando o processo</param>
        /// <returns></returns>
        public ValidationResult Ativar(int id, int idUsuarioLogOn)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _grupoUsuarioService.Ativar(id, idUsuarioLogOn);
                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Desativar o registro
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <param name="idUsuarioLogOn">Código do usuário que esta executando o processo</param>
        /// <returns></returns>
        public ValidationResult Inativar(int id, int idUsuarioLogOn)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _grupoUsuarioService.Inativar(id, idUsuarioLogOn);
                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Retorna a lista de grupos de usuário do Empresa
        /// </summary>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="idEstabelecimentoBase"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase)
        {
            return _grupoUsuarioService.GetPorEmpresa(idEmpresa, idEstabelecimentoBase);
        }
    }
}
using System;
using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class DeclaracaoCiotApp : AppBase, IDeclaracaoCiotApp
    {
        
        private readonly IDeclaracaoCiotService _declaracaoCiotService;

        public DeclaracaoCiotApp(IDeclaracaoCiotService declaracaoCiotService)
        {
            _declaracaoCiotService = declaracaoCiotService;
        }

        public List<ItensGraficoPorEmpresa> ConsultaNumeroGraficoDeclaracaoCiot(int? idEmpresa, DateTime dataInicial, DateTime dataFinal, DateTime? dataSelecinada)
        {
            return _declaracaoCiotService.ConsultaNumeroGraficoDeclaracaoCiot(idEmpresa, dataInicial, dataFinal, dataSelecinada);
        }
    }
}
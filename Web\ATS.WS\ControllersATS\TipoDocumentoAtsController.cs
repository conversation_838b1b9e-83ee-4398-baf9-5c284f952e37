﻿using System;
using System.Linq;
using System.Web.Mvc;
using ATS.WS.ControllersATS.Default;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class TipoDocumentoAtsController : DefaultController
    {
        private readonly ITipoDocumentoApp _tipoDocumentoApp;

        public TipoDocumentoAtsController(ITipoDocumentoApp tipoDocumentoApp)
        {
            _tipoDocumentoApp = tipoDocumentoApp;
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar()
        {
            try
            {
                var tpDocs = _tipoDocumentoApp.All().ToList();
                return ResponderSucesso(tpDocs.Select(c => new {
                    c.IdTipoDocumento,
                    c.Des<PERSON>
                }));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
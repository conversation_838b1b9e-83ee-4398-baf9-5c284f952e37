﻿using ATS.Domain.Entities;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class PaisSeeder
    {
        public void Execute(AtsContext context)
        {
            if (context.Cidade.Any())
                return;

            int idPais = 1;

            context.Pais.AddOrUpdate(new[]
            {
                new Pais { IdPais = idPais++, Nome = "Afeganistão", BACEN = 0132, DataAlteracao = DateTime.Now.Date },
                new Pais { IdPais = idPais++, Nome = "África do Sul", BACEN = 7560, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Albânia", BACEN = 0175, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Alemanha", BACEN = 0230, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Andorra", BACEN = 0370, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Angola", BACEN = 0400, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Anguilla", BACEN = 0418, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Antigua e Barbuda", BACEN = 0434, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Antilhas Holandesas", BACEN = 0477, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Arábia Saudita", BACEN = 0531, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Argélia", BACEN = 0590, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Argentina", BACEN = 0639, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Armênia", BACEN = 0647, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Aruba", BACEN = 0655, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Austrália", BACEN = 0698, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Áustria", BACEN = 0728, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República do Azerbaijão ", BACEN = 0736, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Bahamas ", BACEN = 0779, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Bahrein", BACEN = 0809, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Bangladesh", BACEN = 0817, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Barbados", BACEN = 0833, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Belarus", BACEN = 0850, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Bélgica", BACEN = 0876, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Belize", BACEN = 0884, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Benin", BACEN = 2291, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Bermudas", BACEN = 0906, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Bolívia", BACEN = 0973, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Bósnia Herzegovina", BACEN = 0981, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Botsuana", BACEN = 1015, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Brasil", BACEN = 1058, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Brunei", BACEN = 1082, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Bulgária", BACEN = 1112, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Burkina Faso", BACEN = 0310, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Burundi", BACEN = 1155, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Butão", BACEN = 1198, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República de Cabo Verde ", BACEN = 1279, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Camarões", BACEN = 1457, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Camboja", BACEN = 1414, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Canadá", BACEN = 1490, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Canal Ilhas do (Jersey e Guernsey)", BACEN = 1504, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Canárias", BACEN = 1511, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Catar", BACEN = 1546, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Cayman", BACEN = 1376, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República do Cazaquistão", BACEN = 1538, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Chade", BACEN = 7889, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Chile", BACEN = 1589, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Popular da China", BACEN = 1600, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Chipre", BACEN = 1635, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha (Navidad) Christmas", BACEN = 5118, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Cingapura", BACEN = 7412, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Cocos (Keeling)", BACEN = 1651, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Colômbia", BACEN = 1694, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Comores", BACEN = 1732, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Democrática do Congo", BACEN = 8885, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República do Congo", BACEN = 1775, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Cook", BACEN = 1830, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Rep. Pop. Democrática da Coréia", BACEN = 1872, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Coréia", BACEN = 1902, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Costa do Marfim", BACEN = 1937, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Costa Rica", BACEN = 1961, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Coveite", BACEN = 1988, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Croácia", BACEN = 1953, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Cuba", BACEN = 1996, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Dinamarca", BACEN = 2321, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Djibuti", BACEN = 7838, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Dominica", BACEN = 2356, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Egito", BACEN = 402, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "El Salvador", BACEN = 6874, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Emirados Árabes Unidos", BACEN = 2445, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Equador", BACEN = 2399, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Eritréia", BACEN = 2437, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Escócia", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Eslovaca", BACEN = 2470, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Eslovênia", BACEN = 2461, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Espanha", BACEN = 2453, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Estados Unidos", BACEN = 2496, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Estônia", BACEN = 2518, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Etiópia", BACEN = 2534, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Falkland (Ilhas Malvinas)", BACEN = 2550, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Feroe", BACEN = 2593, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Fiji", BACEN = 8702, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Filipinas", BACEN = 2674, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Finlândia", BACEN = 2712, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Formosa (Taiwan)", BACEN = 1619, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "França", BACEN = 2755, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Gabão", BACEN = 2810, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "País de Gales", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Gâmbia", BACEN = 2852, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Gana", BACEN = 2895, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Geórgia", BACEN = 2917, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Gibraltar", BACEN = 2933, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Grã-Bretanha", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Granada", BACEN = 2976, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Grécia", BACEN = 3018, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Groenlândia", BACEN = 3050, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guadalupe", BACEN = 3093, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guam", BACEN = 3131, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guatemala", BACEN = 3174, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guiana", BACEN = 3379, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guiana Francesa", BACEN = 3255, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guiné", BACEN = 3298, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guiné-Bissau", BACEN = 3344, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Guiné-Equatorial", BACEN = 3310, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Haiti", BACEN = 3417, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Holanda (Países Baixos)", BACEN = 5738, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Honduras", BACEN = 3450, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Região Adm. Especial Hong Kong", BACEN = 3514, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Hungria", BACEN = 3557, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Iêmen", BACEN = 3573, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Índia", BACEN = 3611, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Indonésia", BACEN = 3654, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Inglaterra", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Islâmica do Irã", BACEN = 3727, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Iraque", BACEN = 3697, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Irlanda", BACEN = 3751, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Irlanda do Norte", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Islândia", BACEN = 3794, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Israel", BACEN = 3832, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Itália", BACEN = 3867, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Fed. da Iugoslávia", BACEN = 3883, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Jamaica", BACEN = 3913, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Japão", BACEN = 3999, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Johnston", BACEN = 3964, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Jordânia", BACEN = 4030, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Kiribati", BACEN = 4111, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Rep. Pop. Democrática do Laos", BACEN = 4200, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Lebuan", BACEN = 4235, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Lesoto", BACEN = 4260, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Letônia", BACEN = 4278, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Líbano", BACEN = 4316, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Libéria", BACEN = 4340, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Líbia", BACEN = 4383, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Liechtenstein", BACEN = 4405, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Lituânia", BACEN = 4421, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Luxemburgo", BACEN = 4456, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Macau", BACEN = 4472, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Macedônia", BACEN = 4499, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Madagascar", BACEN = 4502, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha da Madeira", BACEN = 4525, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Malásia", BACEN = 4553, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Malavi", BACEN = 4588, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Maldivas", BACEN = 4618, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Máli", BACEN = 4642, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Malta", BACEN = 4677, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Man", BACEN = 3595, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Marianas do Norte", BACEN = 4723, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Marrocos", BACEN = 4740, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Marshall", BACEN = 4766, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Martinica", BACEN = 4774, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Maurício", BACEN = 4855, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Mauritânia", BACEN = 4880, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "México", BACEN = 4936, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Mianmar (Birmânia)", BACEN = 0930, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Micronésia", BACEN = 4995, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Midway", BACEN = 4901, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Moçambique", BACEN = 5053, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Moldávia", BACEN = 4944, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Mônaco", BACEN = 4952, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Mongólia", BACEN = 4979, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Montserrat", BACEN = 5010, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Namíbia", BACEN = 5070, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nauru", BACEN = 5088, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nepal", BACEN = 5177, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nicarágua", BACEN = 5215, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Niger", BACEN = 5258, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nigéria", BACEN = 5282, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Niue", BACEN = 5312, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Norfolk", BACEN = 5355, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Noruega", BACEN = 5380, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nova Caledônia", BACEN = 5428, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Nova Zelândia", BACEN = 5487, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Omã", BACEN = 5568, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Países Baixos (Holanda)", BACEN = 5738, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Palau", BACEN = 5754, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Panamá", BACEN = 5800, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Papua Nova Guiné", BACEN = 5452, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Paquistão", BACEN = 5762, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Paraguai", BACEN = 5860, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Peru", BACEN = 5894, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Pitcairn", BACEN = 5932, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Polinésia Francesa", BACEN = 5991, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República da Polônia", BACEN = 6033, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Porto Rico", BACEN = 6114, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Portugal", BACEN = 6076, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Quênia", BACEN = 6238, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Quirguiz", BACEN = 6254, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Reino Unido", BACEN = 6289, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Centro-Africana", BACEN = 6408, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Dominicana", BACEN = 6475, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Reunião", BACEN = 6602, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Romênia", BACEN = 6700, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ruanda", BACEN = 6750, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Rússia", BACEN = 6769, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Saara Ocidental", BACEN = 6858, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Salomão", BACEN = 6777, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Samoa", BACEN = 6904, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Samoa Americana", BACEN = 6912, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "San Marino", BACEN = 6971, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Santa Helena", BACEN = 7102, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Santa Lúcia", BACEN = 7153, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "São Cristóvão e Neves", BACEN = 6955, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "São Pedro e Miquelon", BACEN = 7005, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "São Tomé e Príncipe, Ilhas", BACEN = 7200, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "São Vicente e Granadinas", BACEN = 7056, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Senegal", BACEN = 7285, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Serra Leoa", BACEN = 7358, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Seychelles", BACEN = 7315, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Árabe da Síria", BACEN = 7447, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Somália", BACEN = 7480, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Sri Lanka", BACEN = 7501, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Suazilândia", BACEN = 7544, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Sudão", BACEN = 7595, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Suécia", BACEN = 7641, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Suíça", BACEN = 7676, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Suriname", BACEN = 7706, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Tadjiquistão", BACEN = 7722, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Tailândia", BACEN = 7765, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Unida da Tanzânia", BACEN = 7803, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República Tcheca", BACEN = 7919, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Território Britânico Oc. Índico", BACEN = 7820, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Timor Leste", BACEN = 7951, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Togo", BACEN = 8001, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Tonga", BACEN = 8109, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Toquelau", BACEN = 8052, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Trinidad e Tobago", BACEN = 8150, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Tunísia", BACEN = 8206, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Turcas e Caicos, Ilhas", BACEN = 8230, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República do Turcomenistão", BACEN = 8249, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Turquia", BACEN = 8273, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Tuvalu", BACEN = 8281, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ucrânia", BACEN = 8311, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Uganda", BACEN = 8338, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Uruguai", BACEN = 8451, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "República do Uzbequistão", BACEN = 8478, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Vanuatu", BACEN = 5517, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Estado da Cidade do Vaticano", BACEN = 8486, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Venezuela", BACEN = 8508, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Vietnã", BACEN = 8583, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Virgens (Britânicas)", BACEN = 8630, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Virgens (E.U.A.)", BACEN = 8664, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilha Wake ", BACEN = 8737, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Ilhas Wallis e Futuna", BACEN = 8753, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Zâmbia", BACEN = 8907, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Zimbábue", BACEN = 6653, DataAlteracao = DateTime.Now.Date.Date },
                new Pais { IdPais = idPais++, Nome = "Zona do Canal do Panamá", BACEN = 8958, DataAlteracao = DateTime.Now.Date.Date }
            });

            context.SaveChanges();
        }
    }
}
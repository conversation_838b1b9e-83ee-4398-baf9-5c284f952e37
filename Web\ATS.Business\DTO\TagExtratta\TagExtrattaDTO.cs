using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Runtime.Serialization;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using TagExtrattaClient;

namespace ATS.Domain.DTO.TagExtratta
{
    #region Remessa
    
    public class GridTagRemessaBaseRequest
    {
        public int Take { get; set; }
        public int Page { get; set; }
        public OrderFilters Order { get; set; }
        public List<QueryFilters> Filters { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public int? IdEmpresa { get; set; } = null;
    }
    
    public class GridRemessaEnvioRequest : GridTagRemessaBaseRequest
    {
    }
    
    public class GridRemessaRecebimentoRequest : GridTagRemessaBaseRequest
    {
    }
    
    public class GridValePedagioHubRequest
    {
        public int? Take { get; set; }
        public int? Page { get; set; }
        public OrderFilters Order { get; set; }
        public List<QueryFilters> Filters { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public string Cnpj { get; set; }
    }
    
    public class GridRemessaEnvioResponse
    {
        public List<GridRemessaEnvioItemResponse> items { get; set; }
        public int totalItems { get; set; }
    }

    public class GridRemessaEnvioItemResponse
    {
        public int Id { get; set; }
        public string DataCadastro { get; set; }
        public string UsuarioCadastro { get; set; }
        public string DataBaixa { get; set; }
        public string UsuarioBaixa { get; set; }
        public string NomeEmpresa { get; set; }
        public string Status { get; set; }
        public int QuantidadeTags { get; set; }
    }
    
    public class GridRemessaRecebimentoResponse
    {
        public List<GridRemessaRecebimentoItemResponse> items { get; set; }
        public int totalItems { get; set; }
    }

    public class GridRemessaRecebimentoItemResponse
    {
        public int Id { get; set; }
        public string DataCadastro { get; set; }
        public string UsuarioCadastro { get; set; }
        public string DataBaixa { get; set; }
        public string UsuarioBaixa { get; set; }
        public string NomeEmpresa { get; set; }
        public string Status { get; set; }
        public int QuantidadeTags { get; set; }
    }
    
    public class TagRemessaEnvioRequest
    {
        public int IdEmpresa { get; set; }
        public string Informacao { get; set; }
        public List<TagRemessaEnvioItem> Tags { get; set; } 
    }
    
    public class TagRemessaEnvioItem
    {
        public int Id { get; set; }
    }

    public class ConsultarRemessaResponse
    {
        public int IdRemessa { get; set; }
        public List<ConsultarRemessaTagResponse> Remessa { get; set; }
        public string Status { get; set; }
        public string NomeEmpresa { get; set; }
        public int IdEmpresa { get; set; }
        public string DataCadastro { get; set; }
        public string DataBaixa { get; set; }
        public string Informacao { get; set; }
    }
    
    public class ConsultarRemessaTagResponse
    {
        public long SerialNumber { get; set; }
        public string DataCadastro { get; set; }
    }
    
    public class ConsultarPagamentoResponse
    {
        public string Taxa { get; set; }
        public string Valor { get; set; }
        public string StatusTaxa { get; set; }
        public string StatusPassagem { get; set; }
        public string Recibo { get; set; }
        public int? IdEmpresa { get; set; }
        public int? ViagemId { get; set; }
        public string Placa { get; set; }
        public long Id { get; set; }
        public string Cnpj { get; set; }
        public string Praca { get; set; }
        public ETipoTransacaoTag TipoEnum { get; set; }
        public string Tipo { get; set; }
        public decimal TaxaDecimal { get; set; }
        public decimal ValorDecimal { get; set; }
    }

    #endregion
    
    #region Tag
    
    public class ModeloMoveMaisGridResponse
    {
        public List<ModeloMoveMaisItem> items { get; set; }
        public int totalItems { get; set; }
    }
    
    public class ModeloMoveMaisItem
    {
        public string Descricao { get; set; }
        public int? Id { get; set; }
    }
    public class TagReduzidaGridResponse
    {
        public List<TagReduzidaResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }
    
    public class TagReduzidaResponse
    {
        public int Id { get; set; }
        public long? SerialNumber { get; set; }
        public string DataCadastro { get; set; }
    }
    
    public class GridTagResponse
    {
        public List<GridTagItemResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }
    
    
    public class GridTagItemResponse
    {
        public int? Id { get; set; }
        public long? SerialNumber { get; set; }
        public string NomeEmpresa { get; set; }
        public int? IdEmpresa { get; set; }
        public string Placa { get; set; }
        public string Status { get; set; }
        public string UsuarioCadastro { get; set; }
        public string UsuarioVinculo { get; set; }
        public string UsuarioCancelamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataVinculo { get; set; }
        public string DataCancelamento { get; set; }
    }
    
    public class GridPassagensWebhookResponse
    {
        public List<GridPassagemWebhookItemResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }
    
    public class GridPassagemWebhookItemResponse
    {
        public long Id { get; set; }
        public string Lancamento { get; set; }
        public string DataOcorrencia { get; set; }
        public string DataProcessamento { get; set; }
        public string Tipo { get; set; }
        public string Placa { get; set; }
        public string Categoria { get; set; }
        public string Valor { get; set; }
        public string CnpjEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public string CnpjEmissorVp  { get; set; } 
        public string NomeEmissorVp  { get; set; }
        public bool PossuiContestacaoAtiva { get; set; }
    }
    
    public class ConsultarTagResponse
    {
        public int Id { get; set; }
        public long Serial { get; set; }
        public int IdVeiculo { get; set; }
        public int IdEmpresa { get; set; }
        public string NomeEmpresa { get; set; }
        public string Placa { get; set; }
        public string Status { get; set; }
        public string UsuarioCadastro { get; set; }
        public string UsuarioVinculo { get; set; }
        public string UsuarioCancelamento { get; set; }
        public string DataCadastro { get; set; }
        public string DataVinculo { get; set; }
        public string DataCancelamento { get; set; }
    }
    
    public class SalvarTagRequest
    {
        public int? ModeloId { get; set; }
        public long Serial { get; set; }
        public string Placa { get; set; }
    }

    public class VeiculoTagResponse
    {
        public int Id { get; set; }
        public string Placa { get; set; }
        public int? TagId { get; set; }
        public bool? Desbloqueado { get; set; }
        public long? Serial { get; set; }    
        public string ModeloDescricao { get; set; }
        public int? ModeloId { get; set; }
        public string Status { get; set; }
    }
    
    #endregion

    #region BLOQUEIOS

    public class BloqueiosTagUsuarioResponse
    {
        public List<BloqueiosTagUsuarioItemResponse> BloqueiosTag { get; set; }
    }
    
    public class BloqueiosTagUsuarioItemResponse
    {
        public int BloqueioTagTipo { get; set; }
        public string Descricao { get; set; }
        public bool Valor { get; set; }
    }
    
    public class BloqueiosTagUsuarioRequest
    {
        public List<BloqueiosTagUsuarioItemRequest> BloqueiosTag { get; set; }
    }
    
    public class BloqueiosTagUsuarioItemRequest
    {
        public int UsuarioId { get; set; }
        public int BloqueioTagTipo { get; set; }
        public bool Valor { get; set; }
    }
    
    public class GridValePedagioHubResponse
    {
        public List<GridValePedagioHubItemResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }
    
    public class GridPagamentosTagResponse
    {
        public List<PagamentosItemTagResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }
    public class GridValePedagioHubItemResponse
    {
        public int? Id { get; set; }
        public string RodagemDupla { get; set; }
        public int? Eixos { get; set; }
        public string RazaoSocial { get; set; }
        public string Ciot { get; set; }
        public string Cnpj { get; set; }
        public string Proprietario { get; set; }
        public string Fornecedor { get; set; }
        public string Valor { get; set; }
        public string DataEmissao { get; set; }
        public string Placa { get; set; }
        public string Mensagem { get; set; }
        public string DistanciaTotal { get; set; }
        public string ViagemId { get; set; }
        public int FornecedorEnum { get; set; }
    }
    
    public class ProvisionarRequest
    {
        public long? EventoSaldoTagId { get; set; }
        public OrigemProvisionamento Origem { get; set; }
        public string Recibo { get; set; }
        public int? IdViagem { get; set; }
        public decimal Valor { get; set; }
        public decimal? ValorTaxa { get; set; }
        public int IdEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string Praca { get; set; }
        public string Placa { get; set; }
        public FornecedorEnum Fornecedor { get; set; }
    }
    
    public class GridPagamentosTagRequest
    {
        public int? Take { get; set; }
        public int? Page { get; set; }
        public OrderFilters Order { get; set; }
        public List<QueryFilters> Filters { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public int? IdEmpresa { get; set; } = null;
        public FornecedorEnum Fornecedor { get; set; }
    }
    
    public class PagamentosItemTagResponse
    {
        public long Id { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string DataEmissao { get; set; }
        public string Placa { get; set; }
        public long? SerialNumber { get; set; }
        public string Praca { get; set; }
        public string Tipo { get; set; }
        public string Origem { get; set; }
        public string Taxa { get; set; }
        public string Valor { get; set; }
        public string StatusTaxa { get; set; }
        public string StatusPassagem { get; set; }
        public int? ViagemId { get; set; }
        public string MensagemTaxa { get; set; }
        public string MensagemValor { get; set; }
        public decimal? TaxaDecimal { get; set; }
        public decimal? ValorDecimal { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public int? IdCargaAvulsaTaxa { get; set; }
        public string ObservacaoCargaAvulsaTaxa { get; set; }
        public string DataCadastroCargaAvulsaTaxa { get; set; }
        public string DataCadastroCargaAvulsaValor { get; set; }
        public string ObservacaoCargaAvulsaValor { get; set; }
        public int? IdCargaAvulsaValor{ get; set; }
    }
    
    public class GridFaturamentoTagResponse
    {
        public List<FaturamentoTagItemResponse> items { get; set; }
        
        public int totalItems { get; set; }
    }

    public class FaturamentoTagItemResponse
    {
        public string RazaoSocial { get; set; }
        public int EmpresaId { get; set; }
        public string Cnpj { get; set; }
        public string ValorTotalPagoValePedagio { get; set; }
        public string ValorTotalNaoPagoValePedagio { get; set; }
        public string ValorEstornoTotalPagoValePedagio { get; set; }
        public string ValorEstornoTotalNaoPagoValePedagio { get; set; }
        public string ValorTotalPagoPedagio { get; set; }
        public string ValorTotalNaoPagoPedagio { get; set; }
        public string ValorEstornoTotalPagoPedagio { get; set; }
        public string ValorEstornoTotalNaoPagoPedagio { get; set; }
        public string TaxaTotalPagoValePedagio { get; set; }
        public string TaxaTotalNaoPagoValePedagio { get; set; }
        public string TaxaEstornoTotalPagoValePedagio { get; set; }
        public string TaxaEstornoTotalNaoPagoValePedagio { get; set; }
        public string TaxaTotalPagoPedagio { get; set; }
        public string TaxaTotalNaoPagoPedagio { get; set; }
        public string TaxaEstornoTotalPagoPedagio { get; set; }
        public string TaxaEstornoTotalNaoPagoPedagio { get; set; }
        public string ValorTotal { get; set; }
    }
    
    public class FaturamentoTotalizadorRequest
    {
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public FornecedorTagEnum Fornecedor { get; set; }
    }
    
    public class FaturaRequest
    {
        public int EmpresaId { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public FornecedorEnum FornecedorTag { get; set; }
    }
    
    public class FaturamentoTotalizadorResponse
    {
        public string ValorTotalEstornoProvisionado { get; set; }
        public string ValorTotalEstornoNaoProvisionado { get; set; }
        public string ValorTotalProvisionado { get; set; }
        public string ValorTotalNaoProvisionado { get; set; }
        public string ValorTotal { get; set; }
    }

    public class PagamentoTagRequest
    {
        public long Id { get; set; }
        public ETipoPagamento Tipo { get; set; }
        public decimal Valor { get; set; }
        public decimal? Taxa { get; set; }
        public bool PagarViaCargaAvulsa { get; set; }
        public ETipoTransacaoTag TipoTransacao { get; set; }
        public string Recibo { get; set; }
        public int? IdViagem { get; set; }
        public int IdEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string Praca { get; set; }
        public string Placa { get; set; }
    }
    
    public class EstornoTagRequest
    {
        public long Id { get; set; }
        public ETipoPagamento Tipo { get; set; }
    }



    public class FaturaTagGetResponse
    {
       public List<PassagensVPFornecedorTagResponse> PassagensVPFornecedor { get; set; }
       public List<PassagensPedagioTagResponse> PassagensPedagio { get; set; }
       public List<MensalidadeTagResponse> MensalidadeTag { get; set; }
       public EmpresaFaturamentoTagResponse Empresa { get; set; }
       public DadosGeraisTagResponse DadosGerais { get; set; } 
       public TributaveisTagResponse Tributaveis { get; set; } = new TributaveisTagResponse();
       public NaoTributaveisTagResponse NaoTributaveis { get; set; } = new NaoTributaveisTagResponse();
       public TotalizadorTagResponse Totalizador { get; set; } = new TotalizadorTagResponse();
    }

    public class PassagensVPFornecedorTagResponse
    {
        public FornecedorTagEnum Fornecedor { get; set; }
        public List<PassagensValePedagioTag> PassagensValePedagio { get; set; }
    }

    public class PassagensValePedagioTag
    {
        public int? ViagemId { get; set; }
        public DateTime? DataCriacao { get; set; }
        public string Tipo { get; set; }
        public string Placa { get; set; }
        public long? Serial { get; set; }
        public decimal? Valor { get; set; }
        public decimal? Taxa { get; set; }
        public string StatusTaxa { get; set; }
        public string StatusValor { get; set; }
    }

    public class PassagensPedagioTagResponse
    {
        public DateTime? DataPassagem { get; set; }
        public string DescricaoPassagem { get; set; }
        public long? Serial { get; set; }
        public decimal? Valor { get; set; }
        public decimal? Taxa { get; set; }
        public string StatusTaxa { get; set; } 
        public string StatusValor { get; set; } 
        public string Tipo { get; set; }
        public string Placa { get; set; }
    }

    public class MensalidadeTagResponse
    {
        public string Placa { get; set; }
        public long Serial { get; set; }
        public string MesReferente { get; set; }
        public decimal Valor { get; set; }
    }

    public class EmpresaFaturamentoTagResponse
    {
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string Email { get; set; }
        public string Telefone { get; set; }
        public string Endereco { get; set; }
    }

    public class DadosGeraisTagResponse
    {
        public string Vigencia { get; set; }
        public DateTime? Emissao { get; set; }
        public DateTime? Vencimento { get; set; }
        public string NumeroFatura { get; set; }
    }

    public class TributaveisTagResponse
    {
        public int? MensalidadeQtd { get; set; }
        public decimal? MensalidadeValorTotal { get; set; }
        public decimal? ValorTotal { get; set; }
    }

    public class NaoTributaveisTagResponse
    {
        public int? PassagensValePedagioQtd { get; set; }
        public decimal? PassagensValePedagioValorTotal { get; set; }
        public int? PassagensPedagioQtd { get; set; }
        public decimal? PassagensPedagioValorTotal { get; set; }
        public int? EstornoValePedagioQtd { get; set; }
        public decimal? EstornoValePedagioTotal { get; set; }
        public int? EstornoPedagioQtd { get; set; }
        public decimal? EstornoPedagioTotal { get; set; }
        public decimal? ValorTotal { get; set; }
    }

    public class TotalizadorTagResponse
    {
        public decimal? EstornoPago { get; set; }
        public decimal? EstornoNaoPago { get; set; }
        public decimal? TotalPago { get; set; }
        public decimal? TotalNaoPago { get; set; }
        public decimal? ValorFatura { get; set; }
    }
    
    public class GridEmpresasUtilizamTagExtratta
    {
        public List<EmpresasUtilizamTagExtratta> items { get; set; }
        
        public int totalItems { get; set; }
    }

    public class EmpresasUtilizamTagExtratta
    {
        public int IdEmpresa { get; set; }
        public string CNPJ { get; set; }
        public string RazaoSocial { get; set; }
    }
    
    public class ReportPassagensPedagioCompraHub 
    {
        public string Extensao { get; set; }
        public int CompraId { get; set; }
        public FornecedorEnum Fornecedor { get; set; }
    }
    
    public class ContestarPassagemRequest
    {
        public long? Id { get; set; }
        public string Razao { get; set; }
        public ETipoContestacao? Tipo { get; set; }
    }
    
    public enum ETipoContestacao
    {
        CategoriaDivergente = 1,
        DesconheceUtilizacao = 2,
        Duplicidade = 3
    }

    public enum OrigemProvisionamento
    {
        ValePedagio,
        AdicionalValePedagio,
        Pedagio
    }
    
    public enum ETipoPagamento
    {
        [EnumMember, Description("Taxa")]
        Taxa = 0,
        
        [EnumMember, Description("Pedagio")]
        Pedagio = 1
    }
    
    public enum EStatusRemessaTag
    {
        [Description("Pendente")]
        Pendente = 1,
        [Description("Recebida")]
        Recebida = 2
    }
    
    public enum ETipoTransacaoTag
    {
        [EnumMember, Description("Estorno")]
        Estorno = 0,
        
        [EnumMember, Description("Cobranca")]
        Cobranca = 1
    }

    #endregion

    public class PlacaFornecedorResponse
    {
        public StatusPlaca Status {  get; set; }
        public string Mensagem { get; set; }
    }
    
    public class SaldoValePedagioVeiculoTagResponse
    {
        public string Saldo { get; set; }
    }

    public enum StatusPlaca
    {
        [EnumMember, Description("Placa invalida")]
        Invalida = 0,

        [EnumMember, Description("Placa valida")]
        Valida = 1,

        [EnumMember, Description("Fornecedor indisponivel")]
        Indisponivel = 2
    }
}
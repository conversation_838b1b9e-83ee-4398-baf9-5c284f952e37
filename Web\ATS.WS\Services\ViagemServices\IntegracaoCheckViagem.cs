using System;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Services.ViagemServices
{
    public class IntegracaoCheckViagem : SrvBase
    {
        private readonly IViagemApp _viagemApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaApp _empresaApp;

        public IntegracaoCheckViagem(IViagemApp viagemApp, IUsuarioApp usuarioApp, IEmpresaApp empresaApp)
        {
            _viagemApp = viagemApp;
            _usuarioApp = usuarioApp;
            _empresaApp = empresaApp;
        }

        /// <summary>
        ///     Integrar a check de uma respectiva viagem
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<string> IntegrarCheck(ViagemCheckRequestModel @params)
        {
            try
            {
                if (@params.IdViagem <= 0)
                    return new Retorno<string>(false, @"Código da viagem inválido.", null);

                var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFMotorista);
                if (!idUsuario.HasValue)
                    return new Retorno<string>(false, @"Usuário inválido.", null);

                var empresa = _empresaApp.Get(@params.IdEmpresa, null);
                if (empresa == null)
                    return new Retorno<string>(false, @"Código do empresa inválido.", null);

                //Sempre que integrar uma viagem check, grava um check-in para identificar que o check-in pertence a uma viagem
                var checkin = new CheckIn
                {
                    IdUsuario = idUsuario.Value,
                    DataHora = @params.DataHora,
                    Latitude = @params.Latitude,
                    Longitude = @params.Longitude,
                    TipoEvento = ETipoEvento.Automatico,
                    IdViagem = @params.IdViagem,
                    IdEmpresa = @params.IdEmpresa
                };

                var viagemCheck = new ViagemCheck
                {
                    IdUsuario = idUsuario.Value,
                    IdEmpresa = @params.IdEmpresa,
                    Status = @params.Status,
                    DataHora = @params.DataHora
                };

                var validationResult =
                    _viagemApp.IntegrarCheck(@params.IdViagem, @params.IdEmpresa, viagemCheck, checkin);
                if (!validationResult.IsValid)
                    throw new Exception(validationResult.ToFormatedMessage());

                return new Retorno<string>(true, string.Empty);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{e.Message}");
            }
        }
    }
}
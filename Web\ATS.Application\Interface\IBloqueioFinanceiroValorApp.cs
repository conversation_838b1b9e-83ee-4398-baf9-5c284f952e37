﻿using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;

namespace ATS.Application.Interface
{
    public interface IBloqueioFinanceiroValorApp : IAppBase<BloqueioFinanceiroValor>
    {
        IQueryable<BloqueioFinanceiroValor> GetAll();
        BloqueioFinanceiroValorDto[] GetBloqueioFinanceiroValor(int? idEmpresa, int? idFilial);
        void IntegrarValores(BloqueioFinanceiroValorDto[] valores);
    }
}
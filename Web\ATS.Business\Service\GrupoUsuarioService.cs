﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.Reports.GrupoUsuario;

namespace ATS.Domain.Service
{
    public class GrupoUsuarioService : ServiceBase, IGrupoUsuarioService
    {
        private readonly IGrupoUsuarioRepository _repository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IGrupoUsuarioMenuRepository _grupoUsuarioMenuRepository;

        public GrupoUsuarioService(IGrupoUsuarioRepository repository, IUsuarioRepository usuarioRepository, IGrupoUsuarioMenuRepository grupoUsuarioMenuRepository)
        {
            _repository = repository;
            _usuarioRepository = usuarioRepository;
            _grupoUsuarioMenuRepository = grupoUsuarioMenuRepository;
        }
        /// <summary>
        /// Retorna as mensagens de validação se o registro for válido para os processos de inserção e atualização
        /// </summary>
        /// <param name="grpUsuario">Entidade de Grupo de Usuário</param>
        /// <param name="processo">Processo que esta sendo validado</param>
        /// <returns></returns>
        private ValidationResult IsValidToCrud(GrupoUsuario grpUsuario, EProcesso processo)
        {
            ValidationResult validationResult = new ValidationResult();

            validationResult.Add(AssertionConcern.AssertArgumentHasElement(grpUsuario.Menus, $"É obrigatório selecionar ao menos um Menu ou Módulo."));

            return validationResult;
        }

        /// <summary>
        /// Validar a permissão do usuário para manusear o registro
        /// </summary>
        /// <param name="id">Código do registro.</param>
        /// <param name="idUsuarioLogOn">Código do usuário logado.</param>
        /// <returns></returns>
        private ValidationResult ValidarUsuario(int id, int idUsuarioLogOn)
        {
            ValidationResult validationResult = new ValidationResult();

            EPerfil perfilUsuario = _usuarioRepository.GetPerfil(idUsuarioLogOn);
            if (perfilUsuario == EPerfil.Empresa)
                if (_usuarioRepository.Get(idUsuarioLogOn).IdEmpresa != _repository.Get(id).IdEmpresa)
                    validationResult.Add($"Usuário não possui permissão para alterar registros do Empresa vinculado ao Grupo.");
                else if (perfilUsuario != EPerfil.Administrador)
                    validationResult.Add($"Perfil do usuário é inválido para este procedimento.");

            return validationResult;
        }

        /// <summary>
        /// Retorna o grupo de usuário
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GrupoUsuario Get(int id)
        {
            return _repository.Get(id);
        }

        /// <summary>
        /// Retorna o grupo de usuário contendo todos os elementos de referência
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GrupoUsuario GetChilds(int id)
        {
            return _repository.GetChilds(id);
        }

        /// <summary>
        /// Atualizar o registro do grupo de usuário
        /// </summary>
        /// <param name="grupoUsuario"></param>
        /// <returns></returns>
        public ValidationResult Update(GrupoUsuario grupoUsuario, List<GrupoUsuarioMenu> grupoUsuarioMenu)
        {
            try
            {
                _repository.Update(grupoUsuario);

                var menus = _grupoUsuarioMenuRepository.Find(x => x.IdGrupoUsuario == grupoUsuario.IdGrupoUsuario);

                foreach (var menuGrupo in menus)
                {
                    _grupoUsuarioMenuRepository.Delete(menuGrupo);
                }

                foreach (var menu in grupoUsuarioMenu)
                    _grupoUsuarioMenuRepository.Add(menu);

                ValidationResult validationResult = IsValidToCrud(grupoUsuario, EProcesso.Update);
                if (!validationResult.IsValid)
                    return validationResult;
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public void Inserir(int idEmpresa, string descricao, List<int> idMenusSelecionados, int? idEstabelecimentoBase)
        {
            if (idEmpresa < 1)
                throw new Exception($"IdEmpresa {idEmpresa} é inválido!");

            if (string.IsNullOrWhiteSpace(descricao))
                throw new Exception("A descrição é obrigatória!");

            var obj = new GrupoUsuario
            {
                Ativo = true,
                IdEmpresa = idEmpresa,
                Descricao = descricao,
                IdEstabelecimentoBase = idEstabelecimentoBase,
                Menus = new List<GrupoUsuarioMenu>()
            };

            if (idMenusSelecionados == null || !idMenusSelecionados.Any())
                throw new Exception("Selecione ao menos um menu para prosseguir!");

            idMenusSelecionados.ForEach(x => obj.Menus.Add(new GrupoUsuarioMenu
            {
                IdMenu = x
            }));

            _repository.Add(obj);
        }

        public void Atualizar(int idGrupoUsuario, int idEmpresa, string descricao, List<int> idMenusSelecionados)
        {
            var grpUsu = _repository.GetChilds(idGrupoUsuario);
            
            if (grpUsu == null)
                throw new Exception($"Nenhum grupo de usuário encontraro para o id {idGrupoUsuario}!");
            
            grpUsu.Descricao = descricao;
            
            grpUsu.Menus = new List<GrupoUsuarioMenu>();
            
            idMenusSelecionados.ForEach(mm => grpUsu.Menus.Add(new GrupoUsuarioMenu
            {
                IdMenu = mm
            }));
            
            _repository.Update(grpUsu);
        }

        public DataModel<GrupoUsuarioModel> ConsultarGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var grupos = _repository
                .All().Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                grupos = grupos.Where(o => o.IdEmpresa == idEmpresa);

            if (idEstabelecimentoBase.HasValue)
                grupos = grupos.Where(o => o.IdEstabelecimentoBase.HasValue && o.IdEstabelecimentoBase == idEstabelecimentoBase);

            grupos = string.IsNullOrWhiteSpace(order?.Campo)
                ? grupos.OrderBy(x => x.IdGrupoUsuario)
                : grupos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            grupos = grupos.AplicarFiltrosDinamicos<GrupoUsuario>(filters);

            return new DataModel<GrupoUsuarioModel>
            {
                totalItems = grupos.Count(),
                items = grupos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new GrupoUsuarioModel
                {
                    IdGrupoUsuario = x.IdGrupoUsuario,
                    Ativo = x.Ativo,
                    Descricao = x.Descricao,
                    NomeFantasiaEmpresa = x.Empresa?.NomeFantasia
                })
            };
        }

        /// <summary>
        /// Ativar o registro
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <param name="idUsuarioLogOn">Código do usuário que esta executando o processo</param>
        /// <returns></returns>
        public ValidationResult Ativar(int id, int idUsuarioLogOn)
        {
            ValidationResult validationResult;

            try
            {
                #region Validação de usuário

                validationResult = ValidarUsuario(id, idUsuarioLogOn);
                if (!validationResult.IsValid)
                    return validationResult;

                #endregion

                GrupoUsuario grpUsuario = _repository.Get(id);
                grpUsuario.Ativo = true;

                _repository.Update(grpUsuario);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Desativar o registro
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <param name="idUsuarioLogOn">Código do usuário que esta executando o processo</param>
        /// <returns></returns>
        public ValidationResult Inativar(int id, int idUsuarioLogOn)
        {
            ValidationResult validationResult = new ValidationResult();

            try
            {
                #region Validação de usuário

                validationResult = ValidarUsuario(id, idUsuarioLogOn);
                if (!validationResult.IsValid)
                    return validationResult;

                #endregion

                #region Validações

                IQueryable<Usuario> retUsuarioVinc
                    = _usuarioRepository.GetPorGrupoUsuario(id);

                if (retUsuarioVinc.Any())
                    return new ValidationResult().Add($"Existem um ou mais usuários ativos vinculados a este grupo. Não é possível inativá-lo.");

                #endregion

                GrupoUsuario grpUsuario = _repository.Get(id);
                grpUsuario.Ativo = false;

                _repository.Update(grpUsuario);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Consultar os grupos de usuário a partir dos dados de filtro
        /// </summary>
        /// <param name="nome">Nome</param>
        /// <param name="idEmpresa">ID do Empresa</param>
        /// /// <param name="idEstabelecimentoBase">ID do estabelecimento base</param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase)
        {
            return _repository.Consultar(!string.IsNullOrWhiteSpace(nome) ? nome : null, idEmpresa, idEstabelecimentoBase);
        }

        /// <summary>
        /// Adicionar o grupo de usuário
        /// </summary>
        /// <param name="grupoUsuario"></param>
        /// <returns></returns>
        public ValidationResult Add(GrupoUsuario grupoUsuario)
        {
            try
            {
                ValidationResult validationResult = IsValidToCrud(grupoUsuario, EProcesso.Create);
                if (!validationResult.IsValid)
                    return validationResult;

                _repository.Add(grupoUsuario);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna a lista de grupos de usuário do Empresa
        /// </summary>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="idEstabelecimentoBase"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase)
        {
            return _repository.GetPorEmpresa(idEmpresa, idEstabelecimentoBase);
        }

        public IQueryable<GrupoUsuario> GetQuery()
        {
            return _repository.Query();
        }

        public byte[] GerarRelatorioGrid(int? idEmpresa, int? idEstabelecimentoBase, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var grupos = ConsultarGrid(idEmpresa, idEstabelecimentoBase, int.MaxValue, 1, orderFilters, filters);

            return new RelatorioGrupoUsuario().GetReport(grupos.items, tipoArquivo, logo);
        }
    }
}
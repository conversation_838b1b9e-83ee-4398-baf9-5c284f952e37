﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IMotoristaRepository : IRepository<Motorista>
    {
        int? GetIdMotoristaPorCpf(string cpf);
        Motorista GetFromAllTables(string cpfCnpj);
        IQueryable<Motorista> GetIdsMotoristasAtualizados(DateTime dataAtualizacao);
    }
}
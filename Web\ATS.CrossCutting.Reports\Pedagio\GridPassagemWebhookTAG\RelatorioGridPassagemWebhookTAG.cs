﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Pedagio.GridPassagemWebhookTAG
{
    public class RelatorioGridPassagemWebhookTag
    {
        public byte[] GetReport(string tipo, GridPassagemWebhookTagDataType dadosRelatorio)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaGridPassagemWebhookTAG"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Pedagio.GridPassagemWebhookTAG.RelatorioGridPassagemWebhookTAG.rdlc";

                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

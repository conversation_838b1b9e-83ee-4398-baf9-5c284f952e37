using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using Dapper;

namespace ATS.Data.Repository.Dapper
{
    public class ConsultaCheckinDapper : DapperFactory<CheckIn>, ICheckinDapper
    {
        public List<CheckinConsultaModel> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf)
        {
            string query = $@"SELECT 
								E.cnpj as CNPJEmpresa,
								E.Razao<PERSON>ocial as RazaoSocial,
								M.CPF as CPFMotorista,
								M.nome as NomeMotorista,
								cid.ibge  as IBGECidade,
								cid.nome as DescricaoCidade,
								es.sigla as SiglaUf,
								u.cpfcnpj as CPFUsuario,
								u.nome as NomeUsuario,
								c.datahora as DataHora,
								c.idcheckin as IdCheckIn,
								c.tipoevento as TipoEvento,
								c.idmotorista as IdMotorista,
								c.idviagem  as IdViagem,
								c.latitude as Latitude,
								c.longitude as Longitude,
								case when c.idmotorista is NOT null then VEM.placa
								else VEU.placa	end as Placa
							FROM 
								CHECKIN C
								LEFT JOIN VIAGEM V ON V.idviagem = C.idviagem
								LEFT JOIN CIDADE cid ON cid.idcidade = c.idcidadecheckin
								LEFT JOIN ESTADO ES ON ES.idestado = CID.idestado
								LEFT JOIN MOTORISTA m ON m.idmotorista = c.idmotorista
								LEFT JOIN VEICULO_MOTORISTA VM ON VM.idmotorista = M.idmotorista
								LEFT JOIN USUARIO U ON U.idusuario = C.idusuario
								LEFT JOIN EMPRESA E ON E.idempresa = C.idempresa
								LEFT JOIN VEICULO VEM ON VEM.idmotorista = M.idmotorista
								LEFT JOIN VEICULO VEU ON VEU.idusuario = U.idusuario
							WHERE 
								C.datahora BETWEEN @DataInicio AND @DataFim
								AND ((C.idmotorista IS NOT NULL AND M.cpf = @cpf) OR 
								(C.idusuario IS NOT NULL AND U.cpfcnpj = @cpf))";
            
            using (var connection = new DapperContext().GetConnection)
            {
	            var parameters = new
	            {
		            cpf, 
		            DataInicio = dataInicial,
		            DataFim = dataFinal
	            };
                
	            //var totalRegistros = connection.Query<int>(queryTotalizador, parameters).FirstOrDefault();
	            var registros = connection.Query<CheckinConsultaModel>(query, parameters).ToList();

	            return registros;
            }
        }

        public List<CheckinConsultaModel> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa)
        {
	        string query = $@"SELECT 
								E.cnpj as CNPJEmpresa,
								E.RazaoSocial as RazaoSocial,
								M.CPF as CPFMotorista,
								M.nome as NomeMotorista,
								cid.ibge  as IBGECidade,
								cid.nome as DescricaoCidade,
								es.sigla as SiglaUf,
								u.cpfcnpj as CPFUsuario,
								u.nome as NomeUsuario,
								c.datahora as DataHora,
								c.idcheckin as IdCheckIn,
								c.tipoevento as TipoEvento,
								c.idmotorista as IdMotorista,
								c.idviagem  as IdViagem,
								c.latitude as Latitude,
								c.longitude as Longitude,
								case when c.idmotorista is NOT null then VEM.placa
								else VEU.placa	end as Placa
							FROM 
								CHECKIN C
								LEFT JOIN VIAGEM V ON V.idviagem = C.idviagem
								LEFT JOIN CIDADE cid ON cid.idcidade = c.idcidadecheckin
								LEFT JOIN ESTADO ES ON ES.idestado = CID.idestado
								LEFT JOIN MOTORISTA m ON m.idmotorista = c.idmotorista
								LEFT JOIN VEICULO_MOTORISTA VM ON VM.idmotorista = M.idmotorista
								LEFT JOIN USUARIO U ON U.idusuario = C.idusuario
								LEFT JOIN EMPRESA E ON E.idempresa = C.idempresa
								LEFT JOIN VEICULO VEM ON VEM.idmotorista = M.idmotorista
								LEFT JOIN VEICULO VEU ON VEU.idusuario = U.idusuario
							WHERE 
								C.idviagem IS NOT NULL AND 
								V.IDEMPRESA = @IdEmpresa AND
								V.placa = @placa AND 
								C.datahora BETWEEN @DataInicio AND @DataFim";
            
	        using (var connection = new DapperContext().GetConnection)
	        {
		        var parameters = new
		        {
			        IdEmpresa,
			        placa, 
			        DataInicio = dataInicial,
			        DataFim = dataFinal
		        };
                
		        var registros = connection.Query<CheckinConsultaModel>(query, parameters).ToList();

		        return registros;
	        }
        }

        public List<CheckinConsultaModel> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, int idMotoristaVeiculo)
        {
	        string query = $@"SELECT 
								E.cnpj as CNPJEmpresa,
								E.RazaoSocial as RazaoSocial,
								M.CPF as CPFMotorista,
								M.nome as NomeMotorista,
								cid.ibge  as IBGECidade,
								cid.nome as DescricaoCidade,
								es.sigla as SiglaUf,
								u.cpfcnpj as CPFUsuario,
								u.nome as NomeUsuario,
								c.datahora as DataHora,
								c.idcheckin as IdCheckIn,
								c.tipoevento as TipoEvento,
								c.idmotorista as IdMotorista,
								c.idviagem  as IdViagem,
								c.latitude as Latitude,
								c.longitude as Longitude,
								case when c.idmotorista is NOT null then VEM.placa
								else VEU.placa	end as Placa
							FROM 
								CHECKIN C
								LEFT JOIN VIAGEM V ON V.idviagem = C.idviagem
								LEFT JOIN CIDADE cid ON cid.idcidade = c.idcidadecheckin
								LEFT JOIN ESTADO ES ON ES.idestado = CID.idestado
								LEFT JOIN MOTORISTA m ON m.idmotorista = c.idmotorista
								LEFT JOIN VEICULO_MOTORISTA VM ON VM.idmotorista = M.idmotorista
								LEFT JOIN USUARIO U ON U.idusuario = C.idusuario
								LEFT JOIN EMPRESA E ON E.idempresa = C.idempresa
								LEFT JOIN VEICULO VEM ON VEM.idmotorista = M.idmotorista
								LEFT JOIN VEICULO VEU ON VEU.idusuario = U.idusuario
							WHERE 
								c.IDEMPRESA = @IdEmpresa AND
								c.idMotorista = @idMotorista AND 
								C.datahora BETWEEN @DataInicio AND @DataFim";
            
	        using (var connection = new DapperContext().GetConnection)
	        {
		        var parameters = new
		        {
			        IdEmpresa,
			        idMotorista = idMotoristaVeiculo, 
			        DataInicio = dataInicial,
			        DataFim = dataFinal
		        };
                
		        var registros = connection.Query<CheckinConsultaModel>(query, parameters).ToList();

		        return registros;
	        }
        }
    }
}
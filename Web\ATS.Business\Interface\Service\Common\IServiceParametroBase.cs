using System;
using System.Collections.Generic;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service.Common
{
    public interface IServiceParametroBase<TEnum> : IDisposable where TEnum : struct
    {
        ValidationResult SetParametro<T>(TEnum parametro, T valor, string idregistro);
        ValidationResult SetParametro<T>(TEnum parametro, T valor, int idregistro);
        T GetParametro<T>(TEnum parametro, int idregistro);
        T GetParametro<T>(TEnum parametro, string idregistro);
        Dictionary<TEnum, T> GetParametros<T>(IList<TEnum> parametros, string idregistro);
        Dictionary<TEnum, T> GetParametros<T>(IList<TEnum> parametros, int idregistro);
        bool AnyParametro(IEnumerable<TEnum> parametros, int idregistro);
        bool AnyParametro(IEnumerable<TEnum> parametros, string idregistro);
    }
}
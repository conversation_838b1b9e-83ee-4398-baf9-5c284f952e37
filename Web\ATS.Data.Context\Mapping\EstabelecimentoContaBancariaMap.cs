﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoContaBancariaMap : EntityTypeConfiguration<EstabelecimentoContaBancaria>
    {
        public EstabelecimentoContaBancariaMap()
        {
            ToTable("ESTABELECIMENTO_CONTA_BANCARIA");

            HasKey(t => t.IdEstabelecimentoContaBancaria);

            HasRequired(o => o.Estabelecimento)
                        .WithMany(o => o.EstabelecimentoContasBancarias)
                        .HasForeignKey(o => o.IdEstabelecimento);

            Property(o => o.NomeConta)
                        .IsRequired()
                        .HasMaxLength(100);

            Property(o => o.CodigoBanco)
                        .IsRequired()
                        .HasMaxLength(10);

            Property(o => o.NomeBanco)
                        .IsRequired()
                        .HasMaxLength(100);

            Property(o => o.Agencia)
                        .IsRequired()
                        .HasMaxLength(10);

            Property(o => o.Conta)
                        .IsRequired()
                        .HasMaxLength(30);

            Property(o => o.DigitoConta)
                        .IsOptional()
                        .HasMaxLength(10);

            Property(o => o.TipoConta)
                        .IsRequired();

            Property(o => o.CnpjTitular)
                        .IsRequired()
                        .HasMaxLength(14);

            Property(o => o.NomeTitular)
                        .IsOptional()
                        .HasMaxLength(100);
        }
    }
}

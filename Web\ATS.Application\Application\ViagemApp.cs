﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Pedagio.PedagioAvulso;
using ATS.CrossCutting.Reports.Pedagio.Recibo;
using ATS.CrossCutting.Reports.Pedagio.ReciboPagamento;
using ATS.CrossCutting.Reports.Viagem.ConsultaViagem;
using ATS.Domain.DTO;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Common.Request;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ESituacaoRelatorioCiot = ATS.CrossCutting.Reports.Viagem.ConsultaViagem.ESituacaoRelatorioCiot;

namespace ATS.Application.Application
{
    public class ViagemApp : BaseApp<IViagemService>, IViagemApp
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IPedagioViagemApp _pedagioViagemApp;
        private readonly ICheckInService _checkInService;
        private readonly IViagemPendenteGestorService _viagemPendenteGestorService;
        private readonly IUsuarioService _usuarioService;
        private readonly PedagioAppFactoryDependencies _pedagioAppFactoryDependencies;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IViagemRepository _viagemRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IUsuarioPermissaoFinanceiroApp _usuarioPermissaoFinanceiroApp;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IUserIdentity _userIdentity;

        public ViagemApp(ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemService service, IVersaoAnttLazyLoadService versaoAntt,
            IPedagioViagemApp pedagioViagemApp, ICheckInService checkInService, IViagemPendenteGestorService viagemPendenteGestorService, 
            IUsuarioService usuarioService, PedagioAppFactoryDependencies pedagioAppFactoryDependencies, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, ICidadeApp cidadeApp,
            IEstabelecimentoApp estabelecimentoApp, IEmpresaApp empresaApp, IViagemRepository viagemRepository, IViagemEventoRepository viagemEventoRepository, IUsuarioPermissaoFinanceiroApp usuarioPermissaoFinanceiroApp, IParametrosEmpresaService parametrosEmpresaService, IParametrosProprietarioService parametrosProprietarioService, IProprietarioRepository proprietarioRepository, IParametrosUsuarioService parametrosUsuarioService, IUserIdentity userIdentity) : base(service)
        {
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
            _pedagioViagemApp = pedagioViagemApp;
            _checkInService = checkInService;
            _viagemPendenteGestorService = viagemPendenteGestorService;
            _usuarioService = usuarioService;
            _pedagioAppFactoryDependencies = pedagioAppFactoryDependencies;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _cidadeApp = cidadeApp;
            _estabelecimentoApp = estabelecimentoApp;
            _empresaApp = empresaApp;
            _viagemRepository = viagemRepository;
            _viagemEventoRepository = viagemEventoRepository;
            _ciotV2App = ciotV2App;
            _usuarioPermissaoFinanceiroApp = usuarioPermissaoFinanceiroApp;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosProprietarioService = parametrosProprietarioService;
            _proprietarioRepository = proprietarioRepository;
            _parametrosUsuarioService = parametrosUsuarioService;
            _userIdentity = userIdentity;
        }

        /// <summary>
        /// Retorna Viagem por IdViagem
        /// </summary>
        /// <param name="idViagem">Id de Viagem</param>
        /// <param name="comIncludes"></param>
        /// <returns>Objeto de Viagem</returns>
        public Viagem Get(int idViagem, bool comIncludes = true)
        {
            return _viagemRepository.Get(idViagem, comIncludes);
        }

        public Viagem GetComViagemEstabelecimentos(int idViagem)
        {
            return Service.GetComViagemEstabelecimentos(idViagem);
        }

        public object GetEventosViagem(string cpfCnpjProprietario, string cpfMotorista, List<EStatusViagemEvento> statusEvento = null, List<ETipoEventoViagem> tipoEvento = null, DateTime? dataInicio = null, DateTime? dataFim = null)
        {
            return Service.GetEventosViagem(cpfCnpjProprietario, cpfMotorista,  statusEvento, tipoEvento, dataInicio, dataFim);
        }

        public bool PossuiResgateDeSaldoResidualSolicitadoNoDia(string documento, DateTime dia)
        {
            return Service.PossuiResgateDeSaldoResidualSolicitadoNoDia(documento, dia);
        }

        public object GetHistoricoViagem(string placa, int idEmpresa, int skip)
        {
            return Service.GetHistoricoViagem(placa, idEmpresa, skip);
        }

        public byte[] GerarRelatorioPagamentos(int idEmpresa, DateTime dataInicial, DateTime dataFinal, ETipoArquivoPagamento? tipoRelatorio)
        {
            return Service.GerarRelatorioPagamentos(idEmpresa, dataInicial, dataFinal, tipoRelatorio);
        }

        public object GetCartasPagas(int? idEmpresa, int ano, int mes)
        {
            return Service.GetCartasPagas(idEmpresa, ano, mes);
        }

        public object GetCartasEmAberto(int? idEmpresa, int ano, int mes)
        {
            return Service.GetCartasEmAberto(idEmpresa, ano, mes);
        }

        public object GetTotalCartasMes(int? idEmpresa, int ano)
        {
            return Service.GetTotalCartasMes(idEmpresa, ano);
        }

        public object GetTotalGanhosPorMes(int? idEmpresa, int ano)
        {
            return Service.GetTotalGanhosPorMes(idEmpresa, ano);
        }

        public object GetTotalPagamentos(int? idEmpresa, int ano, int mes) => Service.GetTotalPagamentos(idEmpresa, ano, mes);

        public ConsultaPagamentosModel GetPagamentosPorViagem(int idEmpresa, DateTime dataInicial, DateTime dataFinal, int take, int page, OrderFilters order = null, List<QueryFilters> filters = null)
        {
            return Service.GetPagamentosPorViagem(idEmpresa, dataInicial, dataFinal, take, page, order, filters);
        }

        public ConsultaPagamentosModel GetRelatorioPagamentosPorViagem(int idEmpresa, DateTime dataInicio, DateTime dataFim)
        {
            return Service.GetRelatorioPagamentosPorViagem(idEmpresa, dataInicio, dataFim);
        }

        /// <summary>
        /// Busca todas viagens, podendo ser, deste mes, e todas do mês passado
        /// </summary>
        /// <param name="tpBusca"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idFilial"></param>
        /// <param name="idTipoCavalo"></param>
        /// <param name="idTipoCarreta"></param>
        /// <returns></returns>
        public IEnumerable<Viagem> GetTodasViagens(ETipoBuscaViagens tpBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta)
        {
            return Service.GetViagens(tpBusca, idEmpresa, idFilial, idTipoCavalo, idTipoCarreta);
        }

        /// <summary>
        /// Retorna uma lista de viagens que estão com o status em Viagem
        /// </summary>
        /// <returns></returns>
        public IEnumerable<Viagem> GetViagensEmViagem(int idEmpresa)
        {
            return Service.GetViagensEmViagem(idEmpresa);
        }

        /// <summary>
        /// Adicionar um novo registro de viagem
        /// </summary>
        /// <param name="viagem">Informações da viagem</param>
        /// <param name="IsPedagioAvulso">Do tipo viagem avulso</param>
        /// <returns></returns>
        public ValidationResult Add(Viagem viagem,bool IsPedagioAvulso = false)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = Service.Add(viagem);
                    
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Viagem viagem)
        {
            ValidationResult validationResult = new ValidationResult();
            try 
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.Serializable}))
                {
                    validationResult.Add(Service.Update(viagem));
                    transaction.Complete();
                }
            }
            catch (Exception ex)
            {
                var e = ex.GetBaseException();
                if (e.ToString().Contains("deadlock"))
                    validationResult.Add("A viagem está em atualização em outro processo e não é permitido alterações simultâneas");
                else
                    validationResult.Add($"{e.Message} {(e.InnerException != null ? e.InnerException.Message : string.Empty)}".Trim());
            }

            return validationResult;
        }

        /// <summary>
        /// Integrar um novo status (check) da viagem
        /// </summary>
        /// <param name="idViagem">Código da Viagem</param>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="check">Informações do status</param>
        /// <param name="checkIn"></param>
        /// <returns></returns>
        public ValidationResult IntegrarCheck(int idViagem, int idEmpresa, ViagemCheck check, CheckIn checkIn)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = Service.IntegrarCheck(idViagem, idEmpresa, check);

                    if (!validationResult.IsValid)
                        return validationResult;

                    validationResult = _checkInService.Add(checkIn);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna ultimo ViagemCheck de uma viagem
        /// </summary>
        /// <param name="idViagem"></param>
        /// <returns>ViagemCheck</returns>
        public ViagemCheck GetUltimoViagemCheck(int idViagem)
        {
            return Service.GetUltimoViagemCheck(idViagem);
        }

        /// <summary>
        /// Retorna a ultima viagem com status aberto para determinada placa
        /// </summary>
        /// <param name="placa"></param>
        /// <returns></returns>
        public Viagem GetViagemAbertaPlaca(string placa)
        {
            return Service.GetViagemAbertaPlaca(placa);
        }

        /// <summary>
        /// Retorna a ultima viagem fechada de determinada placa
        /// </summary>
        /// <param name="placa"></param>
        /// <returns></returns>
        public Viagem GetUltimaViagemFechadaPlaca(string placa)
        {
            return Service.GetUltimaViagemFechadaPlaca(placa);
        }

        /// <summary>
        /// Retorna id da última viagem realizada pela placa
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public List<FrotaUtilizadaModel> GetUltimaViagemFrota(List<string> placa, int idEmpresa)
        {
            return Service.GetUltimaViagemFrota(placa, idEmpresa);
        }

        /// <summary>
        /// Retorna todas viagens finalizadas hoje
        /// </summary>
        public IEnumerable<Viagem> GetViagensFinalizadasEntre(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta)
        {
            return Service.GetViagensFinalizadasEntre(tipoBusca, idEmpresa, idFilial, idTipoCavalo, idTipoCarreta);
        }

        /// <summary>
        /// Retorna as viagens segundo os parâmetros especificados
        /// </summary>
        /// <returns></returns>
        public IQueryable<Viagem> Consultar(string cpfMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> idsViagem,List<string> numerosControle)
        {
            return Service.Consultar(cpfMotorista, tokenViagem, dataLancamentoInicial, dataLancamentoFinal, statusCheckViagem, statusViagem, token, cnpjAplicacao, idsViagem, numerosControle);
        }

        /// <summary>
        /// Retorna todas viagens em viagem de um determinado CPF
        /// </summary>
        public IEnumerable<Viagem> GetViagensEmViagemPorCPF(string cpfMotorista)
        {
            return Service.GetViagensEmViagemPorCPF(cpfMotorista);
        }

        public List<string> GetPlacas(int idEmpresa)
        {
            return Service.GetPlacas(idEmpresa);
        }

        public void AjustarProprietario(Viagem viagem)
        {
            Service.AjustarProprietario(viagem);
        }

        public IQueryable<Viagem> Find(Expression<Func<Viagem, bool>> predicate, bool @readonly = false)
        {
            return _viagemRepository.Find(predicate, @readonly);
        }

        public bool ViagemCadastrada(int idEmpresa, string numeroControle)
        {
            return Service.ViagemCadastrada(idEmpresa, numeroControle);
        }

        public List<Viagem> GetViagensPorPeriodo(int idEmpresa, DateTime dataInicial, DateTime dataFinal)
        {
            return Service.GetViagensPorPeriodo(idEmpresa, dataInicial, dataFinal);
        }

        public object ConsultarDocumentos(int idViagem)
        {
            return Service.ConsultarDocumentos(idViagem);
        }

        public IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario, DateTime? dataCadastroBase = null)
        {
            return Service.GetMotoristasComViagensDoProprietario(cnpjProprietario, dataCadastroBase);
        }

        public bool ExisteCartaFreteParaCombinacao(string cpfCnpjProprietario, string cpfMotorista, int idEmpresa, int mesesAnteriores)
        {
            return Service.ExisteCartaFreteParaCombinacao(cpfCnpjProprietario, cpfMotorista, idEmpresa, mesesAnteriores);
        }

        public ViagemEvento ConsultarViagemEvento(string token)
        {
            return Service.ConsultarViagemEvento(token);
        }

        public ValidationResult RemoverPlacasDasCarretas(int idViagem, List<int> idViagemCarretaRemovidaList = null)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
                {
                    var validation = Service.RemoverPlacasCarretas(idViagem, idViagemCarretaRemovidaList);
                    if (!validation.IsValid)
                        return validation;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public void RemoverViagemEstabelecimentos(int idViagem, List<int> idViagemEstabelecimentoRemovidoList = null)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
            {
                Service.RemoverViagemEstabelecimentos(idViagem, idViagemEstabelecimentoRemovidoList);

                transaction.Complete();
            }
        }

        public ViagemCalcularValorPedagioResponse CalcularValorPedagio(ViagemCalcularValorPedagioRequest request)
        {
            return Service.CalcularValorPedagio(request);
        }

        public ViagemConsultarResponse ConsultarViagemV2(int idViagem, int? idEmpresa, int idUsuario,
            bool filtrarVeiculoTerceiro = false)
        {
            var consultaViagem = Service.ConsultarViagemV2(idViagem, idEmpresa, filtrarVeiculoTerceiro);

            if (idEmpresa.HasValue) {
                consultaViagem.DesabilitarBotoesViagem = !_usuarioPermissaoFinanceiroApp.PossuiPermissao(idUsuario,
                    EBloqueioFinanceiroTipo.baixarOuCancelarViagem);
                
                consultaViagem.DesabilitarBotaoParcela = !_usuarioPermissaoFinanceiroApp.PossuiPermissao(idUsuario,
                    EBloqueioFinanceiroTipo.permiTirCancelamentoParcelaFrete);
            }
            
            return consultaViagem;
        }

        public ViagemConsultarResponse ConsultarViagemV3(int idViagem, bool filtrarVeiculoTerceiro = false)
        {
            return Service.ConsultarViagemV3(idViagem, filtrarVeiculoTerceiro);
        }

        public IQueryable<Viagem> GetQuery()
        {
            return _viagemRepository.GetQuery();
        }

        public IQueryable<ViagemEvento> GetEvento(int idviagem, int idviagemEvento)
        {
            return Service.GetEvento(idviagem, idviagemEvento);
        }

        public ValidationResult AdicionarAtualizarViagemRota(int idViagem, int idEmpresa, List<LocalizacaoDTO> localizacoes, 
            Guid? identificadorHistorico, FornecedorEnum? fornecedor, ETipoVeiculoPedagioEnum? tipoVeiculo, int? quantidadeEixos, 
            string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var viagemRota = new ViagemRota
            {
                IdViagem = idViagem,
                TipoVeiculo = tipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao,
                FornecedorPedagio = fornecedor ?? FornecedorEnum.Desabilitado,
                IdentificadorHistorico = identificadorHistorico,
                Pontos = new List<ViagemRotaPonto>()
            };

            if (localizacoes != null && localizacoes.Any())
            {
                var i = 1;
                foreach (var localizacao in localizacoes)
                {
                    var ponto = new ViagemRotaPonto
                    {
                        Latitude = localizacao.Latitude,
                        Longitude = localizacao.Longitude,
                        Sequencia = i
                    };

                    var cidadeQuery = _cidadeApp.All().Where(c => c.IBGE == localizacao.IbgeCidade);

                    if (!cidadeQuery.Any())
                        cidadeQuery = _cidadeApp.All().Where(c =>
                            c.Latitude == localizacao.Latitude && c.Longitude == localizacao.Longitude);

                    if (cidadeQuery.Any())
                    {
                        var cidade = cidadeQuery.Select(c => new
                        {
                            c.IdCidade,
                            c.Latitude,
                            c.Longitude
                        }).FirstOrDefault();

                        if (cidade == null)
                            continue;

                        ponto.IdCidade = cidade.IdCidade;

                        if (!ponto.Latitude.HasValue)
                            ponto.Latitude = cidade.Latitude;

                        if (!ponto.Longitude.HasValue)
                            ponto.Longitude = cidade.Longitude;

                    }

                    viagemRota.Pontos.Add(ponto);
                    i++;
                }
            }
            else if (viagemRota.IdentificadorHistorico.HasValue && MesmoHistoricoViagemRota(idViagem, viagemRota.IdentificadorHistorico.Value))
            {
                viagemRota.Pontos = null;
                return AdicionarAtualizarViagemRota(viagemRota);
            }
            else
            {
                // Caso os pontos da rota não tenham sido informados, é realizado uma busca no MS de pedágio pelo histórico para verificar os pontos
                if (identificadorHistorico.HasValue)
                {
                    var pontos = _pedagioViagemApp
                        .ObterPontosRota(idEmpresa, identificadorHistorico.Value, quantidadeEixos ?? 0, documentoUsuarioAudit, nomeUsuarioAudit);

                    if (pontos?.Any() == true)
                        foreach (var ponto in pontos)
                            viagemRota.Pontos.Add(ponto);
                }
            }

            return AdicionarAtualizarViagemRota(viagemRota);
        }

        private bool MesmoHistoricoViagemRota(int idViagem, Guid identificadorHistoricoRequest)
        {
            var historicoBanco = Service.GetHistoricoViagemRota(idViagem);

            return historicoBanco.HasValue && historicoBanco == identificadorHistoricoRequest;
        }

        public ValidationResult AdicionarAtualizarViagemRota(ViagemRota viagemRota)
        {
            var validationResult = new ValidationResult();
            
            var viagemRotaBanco = Service.GetViagemRota(viagemRota.IdViagem);

            if (viagemRotaBanco == null)
            {
                validationResult.Add(Service.AddViagemRota(viagemRota));
            }
            else if (viagemRotaBanco.IdentificadorHistorico.HasValue && viagemRota.IdentificadorHistorico.HasValue &&
                     viagemRotaBanco.IdentificadorHistorico != viagemRota.IdentificadorHistorico 
                     || 
                     viagemRotaBanco.Pontos?.Any() == true && viagemRota.Pontos?.Any() == true && 
                     !ViagemRotaMesmasCidades(viagemRotaBanco.Pontos, viagemRota.Pontos))
            {
                validationResult.Add(Service.DeleteViagemRota(viagemRotaBanco));
                if (!validationResult.IsValid) 
                    return validationResult;
                
                validationResult.Add(Service.AddViagemRota(viagemRota));
            }
            else
            {
                viagemRotaBanco.FornecedorPedagio = viagemRota.FornecedorPedagio;
                viagemRotaBanco.TipoVeiculo = viagemRota.TipoVeiculo;

                validationResult.Add(Service.UpdateViagemRota(viagemRotaBanco));
            }

            return validationResult;
        }

        private bool ViagemRotaMesmasCidades(List<ViagemRotaPonto> pontosBanco, List<ViagemRotaPonto> pontosRequest)
        {
            if (pontosBanco.Count != pontosRequest.Count)
                return false;

            for (int i = 0; i < pontosBanco.Count; i++)
            {
                if(pontosBanco[i].IdCidade != pontosRequest[i].IdCidade)
                    return false;
            }

            return true;
        }

        public ViagemDadosCiotResponse GetDadosCiot(int idviagem)
        {
            return Service.GetDadosCiot(idviagem);
        }

        public ComprovanteValePedagioResponse GetDadosValePedagio(int idviagem, out ViagemDadosValePedagio dadosAtsValePedagio, string CnpjEmpresa = null)
        {
            if (!string.IsNullOrWhiteSpace(CnpjEmpresa))
            {
                var empresaId = _empresaApp.GetIdPorCnpj(CnpjEmpresa);

                if (empresaId.HasValue)
                {
                    var pertence = _viagemRepository.Any(c => c.IdViagem == idviagem && c.IdEmpresa == empresaId);

                    if (!pertence)
                        throw new Exception("Viagem não encontrada para a empresa");
                }
            }

            dadosAtsValePedagio = Service.GetDadosValePedagio(idviagem);

            if (dadosAtsValePedagio == null || string.IsNullOrWhiteSpace(dadosAtsValePedagio.ProtocoloEnvioValePedagio))
                return null;
            
            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, dadosAtsValePedagio.IdEmpresa, CartoesService.AuditDocIntegracao, null);
            var retornoDadosComprovante = pedagioApp
                .ConsultarDadosComprovante(dadosAtsValePedagio.Fornecedor, idviagem);
            
            if (!string.IsNullOrWhiteSpace(dadosAtsValePedagio.CpfCnpjContratante)) {
                retornoDadosComprovante.NomeCampoCpfCnpj =
                    dadosAtsValePedagio.CpfCnpjContratante.Length == 11 ? "CPF:" : "CNPJ:";
                dadosAtsValePedagio.CpfCnpjContratante = dadosAtsValePedagio.CpfCnpjContratante.FormatarCpfCnpj();
            }
            
            var result = Mapper.Map(dadosAtsValePedagio, retornoDadosComprovante);
            
            //Formatação do protocolo
            result.ProtocoloEnvio =
                dadosAtsValePedagio.Fornecedor.RegistraValePedagioCertificadoExtratta()
                    ? dadosAtsValePedagio.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(dadosAtsValePedagio.Fornecedor)
                    : dadosAtsValePedagio.ProtocoloValePedagio.ToProtocoloAnttFormat(dadosAtsValePedagio.Fornecedor);

            return result;
        }
        
        public byte[] GetDadosValePedagioReport(ComprovanteValePedagioResponse dadosComprovante, FornecedorEnum fornecedor)
        {
            var dadosComprovanteDataType = Mapper.Map<ComprovanteCompraPedagioDataType>(dadosComprovante);
            var dadosComprovantePracasDataType = Mapper.Map<List<ComprovanteCompraPedagioHistoricoPracasDataType>>(dadosComprovante.Pracas);

            dadosComprovanteDataType.Fornecedor = fornecedor.GetDescription();
            
            return new ComprovanteCompraPedagio().GetReport(dadosComprovanteDataType, dadosComprovantePracasDataType, dadosComprovante.Valor);
        }

        public CompraPedagioDTOResponse ConsultarReciboPedagio(int idViagem, string cnpjEmpresa)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);

            if (idEmpresa == null)
                throw new Exception("Empresa não encontrada.");

            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa.Value, CartoesService.AuditDocIntegracao, null);
            return pedagioApp.ConsultarReciboPedagio(idViagem);
        }

        public byte[] ConsultarReciboMoedeiro(int idViagem, int idEmpresa)
        {
            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa, CartoesService.AuditDocIntegracao, null);
            return pedagioApp.ConsultarReciboMoedeiro(idViagem);
        }

        public ConsultarEventoResponseModel ConsultarDetalheEvento(ConsultarEventoRequestModel request)
        {
            return Service.ConsultarDetalheEvento(request);
        }

        public IList<ViagemEvento> GetEventos(int idViagem)
        {
            return _viagemEventoRepository.GetViagensEventos(idViagem).ToList();
        }

        public string GerarTokenViagemEvento()
        {
            return Service.GerarTokenViagemEvento();
        }

        public List<Viagem> GetByCte(string numero)
        {
            return Service.GetByCte(numero);
        }

        public SolicitarCompraPedagioResponseDTO GetStatusPedagio(Viagem viagem)
        {
            return Service.GetStatusPedagio(viagem);
        }

        public ValidationResult IsValidToCrud(Viagem viagem, EProcesso processo)
        {
            return Service.IsValidToCrudViagem(viagem, processo);
        }

        /// <summary>
        /// Validar se a viagem caiu em algum bloqueio de gestor e inserir registro para libera-la caso necessário.
        /// Caso já tenha sido autorizada pelo gestor, permite a continuidade da integração.
        /// </summary>
        /// <param name="idsTipoBloqueio"></param>
        /// <param name="viagem"></param>
        /// <param name="valorTotalPedagio">Valor que está sendo pago a ser validado. Na integração da viagem vem todos os status sendo modificados para baixado, na tela de pagamento de frete vem o valor do evento em execução</param>
        /// <param name="origem"></param>
        /// <param name="valorTotalFrete"></param>
        /// <returns></returns>
        public ValidationResult BloqueioProcessoViagem(List<EBloqueioGestorTipo> idsTipoBloqueio, Viagem viagem, decimal valorTotalPedagio,EBloqueioOrigemTipo origem,decimal valorTotalFrete)
        {
            return Service.BloqueioProcessoViagem(idsTipoBloqueio, viagem, valorTotalPedagio,origem,valorTotalFrete);
        }

        public IQueryable<ViagemPendenteGestor> ConsultaViagensPendentes(int? idEmpresa)
        {
            var viagemPGestorService = _viagemPendenteGestorService;
            //return viagemPGestorService.GetQueryIncluded();
            return !idEmpresa.HasValue ? viagemPGestorService.GetQueryIncluded() : viagemPGestorService.GetQueryIncluded().Where(vp=> vp.IdEmpresa == idEmpresa);
        }

        public ValidationResult AlterarViagemPendente(int idViagem, EBloqueioGestorTipo idBloqueioGestorTipo, int status, int idUsuario,string motivo)
        {
            return Service.AlterarViagemPendente(idViagem, idBloqueioGestorTipo, status, idUsuario, motivo);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        public object ConsultarViagens(ConsultaViagemRequestDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarViagens(request, take, page, order, filters);
        }

        public byte[] RelatorioConsultaViagens(RelatorioConsultaViagemRequestDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            var logoEmpresa = _empresaApp.Get(request.IdEmpresa ?? 0, null)?.Logo;
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

            var requestDto = new ConsultaViagemRequestDTO
            {
                IdEmpresa = request.IdEmpresa,
                Filiais = request.Filiais,
                DataInicial = request.DataInicial,
                DataFinal = request.DataFinal,
                Pesquisa = request.Pesquisa
            };

            var list = Service.RelatorioConsultaViagens(requestDto, take, page, order, filters);

            var dados = list.Select(c => new RelatorioConsultaViagemItemDataType
            {
                DataIntegracao = c.DataIntegracao,
                DataEmissao = c.DataEmissao,
                NumeroDocumentoCliente = c.DocumentoCliente,
                StatusViagem = c.StatusViagem.GetDescription(),
                ValorAdiantamento = c.ValorAdiantamento.ToString("C", new CultureInfo("pt-BR")),
    
                ValorSaldo = c.ValorSaldo.ToString("C", new CultureInfo("pt-BR")),
                ValorTarifas = c.ValorTarifa.ToString("C", new CultureInfo("pt-BR")),
                ValorPedagio = c.ValorPedagio.ToString("C", new CultureInfo("pt-BR")),
                Inss = c.Inss.ToString("C", new CultureInfo("pt-BR")),
                Irrf = c.Irrf.ToString("C", new CultureInfo("pt-BR")),
                Sestsenat = c.Sestsenat.ToString("C", new CultureInfo("pt-BR")),
                CIOT = c.CiotHabilitadoBoolean ? c.HasCIOTBoolean ? ESituacaoRelatorioCiot.Sucesso : ESituacaoRelatorioCiot.Erro : ESituacaoRelatorioCiot.NaoSolicitado,
                InformacaoCiot = c.CIOT,
                CnpjEmpresa = c.EmpresaCnpj.FormatarCpfCnpj(ignorarErro: true),
                NomeEmpresa = c.EmpresaNome,
                CnpjFilial = c.FilialCnpj.FormatarCpfCnpj(ignorarErro: true),
                NomeFilial = c.FilialNome,
                ValorAbastecimento = c.ValorAbastecimento.ToString("C", new CultureInfo("pt-BR")),
                ValorEstadia = c.ValorEstadia.ToString("C", new CultureInfo("pt-BR")),
                StatusVpo =  c.ResultadoCompraPedagio == EResultadoCompraPedagio.Erro 
                    ? c.ResultadoCompraPedagio.GetDescription() + ": " + c.MensagemCompraPedagio
                    : c.ResultadoCompraPedagio.GetDescription(),
                CnpjCpfClienteOrigem = c.ClienteOrigemCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                NomeClienteOrigem = c.ClienteOrigemNome,
                CnpjCpfClienteDestino = c.ClienteDestinoCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                NomeClienteDestino = c.ClienteDestinoNome,
                Placa = c.Placa,
                CnpjCpfProprietario = c.ProprietarioCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                NomeProprietario = c.ProprietarioNome,
                RNTRCProprietario = c.ProprietarioRntrc.ToString(),
                CpfMotorista = c.MotoristaCpf.FormatarCpfCnpj(ignorarErro: true),
                NomeMotorista = c.MotoristaNome,
                IdViagem = c.IdViagem,
                CnpjFornecedorPedagio = c.FornecedorCnpj.FormatarCpfCnpj(ignorarErro: true),
                NomeFornecedor = c.FornecedorPedagio.GetDescription(),
                NumeroProtocolo =  c.FornecedorPedagio.RegistraValePedagioCertificadoExtratta() 
                    ? c.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(c.FornecedorPedagio)
                    : c.ProtocoloValePedagio.ToProtocoloAnttFormat(c.FornecedorPedagio)
            }).ToList();
            
            var requestRelatorio = new RelatorioConsultaViagemDataType();

            requestRelatorio.items = dados;
            requestRelatorio.totalItems = dados.Count;

            var relatorio = new RelatorioConsultaViagem().GetReport(extensao, requestRelatorio, logo);
            return relatorio;
        }
        
        public byte[] RelatorioConsultaPedagioVPO(RelatorioConsultaViagemRequestDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            var logoEmpresa = _empresaApp.Get(request.IdEmpresa ?? 0, null)?.Logo;
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

            var requestDto = new ConsultaViagemRequestDTO
            {
                IdEmpresa = request.IdEmpresa,
                Filiais = request.Filiais,
                DataInicial = request.DataInicial,
                DataFinal = request.DataFinal,
                Pesquisa = request.Pesquisa,
                IsPedagioAvulso = request.IsPedagioAvulso
            };
            
            var dados = Service
                .RelatorioConsultaViagens(requestDto, take, page, order, filters)
                .Select(c => new RelatorioPedagioAvulsoItemDataType
                {
                    DataInicioFrete = c.DataColeta,
                    DataFimFrete = c.DataPrevisaoEntrega,
                    DataEmissao = c.DataEmissao,
                    NumeroDocumentoCliente = c.DocumentoCliente,
                    ValorPedagio = c.ValorPedagio.ToString("C", new CultureInfo("pt-BR")),
                    NomeEmpresa = c.EmpresaNome,
                    CnpjFilial = c.FilialCnpj.FormatarCpfCnpj(ignorarErro: true),
                    NomeFilial = c.FilialNome,
                    StatusVpo =  c.ResultadoCompraPedagio == EResultadoCompraPedagio.Erro 
                        ? c.ResultadoCompraPedagio.GetDescription() + ": " + c.MensagemCompraPedagio
                        : c.ResultadoCompraPedagio.GetDescription(),
                    Placa = c.Placa,
                    CnpjCpfProprietario = c.ProprietarioCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                    NomeProprietario = c.ProprietarioNome,
                    CpfMotorista = c.MotoristaCpf.FormatarCpfCnpj(ignorarErro: true),
                    NomeMotorista = c.MotoristaNome,
                    IdViagem = c.IdViagem,
                    FornecedorPedagio = c.FornecedorPedagio.GetDescription(),
                    FornecedorCnpj = c.FornecedorCnpj.FormatarCpfCnpj(ignorarErro: true),
                    ProtocoloEnvioValePedagio = c.FornecedorPedagio.RegistraValePedagioCertificadoExtratta() 
                        ? c.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(c.FornecedorPedagio)
                        : c.ProtocoloValePedagio.ToProtocoloAnttFormat(c.FornecedorPedagio)
                }).ToList();

            var requestRelatorio = new RelatorioConsultaPedagioAvulsoType()
            {
                items = dados,
                totalItems = dados.Count
            };
            
            return new RelatorioPedagioAvulso().GetReport(extensao, requestRelatorio, logo);
        }

        public ConsultaViagemExternalCiotResponseDTO ConsultarDetalhesCiotViagem(int idViagem)
        {
            return Service.ConsultarDetalhesCiotViagem(idViagem);
        }

        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(string cnpjCpf, string rntrc, int idEmpresa)
        {
            var cnpjEmpresa = _empresaApp.All().Where(c => c.IdEmpresa == idEmpresa).Select(c => c.CNPJ).FirstOrDefault();
            
            var ciotRequest = new ConsultarSituacaoTransportadorRequest();
            ciotRequest.RntrcTransportador = rntrc;
            ciotRequest.CpfCnpjInteressado = cnpjEmpresa;
            ciotRequest.CpfCnpjTransportador = cnpjCpf;

            var retorno = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.ConsultarSituacaoTransportador(ciotRequest)
                : _ciotV3App.ConsultarSituacaoTransportador(ciotRequest);

//            if (retorno.Sucesso.HasValue && !retorno.Sucesso.Value)
//            {
//                throw new Exception(retorno.Excecao?.Mensagem);
//            }

            return retorno;
        }

        public ConsultaTiposCargasResponse ConsultarTiposCarga(string cnpjEmpresa)
        {
            return Service.ConsultarTiposCarga(cnpjEmpresa);
        }
        
        public ConsultarTiposCargaResponse ConsultarTiposCarga(int idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarTiposCarga(idEmpresa, take, page, order, filters);
        }
        
        public Viagem ObterViagemPorEmpresa(int viagemId, int empresaId)
        {
            var viagem = Service.ObterViagemPorEmpresa(viagemId, empresaId);
            return viagem;
        }
        
        public int? GetIdViagemEstabelecimentoPorCnpj(string cnpjEstabelecimento, string cnpjEmpresa)
        {
            return _estabelecimentoApp.GetIdEstabelecimentoPorCnpj(cnpjEstabelecimento, cnpjEmpresa);
        }
        
        public bool GetViagemEstabelecimentoAtivo(int idEstabelecimento)
        {
            return _estabelecimentoApp.GetEstabelecimentoAtivo(idEstabelecimento);
        }
        
        public bool GetViagemEstabelecimentoCredenciado(int idEstabelecimento)
        {
            return _estabelecimentoApp.EstaCredenciado(idEstabelecimento);
        }

        public int QuantidadeViagensAbertasPorNumeroCiotsVinculados(int idDclaracaociot)
        {
            return Service.QuantidadeViagensAbertasPorNumeroCiotsVinculados(idDclaracaociot);
        }

        public FornecedorEnum? GetFornecedorViagemRota(int idViagem)
        {
            return Service.GetFornecedorViagemRota(idViagem);
        }
        
        public ValidationResult DesvincularCiot(int? idViagem,int? idUsuario)
        {
             var validation = new ValidationResult();

            if (!idViagem.HasValue)
                return validation.Add("Não foi possível identificar a viagem na base!");

            var viagem = _viagemRepository.Get((int) idViagem);

            if (viagem == null)
                return validation.Add($"Não foi possível encontrar a viagem com id {idViagem}");
            
            if(!idUsuario.HasValue)
                return validation.Add("Não foi possível identificar o usuário na base!");
            
            var usuario = _usuarioService.Get((int) idUsuario, false);

            if (usuario == null)
                return validation.Add($"Não foi possível encontrar o usuário do desnvinculo!");

            var desvinculo = _ciotV2App.DesvincularCiot(viagem,usuario);

            if (!desvinculo.IsValid)
                return validation.Add(desvinculo.Errors.FirstOrDefault()?.Message);
           
            return validation; 
        }

        public ConsultarFilialEDadosPagamentoResponse ConsultarFilialEDadosPagamento(string ciot)
        {
            var cnpjFilialEdadosPagamento = Service.GetCnpjFilialEDadosPagamento(ciot);
               
            return Mapper.Map<ConsultarFilialEDadosPagamentoResponse>(cnpjFilialEdadosPagamento);
        }

        public object ConsultarUltimaViagemEmpresa(int empresaId, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarUltimaViagemEmpresa(empresaId, take, page, order, filters);
        }

        public bool VincularViagemAoContrato(int idContratoCiotAgregado, int idUltimaViagem)
        {
            return Service.VincularViagemAoContrato(idContratoCiotAgregado, idUltimaViagem);
        }

        public ReciboPefDadosResponse GetDadosReciboPef(int idviagem, bool listarParcelasCanceladas, string CnpjEmpresa = null)
        {
            if (!string.IsNullOrWhiteSpace(CnpjEmpresa))
            {
                var empresaId = _empresaApp.GetIdPorCnpj(CnpjEmpresa);

                if (empresaId.HasValue)
                {
                    var pertence = _viagemRepository.Any(c => c.IdViagem == idviagem && c.IdEmpresa == empresaId);

                    if (!pertence)
                        throw new Exception("Viagem não encontrada para a empresa");
                }
            }

            var retornoDadosAtsReciboPef = Service.GetDadosReciboPef(idviagem);

            if (!listarParcelasCanceladas)
            {
                var parcelas = retornoDadosAtsReciboPef.Parcelas;
                retornoDadosAtsReciboPef.Parcelas = new List<Parcelas>();
                
                foreach (var parcela in parcelas)
                {
                    if (parcela.StatusEvento != EStatusViagemEvento.Cancelado)
                        retornoDadosAtsReciboPef.Parcelas.Add(parcela);
                }
            }
            
            if (retornoDadosAtsReciboPef.Parcelas?.Count > 0)
                retornoDadosAtsReciboPef.ValorTotalParcelas = retornoDadosAtsReciboPef.Parcelas
                    .Where(ve => ve.StatusEvento != EStatusViagemEvento.Cancelado).Sum(p => p.Valor);

            if (retornoDadosAtsReciboPef.PlacasCarreta.ToList().Count > 0)
                retornoDadosAtsReciboPef.Placas = retornoDadosAtsReciboPef.PlacaCavalo + " / " +
                                                  String.Join(" / ",
                                                      retornoDadosAtsReciboPef.PlacasCarreta.Select(p => p.Placa));

            #region Pegar dados comprovante de carga
            
            var dadosAtsComprovanteCarga = Service.GetDadosComprovanteCarga(idviagem);
            
            retornoDadosAtsReciboPef.ViagemPossuiPedagio =
                dadosAtsComprovanteCarga.Fornecedor.HasValue;

            if (retornoDadosAtsReciboPef.ViagemPossuiPedagio)
            {
                retornoDadosAtsReciboPef.Fornecedor =
                    dadosAtsComprovanteCarga.Fornecedor.GetDescription().ToUpper();

                if (dadosAtsComprovanteCarga.Fornecedor != FornecedorEnum.Desabilitado &&
                    dadosAtsComprovanteCarga.Fornecedor != FornecedorEnum.Moedeiro &&
                    !string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.PlacaCavalo))
                {
                    retornoDadosAtsReciboPef.NomeCampoComprovanteCarga = "Placa cavalo:";
                    retornoDadosAtsReciboPef.CampoComprovanteCarga = retornoDadosAtsReciboPef.PlacaCavalo;
                }
                else if (dadosAtsComprovanteCarga.Fornecedor == FornecedorEnum.Moedeiro)
                {
                    retornoDadosAtsReciboPef.NomeCampoComprovanteCarga = "Cartão:";

                    if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.DocumentoMotoristra))
                    {
                        retornoDadosAtsReciboPef.CartaoMotorista = ConsultarNumeroCartaoPorDocumento(
                            retornoDadosAtsReciboPef.CnpjContratante, retornoDadosAtsReciboPef.DocumentoMotoristra);
                        retornoDadosAtsReciboPef.CampoComprovanteCarga = retornoDadosAtsReciboPef.CartaoMotorista;
                    }
                }

                retornoDadosAtsReciboPef.ValorComprovanteCarga = dadosAtsComprovanteCarga.ValorComprovanteCarga;
                retornoDadosAtsReciboPef.StatusComprovanteCarga =
                    dadosAtsComprovanteCarga.StatusCompraPedagio.GetDescription();

                if (string.IsNullOrWhiteSpace(dadosAtsComprovanteCarga.ProtocoloEnvioValePedagio))
                {
                    retornoDadosAtsReciboPef.MensagemProtocoladoAnttComprovanteCarga =
                        "ATENÇÃO! VALE PEDÁGIO NÃO PROTOCOLADO NA ANTT.";
                }
                else
                {
                    retornoDadosAtsReciboPef.Protocolo = !dadosAtsComprovanteCarga.Fornecedor.HasValue
                        ? dadosAtsComprovanteCarga.ProtocoloEnvioValePedagio
                        : (dadosAtsComprovanteCarga.Fornecedor.Value.RegistraValePedagioCertificadoExtratta()
                            ? dadosAtsComprovanteCarga.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(dadosAtsComprovanteCarga.Fornecedor.Value)
                            : dadosAtsComprovanteCarga.ProtocoloValePedagio.ToProtocoloAnttFormat(dadosAtsComprovanteCarga.Fornecedor.Value));
                }
            }

            #endregion

            #region Pegar números cartão

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CnpjContratante))
            {
                if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.DocumentoProprietario))
                    retornoDadosAtsReciboPef.CartaoContratado = ConsultarNumeroCartaoPorDocumento(retornoDadosAtsReciboPef.CnpjContratante, retornoDadosAtsReciboPef.DocumentoProprietario);
                
                if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.DocumentoMotoristra) && string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CartaoMotorista))
                    retornoDadosAtsReciboPef.CartaoMotorista = ConsultarNumeroCartaoPorDocumento(retornoDadosAtsReciboPef.CnpjContratante, retornoDadosAtsReciboPef.DocumentoMotoristra);
            }

            #endregion

            #region Formatar campos

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CnpjContratante))
                retornoDadosAtsReciboPef.CnpjContratante = retornoDadosAtsReciboPef.CnpjContratante.FormatarCpfCnpj();

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CnpjFilial))
                retornoDadosAtsReciboPef.CnpjFilial = retornoDadosAtsReciboPef.CnpjFilial.FormatarCpfCnpj();

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.DocumentoProprietario))
                retornoDadosAtsReciboPef.DocumentoProprietario =
                    retornoDadosAtsReciboPef.DocumentoProprietario.FormatarCpfCnpj();

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.DocumentoMotoristra))
                retornoDadosAtsReciboPef.DocumentoMotoristra =
                    retornoDadosAtsReciboPef.DocumentoMotoristra.FormatarCpfCnpj();

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CnpjCpfRemetente))
                retornoDadosAtsReciboPef.CnpjCpfRemetente = retornoDadosAtsReciboPef.CnpjCpfRemetente.FormatarCpfCnpj();

            if (!string.IsNullOrWhiteSpace(retornoDadosAtsReciboPef.CnpjCpfDestinatario))
                retornoDadosAtsReciboPef.CnpjCpfDestinatario = retornoDadosAtsReciboPef.CnpjCpfDestinatario.FormatarCpfCnpj();

            #endregion

            return retornoDadosAtsReciboPef;
        }

        public byte[] GetDadosReciboPefReport(ReciboPefDadosResponse dadosRecibo)
        {
             var dadosReciboDataType = Mapper.Map<ReciboPagamentoDataType>(dadosRecibo);
             var dadosReciboParcelasDataType = Mapper.Map<List<ReciboPagamentoHistoricoParcelasDataType>>(dadosRecibo.Parcelas);
             
             return new ReciboPagamento()
                 .GetReport(dadosReciboDataType, dadosReciboParcelasDataType, dadosRecibo.ValorTotalParcelas.ToString("C"), dadosRecibo.ViagemPossuiPedagio);
        }

        public bool VerificarPermissaoPix(int idempresa, int idProprietario)
        {
            var empresaPix = _parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idempresa);

            if (!empresaPix) return false;
            
            var usuarioPix = _parametrosUsuarioService.GetPermiteRealizarPagamentoPix(_userIdentity.IdUsuario);

            if (!usuarioPix) return false;

            var proprietarioDocumento = _proprietarioRepository.All()
                .Where(c => c.IdProprietario == idProprietario)
                .Select(p => p.CNPJCPF)
                .FirstOrDefault();

            var proprietarioPix = _parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(proprietarioDocumento, idempresa);

            return proprietarioPix;
        }

        public bool PertenceAEmpresa(int? idempresa, int idviagem)
        {
            return Service.GetViagem(idviagem).Any(c => c.IdEmpresa == idempresa);
        }

        private string ConsultarNumeroCartaoPorDocumento(string cnpjContratante, string documento)
        {
            var cartoesApp =
                CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, cnpjContratante, "00000000000", "");
            var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
            var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
            string retornoCartao;

            var resultadoCartaoProprietario =
                retornoCartao = cartoesApp.GetCartoesVinculadosGrid(documento,
                    cartaoIdArray).Objeto.FirstOrDefault(c => c.Identificador != null)?.Identificador.ToString();

            if (string.IsNullOrWhiteSpace(resultadoCartaoProprietario))
                retornoCartao = cartoesApp.GetCartoesBloqueadoGrid(documento, cartaoIdArray).Objeto.FirstOrDefault(p => p.Identificador != null)?.Identificador.ToString();

            return retornoCartao;
        }

    }
}
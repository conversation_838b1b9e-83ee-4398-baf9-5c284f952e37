﻿{
  "runtime": "WinX64",
  "defaultVariables": null,
  "swaggerGenerator": {
    "fromSwagger": {
      "json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Auth/Api\"\r\n  },\r\n  \"basePath\": \"/Auth/Api\",\r\n  \"paths\": {\r\n    \"/Administradoras/{token}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Administradora\"\r\n        ],\r\n        \"summary\": \"Validar token da adminsitradora\",\r\n        \"operationId\": \"AdministradorasByTokenGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"token\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ApiActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/Integrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"operationId\": \"EmpresasIntegrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-adm-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para adminisradoras da plataforma\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaApiResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/GetOrGenerateAppToken\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Obter ou gerar token para aplicação identificada.\\r\\nCaso existir configurado é retornado o token já existente.\\r\\nCaso não existir, o mesmo é criado e retornado.\",\r\n        \"operationId\": \"EmpresasGetOrGenerateAppTokenPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cnpjEmpresa\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"appName\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-adm-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para adminisradoras da plataforma\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/GetOrGenerateAppTokenApiResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Usuarios/LoginDinamico\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Usuario\"\r\n        ],\r\n        \"summary\": \"Iniciar sessão, ignorando os dados cadastrais do usuário, utilizando o vinculo de adminstradora x empersa do token indicado.\\r\\nUtilizado pelo ATS.\",\r\n        \"operationId\": \"UsuariosLoginDinamicoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/LoginDinamicoApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-adm-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para adminisradoras da plataforma\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/LoginDinamicoApiResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Validar/{token}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Validar\"\r\n        ],\r\n        \"summary\": \"Autenticar consumidor da plataforma pelo token de autenticação único\",\r\n        \"operationId\": \"ValidarByTokenGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"token\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ApiActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Validar/Detalhes/{token}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Validar\"\r\n        ],\r\n        \"summary\": \"Autenticar consumidor da plataforma pelo token de autenticação único e obter detalhes do cadastro do token\",\r\n        \"operationId\": \"ValidarDetalhesByTokenGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"token\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ValidarTokenDetalhesResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"ApiActionResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ApiProcessingStateOnServer\": {\r\n      \"required\": [\r\n        \"state\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"state\": {\r\n          \"enum\": [\r\n            \"Ok\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"errorMessage\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarEmpresaApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpj\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarEmpresaApiResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ApiResponseValidation\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ApiResponseValidation\": {\r\n      \"required\": [\r\n        \"type\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"type\": {\r\n          \"enum\": [\r\n            \"Information\",\r\n            \"Warning\",\r\n            \"Validation\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"message\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"GetOrGenerateAppTokenApiResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"token\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"LoginDinamicoApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"token\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"habilitarLoginSemToken\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"appType\": {\r\n          \"enum\": [\r\n            \"NaoIdentificada\",\r\n            \"ATS\",\r\n            \"CartoesDashboard\",\r\n            \"CartoesAcessoPortador\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"LoginDinamicoApiResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sessao\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuario\": {\r\n          \"$ref\": \"#/definitions/UsuarioApiModel\"\r\n        }\r\n      }\r\n    },\r\n    \"UsuarioApiModel\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"empresaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ValidarTokenDetalhesResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradora\": {\r\n          \"$ref\": \"#/definitions/AdministradoraResponse\"\r\n        },\r\n        \"empresa\": {\r\n          \"$ref\": \"#/definitions/EmpresaResponse\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AdministradoraResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EmpresaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpj\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}",
      "url": "http://18.221.47.112/Auth/Api/swagger/v1/swagger.json",
      "output": null
    }
  },
  "codeGenerators": {
    "swaggerToCSharpClient": {
      "clientBaseClass": "SistemaInfoMicroServiceBaseClient",
      "configurationClass": "HttpContext",
      "generateClientClasses": true,
      "generateClientInterfaces": false,
      "generateDtoTypes": true,
      "injectHttpClient": false,
      "disposeHttpClient": true,
      "protectedMethods": [],
      "generateExceptionClasses": true,
      "exceptionClass": "SwaggerException",
      "wrapDtoExceptions": true,
      "useHttpClientCreationMethod": true,
      "httpClientType": "System.Net.Http.HttpClient",
      "useHttpRequestMessageCreationMethod": true,
      "useBaseUrl": true,
      "generateBaseUrlProperty": true,
      "generateSyncMethods": true,
      "exposeJsonSerializerSettings": false,
      "clientClassAccessModifier": "public",
      "typeAccessModifier": "public",
      "generateContractsOutput": false,
      "contractsNamespace": null,
      "contractsOutputFilePath": null,
      "parameterDateTimeFormat": "s",
      "generateUpdateJsonSerializerSettingsMethod": true,
      "serializeTypeInformation": false,
      "queryNullValue": "",
      "className": "{controller}Client",
      "operationGenerationMode": "MultipleClientsFromPathSegments",
      "additionalNamespaceUsages": [
        "System.Web",
        "ATS.Data.Repository.External.SistemaInfo"
      ],
      "additionalContractNamespaceUsages": [],
      "generateOptionalParameters": false,
      "generateJsonMethods": true,
      "enforceFlagEnums": false,
      "parameterArrayType": "System.Collections.Generic.IEnumerable",
      "parameterDictionaryType": "System.Collections.Generic.IDictionary",
      "responseArrayType": "System.Collections.Generic.List",
      "responseDictionaryType": "System.Collections.Generic.Dictionary",
      "wrapResponses": false,
      "wrapResponseMethods": [],
      "generateResponseClasses": true,
      "responseClass": "SwaggerResponse",
      "namespace": "SistemaInfo.Cartoes.Repository.External.SistemaInfo.Auth.Api.Client",
      "requiredPropertiesMustBeDefined": true,
      "dateType": "System.DateTime",
      "jsonConverters": null,
      "dateTimeType": "System.DateTime",
      "timeType": "System.TimeSpan",
      "timeSpanType": "System.TimeSpan",
      "arrayType": "System.Collections.ObjectModel.ObservableCollection",
      "arrayInstanceType": null,
      "dictionaryType": "System.Collections.Generic.Dictionary",
      "dictionaryInstanceType": null,
      "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection",
      "dictionaryBaseType": "System.Collections.Generic.Dictionary",
      "classStyle": "Inpc",
      "generateDefaultValues": true,
      "generateDataAnnotations": true,
      "excludedTypeNames": [],
      "handleReferences": false,
      "generateImmutableArrayProperties": false,
      "generateImmutableDictionaryProperties": false,
      "jsonSerializerSettingsTransformationMethod": null,
      "templateDirectory": null,
      "typeNameGeneratorType": null,
      "propertyNameGeneratorType": null,
      "enumNameGeneratorType": null,
      "serviceHost": null,
      "serviceSchemes": null,
      "output": "Sources/Auth.ApiClient.cs"
    }
  }
}
﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class EstabelecimentoBaseDocumentoService : ServiceBase, IEstabelecimentoBaseDocumentoService
    {
        private readonly IEstabelecimentoBaseDocumentoRepository _repository;
        private readonly IDataMediaServerRepository _mongoRepository;
        private readonly IDocumentoService _documentoService;
        private readonly ICredenciamentoService _credenciamentoService;

        public EstabelecimentoBaseDocumentoService(IEstabelecimentoBaseDocumentoRepository repository, IDataMediaServerRepository mongoRepository, IDocumentoService documentoService,
            ICredenciamentoService credenciamentoService)
        {
            _repository = repository;
            _mongoRepository = mongoRepository;
            _documentoService = documentoService;
            _credenciamentoService = credenciamentoService;
        }

        public IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null)
        {
            return _documentoService.GetAllDocumentoCredenciamento(idsEmpresa, idsDocumentoIgnorar);
        }
        
        public object Download(string token)
        {
            return _mongoRepository.GetMedia(token);
        }

        public string Upload(string dataBase64, string fileName)
        {
            var file = new FileInfo(fileName);
            var mimeType = MimeTypeHelper.GetMimeType(file.Extension);

            return _mongoRepository.Add(0, dataBase64, fileName, mimeType).ToString();
        }

        public ValidationResult AddOrUpdateDocumentos(ICollection<EstabelecimentoBaseDocumento> estabelecimentoDocumentos, int idEstabelecimento, int administradoraPlataforma)
        {
            try
            {
                var temDocumentoNovo = false;
                var idsEmpresaNovosDocumentos = new List<int>();
                var result = new ValidationResult();

                foreach (var doc in estabelecimentoDocumentos)
                {
                    var query = _repository.Where(c => c.IdEstabelecimentoBaseDocumento == doc.IdEstabelecimentoBaseDocumento);

                    if (doc.IdEstabelecimentoBaseDocumento != 0 && query.Any())
                    {
                        var docFromDb = query.First();

                        if (doc.Token != docFromDb.Token)
                        {
                            temDocumentoNovo = true;
                            if (docFromDb.IdDocumento.HasValue)
                                idsEmpresaNovosDocumentos.Add(_documentoService.GetIdEmpresa(docFromDb.IdDocumento.Value));

                            if (!doc.PermiteEditarData)
                                doc.DataValidade = DateTime.Now.AddDays(Convert.ToDouble(doc.DiasValidade));
                        }

                        docFromDb.Descricao = doc.Descricao;
                        docFromDb.Token = doc.Token;
                        docFromDb.DataValidade = doc.DataValidade;

                        _repository.Update(docFromDb);
                        _credenciamentoService.UpdateCredenciamentoAnexo(idEstabelecimento, doc.IdDocumento, doc.Token, doc.DataValidade, doc.Descricao);
                    }
                    else
                    {
                        temDocumentoNovo = true;
                        if (doc.IdDocumento.HasValue)
                            idsEmpresaNovosDocumentos.Add(_documentoService.GetIdEmpresa(doc.IdDocumento.Value));
                        
                        _repository.Add(doc);
                        _credenciamentoService.AddCredenciamentoAnexo(idEstabelecimento, doc.IdDocumento, doc.Token, doc.DataValidade, doc.Descricao);
                    }
                }

                if (temDocumentoNovo && idsEmpresaNovosDocumentos.Any())
                    foreach (var idEmpresa in idsEmpresaNovosDocumentos.Distinct())
                        _credenciamentoService.AtualizarStatusDocumentacaoPorEstabelecimento(idEstabelecimento, idEmpresa, EStatusDocumentacaoCredenciamento.Aguardando, administradoraPlataforma);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                return new ValidationResult().Add($"Ocorreu um erro ao atualizar os documentos no banco de dados: {ex.Message}");
            }

            return new ValidationResult();
        }
        
        public List<EstabelecimentoBaseDocumento> GetDocumentos(int idEstabelecimentoBase)
        {
            return _repository.GetDocumentos(idEstabelecimentoBase);
        }
    }
}

﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ConsultarSituacaoVeiculosAnttRequest : RequestBase
    {
        public string CpfCnpjProprietario
        {
            get { return _cpfCnpjProprietario; }
            set {_cpfCnpjProprietario = value.OnlyNumbers();}
        }
        private string _cpfCnpjProprietario { get; set; }
        public List<string> Placas { get; set; }

        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);

            if (string.IsNullOrWhiteSpace(CpfCnpjProprietario))
                validacao.Add("É obrigatório o envio do campo CpfCnpjProprietario");
            else if (CpfCnpjProprietario.Length != 11 && CpfCnpjProprietario.Length != 14)
                validacao.Add("O campo CpfCnpjProprietario deve conter 11 ou 14 dígitos");
            
            if(Placas == null || !Placas.Any())
                validacao.Add("É obrigatório o envio de ao menos um registro no campo Placas");
            else
                for (var i = 0; Placas.Count > i; i++)
                {
                    Placas[i] = Placas[i].RemoveSpecialCharacters().ToUpper();
                    if (Placas[i].Length != 7)
                        return validacao.Add("Os itens de Placas devem conter 7 caracteres");
                }

            return validacao;
        }
    }
}
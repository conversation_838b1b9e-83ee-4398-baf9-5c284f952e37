﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class CidadeController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly SrvCidade _srvCidade;

        public CidadeController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, ICidadeApp cidadeApp, SrvCidade srvCidade) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _cidadeApp = cidadeApp;
            _srvCidade = srvCidade;
        }

        /// <summary>
        /// Retorna as cidades atualizadas a partir da data base informada
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string Consultar(ConsultarCidadeRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new Models.Mobile.Common.JsonResult().TokenInvalido();

                return new Models.Mobile.Common.JsonResult().Responde(
                    _srvCidade.GetCidadesAtualizadas(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new Models.Mobile.Common.JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public JsonResult ConsultarCidades(ConsultarCidadeRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvCidade.ConsultarCidades(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string ConsultarPorEstado(int idEstado)
        {
            try
            {
                var estados =
                    _cidadeApp.GetListaCidade(idEstado)
                        .OrderBy(o => o.Nome)
                        .Select(o => new {o.IdCidade, Descricao = o.Nome});

                return new Models.Mobile.Common.JsonResult().Responde(estados);
            }
            catch (Exception e)
            {
                return new Models.Mobile.Common.JsonResult().Mensagem(e.Message);
            }
        }
    }
}
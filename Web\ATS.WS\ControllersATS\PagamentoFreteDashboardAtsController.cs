﻿using ATS.Application.Application;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class PagamentoFreteDashboardAtsController : DefaultController
    {
        private readonly IViagemApp _viagemApp;
        private readonly IProtocoloApp _protocoloApp;

        public PagamentoFreteDashboardAtsController(IViagemApp viagemApp, IProtocoloApp protocoloApp)
        {
            _viagemApp = viagemApp;
            _protocoloApp = protocoloApp;
        }

        #region Métodos para os três painéis superiores
        [HttpGet]
        public JsonResult GetCartasPagas(string idEmpresa, int ano, int mes)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _viagemApp.GetCartasPagas(empresa, ano, mes);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        [HttpGet]
        public JsonResult GetCartasAberto(string idEmpresa, int ano, int mes)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _viagemApp.GetCartasEmAberto(empresa, ano, mes);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        //{ reais:[], porcento:[]}
        [HttpGet]
        public JsonResult GetAntecipacoes(string idEmpresa, int ano, int mes)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _protocoloApp.GetAnteciopacoes(empresa, ano, mes);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        #endregion

        //{ data:[], labels:[]}
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetTotalCartasMes(string idEmpresa, int ano)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _viagemApp.GetTotalCartasMes(empresa, ano);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        //{ data:[], labels:[]}
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetTotalGanhoMes(string idEmpresa, int ano)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _viagemApp.GetTotalGanhosPorMes(empresa, ano);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetTotalPagamentos(string idEmpresa, int ano, int mes)
        {
            try
            {
                var empresa = !string.IsNullOrEmpty(idEmpresa) ? Convert.ToInt32(idEmpresa) : new Nullable<int>();
                var retorno = _viagemApp.GetTotalPagamentos(empresa, ano, mes);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Models.ViagemV2.Response;
using ATS.WS.Services.ViagemServices;
using ATS.WS.Services.ViagemV2Services;
using AutoMapper;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services
{
    public class SrvViagemV2 : SrvBase
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly SrvCartoes _srvCartoes;
        private readonly SrvViagem _srvViagem;
        private readonly AlteracaoViagem _alteracaoViagem;
        private readonly IntegracoesPreViagemV2 _integracoesPreViagemV2;
        private readonly SrvVeiculo _srvVeiculo;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly SrvMotorista _srvMotorista;
        private readonly IEmpresaApp _empresaApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IFilialApp _filialApp;

        public SrvViagemV2(IParametrosApp parametrosApp, ICiotV3App ciotV3App, IViagemApp viagemApp,
            SrvCartoes srvCartoes, SrvViagem srvViagem, AlteracaoViagem alteracaoViagem, IntegracoesPreViagemV2 integracoesPreViagemV2, SrvVeiculo srvVeiculo, IProprietarioApp proprietarioApp,
            IResgateCartaoAtendimentoApp resgatarCartaoApp, ICidadeRepository cidadeRepository, IMotoristaApp motoristaApp, IUsuarioApp usuarioApp, SrvMotorista srvMotorista, IEmpresaApp empresaApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, ICadastrosApp cadastrosApp, IVeiculoApp veiculoApp, ICidadeApp cidadeApp, IFilialApp filialApp)
        {
            _parametrosApp = parametrosApp;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _srvCartoes = srvCartoes;
            _srvViagem = srvViagem;
            _alteracaoViagem = alteracaoViagem;
            _integracoesPreViagemV2 = integracoesPreViagemV2;
            _srvVeiculo = srvVeiculo;
            _proprietarioApp = proprietarioApp;
            _resgatarCartaoApp = resgatarCartaoApp;
            _cidadeRepository = cidadeRepository;
            _motoristaApp = motoristaApp;
            _usuarioApp = usuarioApp;
            _srvMotorista = srvMotorista;
            _empresaApp = empresaApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _cadastrosApp = cadastrosApp;
            _veiculoApp = veiculoApp;
            _cidadeApp = cidadeApp;
            _filialApp = filialApp;
        }

        public Retorno<ViagemV2IntegracaoResponseModel> Integrar(ViagemV2IntegracaoRequestModel request)
        {
            const string mensagemFalha = "Falha na integração da viagem.";
            var response = new ViagemV2IntegracaoResponseModel();
            request.CNPJEmpresa = string.IsNullOrEmpty(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa;

            try
            {
                var empresa = _empresaApp.Get(request.CNPJAplicacao);

                if (empresa == null)
                {
                    return new Retorno<ViagemV2IntegracaoResponseModel>
                    {
                        Sucesso = false, 
                        Mensagem = "Empresa não encontrada",
                        Objeto = null
                    };
                }
                
                var validacaoRequest = request.ValidarEntrada(
                    _parametrosApp.GetValidarDocumentosViagemComDocumentosDasIntegracoes(empresa.IdEmpresa), true);

                if (!validacaoRequest.IsValid)
                    return new Retorno<ViagemV2IntegracaoResponseModel>(validacaoRequest, null, mensagemFalha);

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                
                if (request.DadosViagem.CadastrosPreViagem != null)
                {
                    if (request.DadosViagem.CadastrosPreViagem.Proprietario != null
                        && request.DadosViagem.CadastrosPreViagem.Proprietario.Cartao != null
                        && !string.IsNullOrEmpty(request.DadosViagem.CadastrosPreViagem.Proprietario.Cartao.NumeroCartao.ToStringSafe())
                        && !string.IsNullOrEmpty(request.DadosViagem.CadastrosPreViagem.Proprietario.CnpjCpf))
                    {
                        var resultadoProprietario = cartoesApp.GetCartaoHistoricoGrid(StringExtension.OnlyNumbers(request.DadosViagem.CadastrosPreViagem.Proprietario.CnpjCpf), cartaoIdArray);
                        
                        if (resultadoProprietario.Sucesso)
                        {
                            if (resultadoProprietario.Objeto.Any())
                            {
                                var cartaoVinculado = resultadoProprietario.Objeto.FirstOrDefault(x =>
                                    x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado && x.Identificador !=
                                    request.DadosViagem.CadastrosPreViagem.Proprietario.Cartao.NumeroCartao);
                                
                                if (cartaoVinculado != null && !empresa.VinculoNovoCartaoPortador)
                                    return new Retorno<ViagemV2IntegracaoResponseModel>(false,
                                        $"O portador de documento {request.DadosViagem.CadastrosPreViagem.Proprietario.CnpjCpf.FormatMaskCpfCnpj()} já possui um cartão vinculado. Para realizar o vínculo de um novo cartão é necessário desvincular o atual. Em caso de dúvidas entre em contato com a Extratta!",
                                        null);
                                
                                var cartaoBloqueado = resultadoProprietario.Objeto.FirstOrDefault(x =>
                                    x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado && x.Identificador !=
                                    request.DadosViagem.CadastrosPreViagem.Proprietario.Cartao.NumeroCartao);
                                
                                if (cartaoBloqueado != null)
                                    return new Retorno<ViagemV2IntegracaoResponseModel>(false,
                                        $"Não foi possível vincular o cartão, pois o portador já possui um cartão com status bloqueado. Efetue o desbloqueio do cartão para o vínculo de um novo cartão!",
                                        null);
                            }
                        }
                        else
                            return new Retorno<ViagemV2IntegracaoResponseModel>(false, "Não foi possível consultar o historíco de cartões do proprietário integrado!", null);
                    }
                    
                    if (request.DadosViagem.CadastrosPreViagem.Motorista != null
                        && request.DadosViagem.CadastrosPreViagem.Motorista.Cartao != null
                        && !string.IsNullOrEmpty(request.DadosViagem.CadastrosPreViagem.Motorista.Cartao.NumeroCartao.ToStringSafe())
                        && !string.IsNullOrEmpty(request.DadosViagem.CadastrosPreViagem.Motorista.Cpf))
                    {
                        var resultadoMotorista = cartoesApp.GetCartaoHistoricoGrid(StringExtension.OnlyNumbers(request.DadosViagem.CadastrosPreViagem.Motorista.Cpf), cartaoIdArray);

                        if (resultadoMotorista.Sucesso)
                        {
                            if (resultadoMotorista.Objeto.Any())
                            {
                                var cartaoVinculado = resultadoMotorista.Objeto.FirstOrDefault(x =>
                                    x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado && x.Identificador != request.DadosViagem.CadastrosPreViagem.Motorista.Cartao.NumeroCartao);

                                if (cartaoVinculado != null && !empresa.VinculoNovoCartaoPortador)
                                    return new Retorno<ViagemV2IntegracaoResponseModel>(false,
                                        $"O portador documento {request.DadosViagem.CadastrosPreViagem.Motorista.Cpf.FormatMaskCpfCnpj()} já possui um cartão vinculado. Para realizar o vínculo de um novo cartão é necessário desvincular o atual. Em caso de dúvidas entre em contato com a Extratta!",
                                        null);
                                
                                var cartaoBloqueado = resultadoMotorista.Objeto.FirstOrDefault(x =>
                                    x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado && x.Identificador != request.DadosViagem.CadastrosPreViagem.Motorista.Cartao.NumeroCartao);

                                if (cartaoBloqueado != null)
                                    return new Retorno<ViagemV2IntegracaoResponseModel>(false,
                                        $"Não foi possível vincular o cartão, pois o portador já possui um cartão com status bloqueado. Efetue o desbloqueio do cartão para o vínculo de um novo cartão!",
                                        null);
                            }
                        }
                        else
                            return new Retorno<ViagemV2IntegracaoResponseModel>(false, "Não foi possível consultar o historíco de cartões do motorista integrado!", null);
                    }
                }

                var validacaoAutorizacaoEstabelecimentos = request.ValidarPreencherAutorizacaoEstabelecimentos(_viagemApp);
                if (!validacaoAutorizacaoEstabelecimentos.IsValid)
                    return new Retorno<ViagemV2IntegracaoResponseModel>
                    {
                        Sucesso = false, Mensagem = "Falha na requisição para o campo AutorizacaoEstabelecimentos: " + validacaoAutorizacaoEstabelecimentos,
                        Objeto = null, Faults = validacaoAutorizacaoEstabelecimentos.GetFaults()
                    };

                request.DadosViagem.ViagemRegra = GetViagemRegra(request);

                Retorno<ViagemV2IntegracaoResponseModel> resultadoIntegracoes;

                if (request.DadosViagem.DadosIniciais.RealizarIntegracoesPreViagem)
                    if (!RealizarIntegracoes(request, response, out resultadoIntegracoes))
                        return resultadoIntegracoes;

                var viagemIntegrarModel = Mapper.Map<ViagemIntegrarRequestModel>(request);

                var resultadoIntegracaoViagem =
                    _srvViagem.Integrar(viagemIntegrarModel, true);

                if (!resultadoIntegracaoViagem.Sucesso)
                    return new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoViagem.Mensagem, EFaultType.Error), null, mensagemFalha);

                Mapper.Map(resultadoIntegracaoViagem.Objeto, response);
                return new Retorno<ViagemV2IntegracaoResponseModel>(true, GetMensagemRetorno(resultadoIntegracaoViagem), response);
            }
            catch (Exception e)
            {
                return new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(e.Message, EFaultType.Error), null, mensagemFalha);
            }
        }

        public Retorno<ViagemV2IntegracaoResponseModel> Alterar(ViagemV2AlteracaoRequestModel request, bool isApi = false)
        {
            const string mensagemFalha = "Falha na alteração da viagem.";

            try
            {
                var response = new ViagemV2IntegracaoResponseModel();
                var resultadoValidacaoEntrada =
                    request.ValidarEntrada(_parametrosApp.GetValidarDocumentosViagemComDocumentosDasIntegracoes(_empresaApp.GetIdPorCnpj(request.CNPJAplicacao) ?? 0), false);

                if (!resultadoValidacaoEntrada.IsValid)
                    return new Retorno<ViagemV2IntegracaoResponseModel>(resultadoValidacaoEntrada, null, mensagemFalha);

                var viagemCadastrada = _viagemApp.Find(o => o.IdViagem == request.ViagemId).Include(o => o.ViagemEventos).FirstOrDefault();
                var resultadoValidacaoViagemPersistida = request.ValidarEntradaComViagemPersistida(viagemCadastrada);

                if (!resultadoValidacaoViagemPersistida.IsValid)
                    return new Retorno<ViagemV2IntegracaoResponseModel>(resultadoValidacaoViagemPersistida, null, mensagemFalha);

                var validacaoAutorizacaoEstabelecimentos = request.ValidarPreencherAutorizacaoEstabelecimentos(_viagemApp);
                if (!validacaoAutorizacaoEstabelecimentos.IsValid)
                    return new Retorno<ViagemV2IntegracaoResponseModel>
                    {
                        Sucesso = false, Mensagem = "Falha na requisição para o campo AutorizacaoEstabelecimentos: " + validacaoAutorizacaoEstabelecimentos,
                        Objeto = null, Faults = validacaoAutorizacaoEstabelecimentos.GetFaults()
                    };

                Retorno<ViagemV2IntegracaoResponseModel> resultadoIntegracoes;

                if (request.DadosViagem.DadosIniciais.RealizarIntegracoesPreViagem)
                    if (!RealizarIntegracoes(request, response, out resultadoIntegracoes))
                        return resultadoIntegracoes;

                var viagemModel = Mapper.Map<ViagemIntegrarRequestModel>(request);

                var resultadoAlteracao =
                    _srvViagem.Alterar(viagemModel, isApi);

                if (!resultadoAlteracao.Sucesso)
                {
                    return resultadoAlteracao.Faults != null && resultadoAlteracao.Faults.Any()
                        ? new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoAlteracao.Faults.FirstOrDefault()?.Message, EFaultType.Error), null, mensagemFalha)
                        : new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoAlteracao.Mensagem, EFaultType.Error), null, mensagemFalha);
                }

                Mapper.Map(resultadoAlteracao.Objeto, response);
                return new Retorno<ViagemV2IntegracaoResponseModel>(true, GetMensagemRetorno(resultadoAlteracao), response);
            }
            catch (Exception e)
            {
                return new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(e.Message, EFaultType.Error), null, mensagemFalha);
            }
        }

        private bool RealizarIntegracoes(ViagemV2IntegracaoRequestModel request, ViagemV2IntegracaoResponseModel response, out Retorno<ViagemV2IntegracaoResponseModel> retorno)
        {
            retorno = new Retorno<ViagemV2IntegracaoResponseModel>();

            var empresaId = _empresaApp.GetIdPorCnpj(request.CNPJEmpresa);

            if (!empresaId.HasValue || empresaId.Value == 0)
            {
                retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add("Não foi possível localizar a empresa pelo CNPJ informado na requisição.", EFaultType.Error),
                    null, "Falha na integração da viagem.");
                return false;
            }

            if (request.DadosViagem.CadastrosPreViagem != null)
            {
                response.IntegracoesPreViagem = new IntegracoesPreViagem();

                if (request.DadosViagem.CadastrosPreViagem.ClienteOrigem != null)
                {
                    var resultadoIntegracaoClienteOrigem =
                        _integracoesPreViagemV2.RealizarIntegracaoClienteViagem(
                            request.DadosViagem.CadastrosPreViagem.ClienteOrigem, request);

                    if (resultadoIntegracaoClienteOrigem.Sucesso)
                        response.IntegracoesPreViagem.ClienteOrigem = resultadoIntegracaoClienteOrigem;
                    else
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoClienteOrigem.Mensagem, EFaultType.Error), null,
                            "Falha na integração de cliente de origem.");
                        return false;
                    }
                }

                if (request.DadosViagem.CadastrosPreViagem.ClienteDestino != null)
                {
                    var resultadoIntegracaoClienteDestino =
                        _integracoesPreViagemV2.RealizarIntegracaoClienteViagem(
                            request.DadosViagem.CadastrosPreViagem.ClienteDestino, request);

                    if (resultadoIntegracaoClienteDestino.Sucesso)
                        response.IntegracoesPreViagem.ClienteDestino = resultadoIntegracaoClienteDestino;
                    else
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoClienteDestino.Mensagem, EFaultType.Error), null,
                            "Falha na integração de cliente de destino.");
                        return false;
                    }
                }

                if (request.DadosViagem.CadastrosPreViagem.Proprietario != null)
                {
                    var resultadoIntegracaoProprietario =
                        _integracoesPreViagemV2.RealizarIntegracaoProprietarioViagem(request.DadosViagem.CadastrosPreViagem.Proprietario, request, empresaId.Value);

                    if (resultadoIntegracaoProprietario.Sucesso)
                        response.IntegracoesPreViagem.Proprietario = resultadoIntegracaoProprietario;
                    else
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoProprietario.Mensagem, EFaultType.Error), null,
                            "Falha na integração de proprietário.");
                        return false;
                    }
                }

                if (request.DadosViagem.CadastrosPreViagem.Motorista != null)
                {
                    var resultadoIntegracaoMotorista =
                        _integracoesPreViagemV2.RealizarIntegracaoMotoristaViagem(_cartoesAppFactoryDependencies,
                            _srvCartoes, _srvMotorista, request.DadosViagem.CadastrosPreViagem.Motorista, request, empresaId.Value);

                    if (resultadoIntegracaoMotorista.Sucesso)
                        response.IntegracoesPreViagem.Motorista = resultadoIntegracaoMotorista;
                    else
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoMotorista.Mensagem, EFaultType.Error), null,
                            "Falha na integração do motorista.");
                        return false;
                    }
                }

                if (request.DadosViagem.CadastrosPreViagem.Veiculo != null)
                {
                    var resultadoIntegracaoVeiculo = _integracoesPreViagemV2.RealizarIntegracaoVeiculoViagem(_srvVeiculo, request.DadosViagem.CadastrosPreViagem.Veiculo, request);

                    if (resultadoIntegracaoVeiculo.Sucesso)
                        response.IntegracoesPreViagem.Veiculo = resultadoIntegracaoVeiculo;
                    else
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(resultadoIntegracaoVeiculo.Mensagem, EFaultType.Error), null,
                            "Falha na integração do veículo.");
                        return false;
                    }
                }

                if (request.DadosViagem.CadastrosPreViagem.Carretas != null)
                {
                    var resultadoIntegracaoCarretas = _integracoesPreViagemV2.RealizarIntegracaoCarretaViagem(
                        request.DadosViagem.CadastrosPreViagem.Carretas, empresaId.Value, _veiculoApp, _proprietarioApp, _cidadeApp, _filialApp);

                    if (!resultadoIntegracaoCarretas.Sucesso)
                    {
                        retorno = new Retorno<ViagemV2IntegracaoResponseModel>(new ValidationResult().Add(
                            resultadoIntegracaoCarretas.Mensagem, EFaultType.Error), null, "Falha na integração das carretas.");
                        return false;
                    }
                }
            }

            return true;
        }

        private string GetMensagemRetorno(Retorno<ViagemIntegrarResponseModel> resultadoIntegracaoViagem)
        {
            var mensagemRetorno = $"Viagem: {resultadoIntegracaoViagem.Mensagem} ";

            if (resultadoIntegracaoViagem.Objeto?.CIOT?.Resultado == EResultadoDeclaracaoCiot.Sucesso)
            {
                var tipoCiot = _ciotV3App.ObterTipoDeclaracao(resultadoIntegracaoViagem.Objeto.IdViagem);
                
                var mensagemCiot = tipoCiot == ETipoDeclaracao.Padrao
                    ? $"O prazo para cancelamento da viagem passa a ser de 24 horas após a data de declaração do CIOT."
                    : "O prazo para cancelamento da viagem passa a ser de 5 dias após a data de declaração.";
                
                mensagemRetorno += $"Ciot: {mensagemCiot}";
            }

            return mensagemRetorno;
        }

        private List<ViagemRegraIntegrarModel> GetViagemRegra(ViagemV2IntegracaoRequestModel request)
        {
            return new List<ViagemRegraIntegrarModel>
            {
                new ViagemRegraIntegrarModel
                {
                    TotalFrete = request.DadosViagem.ViagemEventos.Sum(o => o.ValorPagamento), TaxaAntecipacao = 0,
                    ToleranciaPeso = 0, TarifaTonelada = 0, TipoQuebraMercadoria = ETipoQuebraMercadoria.Integral
                }
            };
        }

        public Retorno<ViagemRemoverCarretasResponse> RemoverCarretas(ViagemRemoverCarretasRequest request)
        {
            var srvViagem = _srvViagem;
            return srvViagem.RemoverCarretas(request);
        }

        public Retorno<ViagemV2AutorizarEstabelecimentosResponse> AutorizarEstabelecimentos(ViagemV2AutorizarEstabelecimentosRequest request)
        {
            return _alteracaoViagem.AutorizarEstabelecimentos(request);
        }

        public Retorno<ViagemV2DesautorizarEstabelecimentosResponse> DesautorizarEstabelecimentos(ViagemV2DesautorizarEstabelecimentosRequest request)
        {
            return _alteracaoViagem.DesautorizarEstabelecimentos(request);
        }
    }
}
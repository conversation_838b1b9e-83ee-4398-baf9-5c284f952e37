﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using ATS.CrossCutting.IoC;
using ATS.Domain.Extensions;
using ATS.MongoDB.Context.Entities;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.MongoDB.Enum;
using MongoDB.Bson;

namespace ATS.Domain.Service
{
    public class DataMediaServerService : ServiceBase, IDataMediaServerService
    {
        private readonly static Dictionary<string, string> TipoEstabelecimentoIconeCached =
            new Dictionary<string, string>();

        private readonly IDataMediaServerRepository _dataMediaServerRepository;

        public DataMediaServerService(IDataMediaServerRepository dataMediaServerRepository)
        {
            _dataMediaServerRepository = dataMediaServerRepository;
        }

        public ObjectId Add(int type, string base64Data, string fileName, string mimeType = null)
        {
            return _dataMediaServerRepository.Add(type, base64Data, fileName, mimeType);
        }

        public Media GetMedia(string id)
        {
            try
            {
                return _dataMediaServerRepository.GetMedia(id);
            }
            catch(Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// Método onde realiza um In no Mongo pela lista de ID enviado
        /// </summary>
        /// <param name="listId"></param>
        /// <returns></returns>
        public List<Media> GetListMedia(List<string> listId)
        {
            try
            {
                return _dataMediaServerRepository.GetListMedia(listId);
            }
            catch(Exception)
            {
                return null;
            }
        }

        public void DeleteByToken(string token)
        {
            _dataMediaServerRepository.DeleteByToken(token);
        }

        public object VisualizarMedia(string token)
        {
            if (token == null)
                throw new Exception("É necessário informar o token");

            var media = GetMedia(token);

            if (media == null)
                throw new Exception($"Nenhum dado de mídia foi encontrado para o token {token}");

            var extension = Path.GetExtension(media.FileName);

            if (extension == ".docx" || extension == ".xlsx" || extension == ".xls")
                throw new Exception($"Não é possível visualizar o arquivo do tipo {extension}, para visualizar realize o download.");

            switch (media.Type)
            {
                case EMediaType.JPEG:
                    return new { image = media.Data };

                case EMediaType.PNG:
                    return new { image = media.Data };

                case EMediaType.Text:
                    var textBase64 = Convert.FromBase64String(media.Data.Split(',')[1]);
                    return new { text = Encoding.UTF7.GetString(textBase64) };

                case EMediaType.JSON:
                    var jsonBase64 = Convert.FromBase64String(media.Data.Split(',')[1]);
                    return new { text = Encoding.UTF7.GetString(jsonBase64) };

                case EMediaType.PDF:
                    throw new Exception(
                        $"Não é possível visualizar o arquivo do tipo {media.Type.DescriptionAttr()}, para visualizar realize o download.");

                case EMediaType.DOC:
                    throw new Exception(
                        $"Não é possível visualizar o arquivo do tipo {media.Type.DescriptionAttr()}, para visualizar realize o download.");

                case EMediaType.XLS:
                    throw new Exception(
                        $"Não é possível visualizar o arquivo do tipo {media.Type.DescriptionAttr()}, para visualizar realize o download.");

                case EMediaType.DOCX:
                    throw new Exception($"Não é possível visualizar o arquivo do tipo {media.Type.DescriptionAttr()}, para visualizar realize o download.");

                default:
                    throw new Exception(
                        $"Não é possível visualizar o arquivo do tipo {media.Type.DescriptionAttr()}, para visualizar realize o download.");
    }
        }

        public string GetIconeTipoEstabelecimentoCacheable(string mediaId)
        {
            if (TipoEstabelecimentoIconeCached.ContainsKey(mediaId))
                return TipoEstabelecimentoIconeCached[mediaId];

            var data = GetMedia(mediaId);            
            TipoEstabelecimentoIconeCached.Add(mediaId, data?.Data);
            
            return data?.Data;
    }
    }
}
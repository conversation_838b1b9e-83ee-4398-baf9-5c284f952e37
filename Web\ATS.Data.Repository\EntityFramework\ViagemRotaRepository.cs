using System;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.Dapper.Common;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using Dapper;
using NLog;

namespace ATS.Data.Repository.EntityFramework
{
    public class ViagemRotaRepository : Repository<ViagemRota>, IViagemRotaRepository
    {
        public ViagemRotaRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<ViagemRota> GetByViagem(int idviagem)
        {
            return Include(x => x.Pontos).Where(c => c.IdViagem == idviagem);
        }

        public bool DeleteNew(int idviagemrota)
        {
            try
            {
                var query = $@"DELETE FROM VIAGEM_ROTA_PONTO where idviagemrotaponto = @Id";
                var query2 = $@"DELETE FROM VIAGEM_ROTA where idviagemrotaponto = @Id";

                using var connection = new DapperContext().GetConnection;

                var parameters = new { Id = idviagemrota};
                connection.Query<CargaAvulsa>(query, parameters);
                connection.Query<CargaAvulsa>(query2, parameters);

                return true;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return false;
            }
        }
    }
}
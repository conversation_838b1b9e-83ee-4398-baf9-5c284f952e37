﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Security;
using ATS.WS.Security.Configuration;
using System;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Models;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class AutorizarMobile : ActionFilterAttribute
    {
        public IUserIdentity UserIdentity { get; set; }
        public AutorizarMobile()
        {
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {

            var customAttributes = filterContext.ActionDescriptor.GetCustomAttributes(true);

            if (customAttributes == null || !customAttributes.Any() || customAttributes.All(x => x.GetType() != typeof(AutorizarMobile)))
            {
                return;
            }

            string token = filterContext.HttpContext.Request.Headers["Authorization"];
            if (string.IsNullOrEmpty(token))
            {
                token = filterContext.HttpContext.Request.Headers["SessionKey"] ?? filterContext.HttpContext.Request.Params["SessionKey"];
            }
            else
            {
                token = token.Split(' ')[1];
            }

            var exigirAuthMobile = WebConfigurationManager.AppSettings["KC_MOB_REQUIRED"].ToBoolSafe();

            //esse if deve ser removido quando passar a validar definitivamente todas as chamadas do mobile
            if (string.IsNullOrEmpty(token) && !exigirAuthMobile) return;

            new Autenticacao().ValidarToken(token,
                new TokenConfigurations
                {
                    Audience = WebConfigurationManager.AppSettings["ATS_Audience"],
                    Issuer = WebConfigurationManager.AppSettings["ATS_Issuer"],
                    Seconds = Convert.ToInt32(WebConfigurationManager.AppSettings["ATS_TempoSessao"])
                }, new SigningConfigurations(), UserIdentity, filterContext);

            if (filterContext.Result != null) return;

            UserIdentity.Origem = EUserOrigin.AtsWeb;

            HttpContext.Current.Session["Usuario"] = UserIdentity;
        }
    }
}
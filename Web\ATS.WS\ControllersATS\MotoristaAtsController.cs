﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Motorista;
using ATS.WS.Models.Webservice.Response.Motorista;
using ATS.WS.Services;
using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class MotoristaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ISerproApp _serproApp;
        private readonly SrvMotorista _srvMotorista;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public MotoristaAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, IMotoristaApp motoristaApp, SrvMotorista srvMotorista, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IParametrosEmpresaService parametrosEmpresa, ISerproApp serproApp)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _motoristaApp = motoristaApp;
            _srvMotorista = srvMotorista;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, bool ativo = true)
        {
            try
            {
                return ResponderSucesso( _motoristaApp.ConsultarGrid(_userIdentity.IdEmpresa, take, page, order, filters,ativo));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idMotorista)
        {
            if (_userIdentity.IdEmpresa.HasValue)
            {
                if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idMotorista))
                    return ResponderErro("Registro não encontrado.");
            }

            var motorista = _motoristaApp.GetWithChilds(idMotorista, _userIdentity?.IdUsuario);
            var validadeCnhMot = _usuarioApp.GetQueryPorCnpjcpf(motorista.CPF).Select(o => o.ValidadeCNH).FirstOrDefault();

            var retorno = Mapper.Map<Motorista, MotoristaResponse>(motorista);
            retorno.RazaoSocial = motorista.Empresa?.RazaoSocial;

            if (validadeCnhMot.HasValue && validadeCnhMot.Value.Year > 1)
            {
                retorno.ValidadeCNH = validadeCnhMot;
            }
            else
            {
                if (motorista.ValidadeCNH.HasValue)
                    retorno.ValidadeCNH = motorista.ValidadeCNH.Value;
            }

            return ResponderSucesso(retorno);
        }
        
        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorCnpjCpf(string cpfCnpjMotorista)
        {
            var motorista = _motoristaApp.Get(cpfCnpjMotorista);
            if (_userIdentity.IdEmpresa.HasValue)
            {
                if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, motorista.IdMotorista))
                    return ResponderErro("Registro não encontrado.");
            }
            var retorno = Mapper.Map<Motorista, MotoristaResponse>(motorista);

            return ResponderSucesso(retorno);
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetImageJson(int idMotorista)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idMotorista))
                        return ResponderErro("Registro não encontrado.");
                }

                var byteArray = _motoristaApp.GetFoto(idMotorista);

                if (byteArray != null && byteArray.Any())
                {
                    var imgBase64 = Convert.ToBase64String(new FileContentResult(byteArray, "image/png").FileContents);
                    return ResponderSucesso(null, new {image = $"data:image/png;base64,{imgBase64}"});
                }

                return ResponderErro("Imagem não encontrada");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Integrar(MotoristaIntegrarRequest @params)
        {
            try
            {
                //Editando
                if (@params.IdMotorista.HasValue)
                {
                    if (_userIdentity.IdEmpresa.HasValue)
                    {
                        if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, @params.IdMotorista.Value))
                            return ResponderErro("Registro não encontrado.");
                        
                        /*if (@params.Cpf.OnlyNumbers().Length == 11 &&
                            _parametrosEmpresa.GetPermiteCadastrarMotoristaComCpfFicticio(_userIdentity.IdEmpresa.Value) == false)
                        {
                            var validacaoSerpro = _serproApp.ValidarPortador(@params.Cpf);
                            if (!validacaoSerpro.IsValid)
                                throw new Exception(validacaoSerpro.Errors.FirstOrDefault()?.Message);
                        }*/
                    }

                    var motorista = _motoristaApp.Get(@params.IdMotorista.Value);

                    Mapper.Map(@params, motorista);

                    motorista.DataHoraUltimaAtualizacao = DateTime.Now;

                    #region Armazenamento da imagem

                    if (@params.Imagem == null)
                        motorista.Foto = null;

                    if (!string.IsNullOrWhiteSpace(@params.Imagem))
                    {
                        motorista.Foto = Convert.FromBase64String(@params.Imagem);
                    }

                    #endregion

                    if (_userIdentity.Perfil != (int) EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                        motorista.IdEmpresa = _userIdentity.IdEmpresa.Value;

                    var validationResult = _motoristaApp.Update(motorista, _userIdentity.IdUsuario);

                    if (!validationResult.IsValid)
                        return ResponderErro(validationResult.ToString());

                    return ResponderSucesso("Dados alterados com sucesso.");
                }

                //Integrando
                else
                {
                    var motorista = Mapper.Map<MotoristaIntegrarRequest, Motorista>(@params);

                    motorista.DataHoraUltimaAtualizacao = DateTime.Now;

                    if (!string.IsNullOrWhiteSpace(@params.Imagem))
                    {
                        motorista.Foto = Convert.FromBase64String(@params.Imagem);
                    }

                    if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                        motorista.IdEmpresa = _userIdentity.IdEmpresa;

                    /*if (_userIdentity.IdEmpresa.HasValue)
                    {
                        if (@params.Cpf.OnlyNumbers().Length == 11 &&
                            _parametrosEmpresa.GetPermiteCadastrarMotoristaComCpfFicticio(_userIdentity.IdEmpresa.Value) == false)
                        {
                            var validacaoSerpro = _serproApp.ValidarPortador(@params.Cpf);
                            if (!validacaoSerpro.IsValid)
                                throw new Exception(validacaoSerpro.Errors.FirstOrDefault()?.Message);
                        }
                    }*/

                    var validationResult = _motoristaApp.Add(motorista, _userIdentity.IdUsuario);

                    if (!validationResult.IsValid)
                        return ResponderErro(validationResult.ToString());
                }

                return ResponderSucesso("Motorista inserido com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int idMotorista)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idMotorista))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _srvMotorista.AlterarStatus(idMotorista);
                return retorno.IsValid ? ResponderSucesso(null) : ResponderErro(retorno.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CartoesHistorico(int idMotorista)
        {
            var response = new List<object>();

            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idMotorista))
                        return ResponderErro("Registro não encontrado.");
                }

                var motorista = _motoristaApp.Get(idMotorista);
                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, _userIdentity.IdEmpresa, _userIdentity.IdUsuario, true);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();

                if (cartaoProdutosList != null)
                {
                    var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                    var result = cartoesApp.GetCartaoHistorico(motorista.CPF, cartaoIdArray);

                    if (result.Status == HistoricoCartaoPessoaListResponseStatus.Falha)
                        return ResponderErro(result.Mensagem);

                    foreach (var res in result.Cartoes)
                    {
                        response.Add(
                            new
                            {
                                res.Identificador,
                                res.Produto,
                                res.Status,
                                DataDesvinculo = res.DataDesvinculo?.ToString("MM/dd/yyyy HH:mm"),
                                DataVinculo = res.DataVinculo?.ToString("MM/dd/yyyy HH:mm"),
                                res.MotivoDesvinculo,
                                res.StatusDescricao
                            });
                    }
                }
            }
            catch (Exception e)
            {
                ResponderErro(e);
            }

            return ResponderSucesso(response);
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CartoesVinculados(int idMotorista)
        {
            var response = new List<object>();

            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_motoristaApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idMotorista))
                        return ResponderErro("Registro não encontrado.");
                }

                var motorista = _motoristaApp.Get(idMotorista);
                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, _userIdentity.IdEmpresa, _userIdentity.IdUsuario, true);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();

                if (cartaoProdutosList != null)
                {
                    var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                    var result = cartoesApp.GetCartoesVinculados(motorista.CPF, cartaoIdArray).Cartoes;

                    foreach (var res in result)
                        response.Add(new
                        {
                            res.Identificador,
                            res.Produto,
                            DataVinculo = res.DataVinculo?.ToString("MM/dd/yyyy HH:mm")
                        });
                }
            }
            catch (Exception e)
            {
                ResponderErro(e);
            }

            return ResponderSucesso(response);
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridMotoristas(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _srvMotorista.GerarRelatorioGridMotoristas(filtrosGridModel.IdEmpresa,
                filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters,
                filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de motoristas.{filtrosGridModel.Extensao}");
        }
    }
}
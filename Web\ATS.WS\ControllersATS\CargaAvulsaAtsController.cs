﻿using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Domain.Enum;
using JsonResult = System.Web.Mvc.JsonResult;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.CargaAvulsa;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Models.Webservice.Request.CargaAvulsaAts;
using Newtonsoft.Json;
using CargaAvulsa = ATS.Domain.Entities.CargaAvulsa;

namespace ATS.WS.ControllersATS
{
    public class 
CargaAvulsaAtsController : BaseAtsController<ICargaAvulsaApp>
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IFilialApp _filialApp;
        private readonly IPortadorService _portadorService;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IUserIdentity _userIdentity;

        public CargaAvulsaAtsController(ICargaAvulsaApp app, IUserIdentity userIdentity,
            IUsuarioApp usuarioApp, IEmpresaRepository empresaRepository,
            IPortadorService portadorService, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IFilialApp filialApp, IParametrosEmpresaService parametrosEmpresaService) : base(app, userIdentity)
        {
            _usuarioApp = usuarioApp;
            _empresaRepository = empresaRepository;
            _portadorService = portadorService;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _filialApp = filialApp;
            _parametrosEmpresaService = parametrosEmpresaService;
            _userIdentity = userIdentity;
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            try
            {
                var cargaAvulsa = App.ConsultaGrid(SessionUser.IdEmpresa, SessionUser.IdUsuario, take, page, order, filters,apenasLiberacaoGestor);

                return ResponderSucesso(cargaAvulsa);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(CargaAvulsaRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                if (SessionUser.Perfil != (int) EPerfil.Empresa)
                    return ResponderErro("Apenas usuários de perfil empresa podem realizar carga avulsa!");
                
                if (@params.IdFilial.HasValue && SessionUser.IdEmpresa.HasValue &&
                    !_filialApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, @params.IdFilial.Value))
                    return ResponderErro("Usuário não autenticado.");

                @params.IdUsuariocadastro = SessionUser.IdUsuario;
                @params.CPFUsuario = SessionUser.CpfCnpj;

                var cargaAvulsa = Mapper.Map<CargaAvulsaRequestModel, CargaAvulsa>(@params);

                cargaAvulsa.IdEmpresa = SessionUser.IdEmpresa;
                
                var aprovacaoGestor = _parametrosEmpresaService.GetSolicitaAprovacaoGestorCargaAvulsaUnitario(cargaAvulsa.IdEmpresa ?? 0);

                if (aprovacaoGestor)
                    cargaAvulsa.StatusCargaAvulsa = EStatusCargaAvulsa.AguardandoLiberacao; 

                var responseModel = App.Add(cargaAvulsa, _empresaRepository.GetCnpj(SessionUser.IdEmpresa ?? 0),origem:EBloqueioOrigemTipo.Portal, ignorarValidacaoDuplicada:@params.IgnorarValidacaoDuplicada);

                if (responseModel.CargaAvulsaDuplicada == true)
                    return ResponderPadrao(false, "Carga avulsa duplicada detectada.", responseModel);

                if (!responseModel.ValidationResult.IsValid)
                    return ResponderPadrao(false, "Falha ao cadastrar carga avulsa.", responseModel.ValidationResult.GetFaults());

                return ResponderPadrao(true, "Carga avulsa incluida com sucesso.", responseModel);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reprocessar(int cargaAvulsaId)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    throw new Exception("Nenhuma empresa vinculada a este usuário.");

                if (!App.PertenceAEmpresa(SessionUser.IdEmpresa.Value, cargaAvulsaId))
                    throw new Exception("Registro não encontrado.");

                var response = App.ReprocessarCargaAvulsa(cargaAvulsaId);

                return response.IsValid
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Expor(EApi.Portal)]
        public JsonResult EstornarCargaAvulsa(EstornoCargaAvulsaModel @params)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    throw new Exception("Nenhuma empresa vinculada a este usuário.");

                if (!App.PertenceAEmpresa(SessionUser.IdEmpresa.Value, @params.IdCargaAvulsa))
                    throw new Exception("Registro não encontrado.");
                
                var response = App.EstornarCargaAvulsa(@params.IdCargaAvulsa, @params.NumeroControleIntegracao,
                    _empresaRepository.GetCnpj(SessionUser.IdEmpresa ?? 0));

                return response.Sucesso
                    ? ResponderSucesso(string.Empty)
                    : ResponderErro(response.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridPortadores(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                return ResponderSucesso(
                    _portadorService.ConsultarGridPortadores(SessionUser.IdEmpresa, take, page, filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridCargaAvulsa(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<ReportCargaAvulsaRequestModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                filtroGridModel.IdEmpresa = SessionUser.IdEmpresa;

            var relatorio = App.GerarRelatorioGridCargaAvulsa(filtroGridModel.Order, filtroGridModel.Filters,
                filtroGridModel.Extensao, GetLogo(filtroGridModel.IdEmpresa ?? 0), filtroGridModel.IdEmpresa,filtroGridModel.ApenasLiberacaoGestor);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(relatorio, mimeType, $"Carga Avulsa.{filtroGridModel.Extensao}");
        }

        [HttpPost]
        [EnableLogAudit]
        [EnableLogRequest]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa status)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    throw new Exception("Nenhuma empresa vinculada a este usuário.");

                if (!App.PertenceAEmpresa(SessionUser.IdEmpresa.Value, cargaAvulsaId))
                    throw new Exception("Registro não encontrado.");

                var response = App.AlterarStatusCargaAvulsa(cargaAvulsaId, status);

                return response.IsValid
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCartoesPortador(string documento)
        {
            try
            {
                var documentoOnlyNumbers = documento.OnlyNumbers2();
                var usuario = _usuarioApp.GetAllChilds(SessionUser.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();

                if (cartaoProdutosList == null)
                    return ResponderErro($"Nenhum cartão vinculado ao documento {documentoOnlyNumbers.ToCpfOrCnpj()}");
            
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                var cartao = cartoesApp.GetCartoesVinculados(documentoOnlyNumbers, cartaoIdArray)?.Cartoes.LastOrDefault();

                return cartao != null
                    ? ResponderSucesso("O portador possui cartões vinculados.")
                    : ResponderErro($"Nenhum cartão vinculado ao documento {documentoOnlyNumbers.ToCpfOrCnpj()}");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarImpressaoRecibo(string json)
        {
            var request = JsonConvert.DeserializeObject<ImpressaoReciboRequest>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                request.EmpresaId = SessionUser.IdEmpresa;

            var report = App.GerarReciboCargaAvulsa(request.CargaAvulsaId, request.EmpresaId, request.Nome, request.Documento);

            return File(report, ConstantesUtils.PdfMimeType, $"Carga Avulsa {request.CargaAvulsaId}.pdf");
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GestorAprovar(GestorAprovarReprovarCargaAvulsaRequest request)
        {
            try
            {
                var response = App.GestorAprovar(request.IdCargaAvulsa);

                return response.Success
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult  GestorReprovar(GestorAprovarReprovarCargaAvulsaRequest request)
        {
            try
            {
                var response = App.GestorReprovar(request.IdCargaAvulsa,request.MotivoRejeicao,true,true);

                return response.Success
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridPlanilhas(int take, int page, OrderFilters order, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            try
            {
                var cargaAvulsa = App.ConsultaGridPlanilha(SessionUser.IdEmpresa, take, page, order, filters,apenasLiberacaoGestor);

                return ResponderSucesso(cargaAvulsa);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GestorAprovarPlanilha(string codImportacao)
        {
            try
            {
                var response = App.GestorAprovarPlanilha(codImportacao);

                return response.Success
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult  GestorReprovarPlanilha(string codImportacao)
        {
            try
            {
                var response = App.GestorReprovarPlanilha(codImportacao);

                return response.Success
                    ? ResponderSucesso("Operação realizada com sucesso.")
                    : ResponderErro(response.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
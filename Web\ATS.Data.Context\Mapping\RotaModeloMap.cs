using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class RotaModeloMap : EntityTypeConfiguration<RotaModelo>
    {
        public RotaModeloMap()
        {
            ToTable("ROTA_MODELO");

            HasKey(t => t.IdRotaModelo);

            Property(t => t.IdRotaModelo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.CustoRota)
                .IsRequired();

            Property(t => t.DataCadastro)
                .IsRequired();

            Property(t => t.DestinoDescricao)
                .HasMaxLength(500)
                .IsRequired();

            Property(t => t.DestinoIbge)
                .IsRequired();

            Property(t => t.DistanciaTotal)
                .IsRequired();

            Property(t => t.IdEmpresa)
                .IsOptional();

            Property(t => t.NomeRota)
                .IsRequired();

            Property(t => t.OrigemDescricao)
                .HasMaxLength(500)
                .IsRequired();

            Property(t => t.OrigemIbge)
                .IsRequired();

            Property(t => t.QtdeEixos)
                .IsRequired();

            Property(t => t.TipoVeiculo)
                .IsRequired();

            Property(t => t.TipoRodagem)
                .IsRequired();

            Property(t => t.CustoRotaTag)
                .IsRequired();
            
            Property(t => t.CodPolyline)
                .IsOptional();

            Property(t => t.OrigemLatitude)
                .HasPrecision(18, 7);

            Property(t => t.OrigemLongitude)
                .HasPrecision(18, 7);

            Property(t => t.DestinoLatitude)
                .HasPrecision(18, 7);

            Property(t => t.DestinoLongitude)
                .HasPrecision(18, 7);
        }
    }
}
﻿using System.Collections.Generic;
using System.Net.Mail;

namespace ATS.Domain.Models
{
    public class EmailModel
    {
        public string NomeVisualizacao { get; set; }
        public string Assunto { get; set; }
        public string CorpoEmail { get; set; }
        public List<string> Destinatarios { get; set; }
        public List<string> DestinatariosCopia { get; set; }
        public List<string> DestinatariosCopiaOculta { get; set; }
        public MailPriority Prioridade { get; set; } = MailPriority.Normal;
        public AlternateView AlternateView { get; set; }
        public Attachment Anexo { get; set; }
    }
	
    public class EmailConfiguration
    {
        public string EmailNome { get; set; }
        public string EmailEndereco { get; set; }
        public int EmailPorta { get; set; }
        public string EmailServidor { get; set; }
        public bool EmailSsl { get; set; }
        public string EmailUsuario { get; set; }
        public string EmailSenha { get; set; }
    }

    public class ConfiguracaoEnvioEmail
    {
        public string Endereco { get; set; }
        public int Porta { get; set; }
        public string Senha { get; set; }
        public string SMTP { get; set; }
        public bool SSL { get; set; }
    }
}

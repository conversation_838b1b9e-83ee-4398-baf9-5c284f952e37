﻿using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Drawing;
using System.Drawing.Text;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
 using ATS.Domain.Interface.Dapper;
using ATS.Domain.Grid;
using ATS.Domain.Extensions;
using System.Linq.Dynamic;
 using System.Net;
using ATS.Domain.Models.PagamentoFrete;
 using System.Net.Mail;
 using Newtonsoft.Json;
 using ATS.CrossCutting.IoC.Interfaces;
 using ATS.CrossCutting.Reports.CurvaAbc;
 using ATS.CrossCutting.Reports.CurvaAbc.Detalhes;
 using ATS.CrossCutting.Reports.Faturamento;
 using ATS.CrossCutting.Reports.PagamentoFrete.Recibo;
 using ATS.CrossCutting.Reports.ProvisaoPagamento;
 using ATS.Domain.Interface.Service;
 using AutoMapper.QueryableExtensions;
 using ClosedXML.Excel;
 using NLog;
 using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class PagamentoFreteService : ServiceBase, IPagamentoFreteService
    {
        private readonly ICredenciamentoRepository _credenciamentoRepository;
        private readonly IPagamentoConfiguracaoProcessoRepository _pagamentoConfiguracaoProcessoRepository;
        private readonly IPagamentoConfiguracaoRepository _pagamentoConfiguracaoRepository;
        private readonly IProtocoloRepository _protocoloRepository;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly IViagemDocumentoRepository _viagemDocumentoRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IViagemRepository _viagemRepository;
        private readonly IViagemSolicitacaoAbonoRepository _viagemSolicitacaoAbonoRepository;
        private readonly IViagemPendenteGestorRepository _viagemPendenteGestorRepository;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IUsuarioService _usuarioService;
        private readonly IProtocoloEventoRepository _protocoloEventoRepository;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly ILayoutRepository _layoutRepository;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IDocumentoService _documentoService;
        private readonly ICredenciamentoService _credenciamentoService;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IProtocoloService _protocoloService;
        private readonly ICredenciamentoAnexoRepository _credenciamentoAnexoRepository;
        private readonly IViagemValorAdicionalRepository _viagemValorAdicionalRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IDataMediaServerService _dataMediaServerService;
        private readonly IMotivoCredenciamentoService _motivoCredenciamentoService;
        private readonly IPagamentoDapper _pagamentoDapper;
        private readonly IEmailService _emailService;
        private readonly IMotoristaService _motoristaService;
        private readonly IVeiculoService _veiculoService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;

        public PagamentoFreteService(ICredenciamentoRepository credenciamentoRepository, IPagamentoConfiguracaoProcessoRepository pagamentoConfiguracaoProcessoRepository,
            IPagamentoConfiguracaoRepository pagamentoConfiguracaoRepository, IProtocoloRepository protocoloRepository, IVeiculoRepository veiculoRepository,
            IViagemDocumentoRepository viagemDocumentoRepository, IViagemEventoRepository viagemEventoRepository, IViagemRepository viagemRepository,
            IViagemSolicitacaoAbonoRepository viagemSolicitacaoAbonoRepository, IViagemPendenteGestorRepository viagemPendenteGestorRepository,
            IProprietarioRepository proprietarioRepository, IEstabelecimentoService estabelecimentoService, IUsuarioService usuarioService, IProtocoloEventoRepository protocoloEventoRepository,
            IDeclaracaoCiotRepository declaracaoCiotRepository, ILayoutRepository layoutRepository, IViagemEventoService viagemEventoService, IEmpresaRepository empresaRepository,
            IDocumentoService documentoService, ICredenciamentoService credenciamentoService, CartoesServiceArgs cartoesServiceArgs, IProtocoloService protocoloService,
            ICredenciamentoAnexoRepository credenciamentoAnexoRepository, IViagemValorAdicionalRepository viagemValorAdicionalRepository, IUsuarioRepository usuarioRepository,
            IDataMediaServerService dataMediaServerService, IMotivoCredenciamentoService motivoCredenciamentoService, IPagamentoDapper pagamentoDapper, IEmailService emailService,
            IMotoristaService motoristaService, IVeiculoService veiculoService, IParametrosEmpresaService parametrosEmpresaService)
        {
            _credenciamentoRepository = credenciamentoRepository;
            _pagamentoConfiguracaoProcessoRepository = pagamentoConfiguracaoProcessoRepository;
            _pagamentoConfiguracaoRepository = pagamentoConfiguracaoRepository;
            _protocoloRepository = protocoloRepository;
            _veiculoRepository = veiculoRepository;
            _viagemDocumentoRepository = viagemDocumentoRepository;
            _viagemEventoRepository = viagemEventoRepository;
            _viagemRepository = viagemRepository;
            _viagemSolicitacaoAbonoRepository = viagemSolicitacaoAbonoRepository;
            _viagemPendenteGestorRepository = viagemPendenteGestorRepository;
            _proprietarioRepository = proprietarioRepository;
            _estabelecimentoService = estabelecimentoService;
            _usuarioService = usuarioService;
            _protocoloEventoRepository = protocoloEventoRepository;
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _layoutRepository = layoutRepository;
            _viagemEventoService = viagemEventoService;
            _empresaRepository = empresaRepository;
            _documentoService = documentoService;
            _credenciamentoService = credenciamentoService;
            _cartoesServiceArgs = cartoesServiceArgs;
            _protocoloService = protocoloService;
            _credenciamentoAnexoRepository = credenciamentoAnexoRepository;
            _viagemValorAdicionalRepository = viagemValorAdicionalRepository;
            _usuarioRepository = usuarioRepository;
            _dataMediaServerService = dataMediaServerService;
            _motivoCredenciamentoService = motivoCredenciamentoService;
            _pagamentoDapper = pagamentoDapper;
            _emailService = emailService;
            _motoristaService = motoristaService;
            _veiculoService = veiculoService;
            _parametrosEmpresaService = parametrosEmpresaService;
        }

        public decimal GetNumeroDeSacas(decimal? pesoChegada, decimal? pesoSaida, decimal? quantidade)
        {
            decimal numeroSacas = 0;

            if (pesoChegada == 0 || pesoSaida == 0 || quantidade == 0)
                return numeroSacas;

            var pesoPorSaca = pesoSaida / quantidade;

            if (pesoChegada != null && pesoPorSaca != null)
                numeroSacas = pesoChegada.Value / pesoPorSaca.Value;

            return numeroSacas;
        }

        public PagamentoFreteModel ConsultarPorToken(string token, string cpfCnpjUsuario, string nomeUsuario, List<int> idsEstabelecimentosBaseUsuario = null, int? idEstabelecimento = null)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagemEvento = _viagemEventoRepository
                .Include(x => x.ViagemValoresAdicionais)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Viagem)
                .FirstOrDefault(x => x.Token == token);

            if (viagemEvento == null)
                return null;

            var viagemPendente = _viagemPendenteGestorRepository.GetAll().FirstOrDefault(c => c.IdViagem == viagemEvento.IdViagem && c.Status != (int) EBloqueioGestorStatus.Liberado);

            if (viagemPendente != null)
            {
                switch (viagemPendente.Status)
                {
                    case (int) EBloqueioGestorStatus.Bloqueado:
                        throw new Exception("Viagem bloqueada pelo gestor!");

                    case (int) EBloqueioGestorStatus.Pendente:
                        throw new Exception("Viagem pendente de confirmação com o gestor!");
                    default:
                        throw new ArgumentException($"{nameof(ViagemPendenteGestor)} - {nameof(ViagemPendenteGestor.Status)}");
                }
            }

            decimal? PesoChegadaTriagem = viagemEvento.Viagem.PesoChegada;
            if (viagemEvento.IdProtocolo.HasValue)
                PesoChegadaTriagem = _protocoloEventoRepository.Find(y => y.IdViagemEvento == viagemEvento.IdViagemEvento).OrderByDescending(y => y.IdProtocoloEvento).FirstOrDefault().PesoChegada;

            var viagem = _viagemRepository.Find(x => x.ViagemEventos.Any(y => y.IdViagem == viagemEvento.IdViagem))
                .Select(x => new ConsultarPorTokenModel
                {
                    IdViagem = x.IdViagem,
                    StatusViagem = x.StatusViagem,
                    IdEmpresa = x.IdEmpresa,
                    CPFMotorista = x.CPFMotorista,
                    PesoChegada = x.PesoChegada,
                    PesoSaida = x.PesoSaida,
                    RazaoSocialFilial = x.RazaoSocialFilial,
                    NomeMotorista = x.NomeMotorista,
                    CNHMotorista= x.CNHMotorista,
                    NomeProprietario = x.NomeProprietario,
                    CPFCNPJProprietario = x.CPFCNPJProprietario,
                    Placa = x.Placa,
                    Coleta = x.Coleta,
                    Entrega = x.Entrega,
                    Origem= x.Origem,
                    NumeroDocumento= x.NumeroDocumento,
                    DataEmissao = x.DataEmissao,
                    ValorMercadoria = x.ValorMercadoria,
                    Produto = x.Produto,
                    Quantidade = x.Quantidade,
                    Unidade = x.Unidade,
                    INSS = x.INSS,
                    SESTSENAT = x.SESTSENAT,
                    IRRPF = x.IRRPF,
                    NumeroCartao = x.NumeroCartao,
                    ValorPedagio = x.ValorPedagio,
                    PedagioBaixado = x.PedagioBaixado,
                    Carretas = x.ViagemCarretas.Select(y => y.Placa).ToList(),
                    ViagemRegras = x.ViagemRegras.Select(y => new ConsultaPorTokenViagemRegrasModel
                    {
                        TipoQuebraMercadoria = y.TipoQuebraMercadoria,
                        ToleranciaPeso = y.ToleranciaPeso,
                        TarifaTonelada = y.TarifaTonelada,
                        TotalFrete = y.TotalFrete,
                        FreteLotacao = y.FreteLotacao,
                        UnidadeMedida = y.UnidadeMedida
                    }),
                    Proprietario = new ConsultaPortokenProprietarioModel
                    {
                        IdProprietario = x.Proprietario.IdProprietario,
                        NomeFantasia = x.Proprietario.NomeFantasia,
                        CNPJCPF = x.Proprietario.CNPJCPF
                    },
                    ValidaChaveBaixaEvento = x.Empresa.ValidaChaveBaixaEvento,
                    ValidaChaveMHBaixaEvento = x.Empresa.ValidaChaveMHBaixaEvento,
                    TempoValidadeChave = x.Empresa.TempoValidadeChave,
                    NomeFantasiaEmpresa = x.Empresa.NomeFantasia,
                    Remetente = x.ClienteOrigem.NomeFantasia,
                    Destinatario = x.ClienteDestino.NomeFantasia,
                    Tomador = x.ClienteTomador.NomeFantasia,
                    PagamentoFreteToleranciaPesoChegadaMais = x.Empresa.PagamentoFreteToleranciaPesoChegadaMais,
                    PagamentoFreteToleranciaPesoChegadaMenos = x.Empresa.PagamentoFreteToleranciaPesoChegadaMenos,
                    TokenMicroServices = x.Empresa.TokenMicroServices,
                    PesoChegadaTriagem = PesoChegadaTriagem,
                    DataDescarga = x.DataDescarga,
                    ViagemEstabelecimentos = x.ViagemEstabelecimentos.Select(y => new ConsultaPortokenViagemEstabelecimentoModel
                    {
                        IdEstabelecimento = y.IdEstabelecimento,
                        TipoEventoViagem = y.TipoEventoViagem
                    })
                })
                .FirstOrDefault();

            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            if (viagem.StatusViagem == EStatusViagem.Cancelada)
                throw new Exception($"A viagem está cancelada");

            if (viagemEvento.Status == EStatusViagemEvento.Cancelado)
                throw new Exception($"O evento desta viagem está cancelado");

            var declaracaoCiot = _declaracaoCiotRepository
                .Where(o => o.IdViagem == viagem.IdViagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .FirstOrDefault();

            var numeroCiot = declaracaoCiot != null ? $"{declaracaoCiot.Ciot}/{declaracaoCiot.Verificador}" : string.Empty;

            var obrigaDocumentosPagamento = true;
            if (idsEstabelecimentosBaseUsuario?.Any() == true || idEstabelecimento.HasValue)
            {
                IQueryable<Credenciamento> estabelecimentosCredenciados;
                if (idsEstabelecimentosBaseUsuario?.Any() == true)
                    estabelecimentosCredenciados = _credenciamentoRepository
                        .Where(t => t.IdEstabelecimentoBase != null &&
                                    idsEstabelecimentosBaseUsuario.Contains(t.IdEstabelecimentoBase.Value));
                else
                    estabelecimentosCredenciados = _credenciamentoRepository
                        .Where(t => t.IdEstabelecimento == idEstabelecimento.Value);

                estabelecimentosCredenciados = estabelecimentosCredenciados
                    .Where(t => t.IdEmpresa == viagem.IdEmpresa && t.Status == EStatusCredenciamento.Aprovado
                                && (t.Estabelecimento != null && t.Estabelecimento.Ativo))
                                                         .Include(x => x.Estabelecimento);

                if (estabelecimentosCredenciados == null || !estabelecimentosCredenciados.Any())
                    throw new Exception("Estabelecimento não autorizado para esse pagamento!");

                obrigaDocumentosPagamento = estabelecimentosCredenciados.Any(x => x.Estabelecimento.ObrigaDocumentosPagamento);

                if (viagem.ViagemEstabelecimentos?.Any() == true)
                {
                    var idsEstabelecimentosLiberados = viagem.ViagemEstabelecimentos.Where(x => x.TipoEventoViagem == viagemEvento.TipoEventoViagem).Select(x => x.IdEstabelecimento).ToList();
                    if(idsEstabelecimentosLiberados.Any() && !estabelecimentosCredenciados.Any(x => idsEstabelecimentosLiberados.Contains(x.IdEstabelecimento)))
                        throw new Exception("Estabelecimento não autorizado para esse pagamento!");
                }
            }

            //TODO: VERIFICAR ESTE CARA
            var motorista = _motoristaService.GetPorCpf(viagem.CPFMotorista);

            var viagemDomain = _viagemRepository.First(v => v.IdViagem == viagemEvento.IdViagem &&
                                                            v.IdEmpresa == viagemEvento.IdEmpresa);

            var eventosViagem = _viagemEventoRepository.Find(x => x.IdViagem == viagemEvento.IdViagem).ToList();
            var adiantamento = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.Adiantamento && x.Status != EStatusViagemEvento.Cancelado);
            var tarifaAntt = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt && x.Status != EStatusViagemEvento.Cancelado);
            var abastecimento = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.Abastecimento && x.Status != EStatusViagemEvento.Cancelado);
            var saldo = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.Saldo && x.Status != EStatusViagemEvento.Cancelado);
            var estadia = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.Estadia && x.Status != EStatusViagemEvento.Cancelado);
            var RPA = eventosViagem.FirstOrDefault(x => x.TipoEventoViagem == ETipoEventoViagem.RPA && x.Status != EStatusViagemEvento.Cancelado);

            var totalPagar = eventosViagem.Where(x => x.Status != EStatusViagemEvento.Cancelado && x.Status != EStatusViagemEvento.Baixado).Sum(x => x.ValorPagamento);
            var totalPago = eventosViagem.Where(x => x.Status == EStatusViagemEvento.Baixado).Sum(x => x.ValorTotalPagamento);

            if (adiantamento?.Status == EStatusViagemEvento.Baixado &&
                DeveIncluirPedagioJuntoComPagamentoDoEvento(adiantamento, viagemDomain))
                totalPago += viagemDomain.ValorPedagio;

            var diferenca = viagem.ViagemRegras?.FirstOrDefault()?.TipoQuebraMercadoria == ETipoQuebraMercadoria.Diferenca ?
                    ((viagem.PesoChegada - viagem.PesoSaida) - viagem.ViagemRegras?.FirstOrDefault()?.ToleranciaPeso) ?? 0 :
                    (viagem.PesoChegada - viagem.PesoSaida) ?? 0;

            var filial = viagem.RazaoSocialFilial;
            var HasProtocolo = viagemEvento.IdProtocolo != null && _protocoloEventoRepository
                .Any(x => x.IdProtocolo == viagemEvento.IdProtocolo && x.Status != EStatusProtocoloEvento.Rejeitado);

            //Evento já calculado
            var evento = ConsultarEvento(token);

            var valorEvento = evento.ValorTotalPagamento;
            if (!evento.ValorTotalPagamentoBaixado)
            {
                valorEvento = evento.ValorPagamento;
            //Aqui adicionamos os valores de outros descontos levando em consideração para exibição
            if (viagemEvento.ViagemValoresAdicionais != null && viagemEvento.ViagemValoresAdicionais.Any())
            {
                foreach (var valorAdicional in viagemEvento.ViagemValoresAdicionais)
                {
                    switch (valorAdicional.Tipo)
                    {
                        case ETipoValorAdicional.Desconto:
                            valorEvento -= valorAdicional.Valor;
                            break;
                        case ETipoValorAdicional.Acrescimo:
                            valorEvento += valorAdicional.Valor;
                            break;
                    }
                }
            }
            }

            string qrCodeBase64 = "data:image/png;base64,";
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var imageStr = _layoutRepository
                .Find(x => x.IdEmpresa == viagem.IdEmpresa)
                .Select(x => x.Image)
                .FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(imageStr))
            {

                Bitmap image = null;
                if (new FileInfo(caminhoAplicacao + @"\Content\Image\" + imageStr).Exists)
                    image = (Bitmap)Image.FromFile(caminhoAplicacao + @"\Content\Image\" + imageStr);
                qrCodeBase64 += new QRCodeHelper().GerarQRCode(image, token);
            }
            else
            {
                if (new FileInfo(caminhoAplicacao + @"\Content\Image\logo-ats-login.png").Exists)
                {
                    var image = (Bitmap)Image.FromFile(caminhoAplicacao + @"\Content\Image\logo-ats-login.png");
                    qrCodeBase64 += new QRCodeHelper().GerarQRCode(image, token);
                }
                else
                    qrCodeBase64 += new QRCodeHelper().GerarQRCode(null, token);
            }

            var idUsuarioLogado = _usuarioRepository.GetIdPorCNPJCPF(cpfCnpjUsuario);

            var outroDesconto = viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Desconto).Sum(x => x.Valor);

            var pagamentoFreteModel = new PagamentoFreteModel
            {
                IdViagem = viagem.IdViagem,
                Token = token,
                Status = EnumHelpers.GetDescription(viagemEvento.Status),
                EmpresaValidaChaveBaixaEvento = viagem.ValidaChaveBaixaEvento,
                EmpresaValidaChaveMHBaixaEvento = viagem.ValidaChaveBaixaEvento,
                TempoValidadeChave = viagem.TempoValidadeChave,
                QRCodeEvento = qrCodeBase64,
                NomeFantasiaEmpresa = viagem.NomeFantasiaEmpresa,
                Filial = filial,
                AbonoDocument = viagemEvento.TokenAnexoAbono,
                NomeMotorista = !string.IsNullOrWhiteSpace(motorista?.Nome) ? motorista?.Nome : viagem.NomeMotorista,
                CNH = viagem.CNHMotorista,
                ValidadeCNH = motorista != null ? motorista.ValidadeCNH?.ToString("dd/MM/yyyy") : null,
                Celular = motorista?.Celular,
                RG = motorista?.RG,
                TipoEventoViagem = viagemEvento.TipoEventoViagem,
                CPFMotorista = StringHelper.ToCPFFormato(viagem.CPFMotorista),
                IdProprietario = viagem.Proprietario?.IdProprietario,
                NomeProprietario = viagem.Proprietario == null ? viagem.NomeProprietario : viagem.Proprietario?.NomeFantasia,
                CPFCNPJ = viagem.Proprietario == null ? StringHelper.FormatarCpfCnpj(viagem.CPFCNPJProprietario) : StringHelper.FormatarCpfCnpj(viagem.Proprietario?.CNPJCPF),
                PlacaVeiculo = viagem.Placa,
                PesoSaida = viagem.PesoSaida ?? 0,
                Remetente = viagem.Remetente,
                Destinatario = viagem.Destinatario,
                Tomador = viagem.Tomador,
                TipoQuebraMercadoria = EnumHelpers.GetDescription(viagem.ViagemRegras.FirstOrDefault().TipoQuebraMercadoria),
                Coleta = viagem.Coleta,
                Entrega = viagem.Entrega,
                Origem = viagem.Origem,
                AbonoRequisitado = viagemEvento.QuebraMercadoriaAbonada ?? false,
                ValorMercadoria = decimal.Round(viagem.ValorMercadoria ?? 0, 2),
                TarifaTonelada = viagem.ViagemRegras?.FirstOrDefault()?.TarifaTonelada,
                FreteLotacao = viagem.ViagemRegras?.FirstOrDefault()?.FreteLotacao ?? false,
                NumeroCTE = viagem.NumeroDocumento,
                DataEmissao = viagem.DataEmissao?.ToString("G"),
                Produto = viagem.Produto,
                Unidade = EnumHelpers.GetDescription<EUnidadeMedida>(viagem.Unidade),
                UnidadeMedida = !string.IsNullOrEmpty(viagem.ViagemRegras.FirstOrDefault().UnidadeMedida) ? viagem.ViagemRegras.FirstOrDefault().UnidadeMedida : "KG",
                Quantidade = viagem.Quantidade,
                QuantidadeSacas = GetNumeroDeSacas(evento?.PesoChegada ?? 0, viagem.PesoSaida ?? 0, viagem.Quantidade),
                TotalFrete = viagem.ViagemRegras?.FirstOrDefault()?.TotalFrete ?? 0,
                FreteUnitario = viagem.ViagemRegras?.FirstOrDefault()?.TarifaTonelada,
                ValorINSS = viagem.INSS,
                ValorSaca = viagem.Quantidade > 0 ? (viagem.ValorMercadoria / viagem.Quantidade) : 0,
                ValorSESTSENAT = viagem.SESTSENAT,
                ValorIRRF = viagem.IRRPF,
                ValorPedagio = viagem?.ValorPedagio,
                PedagioBaixado = viagem?.PedagioBaixado ?? false,
                Diferenca = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo ? diferenca : 0,
                DataPagamentoAdiantamento = adiantamento?.DataHoraPagamento?.ToString("G"),
                Adiantamento = (adiantamento?.ValorTotalPagamento ?? adiantamento?.ValorPagamento ?? 0) +
                               (string.IsNullOrWhiteSpace(viagem?.NumeroCartao) ? viagem?.ValorPedagio : 0),
                StatusAdiantamento = adiantamento?.Status,
                StatusTarifa = tarifaAntt?.Status,
                ValorTarifaANTT = tarifaAntt?.ValorTotalPagamento ?? tarifaAntt?.ValorPagamento ?? 0,
                Abastecimento = abastecimento?.ValorTotalPagamento ?? abastecimento?.ValorPagamento ?? 0,
                DataPagamentoAbastecimento = abastecimento?.DataHoraPagamento?.ToString("G"),
                StatusAbastecimento = abastecimento?.Status,
                Saldo = saldo?.ValorTotalPagamento ?? 0,
                DataPagamentoSaldo = saldo?.DataHoraPagamento?.ToString("G"),
                StatusSaldo = saldo?.Status,
                Estadia = estadia?.ValorTotalPagamento ?? estadia?.ValorPagamento ?? 0,
                DataPagamentoEstadia = estadia?.DataHoraPagamento?.ToString("G"),
                StatusEstadia = estadia?.Status,
                TotalPago = totalPago,
                TotalPagar = totalPagar,
                Instrucao = viagemEvento.Instrucao,
                ValorEvento = valorEvento,
                ValorPedagioParaIncluirPagamento = evento.ValorPedagioParaIncluirPagamento,
                Carretas = viagem.Carretas,
                AdiantamentoSemPedagio = adiantamento?.ValorTotalPagamento ?? adiantamento?.ValorPagamento,
                HasRPA = RPA != null,
                HasProtocolo = HasProtocolo,
                RPA = new PagamentoFreteEventoModel
                {
                    ValorINSS = RPA?.INSS,
                    ValorSESTSENAT = RPA?.SESTSENAT,
                    ValorIRRF = RPA?.IRRPF,
                    ValorBruto = RPA?.ValorBruto,
                    ValorLiquido = RPA?.ValorTotalPagamento ?? RPA?.ValorPagamento,
                },
                LiberarPagtoSemChave = viagemEvento.LiberarPagtoSemChave,
                StatusRPA = RPA?.Status,
                DataPagamentoRPA = RPA?.DataHoraPagamento?.ToString("G"),
                Evento = evento,
                Anexos = ConsultarAnexos(token, obrigaDocumentosPagamento, idUsuarioLogado),
                TipoEvento = new PagamentoFreteTipoEventoModel
                {
                    Descricao = EnumHelpers.GetDescription(viagemEvento.TipoEventoViagem),
                    Valor = viagemEvento.TipoEventoViagem
                },
                PesoChegada = viagem.PesoChegada ?? 0,
                NumeroCartao = viagem.NumeroCartao,
                HabilitarPagamentoCartao = viagemEvento.HabilitarPagamentoCartao,
                StatusViagemEvento = viagemEvento.Status,
                ParamsEmpPagFrete = new
                {
                    viagem.PagamentoFreteToleranciaPesoChegadaMais,
                    viagem.PagamentoFreteToleranciaPesoChegadaMenos
                },
                DataHoraPagamnto = viagemEvento.DataHoraPagamento?.ToString("dd/MM/yyyy HH:mm"),
                EstabelecimentoPagamento = viagemEvento.EstabelecimentoBase?.Descricao,
                NumeroCiot = numeroCiot,
                PesoChegadaTriagem = viagem.PesoChegadaTriagem,
                DataDescarga = viagem.DataDescarga,
                DataDescargaStr = viagem.DataDescarga?.ToShortDateString()
            };

            if (pagamentoFreteModel.HabilitarPagamentoCartao)
            {
                var cartoesService = new CartoesService(_cartoesServiceArgs, viagem.IdEmpresa, viagem.TokenMicroServices, cpfCnpjUsuario, nomeUsuario);

                var produtoId = new List<int> { cartoesService.GetIdProdutoCartaoFretePadrao() };

                var cpfMotorista = motorista != null ? motorista.CPF.OnlyNumbers() : viagem.CPFMotorista;
                var documentoProprietario = viagem.CPFCNPJProprietario.OnlyNumbers();

                var cartoes = cartoesService.GetCartoesVinculados(cpfMotorista, produtoId, cpfMotorista != documentoProprietario)?.Cartoes?.LastOrDefault();

                pagamentoFreteModel.FormaPagamentoDescricao = string.IsNullOrEmpty(cartoes?.Produto?.Nome) ? string.Empty : "Cartão - " + cartoes?.Produto?.Nome;
            }
            else
            {
                pagamentoFreteModel.FormaPagamentoDescricao = "Outros";
            }

            return pagamentoFreteModel;
        }

        public CheckTokenModel CheckToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagemId = _viagemEventoRepository.Find(x => x.Token == token).Select(c => c.IdViagem).FirstOrDefault();

            if (viagemId == 0)
                throw new Exception("Token do documento não encontrado!");

            var viagemPendente = _viagemPendenteGestorRepository.GetAll().FirstOrDefault(c => c.IdViagem == viagemId && c.Status != (int) EBloqueioGestorStatus.Liberado);

            if (viagemPendente != null)
            {
                switch (viagemPendente.Status)
                {
                    case (int) EBloqueioGestorStatus.Bloqueado:
                        throw new Exception("Viagem bloqueada pelo gestor!");

                    case (int) EBloqueioGestorStatus.Pendente:
                        throw new Exception("Viagem pendente de confirmação com o gestor!");
                    default:
                        throw new ArgumentException($"{nameof(ViagemPendenteGestor)} - {nameof(ViagemPendenteGestor.Status)}");
                }
            }

            var viagemEvento = _viagemEventoRepository.Find(x => x.Token == token)
                .Select(x => new CheckTokenModel
                {
                    IdEmpresa = x.Viagem.Empresa.IdEmpresa,
                    PagamentoViaMeioHomologado = x.HabilitarPagamentoCartao,
                    StatusViagemEvento = x.Status,
                    LiberarPagtoSemChave = x.LiberarPagtoSemChave,
                    EmpresaValidaChaveMHBaixaEvento = x.Viagem.Empresa.ValidaChaveMHBaixaEvento,
                    TempoValidadeChave = x.Viagem.Empresa.TempoValidadeChave,
                })
                .FirstOrDefault();

            return viagemEvento;
        }

        /*public string EnviarSMSValidacao(string token, string cpf, string cnpj, string celular, bool isPessoaJuridica)
        {
            var evento = ConsultarViagemEvento(token);
            if (evento == null)
                throw new Exception($"Nenhum evento encontrado para o token {token}!");

            var celular_ = string.Empty;

            if (!isPessoaJuridica)
            {
                if (evento.Viagem?.CPFMotorista != cpf)
                {
                    if (evento.Viagem?.CPFCNPJProprietario != cpf)
                    {
                        var preUsuario = _preUsuarioRepository.Include(o => o.Contatos).FirstOrDefault(o => o.CPF == cpf);

                        if (preUsuario != null && preUsuario.Perfil != EPerfil.Proprietario)
                            throw new Exception($"CPF/CNPJ informado diverge do cadastro da viagem!");

                            var motorista = _motoristaBaseRepository.FirstOrDefault(o => o.CPF == evento.Viagem.CPFMotorista);

                            if (motorista != null)
                            {
                            var conjunto = _conjuntoRepository.FirstOrDefault(o => o.IdMotoristaBase == motorista.IdMotoristaBase &&o.PlacaCavalo == evento.Viagem.Placa);

                                if (conjunto != null )
                                {
                                var veiculo = _veiculoRepository.Include(o => o.Proprietario).FirstOrDefault(o => o.Placa == conjunto.PlacaCavalo);

                                if (preUsuario?.CPF != veiculo?.Proprietario.CNPJCPF)
                                    {
                                    var listaCnpjs = _preUsuarioCnpjRepository.Where(o => o.IdUsuario == preUsuario.IdUsuario);

                                    if (!listaCnpjs.Any(o => o.CNPJ == veiculo.Proprietario.CNPJCPF))
                                        throw new Exception($"CPF/CNPJ informado diverge do cadastro da viagem!");
                                    else
                                        celular_ = _proprietarioRepository.Include(x => x.Contatos).FirstOrDefault(x => x.CNPJCPF == evento.Viagem.CPFCNPJProprietario && x.Contatos.Any(y => y.Celular == celular))?.Contatos.FirstOrDefault()?.Celular;
                                    }
                                else
                                {
                                    celular_ = _proprietarioRepository.Include(x => x.Contatos).FirstOrDefault(x => x.CNPJCPF == veiculo.Proprietario.CNPJCPF && x.Contatos.Any(y => y.Celular == celular))?.Contatos.FirstOrDefault()?.Celular;
                                }
                            }
                                else
                                throw new Exception($"CPF/CNPJ informado diverge do cadastro da viagem!");
                            }
                            else
                            throw new Exception($"CPF/CNPJ informado diverge do cadastro da viagem!");
                        }
                    else
                        celular_ = _proprietarioRepository.Include(x => x.Contatos).FirstOrDefault(x => x.CNPJCPF == cpf && x.Contatos.Any(y => y.Celular == celular))?.Contatos.FirstOrDefault()?.Celular;
                    }
                else
                    celular_ = _motRep.FirstOrDefault(x => x.CPF == cpf && x.Celular == celular)?.Celular;
            }
            else
                if (evento.Viagem?.CPFCNPJProprietario != cnpj)
                    throw new Exception($"CPF/CNPJ informado diverge do cadastro da viagem!");
                else
                    celular_ = _proprietarioRepository.Include(x => x.Contatos).FirstOrDefault(x => x.CNPJCPF == cnpj && x.Contatos.Any(y => y.Celular == celular))?.Contatos.FirstOrDefault()?.Celular;

            // Here we are going to get the cellphone number  no matter if he is a driver or a owner we need the cellphone number
            //string celular_ = !isPessoaJuridica ?  _motRep.FirstOrDefault(x => x.CPF == cpf && x.Celular == celular)?.Celular :
            //    _proprietarioRepository.Include(x => x.Contatos).FirstOrDefault(x => x.CNPJCPF == cnpj && x.Contatos.Any( y => y.Celular == celular))?.Contatos.FirstOrDefault()?.Celular;

            string doc_ = !isPessoaJuridica ? cpf.ToCPFFormato() : cnpj.ToCNPJFormato();

            if (string.IsNullOrEmpty(celular_))
                throw new Exception($"Nenhum motorista ou proprietário encontrado para o documento {doc_} e celular {celular}!");

            evento.ChaveToken = new Random().Next(10001, 999999).ToString();

            evento.DataValidadeChaveToken = DateTime.Now.AddMinutes((double)_empresaService.Get(evento.IdEmpresa)?.TempoValidadeChave);

            _viagemEventoRepository.Update(evento);

            var viagem = _viagemRepository.FirstOrDefault(o => o.IdViagem == evento.IdViagem);
            var ciot = _declaracaoCiotRepository
                .Where(o => o.IdViagem == viagem.IdViagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .FirstOrDefault();

            new SMService().EnviarSMS(celular_, $"ATS informa: Viagem de {viagem?.Coleta} para {viagem?.Entrega} CIOT: {ciot?.Ciot}, Token de Pagto: {evento.ChaveToken}.");

            return evento.ChaveToken;
        }*/

        public void AtualizarPagamentoSemChave(bool liberarPagamento, string token, int? estabId, string observacao, int idUsuarioLibSemChave)
        {
            var evento = ConsultarViagemEvento(token);
            if (evento == null)
                throw new Exception($"Nenhum evento encontrado para o token {token}!");

            if (string.IsNullOrEmpty(observacao))
                throw new Exception("É obrigatório informar uma observação!");

            evento.IdUsuarioLibSemChave = idUsuarioLibSemChave;
            evento.DataLibSemChave = DateTime.Now;
            evento.LiberarPagtoSemChave = liberarPagamento;
            evento.ObsLibSemChave = observacao;

            if (estabId > 0)
            {
                var estab = _estabelecimentoService.Get(estabId.Value, false);
                if (estab == null)
                    throw new Exception("O estabelecimento informado é inválido ou inexistente!");

                evento.IdEstabelecimentoBase = estab.IdEstabelecimentoBase;
            }
            else throw new Exception("Estabelecimento é obrigatório!");

            _viagemEventoRepository.Update(evento);
        }

        /// <summary>
        /// Realiza a consulta de um evento da viagem por seu token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public ViagemEvento ConsultarViagemEvento(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagemEvento = _viagemEventoRepository.Include(o => o.Viagem).FirstOrDefault(x => x.Token == token);

            return viagemEvento;
        }


        public bool hasCredentials(PagamentoFreteModel pagamentoFreteModel, List<int> idsEstabelecimentosUsuario, Usuario usuarioLogado)
        {
            var pagamentoFrete = _viagemEventoService.GetByToken(pagamentoFreteModel.Token);

            if (pagamentoFrete != null)
            {
                var empresa_ = _empresaRepository.Get(pagamentoFrete.IdEmpresa);

                if (empresa_ == null)
                    return true;
            }

            return true;
        }

        public ValidationResult EfetuarBaixaEvento(PagamentoFreteModel pagamentoFreteModel,
            List<int> idsEstabelecimentosUsuario, bool integracaoAts, Usuario usuarioLogado, Viagem viagem)

            {
            var validationResult = new ValidationResult();

            validationResult.Add(ValidarEfetuarPagamento(pagamentoFreteModel, idsEstabelecimentosUsuario, integracaoAts, usuarioLogado, viagem, out var idEstabelecimentoBase));

            if (!validationResult.IsValid)
                return validationResult;

            var viagemEvento = viagem.ViagemEventos.First(x => x.Token == pagamentoFreteModel.Token && x.Status != EStatusViagemEvento.Cancelado);

            try
            {

                if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo)
                {
                    if (pagamentoFreteModel.Evento.PesoChegada <= 0 && viagem.Unidade == EUnidadeMedida.Peso)
                        validationResult.Add("Peso de chegada deve ser informado.");
                    if (viagem.Unidade == EUnidadeMedida.Saca && pagamentoFreteModel.Evento.PesoChegada <= 0)
                        validationResult.Add("Quantidade de sacas entregues deve ser informada.");

                    viagem.PesoChegada = pagamentoFreteModel.Evento.PesoChegada;
                    viagem.PesoDiferenca = viagem.PesoSaida - pagamentoFreteModel.Evento.PesoChegada;
                    viagem.DifFreteMotorista = pagamentoFreteModel.Evento.DifFreteMotorista;
                    viagem.ValorQuebraMercadoria = pagamentoFreteModel.Evento.QuebraMercadoria;
                    viagem.ValorQuebraMercadoriaCalculado = pagamentoFreteModel.Evento.QuebraMercadoriaCalculada;

                    if (pagamentoFreteModel.DataDescarga.HasValue && viagem.DataEmissao.HasValue &&
                        pagamentoFreteModel.DataDescarga.Value.EndOfDay() < viagem.DataEmissao.Value.Date)
                        validationResult.Add("A data de descarga não pode ser menor que a data de emissão da viagem.");

                    if (pagamentoFreteModel.DataDescarga.HasValue &&
                        pagamentoFreteModel.DataDescarga.Value > DateTime.Now.EndOfDay())
                        validationResult.Add("A data de descarga não pode ser maior que o dia atual.");

                    viagem.DataDescarga = pagamentoFreteModel.DataDescarga;

                    if (!validationResult.IsValid)
                        return validationResult;
                }

                viagemEvento.QuebraMercadoriaAbonada = pagamentoFreteModel.AbonoRequisitado;
                viagemEvento.TokenAnexoAbono = pagamentoFreteModel.AbonoDocument;
                viagemEvento.IdEstabelecimentoBase = usuarioLogado?.Perfil == EPerfil.Empresa
                    ? idEstabelecimentoBase
                    : idsEstabelecimentosUsuario.FirstOrDefault();
                if (viagemEvento.IdEstabelecimentoBase == 0)
                    viagemEvento.IdEstabelecimentoBase = null;

                viagemEvento.DataHoraPagamento = DateTime.Now;
                viagemEvento.ValorTotalPagamento = pagamentoFreteModel.ValorEvento;
                viagemEvento.ValorTotalPagamentoCalculado = pagamentoFreteModel.Evento.ValorTotalPagamentoCalculado;
                viagemEvento.Status = EStatusViagemEvento.Baixado;
                viagemEvento.IdUsuarioBaixaEvento = usuarioLogado?.IdUsuario;

                var alterandoPagamentoCartaoParaNormal = viagemEvento.HabilitarPagamentoCartao &&
                                                         !pagamentoFreteModel.HabilitarPagamentoCartao;

                var alterandoPagamentoNormalParaCartao = !viagemEvento.HabilitarPagamentoCartao &&
                                                         pagamentoFreteModel.HabilitarPagamentoCartao;
                var modificarPedagio = alterandoPagamentoNormalParaCartao &&
                                       DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem);

                viagemEvento.HabilitarPagamentoCartao = pagamentoFreteModel.HabilitarPagamentoCartao;

                if (integracaoAts)
                    viagemEvento.OrigemPagamento = EOrigemIntegracao.ATS;
                else
                    viagemEvento.OrigemPagamento = EOrigemIntegracao.TMS;

                if (string.IsNullOrWhiteSpace(viagemEvento.NumeroRecibo))
                    viagemEvento.NumeroRecibo = DateTime.UtcNow.TimeOfDay.TotalMilliseconds
                        .ToString(CultureInfo.InvariantCulture).Replace(".", "");

                var manterViagemAbertaAposBaixa = _parametrosEmpresaService.GetMantemViagemAbertaAposBaixaDoUltimoEvento(viagem.IdEmpresa);
                if (viagem.ViagemEventos.All(t => t.Status != EStatusViagemEvento.Aberto) && !manterViagemAbertaAposBaixa)
                    viagem.StatusViagem = EStatusViagem.Baixada;

                // Se estiver modificando de pagamento normal para pagamento em cartão,
                // deve adaptar os valores da integração de pedágio para respeitar fluxo normal que se segue após pagamento do evento para geraçao de protocolo e triagem
                if (modificarPedagio)
                {
                    viagem.PedagioBaixado = true;
                    viagemEvento.ValorPagamento += viagem.ValorPedagio;
                    viagemEvento.ValorTotalPagamento += viagem.ValorPedagio;
                }

                if (alterandoPagamentoNormalParaCartao)
                    viagemEvento.ModificouFormaPagamentoCartaFreteParaCartao = true;

                if (alterandoPagamentoCartaoParaNormal)
                    viagemEvento.ModificouFormaPagamentoCartaoParaCartaFrete = true;

                var viagemEventoService = _viagemEventoService;
                _viagemEventoRepository.Update(viagemEvento);

                if (viagemEvento.HabilitarPagamentoCartao && (viagemEvento.QuebraMercadoriaAbonada ?? false) && pagamentoFreteModel.ValorQuebra != null)
                    viagemEventoService.CreateAbonoEvent(pagamentoFreteModel.ValorQuebra.Value, viagemEvento);

                viagem.DataAtualizacao = DateTime.Now;
                _viagemEventoRepository.Update(viagemEvento);
                _viagemRepository.Update(viagem);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao efetuar pagamento. IdVIagem: " + pagamentoFreteModel.IdViagem);

                if (e.ToString().Contains("deadlock"))
                    validationResult.Add($"O pagamento deste evento está já em processamento. Consulte o token novamente para obter o resultado do processamento.</br>Se o erro persistir, contate o administrador do sistema", EFaultType.Error, "9000");
                else
                    validationResult.Add(e.GetBaseException().Message);
            }

            return validationResult;
        }

        private ValidationResult ValidarEfetuarPagamento(PagamentoFreteModel pagamentoFreteModel,
            List<int> idsEstabelecimentosUsuario, bool integracaoAts, Usuario usuarioLogado, Viagem viagem, out int idEstabelecimentoBase)
        {
            var validationResult = new ValidationResult();
            idEstabelecimentoBase = 0;

            try
            {
                Credenciamento credenciamento = null;
                idEstabelecimentoBase = 0;

                if (pagamentoFreteModel.PesoChegada < 0)
                    return validationResult.Add("Não é possível informar valor negativo para o peso de chegada.");

                if (string.IsNullOrWhiteSpace(pagamentoFreteModel.Token))
                    return validationResult.Add("Token do documento não encontrado!");

                var viagemEvento = viagem.ViagemEventos.FirstOrDefault(x => x.Token == pagamentoFreteModel.Token && x.Status != EStatusViagemEvento.Cancelado);

                if (viagemEvento == null)
                    return validationResult.Add("Evento não encontrado para esta viagem!");
                if (viagemEvento.Status == EStatusViagemEvento.Baixado)
                    return validationResult.Add("O pagamento do evento informado já foi efetuado!");
                if (viagemEvento.Status == EStatusViagemEvento.Bloqueado)
                    return validationResult.Add("O evento informado está bloqueado!");
                if (viagemEvento.DataValidade.HasValue && viagemEvento.DataValidade.Value < DateTime.Now)
                    return validationResult.Add("O evento informado já passou da data de validade.");

                if (viagemEvento.DataValidade.HasValue && viagemEvento.DataValidade.Value < DateTime.Now)
                    return validationResult.Add("O evento informado já passou da data de validade.");

                if (viagem.Empresa == null)
                    return validationResult.Add("Empresa não encontrada para esta viagem!");

                if (pagamentoFreteModel.TipoEvento.Valor != ETipoEventoViagem.Saldo)
                {
                    if (pagamentoFreteModel.ValorEvento < 0)
                        return validationResult.Add("Não é possivel efetuar o pagamento de um valor negativo!");
                }
                else
                {
                    if (pagamentoFreteModel.ValorEvento <= 0)
                        pagamentoFreteModel.ValorEvento = 0;
                }

                if (integracaoAts)
                    hasCredentials(pagamentoFreteModel, idsEstabelecimentosUsuario, usuarioLogado);

                if (integracaoAts && usuarioLogado != null && usuarioLogado.Perfil == EPerfil.Empresa)
                {
                    if (viagemEvento.IdEmpresa != usuarioLogado.IdEmpresa)
                        return validationResult.Add("Token não encontrado.");

                    if (pagamentoFreteModel.IdEstabelecimento.HasValue)
                        credenciamento = _credenciamentoService.GetPorEmpresa(usuarioLogado.IdEmpresa)
                            .FirstOrDefault(o => o.IdEstabelecimento == pagamentoFreteModel.IdEstabelecimento.Value);

                    if(credenciamento?.IdEstabelecimentoBase != null)
                        idEstabelecimentoBase = credenciamento.IdEstabelecimentoBase.Value;

                    if (idEstabelecimentoBase == 0)
                        return validationResult.Add("Estabelecimento não autorizado para esse pagamento.");

                    if (!idsEstabelecimentosUsuario.Any())
                        idsEstabelecimentosUsuario.Add(idEstabelecimentoBase);
                }

                if (integracaoAts && (idsEstabelecimentosUsuario == null || !idsEstabelecimentosUsuario.Any()))
                    return validationResult.Add("Estabelecimento não encontrado para este usuário!");

                var estabelecimentosCredenciados =
                    _credenciamentoRepository.Where(t => t.IdEstabelecimentoBase != null
                                                         && idsEstabelecimentosUsuario.Contains(t.IdEstabelecimentoBase.Value)
                                                         && t.IdEmpresa == viagem.IdEmpresa
                                                         && t.Status == EStatusCredenciamento.Aprovado)
                        .Include(x => x.Estabelecimento)
                        .ToList();

                if (integracaoAts && !estabelecimentosCredenciados.Any())
                    return validationResult.Add("Estabelecimento não autorizado para esse pagamento!");

                var idsEstabelecimentosEmpresaUsuario = estabelecimentosCredenciados
                    .Select(x => x.IdEstabelecimento)
                    .ToList();

                if (viagem.ViagemEstabelecimentos?.Any() == true)
                {
                    if (!viagem.ViagemEstabelecimentos.Any(a => idsEstabelecimentosEmpresaUsuario.Contains(a.IdEstabelecimento) && a.TipoEventoViagem == viagemEvento.TipoEventoViagem)
                     && viagem.ViagemEstabelecimentos.Any(x => x.TipoEventoViagem == viagemEvento.TipoEventoViagem))
                        return validationResult.Add("Estabelecimento não autorizado para esse pagamento!");

                    if (integracaoAts)
                    {
                        var idsViagemEstabelecimento = viagem.ViagemEstabelecimentos.Select(x => x.IdEstabelecimento).ToList();
                        var estabelecimentoPagamentoViagem = estabelecimentosCredenciados
                            .Where(x => idsViagemEstabelecimento.Any(y => y == x.IdEstabelecimento))
                            .Select(x => x.Estabelecimento)
                            .ToList();

                        var validacaoAnexos = EventoDocumentosObrigatoriosAnexados(viagemEvento, viagem, estabelecimentoPagamentoViagem);
                        if (validacaoAnexos)
                            validationResult.Add("Existem documentos obrigatórios que não foram anexados! Revise a grade de anexos e efetue o pagamento novamente");
                    }
                }

                if (viagemEvento.TipoEventoViagem != ETipoEventoViagem.Adiantamento && viagemEvento.TipoEventoViagem != ETipoEventoViagem.TarifaAntt)
                {
                    var viagemEventoAdiantamento = viagem.ViagemEventos.FirstOrDefault(t => t.TipoEventoViagem == ETipoEventoViagem.Adiantamento);
                    if (viagemEventoAdiantamento != null)
                    {
                        if (viagemEventoAdiantamento.Status == EStatusViagemEvento.Aberto || viagemEventoAdiantamento.Status == EStatusViagemEvento.Bloqueado)
                            return validationResult.Add("O Adiantamento da viagem não está baixado!");
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao efetuar pagamento. IdVIagem: " + pagamentoFreteModel.IdViagem);

                if (e.ToString().Contains("deadlock"))
                    validationResult.Add($"O pagamento deste evento está já em processamento. Consulte o token novamente para obter o resultado do processamento.</br>Se o erro persistir, contate o administrador do sistema", EFaultType.Error, "9000");
                else
                    validationResult.Add(e.GetBaseException().Message);
            }

            return validationResult;
        }
        public ValidationResult ConsultaCartoesPagamentoEvento(ICartoesService cartoesService, Usuario usuarioLogado, string cpfCnpjProprietario, string cpfMotorista,
            out CartaoVinculadoPessoaListResponse cartaoProprietario, out CartaoVinculadoPessoaListResponse cartaoMotorista)
        {
            var validationResult = new ValidationResult();
            cartaoProprietario = null;
            cartaoMotorista = null;
            try
            {
                var produtoId = new List<int> { cartoesService.GetIdProdutoCartaoFretePadrao() };

                cartaoProprietario = cartoesService.GetCartoesVinculados(cpfCnpjProprietario, produtoId, cpfCnpjProprietario != cpfMotorista,buscarCartoesBloqueados:true);

                if (cartaoProprietario ==null || !cartaoProprietario.Cartoes.Any())
                    validationResult.Add("Proprietario nao tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");

                cartaoMotorista = cartoesService.GetCartoesVinculados(cpfMotorista, produtoId, false,buscarCartoesBloqueados:true);

                if (cartaoMotorista == null || !cartaoMotorista.Cartoes.Any())
                    validationResult.Add("Motorista não tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");
            }
            catch (Exception e)
            {
                validationResult.Add(e.GetBaseException().Message);
            }

            return validationResult;
        }

        public BaixarEventoSaldoResuLt PagamentoCartao(ICartoesService cartoesService, CartaoVinculadoPessoaListResponse cartaoProprietario, CartaoVinculadoPessoaListResponse cartaoMotorista,
            Viagem viagem, ViagemEvento viagemEvento)
                {
            var validationResult = new BaixarEventoSaldoResuLt();
            OperacaoCartaoResponseDTO cargaResultado;
            try
            {
                cargaResultado = cartoesService.RealizarCargaFrete(viagem, viagemEvento, cartaoMotorista, cartaoProprietario);
            }
            catch (Exception e)
            {
                _logger.Error($"Erro ao realizar integração de carga no Meio Homologado: {e}");
                cargaResultado = new OperacaoCartaoResponseDTO
                {
                    Status = EStatusPagamentoCartao.Erro,
                    Mensagem = e.Message,
                    Protocolo = 0
                };
            }

            if (cargaResultado.Status == EStatusPagamentoCartao.Erro)
                validationResult.Add($"Ocorreu um erro ao realizar integração de carga no Meio Homologado e o mesmo será reprocessado em breve.\r\nErro: {cargaResultado.Mensagem}\r\nEm caso de não ocorrer efetuação da carga, entrar em contato com o suporte.", EFaultType.Error, "9000");
            else if (cargaResultado.Status == EStatusPagamentoCartao.Pendente)
                validationResult.Add("Serviço de pagamento em cartão indisponível no momento. A operação será efetivada automaticamente assim que reestabelecida a comunicação.", EFaultType.Alert, "9000");

            validationResult.StatusPagamentoCartao = cargaResultado.Status;

            return validationResult;
        }

        // TRUNK-ATS-ANT
        //public ValidationResult CreditarAbono(int IdViagemEvento, string nome, string cpfCnpj)
        public ValidationResult CreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, IUserIdentity usuarioLogado, decimal? valor)
        {
            try
            {
                var evento = _viagemEventoRepository
                    .Include(o => o.Viagem)
                    .Include(o => o.Viagem.Empresa)
                    .FirstOrDefault(x => x.IdViagemEvento == idViagemEventoabono);

                if (evento == null)
                    return new ValidationResult().Add("Adicionando ");

                evento.Status = EStatusViagemEvento.Baixado;
                evento.DataHoraPagamento = DateTime.Now;
                    evento.ValorTotalPagamento = valor ?? evento.ValorPagamento;
                _viagemEventoRepository.Update(evento);

                var resultadoSetAnalisado = _protocoloService.RealizarAnaliseAbono(idViagemEventoSaldo, idProtocolo, usuarioLogado.IdUsuario, false, null, string.Empty, EStatusAnaliseAbono.Aprovado);

                if (!resultadoSetAnalisado.IsValid)
                    return new ValidationResult().Add(resultadoSetAnalisado.Errors.FirstOrDefault()?.Message);

                //var cartoesService = new CartoesService(evento.Viagem.IdEmpresa, evento.Viagem.Empresa.TokenMicroServices, cpfCnpj, nome);
                var cartoesService = new CartoesService(_cartoesServiceArgs, evento.Viagem.IdEmpresa, evento.Viagem.Empresa.TokenMicroServices, usuarioLogado.CpfCnpj, usuarioLogado.Nome);

                var produtoId = new List<int> { cartoesService.GetIdProdutoCartaoFretePadrao() };
                var cartaoMotorista = cartoesService.GetCartoesVinculados(evento.Viagem.CPFMotorista, produtoId, false);

                if (cartaoMotorista.Cartoes == null || !cartaoMotorista.Cartoes.Any())
                {
                    throw new Exception($"Motorista não tem cartões vinculados na processadora.");
                }

                var cartaoProprietario = cartoesService.GetCartoesVinculados(evento.Viagem.CPFCNPJProprietario, produtoId, evento.Viagem.CPFCNPJProprietario != evento.Viagem.CPFMotorista);

                if (cartaoProprietario.Cartoes == null || !cartaoProprietario.Cartoes.Any())
                {
                    throw new Exception($"Proprietario não tem cartões vinculados na processadora.");
                }

                var cargaResultado = cartoesService.RealizarCargaFrete(evento.Viagem, evento, cartaoMotorista, cartaoProprietario);

                if (cargaResultado.Status == EStatusPagamentoCartao.Erro)
                    return new ValidationResult().Add(cargaResultado.Mensagem);

            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Não foi possível creditar o abono: {e.Message}");
            }

            return new ValidationResult();
        }

        public ValidationResult NaoCreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, int? idMotivo, string descricao = null, int? idUsuario = null )
        {
            try
            {
                var repository = _viagemEventoRepository;
                var viagemEvento = repository.FirstOrDefault(o => o.IdViagemEvento == idViagemEventoabono);

                if (viagemEvento == null)
                    return new ValidationResult().Add("Evento não encontrado");

                viagemEvento.IdMotivoRejeicaoAbono = idMotivo;
                viagemEvento.DescricaoRejeicaoAbono = descricao;
                viagemEvento.DataHoraRejeicaoAbono = DateTime.Now;
                viagemEvento.IdUsuarioRejeicaoAbono = idUsuario;
                viagemEvento.Status = EStatusViagemEvento.Bloqueado;

                repository.Update(viagemEvento);

                var resultadoSetAnalisado = _protocoloService.RealizarAnaliseAbono(idViagemEventoSaldo, idProtocolo, idUsuario ?? 0, false, null, string.Empty, EStatusAnaliseAbono.Recusado);

                if (!resultadoSetAnalisado.IsValid)
                    return new ValidationResult().Add(resultadoSetAnalisado.Errors.FirstOrDefault()?.Message);

                NotificarRejeicaoAbono(idViagemEventoabono, idProtocolo);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult ValidarCredenciamento(Usuario usuarioLogado, int? idEstabelecimento, int administradoraPlataforma)
        {
            var validationResult = new ValidationResult();
            var credenciamentoService = _credenciamentoService;
            Credenciamento credenciamento = null;

            if(usuarioLogado.Perfil != EPerfil.Empresa)
                credenciamento = credenciamentoService.GetByIdEstabelecimentoBase(usuarioLogado.UsuarioEstabelecimentos.FirstOrDefault()?.IdEstabelecimento).FirstOrDefault();
            else if (idEstabelecimento.HasValue)
                credenciamento = credenciamentoService.GetQuery().FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento.Value);

            if (credenciamento == null)
                return validationResult.Add("Estabelecimento não credenciado para empresa");

            if (credenciamento.Status != EStatusCredenciamento.Aprovado)
                return validationResult.Add($"Credenciamento com a empresa não está aprovado, o status atual é {credenciamento.Status.GetDescription()}");

            var idDocumentoCredenciamento = _documentoService.GetAllDocumentoCredenciamento().Select(x => x.IdDocumento).ToList();

            var validadeDocumentosCredenciamento = _credenciamentoAnexoRepository
                .Find(x => x.IdCredenciamento == credenciamento.IdCredenciamento)
                .Where(x => idDocumentoCredenciamento.Contains(x.IdDocumento.Value));

            if(!validadeDocumentosCredenciamento.Any())
                return validationResult.Add("Por favor, anexe os documentos de credenciamento nas configurações do estabelecimento", EFaultType.Alert);

            if(credenciamento.StatusDocumentacao == EStatusDocumentacaoCredenciamento.Irregular)
                return validationResult.Add("O estado da sua documentação de credenciamento está irregular, favor atualizar a documentação!", EFaultType.Alert);

            var menorDataVencimento = new DateTime?();

            var temDocumentoVencido = false;
            foreach (var item in validadeDocumentosCredenciamento)
            {
                if (item.DataValidade.HasValue)
                {
                    if (item.DataValidade < menorDataVencimento || menorDataVencimento == null)
                        menorDataVencimento = item.DataValidade;
                    if (item.DataValidade?.GetDataOnly() < DateTime.Now.GetDataOnly())
                    {
                        temDocumentoVencido = true;
                        break;
                    }
                }
            }

            var prazoParaInformarDocumentos = _empresaRepository.GetQuery(credenciamento.IdEmpresa).Select(x => x.PrazoParaInformarDocumentos).FirstOrDefault() ?? 0;

            if (temDocumentoVencido)
            {
                credenciamentoService.AtualizarStatusDocumentacao(credenciamento.IdCredenciamento, EStatusDocumentacaoCredenciamento.Irregular, administradoraPlataforma, "[Rejeição automática] Data de validade de um ou mais documentos de credenciamento está expirada");
                validationResult.Add("Data de validade de um ou mais documentos de credenciamento está expirada, favor atualizar a documentação!", EFaultType.Alert);
            } else if (menorDataVencimento < DateTime.Now.AddDays(prazoParaInformarDocumentos))
                validationResult.Add($"Existem documentos de credenciamento que vencerão em menos de {prazoParaInformarDocumentos.ToString()} dias, favor regularizar os documentos!", EFaultType.Alert);

            return validationResult;
        }

        public ValidationResult AlterarSaldo(decimal saldo, decimal pesoChegada, decimal quebraMercadoria, decimal difFreteMotorista, string token)
        {
            var viagem = _viagemRepository
                .Include(x => x.ViagemEventos)
                .FirstOrDefault(x => x.ViagemEventos.Any(y => y.Token == token));

            var viagemEvento = viagem?.ViagemEventos.FirstOrDefault(x => x.Token == token.Trim());

            if (viagemEvento == null)
                return new ValidationResult().Add($"Não foi possível identificar o evento pelo seguinte token {token}");

            //Tratamento momentaneo para o pagamento de cartao
            if (viagemEvento.HabilitarPagamentoCartao)
            {
                if (viagemEvento.Status == EStatusViagemEvento.Baixado)
                    return new ValidationResult().Add($"Não é possível alterar valor de evento já pago em cartão.");
            }

            var protocoloEvento = _protocoloEventoRepository
                .Find(x => x.IdViagemEvento == viagemEvento.IdViagemEvento && x.Protocolo.StatusProtocolo != EStatusProtocolo.Rejeitado)
                .Include(x => x.Protocolo)
                .ToList();

            if (protocoloEvento.Count() > 0)
                return new ValidationResult().Add($"Evento {token} está vínculado a um protocolo!");

            viagem.PesoChegada = pesoChegada;
            viagem.PesoDiferenca = viagem.PesoSaida.HasValue ? viagem.PesoSaida - pesoChegada : viagem.PesoSaida;
            viagem.ValorQuebraMercadoria = quebraMercadoria;
            viagemEvento.ValorTotalPagamento = saldo;
            viagem.DifFreteMotorista = difFreteMotorista;

            // Atualiza a data e hora do pagamento quando o saldo é alterado
            if (viagemEvento.Status == EStatusViagemEvento.Baixado)
            {
                viagemEvento.DataHoraPagamento = DateTime.Now;
                _viagemEventoRepository.Update(viagemEvento);
            }

            viagem.DataAtualizacao = DateTime.Now;
            _viagemRepository.Update(viagem);

            return new ValidationResult();
        }

        public byte[] GerarReciboViagem(string token, int? idViagemEvento)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagem = _viagemRepository.Include(t => t.Empresa)
                .Include(v => v.Filial)
                .Include(o => o.ViagemEventos)
                .Include(o => o.ViagemRegras)
                .Include(o => o.ViagemEventos.Select(v=> v.ViagemValoresAdicionais))
                .Include(t => t.ViagemEventos.Select(s => s.EstabelecimentoBase))
                .FirstOrDefault(t => t.ViagemEventos.Any(x => x.Token == token));

            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            if (viagem.Empresa == null)
                throw new Exception("Empresa não encontrada para esta viagem!");

            if (viagem.ViagemEventos == null || !viagem.ViagemEventos.Any())
                throw new Exception("Nenhum evento encontrado para esta viagem!");

            var ciot = _declaracaoCiotRepository
                .Where(o => o.IdViagem == viagem.IdViagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .FirstOrDefault();

            var viagemEvento = viagem.ViagemEventos
                .FirstOrDefault(t => t.Token == token && (!idViagemEvento.HasValue || t.IdViagemEvento == idViagemEvento.Value));
            if (viagemEvento == null)
                throw new Exception("Evento não encontrado para esta viagem!");

            if (idViagemEvento == null)
                idViagemEvento = viagemEvento.IdViagemEvento;

            var barcode = new Spire.Barcode.WebUI.BarCodeControl { Data = viagemEvento.Token };

            var nomeUsuarioBaixa = _usuarioService.GetNomeById(viagemEvento.IdUsuarioBaixaEvento ?? 0);

            var pagamentoService = this;
            var somarPedagio = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem);


            var unidadeMed = !string.IsNullOrEmpty(viagem.ViagemRegras.FirstOrDefault()?.UnidadeMedida)
                ? viagem.ViagemRegras.FirstOrDefault()?.UnidadeMedida
                : "KG";

            var pagamentoFreteReciboModel = new PagamentoFreteReciboModel
            {
                EmpresaLogo = viagem.Empresa.Logo != null ? Convert.ToBase64String(viagem.Empresa.Logo) : string.Empty,
                EstabelecimentoNome = viagemEvento.EstabelecimentoBase?.Descricao,
                EstabelecimentoCnpj = viagemEvento.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                ViagemToken = viagemEvento.Token,
                ViagemEventoTipo = viagemEvento.TipoEventoViagem.ToString(),
                ViagemEventoNumeroRecibo = viagemEvento.NumeroRecibo,
                ViagemEventoValorTotalPagamento = (viagemEvento.ValorTotalPagamento ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemEventoValorTotalPagamentoComPedagio = ((viagemEvento.ValorTotalPagamento ?? 0) + (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0)).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemEventoDataHoraPagamento = viagemEvento.DataHoraPagamento?.ToString("dd/MM/yyyy HH:mm"),
                MotoristaNome = viagem.NomeProprietario,
                ProprietarioCpfCnpj = !string.IsNullOrWhiteSpace(viagem.CPFCNPJProprietario) ? (viagem.CPFCNPJProprietario.Length > 11 ? viagem.CPFCNPJProprietario.ToCNPJFormato() : viagem.CPFCNPJProprietario.ToCPFFormato()) : string.Empty,
                ProprietarioRntrc = viagem.RNTRC?.ToString(),
                ViagemPesoSaida = viagem.PesoSaida?.ToString("N", CultureInfo.GetCultureInfo("pt-BR"))
                                    + " " + unidadeMed,
                ViagemPesoChegada = viagem.PesoChegada?.ToString("N", CultureInfo.GetCultureInfo("pt-BR"))
                                    + " " + unidadeMed,
                ViagemDifFreteMotorista = (viagem.DifFreteMotorista ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemQuebraMercadoria =
                    (viagem.ValorQuebraMercadoria ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ValorPedagio = viagem.ValorPedagio.ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                Barcode = ImageToByteArray(barcode.GenerateImage()),
                Instrucoes = viagemEvento.Instrucao,
                Usuario = nomeUsuarioBaixa,
                Ciot = ciot?.Ciot,
                NumeroCte = viagem.NumeroDocumento,
                NumeroFilialExterno = viagem.Filial?.CodigoFilial,
                DocumentoCliente = viagem.DocumentoCliente,
                DataDescarga = viagem.DataDescarga?.ToShortDateString(),
                PedagioInclusoNoAdiantamento = DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem)
            };

            var listaValoresAcrescimos = new List<PagamentoFreteReciboAcrescimosDescontosModel>();
            var listaValoresDescontos = new List<PagamentoFreteReciboAcrescimosDescontosModel>();


            var valoresAdicionais = _viagemValorAdicionalRepository
                .Find(o => o.IdViagemEvento == idViagemEvento);

            if (valoresAdicionais.Any())
                foreach (var valorAdicional in valoresAdicionais)
                    if (valorAdicional.Tipo == ETipoValorAdicional.Acrescimo)
                        listaValoresAcrescimos.Add(new PagamentoFreteReciboAcrescimosDescontosModel { Descricao = valorAdicional.Descricao, Valor = $"{valorAdicional.Valor:C}" });
                    else
                        listaValoresDescontos.Add(new PagamentoFreteReciboAcrescimosDescontosModel { Descricao = valorAdicional.Descricao, Valor = $"{valorAdicional.Valor:C}" });

            if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.RPA)
            {
                pagamentoFreteReciboModel.INSS = viagemEvento.INSS.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.IRRF = viagemEvento.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.ISSQN = viagemEvento.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.SESTSENAT = viagemEvento.SESTSENAT.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
            }
            else
            {
                pagamentoFreteReciboModel.INSS = viagem.INSS.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.IRRF = viagem.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.ISSQN = viagem.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.SESTSENAT = viagem.SESTSENAT.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
            }

            var barCodeBytes = ImageToByteArray(barcode.GenerateImage());
            var barCodeStr = Convert.ToBase64String(barCodeBytes);

            var telefonesEmpresa = $"{viagem.Empresa.Telefone?.FormatAsTelefone()} / {viagem.Empresa.Telefone0800}";

            switch (viagemEvento.TipoEventoViagem)
            {
                case ETipoEventoViagem.Saldo:
                    return new PagamentoFreteReciboReport().RelatorioSaldo(new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, listaValoresAcrescimos, listaValoresDescontos, barCodeStr, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);
                case ETipoEventoViagem.RPA:
                    return new PagamentoFreteReciboReport().RelatorioRPA(new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);
            }

            return new PagamentoFreteReciboReport().Relatorio(
                new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, listaValoresAcrescimos,
                listaValoresDescontos, barCodeStr, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);

        }

        public byte[] GerarReciboProtocolo(string token, int? idViagemEvento, string usuario)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagem = _viagemRepository.Include(t => t.Empresa)
                .Include(t => t.Filial)
                .Include(t => t.ViagemRegras)
                .Include(t => t.ViagemEventos.Select(s => s.EstabelecimentoBase))
                .FirstOrDefault(t => t.ViagemEventos.Any(x => x.Token == token));

            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            if (viagem.Empresa == null)
                throw new Exception("Empresa não encontrada para esta viagem!");

            if (viagem.ViagemEventos == null || !viagem.ViagemEventos.Any())
                throw new Exception("Nenhum evento encontrado para esta viagem!");

            var proprietario = _veiculoRepository.Include(t => t.Proprietario)
                .FirstOrDefault(t => t.Placa == viagem.Placa)?.Proprietario;

            var viagemEvento = viagem.ViagemEventos
                .FirstOrDefault(t => t.Token == token && (t.IdViagemEvento == idViagemEvento));

            var protocoloEvento = _protocoloEventoRepository.FirstOrDefault(x => x.IdViagemEvento == idViagemEvento);

            if (viagemEvento == null)
                throw new Exception("Evento não encontrado para esta viagem!");

            var ciot = _declaracaoCiotRepository
                .Where(o => o.IdViagem == viagem.IdViagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .FirstOrDefault();

            var nomeTransp = string.Empty;
            var veicVg = _veiculoService.Get(viagem.Placa, viagem.IdEmpresa);
            if (veicVg != null && veicVg.IdProprietario.HasValue)
            {
                var propVg = _proprietarioRepository.GetQuery(veicVg.IdProprietario.Value).Select(o => o.NomeFantasia).FirstOrDefault(); //_proprietarioService.Get(veicVg.IdProprietario.Value);
                if (!string.IsNullOrEmpty(propVg))
                    nomeTransp = propVg;
            }

            var barcode = new Spire.Barcode.WebUI.BarCodeControl();
            barcode.Data = viagemEvento.Token;

            var nomeUsuarioBaixa = _usuarioService.GetNomeById(viagemEvento.IdUsuarioBaixaEvento ?? 0);

            var listaValoresAcrescimos = new List<PagamentoFreteReciboAcrescimosDescontosModel>();
            var listaValoresDescontos = new List<PagamentoFreteReciboAcrescimosDescontosModel>();

            var pagamentoService = this;
            var somarPedagio = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem);

            var unidadeMed = !string.IsNullOrEmpty(viagem.ViagemRegras.FirstOrDefault().UnidadeMedida)
                ? viagem.ViagemRegras.FirstOrDefault().UnidadeMedida
                : "KG";

            var pagamentoFreteReciboModel = new PagamentoFreteReciboModel
            {
                EmpresaLogo = viagem.Empresa.Logo != null ? Convert.ToBase64String(viagem.Empresa.Logo) : string.Empty,
                EstabelecimentoNome = viagemEvento?.EstabelecimentoBase?.Descricao,
                EstabelecimentoCnpj = viagemEvento?.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                ViagemToken = viagemEvento.Token,
                ViagemEventoTipo = viagemEvento.TipoEventoViagem.ToString(),
                ViagemEventoNumeroRecibo = viagemEvento.NumeroRecibo,
                // Valor do protocolo evento, já possui o pedágio incluso quando o evento indica isso. Diferente da viagem_evento
                ViagemEventoValorTotalPagamento = ((protocoloEvento?.ValorTotalPagamento ?? 0) - (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0)).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemEventoValorTotalPagamentoComPedagio = (protocoloEvento?.ValorTotalPagamento ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemEventoDataHoraPagamento = viagemEvento.DataHoraPagamento?.ToString("dd/MM/yyyy HH:mm"),
                MotoristaNome = nomeTransp,
                ProprietarioCpfCnpj = !string.IsNullOrWhiteSpace(viagem?.CPFCNPJProprietario) ? (viagem.CPFCNPJProprietario.Length > 11 ? viagem.CPFCNPJProprietario.ToCNPJFormato() : viagem.CPFCNPJProprietario.ToCPFFormato()) : string.Empty,
                ProprietarioRntrc = proprietario?.RNTRC,
                ViagemPesoSaida = viagem.PesoSaida?.ToString("N", CultureInfo.GetCultureInfo("pt-BR"))
                                    + " " + unidadeMed,
                ViagemPesoChegada = protocoloEvento?.PesoChegada?.ToString("N", CultureInfo.GetCultureInfo("pt-BR"))
                                    + " " + unidadeMed,
                ViagemDifFreteMotorista = (protocoloEvento?.ValorDifFreteMotorista ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ViagemQuebraMercadoria = (protocoloEvento?.ValorQuebraMercadoria ?? 0).ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                ValorPedagio = viagem.ValorPedagio.ToString("C", CultureInfo.GetCultureInfo("pt-BR")),
                Barcode = ImageToByteArray(barcode.GenerateImage()),
                Instrucoes = viagemEvento?.Instrucao,
                Usuario = nomeUsuarioBaixa,
                Ciot = ciot?.Ciot,
                NumeroCte = viagem.NumeroDocumento,
                NumeroFilialExterno = viagem.Filial?.CodigoFilial,
                DocumentoCliente = viagem.DocumentoCliente,
                DataDescarga = viagem.DataDescarga?.ToShortDateString(),
                PedagioInclusoNoAdiantamento = DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem)
            };

            var valoresAdicionais = _viagemValorAdicionalRepository
                .Find(o => o.IdViagemEvento == idViagemEvento);

            if (valoresAdicionais.Any())
            {
                foreach (var valorAdicional in valoresAdicionais)
                    if (valorAdicional.Tipo == ETipoValorAdicional.Acrescimo)
                        listaValoresAcrescimos.Add(new PagamentoFreteReciboAcrescimosDescontosModel { Descricao = valorAdicional.Descricao, Valor = $"{valorAdicional.Valor:C}" });
                    else
                        listaValoresDescontos.Add(new PagamentoFreteReciboAcrescimosDescontosModel { Descricao = valorAdicional.Descricao, Valor = $"{valorAdicional.Valor:C}" });
            }

            if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.RPA)
            {
                pagamentoFreteReciboModel.INSS = viagemEvento.INSS.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.IRRF = viagemEvento.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.ISSQN = viagemEvento.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.SESTSENAT = viagemEvento.SESTSENAT.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
            }
            else
            {
                pagamentoFreteReciboModel.INSS = viagem.INSS.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.IRRF = viagem.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.ISSQN = viagem.IRRPF.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
                pagamentoFreteReciboModel.SESTSENAT = viagem.SESTSENAT.ToString("C", CultureInfo.GetCultureInfo("pt-BR"));
            }

            var barCodeBytes = ImageToByteArray(barcode.GenerateImage());
            var barCodeStr = Convert.ToBase64String(barCodeBytes);

            var telefonesEmpresa = $"{viagem.Empresa.Telefone?.FormatAsTelefone()} / {viagem.Empresa.Telefone0800}";

            if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo)
            {
                return new PagamentoFreteReciboReport().RelatorioSaldo(new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, listaValoresAcrescimos, listaValoresDescontos, barCodeStr, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);
            }
            else if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.RPA)
            {
                return new PagamentoFreteReciboReport().RelatorioRPA(new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);
            }

            return new PagamentoFreteReciboReport().Relatorio(new List<PagamentoFreteReciboModel> { pagamentoFreteReciboModel }, listaValoresAcrescimos, listaValoresDescontos, barCodeStr, telefonesEmpresa, viagemEvento.HabilitarPagamentoCartao);
        }

        public byte[] ImageToByteArray(Image imageIn)
        {
            using (var ms = new MemoryStream())
            {
                imageIn.Save(ms, System.Drawing.Imaging.ImageFormat.Gif);
                return ms.ToArray();
            }
        }

        public PagamentoFreteEventoModel CalcularValoresViagem(string token, bool habilitaPagamentoCartao, decimal? pesoChegada, int? numeroSacas, EUnidadeMedida unidadeInformada, bool? quebraAbonada = null)
        {
            // Round: MidpointRounding.AwayFromZero => Este argumento é o comportamentamento do arredondamento quando o ultimo digito é 5, indicando que sempre deve ser acima.
            // O arredondamento desta rotina joga o 5 sempre para cima, igual ao Excel e o Oracle (Maxicon)
            // Exemplo: Math.Round(7.415, 2,  MidpointRounding.AwayFromZero) = 7,42 /
            // Exemplo: Math.Round(7.425, 2,  MidpointRounding.AwayFromZero) = 7,43 / Pela matemática seria arrendado pra 7,42, pois o predecessor é par.

            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagem = _viagemRepository
                .Include(t => t.ViagemEventos)
                .Include(t => t.ViagemRegras)
                .Include(x => x.ViagemEventos.Select(y => y.Motivo))
                .Include(x => x.ViagemEventos.Select(y => y.ViagemValoresAdicionais))
                .FirstOrDefault(t => t.ViagemEventos.Any(x => x.Token == token));
            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            //var eventoAdiantamento = viagem.ViagemEventos
            //    .FirstOrDefault(ve => ve.TipoEventoViagem == ETipoEventoViagem.Adiantamento);
            var tipoViagemEvento = viagem.ViagemEventos.Where(c => c.Token == token).Select(c => c.TipoEventoViagem).FirstOrDefault();

            if (tipoViagemEvento != ETipoEventoViagem.Saldo)
                return ConsultarEvento(token);

            var viagemRegra = viagem.ViagemRegras.FirstOrDefault();
            if (viagemRegra == null)
                throw new Exception("Nenhuma regra foi encontrada para esta viagem!");

            var viagemEvento = viagem.ViagemEventos.FirstOrDefault(t => t.TipoEventoViagem == ETipoEventoViagem.Saldo);
            if (viagemEvento == null)
                throw new Exception("Nenhum evento de saldo foi encontrado para esta viagem!");

            if (!pesoChegada.HasValue && viagem.Unidade == EUnidadeMedida.Peso)
                pesoChegada = 0;

            if (!numeroSacas.HasValue && viagem.Unidade == EUnidadeMedida.Saca)
                numeroSacas = 0;

            decimal pesoChegadaReal = pesoChegada.HasValue ? pesoChegada.Value : decimal.Zero;
            decimal sacas = numeroSacas ?? 0;
            if (sacas < 1 && viagem.Unidade == EUnidadeMedida.Saca && viagem.PesoSaida.HasValue)
            {
                decimal pesoPorSaca = viagem.PesoSaida.Value / viagem.Quantidade;
                if (pesoPorSaca > 0)
                    sacas = pesoChegadaReal / pesoPorSaca;
            }


            if (unidadeInformada == EUnidadeMedida.Saca)
            {
                if (viagem.Quantidade < 1)
                    throw new Exception("Não é possível calcular os valores sem que a quantidade de sacas tenha sido informada na viagem.");
                if (!numeroSacas.HasValue)
                    throw new Exception("Não é possível calcular os valores sem que o número de sacas entregues seja informada.");

                var difSacas = viagem.Quantidade - numeroSacas.Value;
                var pesoPorSaca = viagem.PesoSaida / viagem.Quantidade;
                pesoChegadaReal = viagem.PesoSaida - (pesoPorSaca * difSacas) ?? 0;
            }

            var pesoDiferenca = viagem.PesoSaida - pesoChegadaReal;
            var pesoDiferencaTolerada = viagem.PesoSaida * viagemRegra.ToleranciaPeso / 100;
            var valorPorKg = viagem.ValorMercadoria / viagem.PesoSaida;

            decimal? valorQuebraMercadoria = 0;
            if (pesoDiferencaTolerada < pesoDiferenca)
            {
                switch (viagemRegra.TipoQuebraMercadoria)
                {
                    case ETipoQuebraMercadoria.Diferenca:
                        valorQuebraMercadoria = Math.Round((pesoDiferencaTolerada - pesoDiferenca) * valorPorKg ?? 0, 2, MidpointRounding.AwayFromZero);
                        break;
                    case ETipoQuebraMercadoria.Integral:
                        valorQuebraMercadoria = Math.Round((pesoChegadaReal - viagem.PesoSaida) * valorPorKg ?? 0, 2, MidpointRounding.AwayFromZero);
                        break;
                    default:
                        throw new ArgumentOutOfRangeException("Tipo de calculo inesperado para quebra: " + viagemRegra.TipoQuebraMercadoria);
                }
            }

            var pagamentoFreteConfiguracao = _pagamentoConfiguracaoRepository.FirstOrDefault(x => x.IdEmpresa == viagem.IdEmpresa && x.Ativo);

            var valorReceberPeloPesoSaida = Math.Round(viagem.PesoSaida.Value * viagemRegra.TarifaTonelada.GetValueOrDefault() / 1000, 2, MidpointRounding.AwayFromZero);
            var valorReceberPeloPesoChegada = Math.Round(pesoChegada.Value * viagemRegra.TarifaTonelada.GetValueOrDefault() / 1000, 2, MidpointRounding.AwayFromZero);
            var diferencaFreteMotorista = valorReceberPeloPesoChegada - valorReceberPeloPesoSaida;

            if (pagamentoFreteConfiguracao != null && !pagamentoFreteConfiguracao.BonificarMotorista && pesoChegadaReal > viagem.PesoSaida)
                diferencaFreteMotorista = 0;

            if (viagemRegra.FreteLotacao)
                diferencaFreteMotorista = 0;

            //var TotalPagar = viagem.ViagemEventos.Where(x => x.Status != EStatusViagemEvento.Baixado && x.TipoEventoViagem != ETipoEventoViagem.Saldo).Sum(x => x.ValorPagamento);
            var totalPago = viagem.ViagemEventos.Where(x => x.Status == EStatusViagemEvento.Baixado && x.TipoEventoViagem != ETipoEventoViagem.Saldo).Sum(x => x.ValorTotalPagamento);

            var pagamentoSolicitacaoAbono = _viagemSolicitacaoAbonoRepository
               .FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento && x.Status != EStatusAbono.Cancelado);


            bool difFreteMotoristaPassivo = diferencaFreteMotorista < 0;

            var evento = new PagamentoFreteEventoModel
            {
                AbonoRequisitado = viagemEvento.QuebraMercadoriaAbonada ?? false,
                PesoChegada = pesoChegadaReal,
                QuantidadeSacas = sacas,
                DataEmissao = DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                DifFreteMotorista = decimal.Round(Math.Abs(diferencaFreteMotorista), 2, MidpointRounding.AwayFromZero),
                QuebraMercadoria = decimal.Round(Math.Abs(valorQuebraMercadoria ?? 0), 2, MidpointRounding.AwayFromZero),
                QuebraMercadoriaCalculada = decimal.Round(Math.Abs(valorQuebraMercadoria ?? 0), 2, MidpointRounding.AwayFromZero),
                ValorPagamento = viagemEvento.ValorPagamento,
                ValorPagamentoBloqueado = viagemEvento.Status == EStatusViagemEvento.Aberto ||
                                          viagemEvento.Status == EStatusViagemEvento.Bloqueado,
                ValorTotalPagamentoBaixado = viagemEvento.Status == EStatusViagemEvento.Baixado,
                PagamentoParametrosCalculo = new PagamentoParametrosCalculo()
                {
                    ValorKG = valorPorKg,
                    Tarifa = Math.Round(((viagemRegra.TarifaTonelada ?? 0) / 1000), 6),
                    Tolerancia = (viagemRegra.ToleranciaPeso ?? 0),
                    Falta = viagemRegra.TipoQuebraMercadoria == ETipoQuebraMercadoria.Diferenca
                        ? Math.Round((pesoDiferenca ?? 0) - (pesoDiferencaTolerada ?? 0), 2, MidpointRounding.AwayFromZero)
                        : pesoDiferenca,
                    PesoDiferencaTolerada = Math.Round(pesoDiferencaTolerada ?? 0, 2),
                    Diferenca = pesoDiferenca,
                    PesoSaida = viagem.PesoSaida
                },
                PagamentoSolicitacaoAbono = pagamentoSolicitacaoAbono != null ? new PagamentoSolicitacaoAbono()
                {
                    IdViagemSolicitacaoAbono = pagamentoSolicitacaoAbono.IdViagemSolicitacao,
                    Status = pagamentoSolicitacaoAbono.Status
                } : null,
                DifFreteMotoristaPassivo = difFreteMotoristaPassivo,
                DescricaoMotivo = viagemEvento?.Motivo?.Descricao
            };



            habilitaPagamentoCartao = viagemEvento.HabilitarPagamentoCartao ? viagemEvento.HabilitarPagamentoCartao : habilitaPagamentoCartao;

            decimal outrosDescontos = 0;
            if (viagemEvento.ViagemValoresAdicionais != null && viagemEvento.ViagemValoresAdicionais.Any())
            {
                foreach (var valoresAdicionais in viagemEvento.ViagemValoresAdicionais)
                {
                    switch (valoresAdicionais.Tipo)
                    {
                        case ETipoValorAdicional.Acrescimo:
                            outrosDescontos += valoresAdicionais.Valor;
                            break;
                        case ETipoValorAdicional.Desconto:
                            outrosDescontos -= valoresAdicionais.Valor;
                            break;
                    }
                }
            }

            // Limitar valor de quebra de mercadoria, ao valor máximo para fechar o calculo de pagamento de saldo em valor 0 (Caso quebra+dif.mot.+outros descontos for maior que saldo)
            var hasAbono = quebraAbonada != null ? quebraAbonada : viagemEvento.QuebraMercadoriaAbonada;
            var totalDescontarDiferencaMotorista = (hasAbono ?? false) && !habilitaPagamentoCartao
                ? diferencaFreteMotorista
                : (valorQuebraMercadoria ?? 0) + diferencaFreteMotorista;

            var valorPagamentoLiquidoIntegrado = evento.ValorPagamento + outrosDescontos;

            var valorQuebra = evento.QuebraMercadoria > valorPagamentoLiquidoIntegrado
                ? valorPagamentoLiquidoIntegrado
                : evento.QuebraMercadoria;

            var valorPagamentoLimpo = evento.ValorPagamento + outrosDescontos;

            if (valorQuebra > valorPagamentoLiquidoIntegrado - evento.DifFreteMotorista)
                valorQuebra = valorPagamentoLiquidoIntegrado - evento.DifFreteMotorista;

            evento.QuebraMercadoria = valorQuebra;

            // Valor total a pagar para o motorista
            if (valorPagamentoLiquidoIntegrado + totalDescontarDiferencaMotorista < 0)
                evento.ValorTotalPagamento = 0;
            else
                evento.ValorTotalPagamento = evento.ValorPagamento + totalDescontarDiferencaMotorista + outrosDescontos;

            evento.ValorTotalPagamentoCalculado = evento.ValorPagamento + totalDescontarDiferencaMotorista + outrosDescontos;

            var totalPagar = (evento.ValorPagamento - evento.QuebraMercadoria) + diferencaFreteMotorista;
            evento.TotalPagar = totalPagar;
            evento.TotalPago = evento.ValorTotalPagamentoBaixado ? totalPago + evento.ValorTotalPagamento : totalPago;
            if (viagemEvento.ViagemValoresAdicionais != null && viagemEvento.ViagemValoresAdicionais.Any())
            {
                evento.TotalOutrosAcrescimos = viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo).Sum(x => x.Valor);
                evento.TotalOutrosDescontos = viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Desconto).Sum(x => x.Valor);
            }

            return evento;
        }

        public PagamentoFreteEventoModel CalcularValoresProtocolo(string token, decimal? pesoChegada, int? numeroSacas)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagem = _viagemRepository
                .Include(t => t.ViagemEventos)
                .Include(x => x.ViagemEventos.Select(y => y.ProtocoloEventos))
                .Include(x => x.ViagemEventos.Select(y => y.ViagemValoresAdicionais))
                .Include(t => t.ViagemRegras)
                .FirstOrDefault(t => t.ViagemEventos.Any(x => x.Token == token));
            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            var viagemRegra = viagem.ViagemRegras.FirstOrDefault();
            if (viagemRegra == null)
                throw new Exception("Nenhuma regra foi encontrada para esta viagem!");

            var viagemEvento = viagem.ViagemEventos.FirstOrDefault(t => t.TipoEventoViagem == ETipoEventoViagem.Saldo);

            if (viagemEvento == null)
                throw new Exception("Nenhum evento de saldo foi encontrado para esta viagem!");


            if (viagem.Unidade == EUnidadeMedida.Peso && pesoChegada == null || pesoChegada < 1)
                throw new Exception("Informe um peso de chegada válido para continuar!");

            var protocoloEvento = viagemEvento.ProtocoloEventos.FirstOrDefault(x => x.Status != EStatusProtocoloEvento.Rejeitado);

            if (!pesoChegada.HasValue && viagem.Unidade == EUnidadeMedida.Peso)
                pesoChegada = 0;

            decimal pesoChegadaReal = pesoChegada.HasValue ? pesoChegada.Value : Decimal.Zero;
            decimal sacas = numeroSacas ?? 0;
            if (viagem.Unidade == EUnidadeMedida.Saca)
            {
                if (pesoChegada < 1)
                {
                    if (viagem.Quantidade < 1)
                        throw new Exception("Não é possível calcular os valores sem que a quantidade de sacas tenha sido informada na viagem.");
                    if (!numeroSacas.HasValue)
                        throw new Exception("Não é possível calcular os valores sem que o número de sacas entregues seja informada.");

                    var difSacas = viagem.Quantidade - numeroSacas.Value;
                    var pesoPorSaca = viagem.PesoSaida / viagem.Quantidade;
                    pesoChegadaReal = viagem.PesoSaida - (pesoPorSaca * difSacas) ?? 0;
                }
                if (sacas < 1 && viagem.PesoSaida.HasValue)
                {
                    decimal pesoPorSaca = viagem.PesoSaida.Value / viagem.Quantidade;
                    if (pesoPorSaca > 0)
                        sacas = pesoChegadaReal / pesoPorSaca;
                }
            }

            var pesoDiferenca = viagem.PesoSaida - pesoChegadaReal;
            var pesoDiferencaTolerada = viagem.PesoSaida * viagemRegra.ToleranciaPeso / 100;
            var valorPorKg = viagem.ValorMercadoria / viagem.PesoSaida;

            decimal? valorQuebraMercadoria = 0;
            if (pesoDiferencaTolerada < pesoDiferenca)
            {
                switch (viagemRegra.TipoQuebraMercadoria)
                {
                    case ETipoQuebraMercadoria.Diferenca:
                        valorQuebraMercadoria = Math.Round((pesoDiferencaTolerada - pesoDiferenca) * valorPorKg ?? 0, 2, MidpointRounding.AwayFromZero);
                        break;
                    case ETipoQuebraMercadoria.Integral:
                        valorQuebraMercadoria = Math.Round((pesoChegadaReal - viagem.PesoSaida) * valorPorKg ?? 0, 2, MidpointRounding.AwayFromZero);
                        break;
                    default:
                        throw new ArgumentOutOfRangeException("Tipo de calculo inesperado para quebra: " + viagemRegra.TipoQuebraMercadoria);
                }
            }
            var pagamentoFreteConfiguracao = _pagamentoConfiguracaoRepository.FirstOrDefault(x => x.IdEmpresa == viagem.IdEmpresa && x.Ativo);

            var valorReceberPeloPesoSaida = Math.Round(viagem.PesoSaida.Value * viagemRegra.TarifaTonelada.GetValueOrDefault() / 1000, 2, MidpointRounding.AwayFromZero);
            var valorReceberPeloPesoChegada = Math.Round(pesoChegada.Value * viagemRegra.TarifaTonelada.GetValueOrDefault() / 1000, 2, MidpointRounding.AwayFromZero);
            var diferencaFreteMotorista = valorReceberPeloPesoChegada - valorReceberPeloPesoSaida;


            if (pagamentoFreteConfiguracao != null && !pagamentoFreteConfiguracao.BonificarMotorista && pesoChegadaReal > viagem.PesoSaida)
                diferencaFreteMotorista = 0;
            bool difFreteMotoristaPassivo = diferencaFreteMotorista < 0;

            var TotalPagar = viagem.ViagemEventos.Where(x => x.Status != EStatusViagemEvento.Baixado && x.TipoEventoViagem != ETipoEventoViagem.Saldo).Sum(x => x.ValorPagamento);

            var pagamentoSolicitacaoAbono = _viagemSolicitacaoAbonoRepository
              .FirstOrDefault(x => x.IdViagemEvento == protocoloEvento.IdViagemEvento && x.Status != EStatusAbono.Cancelado);

            var evento = new PagamentoFreteEventoModel
            {
                PesoChegada = pesoChegadaReal,
                QuantidadeSacas = sacas,
                DataEmissao = DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                DifFreteMotorista = decimal.Round(Math.Abs(diferencaFreteMotorista), 2, MidpointRounding.AwayFromZero),
                QuebraMercadoria = decimal.Round(Math.Abs((decimal) valorQuebraMercadoria), 2, MidpointRounding.AwayFromZero),
                ValorPagamento = protocoloEvento.ValorPagamento ?? 0,
                ValorPagamentoBloqueado = viagemEvento.Status == EStatusViagemEvento.Aberto ||
                                          viagemEvento.Status == EStatusViagemEvento.Bloqueado,
                ValorTotalPagamentoBaixado = viagemEvento.Status == EStatusViagemEvento.Baixado,
                PagamentoParametrosCalculo = new PagamentoParametrosCalculo()
                {
                    ValorKG = valorPorKg,
                    Tarifa = Math.Round(((viagemRegra.TarifaTonelada ?? 0) / 1000), 6),
                    Tolerancia = (viagemRegra.ToleranciaPeso ?? 0),
                    Falta = viagemRegra.TipoQuebraMercadoria == ETipoQuebraMercadoria.Diferenca
                        ? Math.Round(pesoDiferenca ?? 0 - pesoDiferencaTolerada ?? 0, 2)
                        : pesoDiferenca,
                    PesoDiferencaTolerada = Math.Round(pesoDiferencaTolerada ?? 0, 2),
                    Diferenca = pesoDiferenca,
                    PesoSaida = viagem.PesoSaida
                },
                PagamentoSolicitacaoAbono = pagamentoSolicitacaoAbono != null ? new PagamentoSolicitacaoAbono()
                {
                    IdViagemSolicitacaoAbono = pagamentoSolicitacaoAbono.IdViagemSolicitacao,
                    Status = pagamentoSolicitacaoAbono.Status
                } : null,
                DifFreteMotoristaPassivo = difFreteMotoristaPassivo,
                TipoEvento = (int)viagemEvento.TipoEventoViagem
            };
            var totalDescontar = viagemEvento.QuebraMercadoriaAbonada ?? false ? diferencaFreteMotorista : ((valorQuebraMercadoria ?? 0) + diferencaFreteMotorista);
            decimal outrosDescontos = 0;
            if (viagemEvento.ViagemValoresAdicionais != null && viagemEvento.ViagemValoresAdicionais.Any())
            {
                foreach (var valoresAdicionais in viagemEvento.ViagemValoresAdicionais)
                {
                    switch (valoresAdicionais.Tipo)
                    {
                        case ETipoValorAdicional.Acrescimo:
                            outrosDescontos += valoresAdicionais.Valor;
                            break;
                        case ETipoValorAdicional.Desconto:
                            outrosDescontos -= valoresAdicionais.Valor;
                            break;
                    }
                }
            }
            if (viagemEvento.ViagemValoresAdicionais != null && viagemEvento.ViagemValoresAdicionais.Any())
            {
                evento.TotalOutrosAcrescimos = viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo).Sum(x => x.Valor);
                evento.TotalOutrosDescontos = viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Desconto).Sum(x => x.Valor);
            }
            evento.ValorTotalPagamento = evento.ValorPagamento + totalDescontar + outrosDescontos;
            evento.TotalPagar = TotalPagar + evento.ValorTotalPagamento;

            return evento;
        }

        public PagamentoFreteEventoModel ConsultarEvento(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");

            var viagemEvento = _viagemEventoRepository
                .Include(t => t.Motivo)
                .FirstOrDefault(x => x.Status != EStatusViagemEvento.Cancelado && x.Token == token);

            if (viagemEvento == null)
                return new PagamentoFreteEventoModel();

            var viagem = _viagemRepository
                .Include(t => t.ViagemEventos)
                .Include(x => x.ViagemEventos.Select(ve => ve.ViagemValoresAdicionais))
                .FirstOrDefault(t => t.IdViagem == viagemEvento.IdViagem);

            if (viagem == null)
                throw new Exception("Viagem não encontrada!");

            if (viagem.ViagemEventos == null || !viagem.ViagemEventos.Any())
                throw new Exception("Nenhum evento encontrado para esta viagem!");

            var protEvento = _protocoloEventoRepository.FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento);


            var valido = true;
            var validationMessage = "";
            if (viagemEvento.DataValidade.HasValue)
            {
                if (viagemEvento.DataValidade.Value < DateTime.Now)
                {
                    valido = false;
                    validationMessage = "A data de validade para o pagamento deste evento já expirou. ";
                }
            }

            decimal descontos = 0;
            if (viagemEvento.ViagemValoresAdicionais != null)
                descontos += (viagemEvento.ViagemValoresAdicionais).Where(x => x.Tipo == ETipoValorAdicional.Desconto).Sum(outrosValores => outrosValores.Valor);

            decimal acrescimos = 0;
            if (viagemEvento.ViagemValoresAdicionais != null)
                acrescimos += (viagemEvento.ViagemValoresAdicionais).Where(x => x.Tipo == ETipoValorAdicional.Acrescimo).Sum(outrosValores => outrosValores.Valor);

            PagamentoParametrosCalculo parametros = null;
            var ccVals = new PagamentoFreteEventoModel();
            if (viagem.PesoChegada != null && viagem.PesoChegada > 0 && viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo)
            {
                ccVals = CalcularValoresViagem(viagemEvento.Token, false, viagem.PesoChegada, 0, EUnidadeMedida.Peso);
                parametros = ccVals?.PagamentoParametrosCalculo;
            }

            var pagamentoSolicitacaoAbono = _viagemSolicitacaoAbonoRepository
                .FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento && x.Status != EStatusAbono.Cancelado);

            decimal valorPedagio = 0;
            if (DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagem))
                valorPedagio = viagem.ValorPedagio;

            var eventBaixadoHasDesconto = viagemEvento.Status == EStatusViagemEvento.Baixado && (protEvento?.DescricaoMotivoDesconto != null && protEvento.DescricaoMotivoDesconto.Length > 0);

            return new PagamentoFreteEventoModel
            {
                IdViagemEvento = viagemEvento.IdViagemEvento,
                TipoEventoViagem = viagemEvento.TipoEventoViagem,
                DataHoraLimiteValidadeToken = viagemEvento.DataValidadeChaveToken,
                ChaveToken = viagemEvento.ChaveToken,
                PesoChegada = protEvento?.PesoChegada ??0,
                DataEmissao = viagemEvento.DataHoraPagamento?.ToString("dd/MM/yyyy HH:mm") ??
                              DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                DifFreteMotorista = Math.Abs(decimal.Round(viagem.DifFreteMotorista ?? 0, 2)),
                DifFreteMotoristaPassivo = viagem.DifFreteMotorista < 0,
                QuebraMercadoria = Math.Abs(decimal.Round(viagem.ValorQuebraMercadoria ?? 0, 2)),
                ValorPagamento = decimal.Round(viagemEvento.ValorPagamento, 2),
                ValorTotalPagamento = eventBaixadoHasDesconto && protEvento.ValorTotalPagamento != 0 ? decimal.Round(protEvento.ValorTotalPagamento.Value, 2) : decimal.Round(viagemEvento.ValorTotalPagamento ?? viagemEvento.ValorPagamento, 2),
                ValorTotalPagamentoCalculado = viagemEvento.ValorTotalPagamentoCalculado ?? 0,
                QuebraMercadoriaCalculada = viagem.ValorQuebraMercadoriaCalculado ?? 0,
                ValorPedagioParaIncluirPagamento = valorPedagio,
                ValorPagamentoBloqueado = viagemEvento.Status == EStatusViagemEvento.Aberto ||
                                          viagemEvento.Status == EStatusViagemEvento.Bloqueado,
                ValorTotalPagamentoBaixado = viagemEvento.Status == EStatusViagemEvento.Baixado,
                DataValidade = viagemEvento.DataValidade?.ToString("dd/MM/yyyy HH:mm"),
                PossuiOutrosValores = viagemEvento?.ViagemValoresAdicionais?.Any() ?? false,
                Valido = valido,
                PagamentoParametrosCalculo = parametros,
                ValidationMessage = validationMessage,
                TotalOutrosDescontos = descontos,
                TotalOutrosAcrescimos = acrescimos,
                AbonoRequisitado = viagemEvento.QuebraMercadoriaAbonada ?? true,
                AbonoDocument = viagemEvento.TokenAnexoAbono,
                PagamentoSolicitacaoAbono = pagamentoSolicitacaoAbono != null ? new PagamentoSolicitacaoAbono()
                {
                    IdViagemSolicitacaoAbono = pagamentoSolicitacaoAbono.IdViagemSolicitacao,
                    Status = pagamentoSolicitacaoAbono.Status
                } : null,
                PesoChegadaOriginal = viagem.PesoChegadaOriginal,
                PesoChegadaAlterado = viagem.PesoChegadaOriginal.HasValue && viagem.PesoChegadaOriginal > 0 && viagem.PesoChegada != viagem.PesoChegadaOriginal && viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo,
                DescricaoMotivo = viagemEvento.Motivo?.Descricao
            };
        }

        public List<PagamentoFreteValorAdicionalModel> ConsultarOutrosDescontos(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");


            var viagemEvento = _viagemEventoRepository
                .Include(x => x.ViagemValoresAdicionais)
                .FirstOrDefault(t => t.Token == token);

            if (viagemEvento == null)
                throw new Exception("Evento não encontrado para esta viagem!");

            if (viagemEvento.ViagemValoresAdicionais == null || !viagemEvento.ViagemValoresAdicionais.Any())
                return new List<PagamentoFreteValorAdicionalModel>();

            return viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Desconto).Select(t => new PagamentoFreteValorAdicionalModel()
            {
                NumeroDocumento = t.NumeroDocumento,
                Descricao = t.Descricao,
                Valor = decimal.Round(t.Valor, 2)
            }).ToList();
        }

        public List<PagamentoFreteValorAdicionalModel> ConsultarOutrosAcrescimos(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new Exception("Token do documento não encontrado!");


            var viagemEvento = _viagemEventoRepository
                .Include(x => x.ViagemValoresAdicionais)
                .FirstOrDefault(t => t.Token == token);

            if (viagemEvento == null)
                throw new Exception("Evento não encontrado para esta viagem!");

            if (viagemEvento.ViagemValoresAdicionais == null || !viagemEvento.ViagemValoresAdicionais.Any())
                return new List<PagamentoFreteValorAdicionalModel>();

            return viagemEvento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo).Select(t => new PagamentoFreteValorAdicionalModel()
            {
                NumeroDocumento = t.NumeroDocumento,
                Descricao = t.Descricao,
                Valor = decimal.Round(t.Valor, 2)
            }).ToList();
        }

        public List<PagamentoFreteAnexoModel> ConsultarAnexos(string token, bool? estabelecimentoObrigaDocumentosPagamento = null, int? idUsuarioLogado = null)
        {
            if (!estabelecimentoObrigaDocumentosPagamento.HasValue)
                estabelecimentoObrigaDocumentosPagamento = true;

            var retorno = new List<PagamentoFreteAnexoModel>();

            var vgEvento = _viagemEventoRepository
                .Include(x => x.ViagemDocumentos)
                .FirstOrDefault(t => t.Token == token);

            if (vgEvento == null)
                throw new Exception($"Evento não encontrado!");

            var viagem = _viagemRepository.FirstOrDefault(x => x.IdViagem == vgEvento.IdViagem);

            var cfgQuery = _pagamentoConfiguracaoRepository.Where(x => x.IdEmpresa == viagem.IdEmpresa && x.Ativo);

            if (viagem.IdFilial != null)
            {
                cfgQuery = cfgQuery.Where(c => c.IdFilial == viagem.IdFilial);
            }

            var cfg = cfgQuery.FirstOrDefault();

            AtualizarViagemDocumentosObrigatorios(cfg, vgEvento);

            // BUscamos novamente para trazer a entidades filhas atualizadas
            if (cfg != null)
            {
                vgEvento = _viagemEventoRepository.FirstOrDefault(x => x.Token == token);
                viagem = _viagemRepository.FirstOrDefault(t => t.IdViagem == vgEvento.IdViagem);
            }

            var usuario = idUsuarioLogado != null ? _usuarioRepository
                .Get(idUsuarioLogado.Value) : null;

            bool usuarioMatriz = usuario != null && usuario.Perfil == EPerfil.Empresa && (usuario.Matriz??false);

            bool empresaUtilizaValidacao = false;
            if (usuario != null)
                empresaUtilizaValidacao = (_empresaRepository.Get(usuario.IdEmpresa ?? 0)
                                               ?.UtilizaValidacaoPorPerfilNoPagamentoDeFrete) ?? false;

            // Tem eventos.. Então devemos buscar os documentos de seus eventos para mostra-los na grade de anexos
            vgEvento.ViagemDocumentos.ToList().ForEach(vd => {


                bool obrigaDocumento;

                if (empresaUtilizaValidacao)
                    if (usuarioMatriz)
                        obrigaDocumento = vd.ObrigaAnexoMatriz;
                    else
                        obrigaDocumento = usuario?.Perfil == EPerfil.Empresa ? vd.ObrigaAnexoFilial : vd.ObrigaAnexo;
                else
                    obrigaDocumento = vd.ObrigaAnexo;

                retorno.Add(new PagamentoFreteAnexoModel
                {
                    Descricao = vd.Descricao,
                    IdViagemDocumento = vd.IdViagemDocumento,
                    IdViagemEvento = vd.IdViagemEvento,
                    NumeroDocumento = vd.IdDocumento.HasValue ? 0 : vd.NumeroDocumento,
                    TipoDocumento = vd.TipoDocumento,
                    TipoEvento = vd.TipoEvento,
                    ObrigaAnexo = vd.ObrigaDocOriginal || obrigaDocumento && estabelecimentoObrigaDocumentosPagamento.Value,
                    StatusOk = !string.IsNullOrEmpty(vd.TokenAnexo),
                    TokenAnexo = vd.TokenAnexo,
                    IdDocumento = vd.IdDocumento,
                    ObrigaDocOriginal = vd.ObrigaDocOriginal
                });
            });

            return retorno;
        }

        private void AtualizarViagemDocumentosObrigatorios(PagamentoConfiguracao pagCfg, ViagemEvento eventoViagem)
        {
            //Para cada documento obrigatorio na tela de configuração para pagamento de frete
            //um novo viagem_documento obrigatório será feito

            // Só faz isto caso este evento não tenha nenhum viagem_documento integrado = IdDocumento == null
            if (pagCfg != null && eventoViagem != null &&
                !eventoViagem.ViagemDocumentos.Any(p => p.IdDocumento == null))
            {
                var cfgProcs = _pagamentoConfiguracaoProcessoRepository
                    .Find(x => x.IdConfiguracao == pagCfg.IdPagamentoConfiguracao &&
                               x.Processo == EProcessoPgtoFrete.PagamentoFrete)
                    .Include(y => y.Documento).ToList();

                if (cfgProcs.Any())
                {
                    var idsDocumentosObrigatorios = cfgProcs.Select(x => x.IdDocumento).ToList();

                    var idsViagemDocumentoParaDeletar = new List<int>();

                    cfgProcs.ForEach(cfg =>
                    {
                        // Para cada documento obrigatório...
                        var viagemDocumento =
                            eventoViagem.ViagemDocumentos.FirstOrDefault(x => x.IdDocumento == cfg.IdDocumento);

                        // Agora devemos excluir os VIAGEM_DOCUMENTO com os iddocumento q
                        // foram excluidos da configuração de documentos obrigatorios do pagamento de frete
                        idsViagemDocumentoParaDeletar.AddRange(eventoViagem.ViagemDocumentos
                            .Where(p => !idsDocumentosObrigatorios.Contains(p.IdDocumento.Value))
                            .Select(x => x.IdViagemDocumento).ToList());

                        // Se não existir nenhum VIAGEM_DOCUMENTO com este IdDocumento, então inserimos uma nova VIAGEM_DOCUMENTO
                        if (viagemDocumento == null)
                            _viagemDocumentoRepository.Add(new ViagemDocumento
                            {
                                IdViagemEvento = eventoViagem.IdViagemEvento,
                                NumeroDocumento = 0,
                                TipoDocumento = ETipoDocumento.Outros,
                                IdDocumento = cfg.IdDocumento,
                                ObrigaAnexo = true,
                                ObrigaDocOriginal = cfg.Documento.ObrigaDocOriginal,
                                Descricao = cfg.Documento.Descricao,
                                TipoEvento = eventoViagem.TipoEventoViagem,
                                TokenAnexo = null
                            });
                    });


                    // Agora vamos deletar os documentos q estão no VIAGEM_DOCUMENTO mas que foram removidos da tela de configuração como obrigatórios
                    idsViagemDocumentoParaDeletar = idsViagemDocumentoParaDeletar.Distinct().ToList();
                    if (idsViagemDocumentoParaDeletar.Distinct().Any())
                    {
                        var toDelete = new List<ViagemDocumento>();

                        idsViagemDocumentoParaDeletar.ForEach(a => toDelete.Add(
                            _viagemDocumentoRepository.FirstOrDefault(x => x.IdViagemDocumento == a)));

                        // Apaga um a um..
                        if (toDelete.Any()) toDelete.ForEach(z => _viagemDocumentoRepository.Delete(z));
                    }
                }
            }
        }

        public void VincularAnexoMidiaAoViagemDocumento(string tokenMidia, int idViagemDocumento)
        {
            var vgDoc = _viagemDocumentoRepository.FirstOrDefault(x => x.IdViagemDocumento == idViagemDocumento);
            if (vgDoc == null)
                throw new Exception($"Nenhum viagem documento encontrado para o id {idViagemDocumento}");

            if (vgDoc.TokenAnexo != null && !string.IsNullOrEmpty(vgDoc.TokenAnexo))
                try
                {
                    // Antes de atrelar o anexo novo, devemos apagar o antigo do banco de Media para encomizar dados de disco
                    _dataMediaServerService.DeleteByToken(vgDoc.TokenAnexo);
                }
                catch (Exception)
                {
                    // ignored
                }

            vgDoc.TokenAnexo = tokenMidia;

            _viagemDocumentoRepository.Update(vgDoc);
        }


        public bool RemoverAnexo(string token_)
        {

            if (!string.IsNullOrEmpty(token_))
            {
                try
                {
                    // Antes de atrelar o anexo novo, devemos apagar o antigo do banco de Media para encomizar dados de disco
                    _dataMediaServerService.DeleteByToken(token_);
                }
                catch (Exception e)
                {
                    _logger.Error($"{e.Message} : {e.InnerException.ToString()}");
                    return false;
                }
            }

            return true;
        }

        public void RemoverAnexo(int idViagemDocumento)
        {
            var vgDoc = _viagemDocumentoRepository.FirstOrDefault(x => x.IdViagemDocumento == idViagemDocumento);
            if (vgDoc == null)
                throw new Exception($"Nenhum viagem documento encontrado para o id {idViagemDocumento}");

            if (vgDoc.TokenAnexo != null && !string.IsNullOrEmpty(vgDoc.TokenAnexo))
            {
                RemoverAnexo(vgDoc.TokenAnexo);

                vgDoc.TokenAnexo = null;

                _viagemDocumentoRepository.Update(vgDoc);
            }
        }

        public bool EventoDocumentosObrigatoriosAnexados(ViagemEvento vgEvent, Viagem vg, List<Estabelecimento> estabelecimentos, int? idUsuarioLogado= null)
        {
            var cfg = _pagamentoConfiguracaoRepository
                .FirstOrDefault(x => x.IdEmpresa == vg.IdEmpresa && x.IdFilial == vg.IdFilial && x.Ativo);

            AtualizarViagemDocumentosObrigatorios(cfg, vgEvent);

            var usuario  = _usuarioRepository.GetWithRelationships(idUsuarioLogado ?? 0 );


            var empresa = _empresaRepository.Get(usuario?.IdEmpresa ?? 0);

            if (empresa != null && (empresa.UtilizaValidacaoPorPerfilNoPagamentoDeFrete?? false))
            {
                bool isMatriz = (usuario.Perfil == EPerfil.Empresa && (usuario.Matriz?? false));

                if (isMatriz)
                    return vgEvent.ViagemDocumentos.Any(x => x.ObrigaAnexoMatriz && x.TokenAnexo == null);
                else if (usuario.Perfil == EPerfil.Empresa)
                    return vgEvent.ViagemDocumentos.Any(x => x.ObrigaAnexoFilial && x.TokenAnexo == null);
            }


            return vgEvent.ViagemDocumentos.Any(x => x.ObrigaAnexo && x.TokenAnexo == null) && estabelecimentos.Any(y => y.ObrigaDocumentosPagamento);

        }

        public List<Protocolo> ConsultarProtocolosProcessados(int idEmpresa, int? idProtocolo, bool includeEstabelecimentoBase = false)
        {
            var retorno = _protocoloRepository.Consultar(idEmpresa, includeEstabelecimentoBase);

            if (idProtocolo.HasValue)
                retorno = retorno.Where(x => x.IdProtocolo == idProtocolo.Value);

            return retorno.ToList();
        }

        public List<Protocolo> ConsultarProtocolosPorEstabelecimentos(List<int> idsEstabelecimentoBase, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, int? idProtocolo)
        {
            var retorno = _protocoloRepository.ConsultarProtocolosPorEstabelecimentos(idsEstabelecimentoBase, dataPagamentoInicial, dataPagamentoFinal, dataGeracaoInicial, dataGeracaoFinal);

            if (idProtocolo.HasValue)
                retorno = retorno.Where(x => x.IdProtocolo == idProtocolo.Value);

            return retorno.ToList();
        }

        public ViagemEvento AbonarViagemEvento(ViagemEvento viagemEvento_, string token_)
        {
            viagemEvento_.QuebraMercadoriaAbonada = true;
            //viagemEvento_.Token = token_;

            _viagemEventoRepository.Update(viagemEvento_);

            return viagemEvento_;
        }

        public ViagemSolicitacaoAbono SolicitarAbono(int IdViagemEvento, int? IdMotivo, string Detalhamento, int IdUsuario)
        {

            var viagemEvento = _viagemEventoRepository.FirstOrDefault(x => x.IdViagemEvento == IdViagemEvento);

            var viagemSolicitacao = _viagemSolicitacaoAbonoRepository
                 .Include(x => x.ViagemEvento)
                .FirstOrDefault(x => x.IdViagemEvento == IdViagemEvento && x.Status != EStatusAbono.Cancelado);


            #region Validations

            if (viagemSolicitacao != null)
                throw new Exception("Já existe uma solicitação de abono pendente!");

            if (viagemEvento != null && viagemEvento.Status != EStatusViagemEvento.Aberto)
                throw new Exception("Não é permitido solicitar um abono para um evento " + EnumHelpers.GetDescription(viagemEvento.Status)?.ToLower() + "!");

            if (viagemEvento != null && viagemEvento.TipoEventoViagem != ETipoEventoViagem.Saldo)
                throw new Exception("Não é permitido solicitar um abono para um evento do tipo " + EnumHelpers.GetDescription(viagemEvento.TipoEventoViagem)?.ToLower() + "!");


            #endregion

            var motivo = _motivoCredenciamentoService.Get(IdMotivo.Value);
            var usuario = _usuarioRepository.GetWithRelationships(IdUsuario);

            ViagemSolicitacaoAbono solicitacao = new ViagemSolicitacaoAbono()
            {
                IdViagemEvento = IdViagemEvento,
                Status = EStatusAbono.Solicitado,
                DataSolicitacao = DateTime.Now,
                IdMotivo = IdMotivo,
                DescricaoMotivo = motivo.Descricao,
                Detalhamento = Detalhamento,
                IdUsuario = IdUsuario,
                NomeUsuario = usuario.Nome
            };

            _viagemSolicitacaoAbonoRepository.Add(solicitacao);

            viagemEvento.QuebraMercadoriaAbonada = true;
            _viagemEventoRepository.Update(viagemEvento);

            return solicitacao;
        }

        public void CancelarSolicitacao(string token)
        {

            var viagemSolicitacao = _viagemSolicitacaoAbonoRepository
                .Include(x => x.ViagemEvento)
                .FirstOrDefault(x => x.ViagemEvento.Token == token && x.Status == EStatusAbono.Solicitado);
            if (viagemSolicitacao == null)
                throw new Exception("Nenhuma solicitação encontrada!");

            if (viagemSolicitacao.ViagemEvento.Status == EStatusViagemEvento.Baixado)
                throw new Exception("Não é possível cancelar um abono de um evento baixado!");

            viagemSolicitacao.Status = EStatusAbono.Cancelado;

            _viagemSolicitacaoAbonoRepository.Update(viagemSolicitacao);

            var viagemEvento = _viagemEventoRepository.FirstOrDefault(x => x.Token == token);

            viagemEvento.QuebraMercadoriaAbonada = false;

            _viagemEventoRepository.Update(viagemEvento);


        }

        public object ConsultarTotalPagamentosCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int page_, int take_, int idempresa)
        {
            var dapper_ = _pagamentoDapper;

            var totalItens = dapper_.CountTotalCurvaABC(dataInicio_, dataFim_, UF_, idempresa);
            var items = dapper_.ConsultarTotalCurvaABC(dataInicio_, dataFim_, UF_, page_, take_, idempresa)
                .ToList();

            double A = Math.Ceiling((double)(totalItens * (filtroA_ / 100)));
            double B = Math.Ceiling((double)(totalItens * (filtroB_ / 100)));
            double C = Math.Ceiling((double)(totalItens * (filtroC_ / 100)));

            int count_ = 0;

            //Setrow color to front-end interaction
            for (int i = 0; (i < A) && (count_ < items.Count()); i++)
            {
                items[count_].Color = "green";
                count_++;
            }


            for (int i = 0; (i < B) && (count_ < items.Count()); i++)
            {
                items[count_].Color = "yellow";
                count_++;
            }

            for (int i = 0; (i < C) && (count_ < items.Count()); i++)
            {
                items[count_].Color = "red";
                count_++;
            }

            return new
            {
                totalItems = totalItens,
                items = items
            };
        }

        public byte[] GerarRelatorioGrid(DateTime dataInicial, DateTime dataFinal, string uf, double? a, double? b, double? c, string tipoArquivo, string logo, int idempresa)
        {
            var periodo = $"{dataInicial.ToShortDateString()} à {dataFinal.ToShortDateString()}";
            var dapper = _pagamentoDapper;

            var totalItens = dapper.CountTotalCurvaABC(dataInicial, dataFinal, uf, idempresa);
            var itens = dapper.ConsultarTotalCurvaABC(dataInicial, dataFinal, uf, 1, int.MaxValue, idempresa);

            var listaDados = itens.Select(item => new RelatorioCurvaAbcDataType
            {
                Bairro = item.Bairro,
                Cidade = item.Cidade,
                Cnpj = item.CNPJ.ToCNPJFormato(),
                NomeFantasia = item.NomeFantasia,
                Ranking = $"{item.Ranking.ToString()}º",
                Uf = item.UF,
                Valor = $"R$ {item.Valor:N}",
            }).ToList();

            var filtroA = Math.Ceiling((double)(totalItens * (a / 100)));
            var filtroB = Math.Ceiling((double)(totalItens * (b / 100)));
            var filtroC = Math.Ceiling((double)(totalItens * (c / 100)));

            var count = 0;

            for (var i = 0; i < filtroA && count < listaDados.Count; i++)
            {
                listaDados[count].Cor = "green";
                count++;
            }

            for (var i = 0; i < filtroB && count < listaDados.Count; i++)
            {
                listaDados[count].Cor = "yellow";
                count++;
            }

            for (var i = 0; i < filtroC && count < listaDados.Count; i++)
            {
                listaDados[count].Cor = "red";
                count++;
            }

            return new RelatorioCurvaAbc().GetReport(listaDados, tipoArquivo, logo, periodo, uf, a.ToString(),
                b.ToString(), c.ToString());
        }

        public object ConsultarGraficosSumPorEstabelecimento(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int idempresa)
        {
            var dapper_ = _pagamentoDapper;

            var totalItens = dapper_.CountTotalCurvaABC(dataInicio_, dataFim_, UF_, idempresa);

            double A = Math.Ceiling((double)(totalItens * (filtroA_ / 100)));
            double B = Math.Ceiling((double)(totalItens * (filtroB_ / 100)));
            double C = Math.Ceiling((double)(totalItens * (filtroC_ / 100)));


            decimal[] totais_ = new decimal[3];

            totais_[0] = dapper_.SumValorTotalABC(dataInicio_, dataFim_, UF_, A, 0, idempresa);
            totais_[1] = dapper_.SumValorTotalABC(dataInicio_, dataFim_, UF_, A + B, A, idempresa);
            totais_[2] = dapper_.SumValorTotalABC(dataInicio_, dataFim_, UF_, A + B + C, (A + B), idempresa);

            return totais_;

        }

        public object ConsultarPagamentosEstabelecimentoGrid(DateTime dataInicio_, DateTime dataFim_, int idEstabelecimentoBase, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var pagamentos = ConsultarPagamentosEstabelecimentoGridAndReport(dataInicio_, dataFim_,
                idEstabelecimentoBase, orderFilters, filters);

            return new
            {
                totalItems = pagamentos.Count(),
                items = pagamentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    TipoEventoViagem = x.TipoEventoViagem.DescriptionAttr(),
                    ValorTotalPagamento = x.ValorTotalPagamento?.ToString("N"),
                    DataHoraPagamento = x.DataHoraPagamento?.ToString("G"),
                    x.IdViagem,
                    x.Viagem.Placa,
                    x.Viagem.NomeMotorista
                })
            };
        }

        public byte[] ConsultarPagamentosEstabelecimentoReport(DateTime? dataInicio, DateTime? dataFim, int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var listaDados = new List<RelatorioDetalhesCurvaAbcDataType>();
            var itens = ConsultarPagamentosEstabelecimentoGridAndReport(dataInicio, dataFim, idEstabelecimentoBase, orderFilters, filters);

            foreach (var item in itens)
            {
                listaDados.Add(new RelatorioDetalhesCurvaAbcDataType
                {
                    Valor = $"R$ {item.ValorTotalPagamento:N}",
                    DataPagamento = item.DataHoraPagamento.ToString(),
                    Motorista = item.Viagem.NomeMotorista,
                    Viagem = item.IdViagem.ToString(),
                    Placa = item.Viagem.Placa.ToPlacaFormato(),
                    TipoEvento = item.TipoEventoViagem.DescriptionAttr()
                });
            }

            return new RelatorioDetalhesCurvaAbc().GetReport(listaDados, tipoArquivo, logo);
        }

        public IQueryable<ViagemEvento> ConsultarPagamentosEstabelecimentoGridAndReport(DateTime? dataInicio_, DateTime? dataFim_, int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var pagamentos = _viagemEventoRepository.All()
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoBase.Estado)
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemEventos);


            pagamentos = pagamentos.Where(x => x.DataHoraPagamento >= dataInicio_
                                               && x.DataHoraPagamento <= dataFim_
                                               && x.IdEstabelecimentoBase == idEstabelecimentoBase);


            pagamentos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? pagamentos.OrderBy(x => x.IdViagemEvento)
                : pagamentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            pagamentos = pagamentos.AplicarFiltrosDinamicos(filters);

            return pagamentos;
        }

        public object ConsultarGridDetalhesAberto(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            var pagamentos = ConsultarGridDetalhesAbertoToGridAndReport(dataInicio_, dataFim_, HabilitarPagamentoCartao,
                UF, idEstabelecimentoBase, TipoEventoViagem, orderFilters, filters, idEmpresa);

            return new
            {
                totalItems = pagamentos.Count(),
                items = pagamentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    TipoEventoViagem = x.TipoEventoViagem.DescriptionAttr(),
                    NumeroDocumento = x.Viagem.NumeroDocumento,
                    ValorPagamento = x.ValorPagamento.ToString("N"),
                    DataEmissao = x.Viagem.DataEmissao?.ToString("G"),
                    x.Viagem.Placa,
                    x.Viagem.NomeMotorista
                })
            };
        }

        public byte[] GerarRelatorioDetalhesProvisaoAberto(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa)
        {
            var dataInicial = dataInicio.StartOfDay();
            var dataFinal = dataFim.EndOfDay();

            var listaDados = new List<RelatorioProvisaoPagamentoDataType>();
            var itens = ConsultarGridDetalhesAbertoToGridAndReport(dataInicial, dataFinal, habilitarPagamentoCartao, uf,
                idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters, idEmpresa);

            foreach (var item in itens)
            {
                listaDados.Add(new RelatorioProvisaoPagamentoDataType
                {
                    Valor = $"R$ {item.ValorPagamento:N}",
                    Placa = item.Viagem.Placa.ToPlacaFormato(),
                    TipoEvento = item.TipoEventoViagem.DescriptionAttr(),
                    Motorista = item.Viagem.NomeMotorista,
                    DataEmissao = item.Viagem.DataEmissao.ToString(),
                    NumeroDocumento = item.Viagem.NumeroDocumento
                });
            }

            var tipoEvento = tipoEventoViagem.HasValue ? tipoEventoViagem.DescriptionAttr() : "Total";

            var titulo = habilitarPagamentoCartao
                ? $"{tipoEvento} - Aberto Meio Homologado"
                : $"{tipoEvento} - Aberto Carta Frete";

            return new RelatorioProvisaoPagamento().GetReport(listaDados, tipoArquivo, logo, titulo, "Data de emissão", false);
        }

        public object ConsultarGridDetalhesBaixado(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            var pagamentos = ConsultarGridDetalhesBaixadoToGridAndReport(dataInicio_, dataFim_,
                HabilitarPagamentoCartao, UF, idEstabelecimentoBase, TipoEventoViagem, orderFilters, filters, idEmpresa);

            var objeto = new
            {
                totalItems = pagamentos.Count(),
                items = pagamentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    TipoEventoViagem = x.TipoEventoViagem.DescriptionAttr(),
                    NumeroDocumento = x.Viagem?.NumeroDocumento ?? string.Empty,
                    ValorTotalPagamento = x.ValorTotalPagamento?.ToString("N"),
                    DataHoraPagamento = x.DataHoraPagamento?.ToString("G"),
                    Estabelecimento = x.EstabelecimentoBase?.Descricao ?? string.Empty,
                    Placa = x.Viagem?.Placa ?? string.Empty,
                    NomeMotorista = x.Viagem?.NomeMotorista ?? string.Empty
                })
            };

            return objeto;
        }

        public byte[] GerarRelatorioDetalhesProvisaoBaixado(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa)
        {
            var dataInicial = dataInicio.StartOfDay();
            var dataFinal = dataFim.EndOfDay();

            var listaDados = new List<RelatorioProvisaoPagamentoDataType>();
            var itens = ConsultarGridDetalhesBaixadoToGridAndReport(dataInicial, dataFinal, habilitarPagamentoCartao, uf,
                idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters, idEmpresa);

            foreach (var item in itens)
            {
                listaDados.Add(new RelatorioProvisaoPagamentoDataType
                {
                    Valor = $"R$ {item.ValorPagamento:N}",
                    Placa = item.Viagem.Placa.ToPlacaFormato(),
                    TipoEvento = item.TipoEventoViagem.DescriptionAttr(),
                    Motorista = item.Viagem.NomeMotorista,
                    DataEmissao = item.DataHoraPagamento.ToString(),
                    NumeroDocumento = item.Viagem.NumeroDocumento,
                    Estabelecimento = item.EstabelecimentoBase?.Descricao
                });
            }

            var tipoEvento = tipoEventoViagem.HasValue ? tipoEventoViagem.DescriptionAttr() : "Total";

            var titulo = habilitarPagamentoCartao
                ? $"{tipoEvento} - Baixado Meio Homologado"
                : $"{tipoEvento} - Baixado Carta Frete";

            if (string.IsNullOrEmpty(titulo))
                titulo = "Total";

            return new RelatorioProvisaoPagamento().GetReport(listaDados, tipoArquivo, logo, titulo, "Data de pagamento", true);
        }

        public IQueryable<ViagemEvento> ConsultarGridDetalhesAbertoToGridAndReport(DateTime dataInicio_,
            DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase,
            ETipoEventoViagem? TipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            var pagamentos = _viagemEventoRepository.All()
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoBase.Estado)
                .Include(x => x.Viagem);

            pagamentos = pagamentos.Where(x => x.Viagem.DataEmissao >= dataInicio_
                                               && x.Viagem.DataEmissao <= dataFim_
                                               && x.HabilitarPagamentoCartao == HabilitarPagamentoCartao
                                               && x.Status == EStatusViagemEvento.Aberto);

            if (idEmpresa.HasValue && idEmpresa != 0)
                pagamentos = pagamentos.Where(x => x.IdEmpresa == idEmpresa);

            if (idEstabelecimentoBase != null)
            {
                var estabelecimento = _estabelecimentoService.Get(idEstabelecimentoBase.Value, false);

                if (estabelecimento != null)
                    pagamentos = pagamentos.Where(o => o.IdEstabelecimentoBase == estabelecimento.IdEstabelecimentoBase);
            }

            if (TipoEventoViagem != null)
                pagamentos = pagamentos.Where(x => x.TipoEventoViagem == TipoEventoViagem);

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                pagamentos = pagamentos.OrderBy(x => x.IdViagemEvento);
            else
            {
                pagamentos = pagamentos.OrderBy(orderFilters.Campo.ToString() == "NumeroDocumento"
                    ? $"Viagem.NumeroDocumento {orderFilters.Operador.DescriptionAttr()}"
                    : $"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            }

            pagamentos = pagamentos.AplicarFiltrosDinamicos<ViagemEvento>(filters);

            return pagamentos;
        }

        public IQueryable<ViagemEvento> ConsultarGridDetalhesBaixadoToGridAndReport(DateTime dataInicio_,
            DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase,
            ETipoEventoViagem? TipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            var pagamentos = _viagemEventoRepository.All()
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoBase.Estado)
                .Include(x => x.Viagem);

            pagamentos = pagamentos.Where(x => x.DataHoraPagamento >= dataInicio_
                                               && x.DataHoraPagamento <= dataFim_
                                               && x.HabilitarPagamentoCartao == HabilitarPagamentoCartao
                                               && x.Status == EStatusViagemEvento.Baixado);

            if (idEmpresa.HasValue && idEmpresa != 0)
                pagamentos = pagamentos.Where(x => x.IdEmpresa == idEmpresa);

            if (TipoEventoViagem != null)
                pagamentos = pagamentos.Where(x => x.TipoEventoViagem == TipoEventoViagem);

            if (UF != null)
                pagamentos = pagamentos.Where(x => x.EstabelecimentoBase.IdEstado == UF);

            if (idEstabelecimentoBase != null)
            {
                var estabelecimento = _estabelecimentoService.Get(idEstabelecimentoBase.Value, false);

                if (estabelecimento != null)
                    pagamentos = pagamentos.Where(o => o.IdEstabelecimentoBase == estabelecimento.IdEstabelecimentoBase);
            }

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                pagamentos = pagamentos.OrderBy(x => x.IdViagemEvento);
            else
            {
                if (orderFilters.Campo.ToString() == "NumeroDocumento")
                    pagamentos = pagamentos.OrderBy($"Viagem.NumeroDocumento {orderFilters.Operador.DescriptionAttr()}");
                else
                    pagamentos = pagamentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            }

            pagamentos = pagamentos.AplicarFiltrosDinamicos(filters);

            return pagamentos;
        }

        public object GetTotaisPagamentosPorTipo(DateTime dataInicio, DateTime dataFinal, int? UF_,
            EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimento, int? idEmpresa)
        {
            Estabelecimento estabelecimento = null;
            if(idEstabelecimento.HasValue)
                estabelecimento = _estabelecimentoService.Get(idEstabelecimento.Value, false);

            var totais = _pagamentoDapper
                .GetTotalPagamento(dataInicio, dataFinal, UF_, status_, HabilitarPagamentoCartao, estabelecimento?.IdEstabelecimentoBase, idEmpresa);

            var totaisPorDia = _pagamentoDapper
                .GetTotalPagamento(new DateTimeHelper().StartOfDay(DateTime.Now), new DateTimeHelper().EndOfDay(dataFinal), UF_, status_, HabilitarPagamentoCartao, estabelecimento?.IdEstabelecimentoBase, idEmpresa);

            return new
            {
                Adiantamento = new
                {
                    TotalHoje = totaisPorDia.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Adiantamento).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Adiantamento).Sum(x => x.Total ?? 0).ToString("N")
                },
                Saldo = new
                {
                    TotalHoje = totaisPorDia.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Saldo).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Saldo).Sum(x => x.Total ?? 0).ToString("N")
                },
                Estadia = new
                {
                    TotalHoje = totaisPorDia.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Estadia).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Estadia).Sum(x => x.Total ?? 0).ToString("N")
                },
                RPA = new
                {
                    TotalHoje = totaisPorDia.Where(x => x.TipoEventoViagem == ETipoEventoViagem.RPA).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.RPA).Sum(x => x.Total ?? 0).ToString("N")
                },
                TotalANTT = new
                {
                    TotalHoje = totaisPorDia.Where(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).Sum(x => x.Total ?? 0).ToString("N")
                },
                Abono = new
                {
                    TotalHoje = totaisPorDia.Where(o => o.TipoEventoViagem == ETipoEventoViagem.Abono).Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Abono).Sum(x => x.Total ?? 0).ToString("N")
                },
                Pedagio = new
                {
                    TotalHoje = totaisPorDia.Sum(x => x.Pedagio ?? 0).ToString("N"),
                    TotalPeriodo = totais.Sum(x => x.Pedagio ?? 0).ToString("N")
                },
                Total = new
                {
                    TotalHoje = totaisPorDia.Sum(x => x.Total ?? 0).ToString("N"),
                    TotalPeriodo = totais.Sum(x => x.Total ?? 0).ToString("N")
                }
            };
        }

        /// <summary>
        /// Verificar se o evento indicado deve possuir o valor do pedágio somando para apresentar ao operador do posto.
        /// Somente adiantamento, no modelo de pgto Carta Frete e sem a informação de pedágio baixado deve ser incluso.
        /// </summary>
        /// <param name="eventoAdiantamento"></param>
        /// <param name="viagem"></param>
        /// <returns></returns>
        public bool DeveIncluirPedagioJuntoComPagamentoDoEvento(ViagemEvento eventoAdiantamento, Viagem viagem)
        {
            if (eventoAdiantamento.TipoEventoViagem == ETipoEventoViagem.Adiantamento &&
                !eventoAdiantamento.HabilitarPagamentoCartao && !viagem.PedagioBaixado)
                return true;

            return false;
        }

        public List<ViagemEventoByCiotModel> GetByCiot(string numeroCiot)
        {
            if (string.IsNullOrWhiteSpace(numeroCiot))
                throw new Exception($"Necessário informar um CIOT válido.");

            var declaracaoCiotRepository = _declaracaoCiotRepository;

            DeclaracaoCiot ciot;

            var ciotComVerificador = numeroCiot.Split('/');

            if (ciotComVerificador.Length == 2)
            {
                var numero = ciotComVerificador[0];
                var verificador = ciotComVerificador[1];

                ciot = declaracaoCiotRepository.FirstOrDefault(o => o.Ciot == numero && o.Verificador == verificador);
            }
            else
            {
                ciot = declaracaoCiotRepository.FirstOrDefault(o => o.Ciot == numeroCiot);
            }

            if (ciot == null)
                throw new Exception($"CIOT {numeroCiot} não encontrado na base.");

            if (ciot.DataCancelamento.HasValue)
                throw new Exception($"CIOT {numeroCiot} cancelado.");

            var viagemEventos = _viagemEventoRepository
                .Find(o => o.IdViagem == ciot.IdViagem && (o.Status == EStatusViagemEvento.Aberto || o.Status == EStatusViagemEvento.Bloqueado || o.Status  == EStatusViagemEvento.Baixado)).ToList();

            if (!viagemEventos.Any())
                throw new Exception($"Nenhum evento desta viagem se encontra aberto.");

            var objToReturn = viagemEventos.Select(o => new ViagemEventoByCiotModel
            {
                Valor = $"R$ {o.ValorPagamento:N}",
                TipoEvento = o.TipoEventoViagem.DescriptionAttr(),
                Status = o.Status.DescriptionAttr(),
                StatusInt = (int) o.Status,
                Token = o.Token
            }).ToList();

            return objToReturn;
        }

        public ValidationResult ValidarChavePagamento(int empresaId, string documento, string chave, string tokenPagamento)
        {
            try
            {
                if (string.IsNullOrEmpty(documento))
                    return new ValidationResult().Add("CPF/CNPJ não informado ou inválido para validação da chave");

                var evento = _viagemEventoService.GetByToken(tokenPagamento);

                if (evento == null)
                    return new ValidationResult().Add($"Token “{tokenPagamento}” não encontrado. Entre em contato com o suporte");

                var minutosValidadeChave = _empresaRepository.ObterMinutosValidadeChavePagamento(empresaId > 0 ? empresaId : evento.IdEmpresa);
                var usuario = _usuarioRepository.FirstOrDefault(o => o.CPFCNPJ == documento);

                if (usuario == null)
                    return new ValidationResult().Add($"Usuário documento {documento.ToCpfOrCnpj()} não encontrado na base de dados");

                if (evento.Status != EStatusViagemEvento.Aberto)
                    return new ValidationResult().Add($"Evento do token “Token” não encontra-se aberto para pagamento");

                var chavePagamento = new ChavePagamento(chave, usuario.KeyCodeTransaction, minutosValidadeChave);
                chavePagamento.ValidarChave();

                if (usuario.IdUsuario != chavePagamento.UsuarioId)
                    return new ValidationResult().Add($"Id Usuário da chave diverge do Id de usuário filtrado pelo CPF/CNPJ informado");

                if (usuario.CPFCNPJ != documento)
                    return new ValidationResult().Add($"Documento “{documento.ToCpfOrCnpj()}” não corresponde à chave digitada");

                var viagem = _viagemRepository.Where(o => o.IdViagem == evento.IdViagem).FirstOrDefault();

                if (viagem?.CPFMotorista == documento)
                    return new ValidationResult();

                return viagem?.CPFCNPJProprietario != documento
                    ? new ValidationResult().Add($"Documento “{documento.ToCpfOrCnpj()}” informado não corresponde ao Motorista ou Proprietário da Viagem informada")
                    : new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetMotivoRejeicaoAbono(int IdViagemEvento, int? IdMotivo, string detalhamentoMotivo, int? idUsuario)
        {
            try
            {
                var viagemEvento = _viagemEventoRepository
                    .FirstOrDefault(x => x.IdViagemEvento == IdViagemEvento);

                if (viagemEvento == null)
                    throw new Exception("Viagem evento não encontrada!");

                viagemEvento.IdMotivoRejeicaoAbono = IdMotivo;
                viagemEvento.DescricaoRejeicaoAbono = detalhamentoMotivo;
                viagemEvento.IdUsuarioRejeicaoAbono = idUsuario;
                viagemEvento.DataHoraRejeicaoAbono = DateTime.Now;

                _viagemEventoRepository.Update(viagemEvento);

                return new ValidationResult();

            } catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult NotificarRejeicaoAbono(int idViagemEvento, int idProtocolo)
        {
            try
            {
                var viagemEvento = _viagemEventoRepository
                    .Include(x => x.Viagem)
                    .Include(x => x.EstabelecimentoBase)
                    .Include(x => x.MotivoRejeicaoAbono)
                    .FirstOrDefault(x => x.IdViagemEvento == idViagemEvento);

                var empresa = _empresaRepository.GetQuery(viagemEvento.Viagem.IdEmpresa)
                    .Select(x => new { x.Logo, x.EmailCartaFrete }).FirstOrDefault();

                if (empresa == null)
                    return new ValidationResult();

                var nomeAplicativo = "";
                var layout = _layoutRepository.FirstOrDefault(x => x.IdEmpresa == viagemEvento.Viagem.IdEmpresa);

                nomeAplicativo = layout == null ? ConstantesUtils.GetNomeAdministradoraPlataforma : layout.MailFooterCompanyName;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel { Assunto = $"Entrega não finalizada - {nomeAplicativo}" };

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\protocolo-criacao-rejeicao-abono.html"))
                {
                    var logoHeader = empresa.Logo != null
                        ? new LinkedResource(new MemoryStream(empresa.Logo))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };


                    var banner = new LinkedResource(caminhoAplicacao + @"\Content\Image\header-quebra-mercadoria-nao.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };


                    var numeroCiot = _declaracaoCiotRepository
                        .Where(o => o.IdViagem == viagemEvento.IdViagem)
                        .OrderByDescending(x => x.IdDeclaracaoCiot).FirstOrDefault()?.Ciot;


                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", banner.ContentId);
                    html = html.Replace("{nome_posto}", viagemEvento.EstabelecimentoBase?.RazaoSocial);
                    html = html.Replace("{protocolo}", (viagemEvento?.IdProtocolo ?? idProtocolo).ToString());
                    html = html.Replace("{token}", $"{viagemEvento.Token} {viagemEvento.TipoEventoViagem.GetDescription()}");
                    html = html.Replace("{ciot}", numeroCiot);
                    html = html.Replace("{motorista}", viagemEvento.Viagem.NomeMotorista);
                    html = html.Replace("{cpf}", viagemEvento.Viagem.CPFMotorista);
                    html = html.Replace("{placa}", viagemEvento.Viagem.Placa);
                    html = html.Replace("{quebra}", viagemEvento.ValorPagamento.ToString());
                    html = html.Replace("{motivo}", viagemEvento.MotivoRejeicaoAbono?.Descricao);
                    html = html.Replace("{detalhamento}", viagemEvento.DescricaoRejeicaoAbono);
                    html = html.Replace("{data}", viagemEvento.DataHoraRejeicaoAbono!= null ? viagemEvento.DataHoraRejeicaoAbono.Value.ToString("G") :  "");
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(banner);
                    emailModel.AlternateView = view;

                }
                emailModel.Destinatarios = empresa.EmailCartaFrete != null ? empresa.EmailCartaFrete.Split(';')?.ToList() : new List<string>();
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} --- {e.InnerException?.Message}");
            }
        }

        public object ConsultarMotivoAbono(int IdViagemEvento)
        {
            var viagemEvento = _viagemEventoRepository
                .Where(x => x.IdViagemEvento == IdViagemEvento)
                .Include(x => x.MotivoRejeicaoAbono)
                .ToList()
                .Select(x => new
                {
                    Motivo = x.MotivoRejeicaoAbono.Descricao,
                    Detalhamento = x.DescricaoRejeicaoAbono,
                    Data= x.DataHoraRejeicaoAbono.Value.ToString("G")
                })
                .FirstOrDefault();

            return viagemEvento;
        }

        public List<BancosFebrabanModel> GetBancosFebraban()
        {
            try
            {
                var url = ConfigurationManager.AppSettings["MS_URL"];

                var request = (HttpWebRequest)WebRequest.Create($"{url}/Cadastros/Api/Bancos");
                request.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;

                using (var response = (HttpWebResponse)request.GetResponse())
                using (var stream = response.GetResponseStream())

                using (var reader = new StreamReader(stream ?? throw new InvalidOperationException()))
                {
                    var json = reader.ReadToEnd();
                    var dados = JsonConvert.DeserializeObject<List<BancosFebrabanModel>>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                    return dados.ToList().OrderBy(o => o.nome).ToList();
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
            finally
            {
                Dispose();
            }
        }

        public ValidationResult DeletarEventoAbono(int idViagem)
        {
            var viagemEventoAbono = _viagemEventoRepository.FirstOrDefault(o =>
                o.IdViagem == idViagem && o.TipoEventoViagem == ETipoEventoViagem.Abono);

            if (viagemEventoAbono != null)
                _viagemEventoRepository.Delete(viagemEventoAbono);

            return new ValidationResult();
    }

        public byte[] GerarRelatorioFaturamento(List<FaturamentoGridDto> data,string extensao,int? empresaFiltro)
        {
            byte[] logoEmpresa = { };

            if (empresaFiltro.HasValue)
                logoEmpresa = _empresaRepository.Get((int) empresaFiltro).Logo;

            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

            if (extensao == "pdf")
                return new RelatorioFaturamento().GetReport(new RelatorioFaturamentoDataType()
                {
                    DataGrid = data.AsQueryable().ProjectTo<EmpresaFaturamento>().ToList()
                }, extensao, logo);

            using var package = new XLWorkbook();

            var worksheet = package.Worksheets.Add("Faturamento");

            worksheet.Cell("A1").Value = "Cnpj";
            worksheet.Cell("B1").Value = "Razão Social";
            worksheet.Cell("C1").Value = "Status";
            worksheet.Cell("D1").Value = "Quantidade Viagens";
            worksheet.Cell("E1").Value = "Total Adiantamento";
            worksheet.Cell("F1").Value = "Total Saldo";
            worksheet.Cell("G1").Value = "Total Tarifas ANTT";
            worksheet.Cell("H1").Value = "Total Estadias";
            worksheet.Cell("I1").Value = "Total Abastecimento";
            worksheet.Cell("J1").Value = "Total Pedágio";
            worksheet.Cell("K1").Value = "Total Carga Avulsa";
            worksheet.Cell("L1").Value = "Total Estornos";
            worksheet.Cell("M1").Value = "Total";

            var line = 2;
            foreach (var item in data)
            {
                worksheet.Cell($"A{line}").Value = item.Cnpj;
                worksheet.Cell($"B{line}").Value = item.RazaoSocial;
                worksheet.Cell($"C{line}").Value = item.StatusEmpresa;
                worksheet.Cell($"D{line}").Value = item.QtdViagens;
                worksheet.Cell($"E{line}").Value = item.Adiantamento;
                worksheet.Cell($"F{line}").Value = item.Saldo;
                worksheet.Cell($"G{line}").Value = item.TarifaANTT;
                worksheet.Cell($"H{line}").Value = item.Estadias;
                worksheet.Cell($"I{line}").Value = item.Abastecimento;
                worksheet.Cell($"J{line}").Value = item.Pedagio;
                worksheet.Cell($"K{line}").Value = item.CargaAvulsa;
                worksheet.Cell($"L{line}").Value = item.Estornos;
                worksheet.Cell($"M{line}").Value = item.Total;

                line++;
            }

            worksheet.Column("A").AdjustToContents();
            worksheet.Column("B").AdjustToContents();
            worksheet.Column("C").AdjustToContents();
            worksheet.Column("D").AdjustToContents();
            worksheet.Column("E").AdjustToContents();
            worksheet.Column("F").AdjustToContents();
            worksheet.Column("G").AdjustToContents();
            worksheet.Column("H").AdjustToContents();
            worksheet.Column("I").AdjustToContents();
            worksheet.Column("J").AdjustToContents();
            worksheet.Column("K").AdjustToContents();
            worksheet.Column("L").AdjustToContents();
            worksheet.Column("M").AdjustToContents();

            using var stream = new MemoryStream();

            package.SaveAs(stream);
            stream.Position = 0;
            return stream.ToArray();
        }

        public IList<FaturamentoGridDto> RelatorioFaturamento(DateTime dataInicial, DateTime datafinal, int? empresaFiltro)
        {
            return _pagamentoDapper.RelatorioFaturamento(dataInicial,datafinal,empresaFiltro);
        }
    }
}


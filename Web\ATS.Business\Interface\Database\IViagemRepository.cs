﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Database
{
    public interface IViagemRepository : IRepository<Viagem>
    {
        Viagem GetChilds(int id, int idEmpresa);
        Viagem Get(int id, int idEmpresa);
        Viagem Get(int id, bool comIncludes = true);
        IQueryable<Viagem> Consultar(string nCPFMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> idsViagem, bool? permiteConsultarSemFiltro,List<string> NumerosControle);

        IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario, DateTime? dataCadastroBase = null);

        List<FrotaUtilizadaModel> GetFrotaUtilizada(int idEmpresa, EStatusViagem statusViagem);
        DadosCiotV2Dto GetDadosCiotV2(int idViagem);
        DadosCiotV3Dto GetDadosCiotV3(int idViagem);
        int? GetIbgeOrigemCiot(int idViagem);
        int? GetIbgeDestinoCiot(int idViagem);
        IQueryable<Viagem> GetQuery();
        IQueryable<Viagem> GetQuery(int idViagem);
    }
}
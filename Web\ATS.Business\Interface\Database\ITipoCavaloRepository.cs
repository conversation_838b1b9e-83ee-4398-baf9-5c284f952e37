﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface ITipoCavaloRepository : IRepository<TipoCavalo>
    {
        IQueryable<TipoCavaloGrid> Consultar(string nome);
        IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria);
        IQueryable<TipoCavalo> GetTipoCavaloAtualizado(DateTime dataHora, List<int> idsEmpresa);
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class EstabelecimentoApp : AppBase, IEstabelecimentoApp
    {
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IEstabelecimentoProdutoService _estabelecimentoProdutoService;
        private readonly IEmpresaApp _empresaApp;

        public EstabelecimentoApp(IEstabelecimentoService estabelecimentoService, IEstabelecimentoProdutoService estabelecimentoProdutoService, IEmpresaApp empresaApp)
        {
            _estabelecimentoService = estabelecimentoService;
            _estabelecimentoProdutoService = estabelecimentoProdutoService;
            _empresaApp = empresaApp;
        }

        public Estabelecimento GetByIdEstabelecimentoBase(int idEstabelecimentoBase, int idEmpresa)
        {
            return _estabelecimentoService.GetByIdEstabelecimentoBase(idEstabelecimentoBase, idEmpresa);
        }

        public List<KeyValuePair<int, int>> GetIdEstabelecimentosAssociadosLiberacaoProtocolo(int idEstabelecimentoBase)
        {
            return _estabelecimentoService.GetIdEstabelecimentosAssociadosLiberacaoProtocolo(idEstabelecimentoBase);
        }
        //
        public Estabelecimento GetByIdEstabelecimentoEmpresa(int idEstabelecimentoBase, int idEmpresa)
        {
            return _estabelecimentoService.GetByIdEstabelecimentoEmpresa(idEstabelecimentoBase, idEmpresa);
        }

        public IQueryable<EstabelecimentoProduto> GetQueryProduto()
        {
            return _estabelecimentoService.GetQueryProduto();
        }

        public IQueryable<Estabelecimento> GetEstabelecimentoPorIdBase(int idEstabelecimentoBase)
        {
            return _estabelecimentoService.GetEstabelecimentoPorIdBase(idEstabelecimentoBase);
        }

        public ValidationResult RemoverProdutos(IList<int> idprodutobase)
        {
            return _estabelecimentoService.RemoverProdutos(idprodutobase);
        }

        public ValidationResult Add(Estabelecimento estabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estabelecimentoService.Add(estabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Estabelecimento estabelecimento, List<EstabelecimentoProduto> produtosExclusao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estabelecimentoService.Update(estabelecimento);
                    if (produtosExclusao != null && produtosExclusao.Any())
                    {
                        var validationResultProdutos = _estabelecimentoProdutoService.Delete(produtosExclusao);
                        if (!validationResultProdutos.IsValid)
                            return validationResultProdutos;
                    }
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Inativar(int idEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estabelecimentoService
                        .Inativar(idEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estabelecimentoService.Reativar(idEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Estabelecimento Get(int idTipoEstabelecimento)
        {
            return _estabelecimentoService.Get(idTipoEstabelecimento);
        }

        public KeyValuePair<int, bool> GetEstabelecimentoGeracaoProtocolo(int idEstabelecimentoBase, int idEmpresa)
        {
            return _estabelecimentoService.GetEstabelecimentoGeracaoProtocolo(idEstabelecimentoBase, idEmpresa);
        }

        public int? GetByBase(int idBase)
        {
            return _estabelecimentoService.GetByBase(idBase);
        }

        public int? GetIdBase(int idestabelecimento)
        {
            return _estabelecimentoService.GetIdBase(idestabelecimento);
        }

        public Estabelecimento Get(string cnpj, int idEmpresa)
        {
            return _estabelecimentoService.Get(cnpj, idEmpresa);
        }

        public IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarPorEmpresa(int idEmpresa, string cnpjEstabelecimento)
        {
            return _estabelecimentoService.ConsultarPorEmpresa(idEmpresa, cnpjEstabelecimento);
        }

        public IQueryable<Estabelecimento> ConsultarEstabelecimentosRota(
            int idEmpresa,
            decimal latitudeOrigem, decimal longitudeOrigem, decimal? latitudeDestino = null,
            decimal? longitudeDestino = null, decimal? raio = 50, int[] idsTipoEstabelecimento = null, bool buscaCredenciados = false)
        {
            return _estabelecimentoService.ConsultarEstabelecimentosRota(idEmpresa, latitudeOrigem, longitudeOrigem,
                latitudeDestino, longitudeDestino, raio, idsTipoEstabelecimento, buscaCredenciados);
        }

        public Estabelecimento ConsultarPorId(int idEstabelecimento)
        {
            return _estabelecimentoService.ConsultarPorId(idEstabelecimento);
        }

        public object ConsultarAssociacoes(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _estabelecimentoService.ConsultarAssociacoes(idTipoEstabelecimento, descricao, take, page, orderFilters, filters);
        }

        public object GetEstabelecimentosPorViagem(string tokenViagemEvento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _estabelecimentoService.GetEstabelecimentoPorViagem(tokenViagemEvento, take, page, order, filters);
        }

        public bool IsRegular(int idEstabelecimento)
        {
            return _estabelecimentoService.IsRegular(idEstabelecimento); 
        }
        
        public bool EstaCredenciado(int idEstabelecimento)
        {
            return _estabelecimentoService.EstaCredenciado(idEstabelecimento); 
        }

        public bool ValidarChaveTokenPorHorarario(int idEstabelecimentoBase, int idEmpresa)
        {
            return _estabelecimentoService.ValidarChaveTokenPorHorarario(idEstabelecimentoBase, idEmpresa);
        }
        
        public ValidationResult ReplicarProdutoBase(EstabelecimentoBaseProduto estabelecimentoBaseProduto)
        {
            return _estabelecimentoService.ReplicarProdutoBase(estabelecimentoBaseProduto);
        }
        
        public IQueryable<Estabelecimento> GetQueryByCnpj(string cnpj)
        {
            return _estabelecimentoService.GetQueryByCnpj(cnpj);
        }

        public List<Estabelecimento> EstabelecimentosDaRota(int idRota)
        {
            return _estabelecimentoService.EstabelecimentosDaRota(idRota);
        }

        public int? GetIdEstabelecimentoPorCnpj(string cnpjEstabelecimento, string cnpjEmpresa)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
            if (!idEmpresa.HasValue)
                return null;
            
            return _estabelecimentoService.GetIdPorCnpj(cnpjEstabelecimento, idEmpresa.Value);
        }
        
        public bool GetEstabelecimentoAtivo(int idEstabelecimento)
        {
            return _estabelecimentoService.GetEstabelecimentoAtivo(idEstabelecimento);
        }

        public bool PertenceAEmpresa(int idempresa, int idEstabelecimento)
        {
            return _estabelecimentoService.GetQueryById(idEstabelecimento).Any(c => c.IdEmpresa == idempresa);
        }
    }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface ITipoDocumentoService : IService<TipoDocumento>
    {
        TipoDocumento Get(int id);
        ValidationResult Update(TipoDocumento entity);
        ValidationResult Add(TipoDocumento entity);
        IQueryable<TipoDocumento> All();
        ValidationResult Inativar(int idTipoDocumento);
        ValidationResult Reativar(int idTipoDocumento);
        IQueryable<TipoDocumento> Consultar(string descricao);
        int? GetCNH();
    }
}
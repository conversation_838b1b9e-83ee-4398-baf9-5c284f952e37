using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface.Common;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ConsultarExtratoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarExtratoRequest;
using ConsultarSaldoCartaoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoRequest;
using ConsultarSaldoCartaoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoResponse;
using ConsultarSaldoEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoEmpresaResponse;
using CustomFilter = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilter;
using Empresa = ATS.Domain.Entities.Empresa;
using IntegrarEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponse;
using ProdutoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ProdutoResponse;

namespace ATS.Application.Interface
{
    public interface ICartoesApp : IAppBase
    {
        IntegrarPessoaResponse IntegrarPessoaMicroServico(Estabelecimento estabelecimento);
        CartaoVinculadoPessoaListResponse GetCartoesVinculados(string documento, List<int> idProdutos, 
            bool ativarCartaoVirtualCasoNaoPossuir = false, string documentoUsuarioAudit = null, 
            string nomeUsuarioAudit = null, bool buscarCartoesBloqueados = false);
        List<ProdutoResponse> GetCartaoProdutos();
        ConsultaCartaoResponse GetCartaoProcessadora(int identificador, int produto);
        HistoricoCartaoPessoaListResponse GetCartaoHistorico(string documento, List<int> idProdutos);
        HistoricoCartaoPessoaListResponseDto GetCartaoHistoricoGrid(string documento, List<int> idProdutos);
        CartaoVinculadoPessoaListResponseDto GetCartoesVinculadosGrid(string documento, List<int> idProdutos);
        FilteredResultOfMotivoBloqueioModel BuscarMotivosBloquearCartao(int Take, int Page, OrderFilters Order, List<CustomFilter> Filters);
        BloquearCartaoResponse BloquearCartao(BloquearCartaoRequest request);
        DesbloquearCartaoResponse DesbloquearCartao(DesbloquearCartaoRequest request);
        AtsPortadorRequest CarregarInformacoesPortador(string documento);
        VincularResponse VincularCartaoPortador(int identificador, int idProdutos, AtsPortadorRequest atsPortadorRequest, int idEmpresa);
        DesvincularResponse DesvincularCartaoPortador(int identificador, int idProdutos, string motivoDesvinculo, AtsPortadorRequest atsPortadorRequest, int? cartaoMestreId);
        ETipoProcessamentoCartao GetTipoProcessamentoCartaoCarga(ETipoEventoViagem tipoEventoViagem, ETipoOperacaoCartao tipoOperacao);
        OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento);
        OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento, CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario);
        OperacaoCartaoResponseDTO RealizarEstornoCargaFrete(ViagemEvento viagemEvento, int idEmpresa, string cpfMotorista, string cpfCnpjProprietario, EOrigemTransacaoCartao origem);
        OperacaoCartaoResponseDTO TransferirValorCartao(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem, string senha, int? administradoraId = null);
        ConsultarSaldoCartaoResponse ConsultarSaldoCartao(string documento);
        ConsultarSaldoCartaoResponseDTO ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        AlterarSenhaCartaoResponse AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO requestDto);
        OperacaoCartaoResponseDTO TransferirValorContaBancaria(TransferirContaBancariaRequestDTO request, EOrigemTransacaoCartao origem);
        List<ConsultarPontoDistribuicaoResponse> GetPontosDistribuicao(List<string> cnpjList);
        EnvioRemessaResponse EnviarRemessaCartoes(EnvioRemessaRequest request);
        EnvioRemessaResponse ValidarCartaoRemessa(ValidarCartaoRequest request);
        BaixaRemessaResponse ReceberRemessaCartoes(BaixaRemessaRequest request);
        ConsultarRemessaResponse ConsultarCartoesLote(int loteId);
        ConsultarRemessaResponseDTO CarregarRemessaEmpresa(List<string> cnpjList, bool filtrarEmpresaOrigem, int take, int page, OrderFilters order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim);
        int GetIdProdutoCartaoFretePadrao();
        ConsultaCompraPedagioResponse ConsultarCompraPedagio(int pageSize, int pageIndex, IEnumerable<object> orderBy, List<SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.CustomFilter> customFilter, ConsultaCompraPedagioRequest request);
        CancelarCompraPedagioResponse CancelarCompraPedagio(CancelarCompraPedagioRequest request);
        GetStatusPedagioResponse GetStatusCompraPedagio();
        ConsultaRotaResponseDto ConsultarCustoRota(ConsultaRotaRequest request);
        SolicitarCompraPedagioResponseDTO SolicitarCompraPedagio(int empresaId, SolicitarCompraPedagioRequest request, IList<LocalizacaoDTO> localizacoes);
        ConsultaHistoricoRotaResponse ConsultaHistoricoRota(ConsultaHistoricoRotaRequest request);
        object RelatorioSituacaoCartao(RelatorioCartaoApiRequest request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        RelatorioConciliacaoAnaliticoDataType RelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoTransacoesDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        object RelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        byte[] GerarRelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoTransacoesDTO request, OrderFilters Order, List<QueryFilters> filters, EExtensaoArquivoRelatorio extensao, int idEmpresa);
        byte[] GerarSolicitarCompraPedagio(ConsultaCompraPedagioRequest request, int Take, int Page, IEnumerable<object> Order, List<QueryFilters> filters, string extensao, int idEmpresa);
        byte[] GerarRelatorioSituacaoCartoes(RelatorioCartaoApiRequest request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, string extensao, int idEmpresa);
        SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponse IntegrarEmpresa(Empresa empresa);
        byte[] GerarRelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, string extensao, int idEmpresa);
        List<PessoaContaBancariaResponse> GetContaBancaria(string documento);
        BusinessResult<GetExtratoBizGridResponse> ConsultarExtratoV2(ConsultaExtratoV2DTORequest request);
        ObjetoGridExtratoResponseDTO ConsultarExtrato(ConsultaExtratoDTORequest request, OrderFilters order, List<QueryFilters> filters);
        ConsultarExtratoResponseDTO ConsultarExtrato(ConsultarExtratoRequest request, string cpfCnpj = null,
            string documentoUsuarioAudit = null, string nomeUsuarioAudit = null);
        AtsPortadorRequest ConsultarPortadorAtendimento(string documento);
        ConsultarSaldoCartaoResponse ConsultarSaldoCartaoAtendimento(ConsultarSaldoCartaoRequest request);
        SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IdentificadorCartao GetUltimoCartao(string documento, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null);
        ConsultarContasBancariasResponseDTO ContasBancarias(string documento, string documentoUsuarioAudit, string nomeUsuarioAudit);
        InativarContaBancariaAtsResponseDTO InativarContaBancaria(InativarContaBancariaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        object GetResgatarValor(ConsultarResgateValorDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        ConsultaCartaoAdministradoraResponse AdministradoraCartao(int identificador, int idproduto);
        EmpresaTokenMicroServicoDto GetOrGenerateTokenEmpresa(string cnpjEmpresa, string appName, int grupoContabilizacao);
        List<PessoasCartoesVinculadosItem> ConsultarListaPortadorCartaoComSaldo(CartoesVinculadosListaSaldoRequest listMotorista);        
        ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpj);
    }
}
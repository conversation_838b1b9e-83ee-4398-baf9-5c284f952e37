using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Models.Ciot;
using ATS.WS.Models.Common.Response;

namespace ATS.WS.Models.ViagemV2.Response
{
    public class ViagemV2IntegracaoResponseModel
    {
        /// <summary>
        /// Código da viagem integrada
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Número do documento
        /// </summary>
        public string NumeroDocumento { get; set; }

        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal Irrpf { get; set; }

        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal Inss { get; set; }

        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal SestSenat { get; set; }

        /// <summary>
        /// Objetos das integrações
        /// </summary>
        public IntegracoesPreViagem IntegracoesPreViagem { get; set; }

        /// <summary>
        /// Dados Declaração de Ciot
        /// </summary>
        public DeclararCiotResult Ciot { get; set; }

        /// <summary>
        /// Dados do pedágio
        /// </summary>
        public SolicitarCompraPedagioResponseDTO Pedagio { get; set; }

        /// <summary>
        /// Lista de Ids de estabelecimentos da viagem
        /// </summary>
        public IList<int> IdsViagemEstabelecimento { get; set; }

        /// <summary>
        /// Eventos da viagem
        /// </summary>
        public IList<ViagemIntegrarEventoResponseModel>  Eventos { get; set; }
    }

    public class IntegracoesPreViagem
    {
        /// <summary>
        /// Cliente de origem integrado ou atualizado
        /// </summary>
        public ViagemV2ResultadoIntegracoesPreViagem ClienteOrigem { get; set; }
        
        /// <summary>
        /// Cliente de destino integrado ou atualizado
        /// </summary>
        public ViagemV2ResultadoIntegracoesPreViagem ClienteDestino { get; set; }
        
        /// <summary>
        /// Proprietário integrado ou atualizado
        /// </summary>
        public ViagemV2ResultadoIntegracoesPreViagem Proprietario { get; set; }
        
        /// <summary>
        /// Motorista integrado ou atualizado
        /// </summary>
        public ViagemV2ResultadoIntegracoesPreViagem Motorista { get; set; }
        
        /// <summary>
        /// Veículo integrado ou atualizado
        /// </summary>
        public ViagemV2ResultadoIntegracoesPreViagem Veiculo { get; set; }
    }
}
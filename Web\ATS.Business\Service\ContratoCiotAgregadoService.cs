﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using System.Data.Entity.SqlServer;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Domain.Service
{
    public class ContratoCiotAgregadoService : ServiceBase, IContratoCiotAgregadoService
    {
        private readonly IContratoCiotAgregadoRepository _repository;
        private readonly ICiotV2Service _ciotV2Service;
        private readonly ICiotV3Service _ciotV3Service;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly ContratoCiotAgregadoActionDependencies _contratoCiotAgregadoActionDependencies;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IVeiculoDapper _veiculoDapper;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IContratoCiotAgregadoVeiculoRepository _contratoCiotAgregadoVeiculoRepository;
        private readonly IContratoCiotAgregadoRepository _contratoCiotAgregadoRepository;

        public IContratoCiotAgregadoRepository Repository => _repository;

        public ContratoCiotAgregadoService(IContratoCiotAgregadoRepository repository, ICiotV2Service ciotV2Service, ICiotV3Service ciotV3Service, IVersaoAnttLazyLoadService versaoAntt,
            IProprietarioRepository proprietarioRepository, ContratoCiotAgregadoActionDependencies contratoCiotAgregadoActionDependencies, IDeclaracaoCiotRepository declaracaoCiotRepository,
            IVeiculoDapper veiculoDapper, IEmpresaRepository empresaRepository, IContratoCiotAgregadoVeiculoRepository contratoCiotAgregadoVeiculoRepository,
            IContratoCiotAgregadoRepository contratoCiotAgregadoRepository)
        {
            _repository = repository;
            _ciotV2Service = ciotV2Service;
            _ciotV3Service = ciotV3Service;
            _versaoAntt = versaoAntt;
            _proprietarioRepository = proprietarioRepository;
            _contratoCiotAgregadoActionDependencies = contratoCiotAgregadoActionDependencies;
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _veiculoDapper = veiculoDapper;
            _empresaRepository = empresaRepository;
            _contratoCiotAgregadoVeiculoRepository = contratoCiotAgregadoVeiculoRepository;
            _contratoCiotAgregadoRepository = contratoCiotAgregadoRepository;
        }

        /// <summary>
        /// Retorna o objeto de proprietário contendo os registros filhos
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ContratoCiotAgregado Get(int id)
        {
            return _repository.Get(id);
        }

        /// <summary>
        /// Buscar Proprietário
        /// </summary>
        /// <param name="idProprietario">Código de Proprietário</param>
        /// <returns>Objeto ContratoCiotAgregado</returns>
        public ContratoCiotAgregado GetWithDeclaracaoCiot(int idProprietario)
        {
            return _repository
                .Find(x => x.IdProprietario == idProprietario)
                .Include(x => x.DeclaracaoCiot)
                .FirstOrDefault();
        }

        /// <summary>
        /// Busca contrato de agregado
        /// </summary>
        /// <param name="idContratoAgregado"></param>
        /// <returns>Objeto ContratoCiotAgregado</returns>
        public ContratoCiotAgregado GetWithAllIncludes(int idContratoAgregado)
        {
            return _repository
                .Find(x => x.IdContratoCiotAgregado == idContratoAgregado)
                .Include(x => x.DeclaracaoCiot)
//                .Include(x => x.DeclaracaoCiot.ViagemOrigem)
//                .Include(x => x.DeclaracaoCiot.ViagemOrigem.ClienteOrigem)
//                .Include(x => x.DeclaracaoCiot.ViagemOrigem.ClienteOrigem.Cidade)
//                .Include(x => x.DeclaracaoCiot.ViagemOrigem.ClienteOrigem)
//                .Include(x => x.DeclaracaoCiot.ViagemOrigem.ClienteOrigem.Cidade)
                .Include(x => x.Empresa)
                .Include(x => x.Proprietario)
                .Include(x => x.ContratoCiotAgregadoVeiculos.Select(e=> e.Veiculo))
                .FirstOrDefault();
        }

        /// <summary>
        /// Registrar contrato de agregado e executar integração com a ANTT
        /// </summary>
        /// <param name="contratoAberturaModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public AbrirContratoCiotAgregadoResultModel AbrirContratoCiotAgregado(ContratoAberturaModel contratoAberturaModel)
        {
            // Validando com throw new exception porque não há DTO para expressar a resposta do método!

            if (contratoAberturaModel.IdProprietario == null)
                return new AbrirContratoCiotAgregadoResultModel("Por favor, informe um proprietário.");

            if (contratoAberturaModel.IdEmpresa == null)
                return new AbrirContratoCiotAgregadoResultModel("Por favor, informe uma empresa.");

            if (contratoAberturaModel.DataInicio == null || contratoAberturaModel.DataFinal == null)
                return new AbrirContratoCiotAgregadoResultModel("Data inicio/final deve ser informada para declarar um novo contrato de CIOT agregado.");

            if (contratoAberturaModel.DataInicio.Value < DateTime.Today)
                return new AbrirContratoCiotAgregadoResultModel("Data inicio deve ser maior ou igual a hoje para declarar novo contrato de CIOT agregado.");

            if (contratoAberturaModel.DataFinal.Value >= DateTime.Today.AddDays(31))
                return new AbrirContratoCiotAgregadoResultModel("Data final deve ter no máximo 30 dias ({0}) em relação a atual para declarar um novo contrato de CIOT agregado."
                    .FormatEx(DateTime.Today.AddDays(30).FormatDateBr()));

            if (contratoAberturaModel.Veiculos == null || !contratoAberturaModel.Veiculos.Any())
                return new AbrirContratoCiotAgregadoResultModel("Informe ao menos um veículo para declarar contrato de CIOT agregado.");

            var proprietarioRepository = _proprietarioRepository;
            if (!proprietarioRepository.Any(p =>
                p.IdProprietario == contratoAberturaModel.IdProprietario
                && p.IdEmpresa == contratoAberturaModel.IdEmpresa))
                return new AbrirContratoCiotAgregadoResultModel(
                    "Por favor, informe um proprietário vinculado à empresa informada.");

            if (AnyContratoAberto(contratoAberturaModel.IdProprietario.Value, contratoAberturaModel.IdEmpresa.Value))
                return new AbrirContratoCiotAgregadoResultModel(
                    "Há um contrato em vigência para o proprietário selecionado. É necessário encerrá-lo antes de abrir um novo.");

            contratoAberturaModel.DataFinal = contratoAberturaModel.DataFinal.Value.EndOfDayWithDelay(); 
            
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    _ciotV2Service.GerarToken(contratoAberturaModel.IdEmpresa.Value);
                    break;
                case EVersaoAntt.Versao3:
                    _ciotV3Service.GerarToken(contratoAberturaModel.IdEmpresa.Value);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
            
            ConsultarVeiculosInclusoCiot(_ciotV2Service, _ciotV3Service, _versaoAntt.Value, contratoAberturaModel.Veiculos, 
                contratoAberturaModel.IdEmpresa.Value, proprietarioRepository.GetDadosProprietarioAntt(contratoAberturaModel.IdProprietario.Value));
            
            if (!contratoAberturaModel.Veiculos.Any(x => x.InclusoCiot))
                return new AbrirContratoCiotAgregadoResultModel("Informe ao menos um veículo pertencente ao proprietário para declarar contrato de CIOT agregado.");

            var contrato = Mapper.Map<ContratoCiotAgregado>(contratoAberturaModel);
            _repository.Add(contrato);

            var ciotResult = contrato.VerificarEDeclararCiotAntt(_repository, _ciotV2Service, _ciotV3Service, _versaoAntt.Value, _declaracaoCiotRepository, _contratoCiotAgregadoActionDependencies);
            if (!ciotResult.Sucesso.GetValueOrDefault(false))
                return new AbrirContratoCiotAgregadoResultModel
                {
                    ContratoAgregado = contrato,
                    Mensagem = ciotResult.Excecao?.Mensagem + $" - Contrato Id: {contrato.IdContratoCiotAgregado}."
                };

            contrato = _repository.GetAll()
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.ContratoCiotAgregadoVeiculos)
                .FirstOrDefault(c => c.IdContratoCiotAgregado == contrato.IdContratoCiotAgregado);

            return new AbrirContratoCiotAgregadoResultModel
            {
                Sucesso = true,
                ContratoAgregado = contrato,
                CiotResult = ciotResult
            };
        }
        
        private void ConsultarVeiculosInclusoCiot(ICiotV2Service ciotV2Service, ICiotV3Service ciotV3Service, EVersaoAntt versaoAntt, 
            ICollection<VeiculoModelAgregado> veiculos, int idEmpresa, ProprietarioAnttDto dadosProprietario)
        {
            var veiculoDapper = _veiculoDapper;
            var cnpjEmpresa = _empresaRepository.GetCnpj(idEmpresa);
            dadosProprietario.Rntrc = dadosProprietario.Rntrc.PadLeft(9, '0');
            
            foreach (var veiculo in veiculos)
            {
                var dadosVeiculo = veiculo.IdVeiculo == 0 
                    ? veiculoDapper.GetVeiculoRntrc(veiculo.Placa, idEmpresa) 
                    : veiculoDapper.GetVeiculoRntrc(veiculo.IdVeiculo);

                if (!string.IsNullOrWhiteSpace(dadosVeiculo.RNTRC))
                {
                    dadosVeiculo.RNTRC = dadosVeiculo.RNTRC.PadLeft(9, '0');

                    if (dadosVeiculo.RNTRC != dadosProprietario.Rntrc && dadosVeiculo.DocumentoProprietario != dadosProprietario.CnpjCpf)
                    {
                        // No caso do contrato agregado, o veículo que não pertence ao proprietário não precisa ser informado para a ANTT, eles consultam isso de outra forma na fiscalização
                        veiculo.InclusoCiot = false;
                        continue;
                    }
                }
                else
                {
                    dadosVeiculo.RNTRC = dadosProprietario.Rntrc;
                    dadosVeiculo.DocumentoProprietario = dadosProprietario.CnpjCpf;
                }

                var consultaFrotaTransportadorRequest = new ConsultarFrotaTransportadorRequest
                {
                    Placa = new ObservableCollection<string> {dadosVeiculo.Placa},
                    RntrcTransportador = dadosVeiculo.RNTRC,
                    CpfCnpjInteressado = cnpjEmpresa,
                    CpfCnpjTransportador = dadosVeiculo.DocumentoProprietario
                };

                var consultaFrotaTransportadorResponse = versaoAntt == EVersaoAntt.Versao2 
                    ? ciotV2Service.ConsultarFrotaTransportador(consultaFrotaTransportadorRequest) 
                    : ciotV3Service.ConsultarFrotaTransportador(consultaFrotaTransportadorRequest);
                
                if (consultaFrotaTransportadorResponse.Sucesso != true || consultaFrotaTransportadorResponse.FalhaComunicacaoAntt == true || !consultaFrotaTransportadorResponse.VeiculoTransportador.Any())
                    veiculo.InclusoCiot = true;
                else if (consultaFrotaTransportadorResponse.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true)
                    veiculo.InclusoCiot = true;
                else
                    // Placa é salva no banco e não é enviada na retificação
                    veiculo.InclusoCiot = false;
            }
        }

        public bool AnyContratoAberto(int idProprietario, int idEmpresa)
        {
            return _repository.AnyContratoAberto(idProprietario, idEmpresa);
        }

        public List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa)
        {
            return _repository.GetPlacasContratoAberto(idProprietario, idEmpresa);
        }

        /// <summary>
        /// Buscar contrato de agregado em vigência para vincular a viagem
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="rntc"></param>
        /// <returns></returns>
        public ContratoCiotAgregado GetContratoVigente(int idEmpresa, string rntc)
        {         
            var contratoCiotAgregado = _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.Proprietario)
                .Include(c => c.ContratoCiotAgregadoVeiculos)
                .FirstOrDefault(c =>
                    c.Proprietario.RNTRC == rntc &&
                    c.IdEmpresa == idEmpresa &&
                    c.DataInicio <= DateTime.Today &&
                    c.DataFinal >= DateTime.Today &&                    
                    c.Status == EStatusContratoAgregado.Vigente &&
                    c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso);
            return contratoCiotAgregado;
        }

        /// <summary>
        /// Buscar lista de contratos de agregado em vigência para cancelar
        /// </summary>
        /// <returns></returns>
        public List<ContratoCiotAgregado> GetContratosSemViagensParaCancelar()
        {
            var dataLimite = DateTime.Now.AddDays(-4).AddHours(23);
            
            var contratoCiotAgregado = _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas)
                .Where(c => (!c.DeclaracaoCiot.ViagensVinculadas.Any()
                             || (c.DeclaracaoCiot.ViagensVinculadas.Any() && c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada)))
                            && EStatusContratoAgregado.Vigente == c.Status
                            && c.DataInicio < dataLimite).ToList();
            return contratoCiotAgregado;
        }
        
        public List<ContratoCiotAgregado> GetContratosSemViagensParaCancelamentoFuturo()
        {
            var dataAtual = DateTime.Now;
            
            var contratoCiotAgregado = _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.Empresa)
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas)
                .Where(c => (!c.DeclaracaoCiot.ViagensVinculadas.Any()
                             || (c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada)))
                            && EStatusContratoAgregado.Vigente == c.Status
                            && c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso
                            && dataAtual < SqlFunctions.DateAdd("dd", 5, c.DataCadastro)).ToList();
            
            return contratoCiotAgregado;
        }
        
        public List<ContratoCiotAgregado> GetContratosExpiradosParaEncerramentoFuturo()
        {
            var dataHoje = DateTime.Today.EndOfDay();
            var contratoCiotAgregado = _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.Empresa)
                .Where(c => c.DataFinal.HasValue
                            && DbFunctions.AddDays(c.DataFinal.Value, 25) < dataHoje
                            && DbFunctions.AddDays(c.DataFinal.Value, 30) >= dataHoje
                            && c.Status == EStatusContratoAgregado.Vigente
                            && c.DeclaracaoCiot.ViagensVinculadas.Any(v => v.StatusViagem != EStatusViagem.Cancelada)
                            && c.IdDeclaracaoCiot.HasValue)
                .ToList();

            return contratoCiotAgregado;
        }

        /// <summary>
        /// Buscar lista de contratos de agregado em vigência para encerrar
        /// </summary>
        /// <returns></returns>
        public List<ContratoCiotAgregado> GetContratosExpiradosParaEncerrar()
        {
            var hoje = DateTime.Today.EndOfDay();
            var contratoCiotAgregado = _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas)
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas.Select(v => v.ClienteOrigem.Cidade))
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas.Select(v => v.ClienteDestino.Cidade))
                .Where(c =>
                    hoje > SqlFunctions.DateAdd("dd", 30, c.DataFinal.Value)
                    && EStatusContratoAgregado.Vigente == c.Status
                    && c.DeclaracaoCiot.ViagensVinculadas.Any(v => EStatusViagem.Cancelada != v.StatusViagem)).ToList();
            return contratoCiotAgregado;
        }
        
        /// <summary>
        /// Verifica se o contrato vigente possui viagens ativas
        /// </summary>
        /// <returns></returns>
        public bool ExistViagensAtivasContrato()
        {         
            return _repository
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.DeclaracaoCiot.ViagensVinculadas)
                .Any(c =>
                    DbFunctions.DiffDays(c.DataInicio,DateTime.Today).Value > 30 
                    && EStatusContratoAgregado.Vigente == c.Status
                    && c.DeclaracaoCiot.ViagensVinculadas.Count > 0);
        }
        
        public ValidationResult AtualizarVeiculosContratoAgregado(ContratoCiotAgregado contratoCiotAgregado, ICollection<VeiculoModelAgregado> veiculos)
        {
            var validationResult = new ValidationResult();
            try
            {
                if (!veiculos.Any())
                    return validationResult;
            
                var repository = _contratoCiotAgregadoVeiculoRepository;

                //REMOVE VEICULOS
                var placasRetificacao = veiculos.Select(v => v.Placa);
                var veiculosParaRemover =
                    contratoCiotAgregado.ContratoCiotAgregadoVeiculos.Where(v => !placasRetificacao.Contains(v.Veiculo.Placa))
                    .ToList();

                if (veiculosParaRemover.Count > 0)
                    repository.DeleteRange(veiculosParaRemover, false);

                //ADICIONA VEICULOS
                var placasSalvas = contratoCiotAgregado.ContratoCiotAgregadoVeiculos.Select(v => v.Veiculo.Placa);
                var veiculosParaAdicionar =
                    veiculos.Where(v => !placasSalvas.Contains(v.Placa)).ToList();

                foreach (var veiculo in veiculosParaAdicionar)
                {
                    var contratoCiotAgregadoVeiculo = new ContratoCiotAgregadoVeiculo
                    {
                        IdVeiculo = veiculo.IdVeiculo,
                        DataCadastro = DateTime.Today,
                        IdContratoCiotAgregado = contratoCiotAgregado.IdContratoCiotAgregado,
                        InclusoCiot = veiculo.InclusoCiot
                    };

                    repository.Add(contratoCiotAgregadoVeiculo, false);
                }
             
                repository.SaveChanges();
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
            }

            return validationResult;
        }

        /// <summary>
        /// Cancela o contrato.
        /// </summary>
        public void CancelarContrato(ContratoCiotAgregado contratoCiotAgregado, string motivoCancelamento)
        {
            contratoCiotAgregado.Status = EStatusContratoAgregado.Cancelado;
            var contratoCiotAgregadoRepository = _contratoCiotAgregadoRepository;
            contratoCiotAgregadoRepository.Update(contratoCiotAgregado);
            contratoCiotAgregadoRepository.Detach(contratoCiotAgregado);
        }
    }
}
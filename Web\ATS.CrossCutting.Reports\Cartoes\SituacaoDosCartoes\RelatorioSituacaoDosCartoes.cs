﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Cartoes.SituacaoDosCartoes
{
    public class RelatorioSituacaoDosCartoes
    {
        public byte[] GetReport(string tipo, List<RelatorioSituacaoDosCartoesDataType> listaDados, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;
                
                var parametros = new ReportParameter[1];

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaDados,
                    Name = "DtsSituacao"
                });

                var path = ReportUtils.CreateLogo(logo);
                parametros[0] = new ReportParameter("Logo", "file:///" + path);

                
                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Cartoes.SituacaoDosCartoes.RelatorioSituacaoDosCartoes.rdlc";
                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}

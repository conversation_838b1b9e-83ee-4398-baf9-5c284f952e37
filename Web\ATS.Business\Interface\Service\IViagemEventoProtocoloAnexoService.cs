using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemEventoProtocoloAnexoService : IService<ViagemEventoProtocoloAnexo>
    {
        ValidationResult Add(ViagemEventoProtocoloAnexoModel viagemEventoProtocoloAnexo);
        
        ValidationResult AddListOfAnexos(List<ViagemEventoProtocoloAnexoModel> viagemEventoProtocoloAnexos, List<int> idsAnexosRemover);

        ValidationResult RemoverAnexos(List<int> idsAnexosRemover);

        List<ViagemEventoProtocoloAnexoModel> GetAnexosCadastrados(int idViagemEvento);
    }
}
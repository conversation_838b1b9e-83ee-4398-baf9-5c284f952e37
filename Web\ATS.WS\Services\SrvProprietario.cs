﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Configuration;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Models.Proprietarios;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Response.Proprietario;
using AutoMapper;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using Cartao = ATS.WS.Models.Webservice.Response.Proprietario.Cartao;
using Conta = ATS.WS.Models.Webservice.Response.Proprietario.Conta;

namespace ATS.WS.Services
{
    public class SrvProprietario : SrvBase
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IUsuarioApp _usuarioApp;
        private readonly SrvUtils _srvUtils;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaService _empresaService;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IEstadoApp _estadoApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ISerproApp _serproApp;
        private readonly IUsuarioService _usuarioService;
        private readonly IUserIdentity _userIdentity;
        private readonly IExtrattaBizApiClient _bizApiClient;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly ILimiteTransacaoPortadorApp _limiteTransacaoPortadorApp;

        public SrvProprietario(IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IVersaoAnttLazyLoadService versaoAntt,
            IUsuarioApp usuarioApp, SrvUtils srvUtils, IEmpresaApp empresaApp, ICidadeApp cidadeApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaService empresaService, 
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IEstadoApp estadoApp, IParametrosEmpresaService parametrosEmpresa, ISerproApp serproApp, IUsuarioService usuarioService, 
            IUserIdentity userIdentity, IExtrattaBizApiClient bizApiClient, IParametrosUsuarioService parametrosUsuarioService, ILimiteTransacaoPortadorApp limiteTransacaoPortadorApp)
        {
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
            _usuarioApp = usuarioApp;
            _srvUtils = srvUtils;
            _empresaApp = empresaApp;
            _cidadeApp = cidadeApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaService = empresaService;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _estadoApp = estadoApp;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
            _usuarioService = usuarioService;
            _userIdentity = userIdentity;
            _bizApiClient = bizApiClient;
            _parametrosUsuarioService = parametrosUsuarioService;
            _limiteTransacaoPortadorApp = limiteTransacaoPortadorApp;
        }
        
        public Retorno<ProprietarioModel> Integrar(ProprietarioIntegrarRequestModel @params)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                var validationResultUsuario = new Retorno<ProprietarioModel>();

                if (!idEmpresa.HasValue)
                    return new Retorno<ProprietarioModel>(false, @"Empresa inválido", null);

                var empresa = _empresaApp.Get(idEmpresa.Value);

                if (@params.Enderecos == null || @params.Enderecos.Count < 1)
                    return new Retorno<ProprietarioModel>(false, @"Endereço obrigatório no cadastro do proprietário", null);

                // Criado suporte a atualização de proprietário pelo método integrar.
                var propId = _proprietarioApp.GetIdPorCpfCnpj(@params.CnpjCpf, idEmpresa);

                if (propId > 0)
                    return Editar(@params);

                /*if (@params.CnpjCpf.OnlyNumbers().Length == 11 &&
                    _parametrosEmpresa.GetPermiteCadastrarProprietarioComCpfFicticio(empresa.IdEmpresa) == false)
                {
                    var validacaoSerpro = _serproApp.ValidarPortador(@params.CnpjCpf);
                    if (!validacaoSerpro.IsValid)
                        return new Retorno<ProprietarioModel>(false, validacaoSerpro.Errors.FirstOrDefault()?.Message, null);
                }*/
                
                // Criar novo proprietário
                var proprietario = Mapper.Map<ProprietarioIntegrarRequestModel, Proprietario>(@params);

                #region Endereços

                for (var i = 0; i < @params.Enderecos.Count; i++)
                {
                    @params.Enderecos[i].BACENPais = @params.Enderecos[i].BACENPais == 0 ? 1058 :  @params.Enderecos[i].BACENPais;
                    var idRetPais = _srvUtils.GetIdPaisPorBACEN(@params.Enderecos[i].BACENPais);

                    if (idRetPais == 0 || idRetPais == null)
                    {
                        idRetPais = _srvUtils.GetIdPaisPorBACEN(1058);
                    }

                    proprietario.Enderecos.ElementAt(i).IdPais = idRetPais.GetValueOrDefault();

                    var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(@params.Enderecos[i].IBGEEstado);
                    if (idRetEstado.HasValue && idRetEstado > 0)
                        proprietario.Enderecos.ElementAt(i).IdEstado = idRetEstado.GetValueOrDefault();

                    var idRetCidade = _srvUtils.GetIdCidadePorIBGE(@params.Enderecos[i].IBGECidade);

                    if (idRetCidade == null)
                        return new Retorno<ProprietarioModel>(false, @"Cidade não cadastrada!", null);

                    if (idRetCidade > 0)
                    {
                        proprietario.Enderecos.ElementAt(i).IdCidade = idRetCidade.GetValueOrDefault();
                        var cidade = _cidadeApp.Get(idRetCidade.Value);

                        if (cidade == null)
                            return new Retorno<ProprietarioModel>(false, @"Cidade não cadastrada!", null);

                        if (proprietario.Enderecos.ElementAt(i)?.IdEstado == 0)
                        {
                            proprietario.Enderecos.ElementAt(i).IdEstado = cidade.IdEstado;
                        }

                        if (proprietario.Enderecos.ElementAt(i)?.IdPais == 0)
                        {
                            var estado = _estadoApp.Get(cidade.IdEstado);
                            if (estado != null)
                                proprietario.Enderecos.ElementAt(i).IdPais = estado.IdPais;
                        }
                    }
                }

                #endregion

                proprietario.IdEmpresa = idEmpresa.Value;

                if (@params.DataNascimento.HasValue)
                {
                    // ReSharper disable once NotAccessedVariable
                    DateTime date;
                    if (!DateTime.TryParse(@params.DataNascimento.ToString(), out date))
                        return new Retorno<ProprietarioModel>(false, @"A data de nascimento está inválida.", null);

                    if (@params.DataNascimento.Value.Year < 1900)
                        return new Retorno<ProprietarioModel>(false, @"A data de nascimento está inválida.", null);
                }

                if (string.IsNullOrWhiteSpace(@params.RNTRC))
                    return new Retorno<ProprietarioModel>(false, @"Obrigatório informar o RNTRC para o proprietário.", null);

                var validationResult = _proprietarioApp.Add(proprietario);

                if (!validationResult.IsValid)
                    return new Retorno<ProprietarioModel>(false, validationResult.ToFormatedMessage(), null);

                //Integração como usuário caso parametro na empresa marcado e integração sucesso
                if (empresa.IntegrarComoUsuario && validationResult.IsValid)
                    validationResultUsuario = IntegrarProprietarioUsuario(@params);
                
                return new Retorno<ProprietarioModel>(true, validationResultUsuario.Mensagem ?? string.Empty,Mapper.Map<Proprietario, ProprietarioModel>(proprietario));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Integrar)} >> {e.Message}");
            }
        }
        
        private Retorno<ProprietarioModel> IntegrarProprietarioUsuario(ProprietarioIntegrarRequestModel @params)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            var empresa = _empresaApp.Get(@params.CNPJEmpresa);

            var proprietario = Mapper.Map<ProprietarioIntegrarRequestModel, Proprietario>(@params);
            var idUsuarioLogon = _usuarioApp.GetIdPorCNPJCPF(@params.CnpjCpf) ?? 0;

            proprietario.NomeFantasia = new Regex("[*'\",_&#^@]").Replace(proprietario.NomeFantasia,String.Empty);

            Usuario usuario = null;
            if(idEmpresa.HasValue)
                usuario = _usuarioApp.GetPorCNPJCPF(proprietario.CNPJCPF, true, idEmpresa);

            if(usuario == null)
                usuario = _usuarioApp.GetPorCNPJCPF(proprietario.CNPJCPF);

            ValidationResult validationResult;
            var contato = @params.Contatos.FirstOrDefault();
            var endereco = @params.Enderecos.FirstOrDefault();
            var cidade = _cidadeApp.GetCidadeByIBGE(endereco?.IBGECidade ?? 1058);

            //Trata quando ja existe um usuário na base de dados...
            if (usuario != null)
                return new Retorno<ProprietarioModel>(true, "Proprietário integrado com sucesso. Usuário não integrado devido a já existir no sistema.", null);

            usuario = new Usuario()
            {
                Nome = proprietario.NomeFantasia,
                CPFCNPJ = StringExtension.OnlyNumbers(proprietario.CNPJCPF),
                DataCadastro = DateTime.Now,
                Perfil = EPerfil.Proprietario,
                Login = StringExtension.OnlyNumbers(proprietario.CNPJCPF),
                RG = proprietario.RG,
                RGOrgaoExpedidor = proprietario.RGOrgaoExpedidor,
                Referencia1 = @params.Referencia1,
                Referencia2 = @params.Referencia2,
                Carreteiro = false,
                IdEmpresa = idEmpresa,
                Senha = StringExtension.OnlyNumbers(proprietario.CNPJCPF),
                NomeMae = proprietario.NomeMae,
                NomePai = proprietario.NomePai,
                TipoCobranca = proprietario.CNPJCPF.Length > 11 ? ETipoCobranca.PessoaJuridica : ETipoCobranca.PessoaFisica,
                DataNascimento = proprietario.DataNascimento,
                Enderecos = new List<UsuarioEndereco>()
                  {
                      new UsuarioEndereco()
                      {
                         CEP =  endereco?.CEP,
                         Bairro = endereco?.Bairro,
                         Endereco = endereco?.Endereco,
                         IdCidade = cidade.IdCidade,
                         IdPais = cidade.Estado.IdPais,
                         IdEstado= cidade.IdEstado
                      }
                  },
                Contatos = new List<UsuarioContato>()
                  {
                      new UsuarioContato()
                      {
                          Email= contato?.Email == null ? empresa.Email == null ?  empresa.Email : "<EMAIL>" : contato.Email,
                          Telefone = contato?.Telefone,
                          Celular = contato?.Celular,
                      }
                  },
                Ativo = true
            };


            usuario.Senha = _usuarioService.GerarSenhaAleatoria(usuario.Perfil);

            validationResult = _usuarioApp.Add(usuario, idUsuarioLogon);

            if (!validationResult.IsValid)
                return new Retorno<ProprietarioModel>(false, "Proprietário integrado com sucesso e usuário não devido a/o " + validationResult, null);


            var tipoPessoa = usuario.TipoCobranca == ETipoCobranca.PessoaFisica ? ETipoPessoa.Fisica : ETipoPessoa.Juridica;

            _limiteTransacaoPortadorApp.LimitarValoresPadrão(tipoPessoa, usuario.CPFCNPJ);


            return new Retorno<ProprietarioModel>(true, "Proprietário e usuário integrados com sucesso.", null);
        }

        public Retorno<ProprietarioModel> Editar(ProprietarioIntegrarRequestModel model)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(model.CNPJEmpresa);
                if (!idEmpresa.HasValue)
                    return new Retorno<ProprietarioModel>(false, @"Empresa inválido", null);

                var proprietario = _proprietarioApp.GetPorCpfCnpj(model.CnpjCpf, idEmpresa);

                if (proprietario == null)
                    return new Retorno<ProprietarioModel>(false,
                        $"{WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]} não encontrado!", null);

                if (model.Enderecos == null || model.Enderecos.Count < 1)
                    return new Retorno<ProprietarioModel>(false, @"Endereço obrigatório no cadastro do proprietário", null);

                /*if (model.CnpjCpf.OnlyNumbers().Length == 11 &&
                    _parametrosEmpresa.GetPermiteCadastrarProprietarioComCpfFicticio(idEmpresa.Value) == false)
                {
                    var validacaoSerpro = _serproApp.ValidarPortador(model.CnpjCpf);
                    if (!validacaoSerpro.IsValid)
                        return new Retorno<ProprietarioModel>(false, validacaoSerpro.Errors.FirstOrDefault()?.Message, null);
                }*/

                Mapper.Map(model, proprietario);

                if (proprietario.Contatos.Any())
                    Mapper.Map(model.Contatos.First(), proprietario.Contatos.First());

                if (proprietario.Enderecos.Any())
                    Mapper.Map(model.Enderecos.First(), proprietario.Enderecos.First());

                for (var i = 0; i < model.Enderecos.Count; i++)
                {
                    model.Enderecos[i].BACENPais = model.Enderecos[i].BACENPais == 0 ? 1058 :  model.Enderecos[i].BACENPais;
                    var idRetPais = _srvUtils.GetIdPaisPorBACEN(model.Enderecos[i].BACENPais);

                    if (idRetPais == 0 || idRetPais == null)
                    {
                        idRetPais = _srvUtils.GetIdPaisPorBACEN(1058);
                    }

                    proprietario.Enderecos.ElementAt(i).IdPais = idRetPais.GetValueOrDefault();

                    var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(model.Enderecos[i].IBGEEstado);
                    if (idRetEstado.HasValue && idRetEstado > 0)
                        proprietario.Enderecos.ElementAt(i).IdEstado = idRetEstado.GetValueOrDefault();

                    var idRetCidade = _srvUtils.GetIdCidadePorIBGE(model.Enderecos[i].IBGECidade);
                    if (idRetCidade == null)
                        return new Retorno<ProprietarioModel>(false, @"Cidade não cadastrada!", null);

                    if (idRetCidade > 0)
                    {
                        proprietario.Enderecos.ElementAt(i).IdCidade = idRetCidade.GetValueOrDefault();
                        var cidade = _cidadeApp.Get(idRetCidade.Value);

                        if (cidade != null)
                        {
                            if (proprietario.Enderecos.ElementAt(i)?.IdEstado == 0)
                            {
                                proprietario.Enderecos.ElementAt(i).IdEstado = cidade.IdEstado;
                            }

                            if (proprietario.Enderecos.ElementAt(i)?.IdPais == 0)
                            {
                                var estado = _estadoApp.Get(cidade.IdEstado);
                                if (estado != null)
                                    proprietario.Enderecos.ElementAt(i).IdPais = estado.IdPais;
                            }
                        }
                    }
                }

                var validationResult = _proprietarioApp.Update(proprietario);
                if (!validationResult.IsValid)
                    return new Retorno<ProprietarioModel>(false, validationResult.ToFormatedMessage(), null);

                var retorno = Mapper.Map<Proprietario, ProprietarioModel>(proprietario);

                return new Retorno<ProprietarioModel>(true, retorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        public Retorno<ProprietarioConsultarModel> Consultar(string token, string cnpjAplicacao,
            string cnpjcpfProprietario)
        {
            var empresasAutorizadas = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token).Select(c => c.IdEmpresa).ToList();
            var proprietario = _proprietarioApp.All().Include(o => o.Contatos).Include(o => o.Enderecos).FirstOrDefault(c => c.CNPJCPF == cnpjcpfProprietario && empresasAutorizadas.Contains(c.IdEmpresa));

            if (proprietario == null)
                return new Retorno<ProprietarioConsultarModel>(false, $"{WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]} não encontrado. ", null);

            var retorno = new ProprietarioConsultarModel
            {
                IdEmpresa = proprietario.IdEmpresa,
                RazaoSocial = proprietario.RazaoSocial,
                CNPJCPF = proprietario.CNPJCPF,
                IE = proprietario.IE,
                IdProprietario = proprietario.IdProprietario,
                NomeFantasia = proprietario.NomeFantasia,
                RG = proprietario.RG,
                RGOrgaoExpedidor = proprietario.RGOrgaoExpedidor,
                RNTRC = proprietario.RNTRC,
                StatusIntegracao = proprietario.StatusIntegracao,
                TipoContrato = proprietario.TipoContrato,
                Contatos = new List<ProprietarioContatoConsultarModel>(),
                Enderecos = new List<ProprietarioEnderecoConsultarModel>()
            };

            if (proprietario.Contatos != null && proprietario.Contatos.Any())
                foreach (var contato in proprietario.Contatos)
                {
                    retorno.Contatos.Add(new ProprietarioContatoConsultarModel
                    {
                        Email = contato.Email,
                        Celular = contato.Celular,
                        IdContato = contato.IdContato,
                        Telefone = contato.Telefone
                    });
                }

            if (proprietario.Enderecos != null && proprietario.Enderecos.Any())
                foreach (var endereco in proprietario.Enderecos)
                {
                    retorno.Enderecos.Add(new ProprietarioEnderecoConsultarModel
                    {
                        Bairro = endereco.Bairro,
                        CEP = endereco.CEP,
                        Complemento = endereco.Complemento,
                        Endereco = endereco.Endereco,
                        IdEndereco = endereco.IdEndereco,
                        Numero = endereco.Numero,
                        CodigoIBGECidade = endereco.Cidade?.IBGE,
                        CodigoIBGEEstado = endereco.Estado?.IBGE,
                        CodigoBACENPais = endereco.Pais?.BACEN
                    });
                }

            return new Retorno<ProprietarioConsultarModel>(true, retorno);
        }

        public Retorno<ProprietarioConsultarPorRntrcModel> ConsultarTransportadorPorRntrc(string token, string cnpjAplicacao, string rntrc)
        {
            var empresasAutorizadas = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token).Select(c => c.IdEmpresa).ToList();
            var proprietario = _proprietarioApp.All().FirstOrDefault(c => c.RNTRC == rntrc && empresasAutorizadas.Contains(c.IdEmpresa));

            if (proprietario == null)
                return new Retorno<ProprietarioConsultarPorRntrcModel>(false, $"{WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]} não encontrado. ", null);

            var request = new ConsultarSituacaoTransportadorRequest
            {
                RntrcTransportador = proprietario.RNTRC,
                CpfCnpjInteressado = cnpjAplicacao,
                CpfCnpjTransportador = proprietario.CNPJCPF
            };

            var consultarSituacaoTransportadorReponse = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.ConsultarSituacaoTransportador(request)
                : _ciotV3App.ConsultarSituacaoTransportador(request);

            if (consultarSituacaoTransportadorReponse == null )
                return new Retorno<ProprietarioConsultarPorRntrcModel>(false, "Não foi possível verificar as informações de RNTRC junto à ANTT.", null);

            if (!consultarSituacaoTransportadorReponse.Sucesso.GetValueOrDefault())
                if(consultarSituacaoTransportadorReponse.FalhaComunicacaoAntt == true)
                    return new Retorno<ProprietarioConsultarPorRntrcModel>(true, "Ocorreu uma falha de comunicação com a ANTT, os dados são da base de dados do ATS", new ProprietarioConsultarPorRntrcModel
                    {
                        RazaoSocial = proprietario.RazaoSocial,
                        NomeFantasia = proprietario.NomeFantasia,
                        RNTRC = proprietario.RNTRC,
                        EquiparadoTAC = proprietario.EquiparadoTac == false ? "NAO" : "SIM",
                        Situacao = "Erro consulta ANTT",
                        TipoRNTRC = "",
                        ValidadeRNTRC = ""
                    });
                else
                    return new Retorno<ProprietarioConsultarPorRntrcModel>(false,
                        consultarSituacaoTransportadorReponse.ExceptionMessage ?? consultarSituacaoTransportadorReponse.Excecao.Mensagem, null);

            var retorno = new ProprietarioConsultarPorRntrcModel
            {
                RazaoSocial = consultarSituacaoTransportadorReponse.NomeRazaoSocialTransportador,
                NomeFantasia = proprietario.NomeFantasia,
                RNTRC = consultarSituacaoTransportadorReponse.RntrcTransportador,
                EquiparadoTAC = consultarSituacaoTransportadorReponse.EquiparadoTAC.GetValueOrDefault() ? "SIM" : "NAO",
                Situacao = consultarSituacaoTransportadorReponse.RntrcAtivo.GetValueOrDefault() ? "Ativo" : "Inativo",
                TipoRNTRC = consultarSituacaoTransportadorReponse?.TipoTransportador ?? "",
                ValidadeRNTRC = consultarSituacaoTransportadorReponse?.DataValidadeRNTRC.FormatDateTimeBr()
            };

            return new Retorno<ProprietarioConsultarPorRntrcModel>(true, retorno);
        }

        public Retorno<ProprietarioConsultarPamcardModel> ConsultarPamcard(string token, string cnpjAplicacao, string cnpjcpfProprietario)
        {
            var empresasAutorizadas = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token).Select(c => c.IdEmpresa).ToList();
            var proprietario = _proprietarioApp.All().FirstOrDefault(c => c.CNPJCPF == cnpjcpfProprietario && empresasAutorizadas.Contains(c.IdEmpresa));

            if (proprietario == null)
                return new Retorno<ProprietarioConsultarPamcardModel>(false, $"{WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]} não encontrado. ", null);

            var retorno = new ProprietarioConsultarPamcardModel
            {
                IdEmpresa = proprietario.IdEmpresa,
                RazaoSocial = proprietario.RazaoSocial,
                CNPJCPF = proprietario.CNPJCPF,
                IE = proprietario.IE,
                IdProprietario = proprietario.IdProprietario,
                NomeFantasia = proprietario.NomeFantasia,
                RG = proprietario.RG,
                RGOrgaoExpedidor = proprietario.RGOrgaoExpedidor,
                RNTRC = proprietario.RNTRC,
                StatusIntegracao = proprietario.StatusIntegracao,
                TipoContrato = proprietario.TipoContrato,
                Contatos = new List<ProprietarioContatoConsultarModel>(),
                Enderecos = new List<ProprietarioEnderecoConsultarModel>()
            };

            if (proprietario.Contatos != null && proprietario.Contatos.Any())
                foreach (var contato in proprietario.Contatos)
                {
                    retorno.Contatos.Add(new ProprietarioContatoConsultarModel
                    {
                        Email = contato.Email,
                        Celular = contato.Celular,
                        IdContato = contato.IdContato,
                        Telefone = contato.Telefone
                    });
                }

            if (proprietario.Enderecos != null && proprietario.Enderecos.Any())
                foreach (var endereco in proprietario.Enderecos)
                {
                    retorno.Enderecos.Add(new ProprietarioEnderecoConsultarModel
                    {
                        Bairro = endereco.Bairro,
                        CEP = endereco.CEP,
                        Complemento = endereco.Complemento,
                        Endereco = endereco.Endereco,
                        IdEndereco = endereco.IdEndereco,
                        Numero = endereco.Numero,
                        CodigoIBGECidade = endereco.Cidade?.IBGE,
                        CodigoIBGEEstado = endereco.Estado?.IBGE,
                        CodigoBACENPais = endereco.Pais?.BACEN
                    });
                }

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, cnpjAplicacao, CartoesService.AuditDocIntegracao, null);

            var produtos = new List<int> {cartoesApp.GetIdProdutoCartaoFretePadrao()};

            var cartaoVinculadoProprietario = cartoesApp.GetCartoesVinculados(cnpjcpfProprietario, produtos);

            if (cartaoVinculadoProprietario.Cartoes != null && cartaoVinculadoProprietario.Cartoes.Any())
            {
                retorno.Cartoes = new List<Cartao>();
                foreach (var cartao in cartaoVinculadoProprietario.Cartoes)
                {
                    retorno.Cartoes.Add(new Cartao
                    {
                        Numero = cartao.Identificador.HasValue ? cartao.Identificador.Value : 0,
                        Tipo = cartao.Produto.Nome
                    });
                }
            }

            var contasBancarias = cartoesApp.GetContaBancaria(cnpjcpfProprietario);
            if (contasBancarias != null && contasBancarias.Any())
            {
                retorno.Contas = new List<Conta>();
                foreach (var conta in contasBancarias)
                    retorno.Contas.Add(new Conta
                    {
                        Numero = conta.Conta,
                        Agencia = conta.Agencia,
                        Banco = conta.CodigoBacenBanco,
                        BancoNome = conta.NomeBanco,
                        Tipo = conta.TipoConta.ToString()
                    });
            }

            var request = new ConsultarSituacaoTransportadorRequest
            {
                RntrcTransportador = proprietario.RNTRC,
                CpfCnpjInteressado = cnpjAplicacao,
                CpfCnpjTransportador = proprietario.CNPJCPF
            };

            var consultarSituacaoTransportadorReponse = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.ConsultarSituacaoTransportador(request)
                : _ciotV3App.ConsultarSituacaoTransportador(request);

            retorno.StatusRntrc = consultarSituacaoTransportadorReponse.RntrcAtivo.HasValue && consultarSituacaoTransportadorReponse.RntrcAtivo.Value ? "Ativo" : "Inativo";

            return new Retorno<ProprietarioConsultarPamcardModel>(true, retorno);
        }

        public byte[] GerarRelatorioGridProprietarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string extensao)
        {
            return _proprietarioApp.GerarRelatorioGridProprietarios(idEmpresa, orderFilters, filters, GetLogo(idEmpresa), extensao);
        }

        public ValidationResult Cadastrar(ProprietarioCreateModel model, EPerfil perfilUsuario, int? idEmpresa)
        {
            try
            {
                var proprietario = new Proprietario();

                if (model.IdProprietario > 0)
                    proprietario = _proprietarioApp.GetAllChilds(model.IdProprietario);

                ReflectionHelper.CopyProperties(model, proprietario);

                if (!model.TipoCarregamentoFrete.HasValue)
                    return new ValidationResult().Add("Tipo de carregamente Frete não informado");

                proprietario.TipoCarregamentoFrete = model.TipoCarregamentoFrete.Value;
                proprietario.PercentualTransferenciaMotorista = model.PercentualTransferenciaMotorista;
                proprietario.TransferirEntreCartoes = model.TransferirEntreCartoes;

                if (perfilUsuario != EPerfil.Administrador)
                    proprietario.IdEmpresa = idEmpresa.GetValueOrDefault();

                var contato = model.Contatos?.FirstOrDefault();
                
                if (contato?.Telefone == null)
                    return new ValidationResult().Add("Telefone não informado.");
                    
                proprietario.Contatos = new List<ProprietarioContato>
                {
                    new ProprietarioContato
                    {
                        IdEmpresa = contato.IdEmpresa,
                        Celular = contato.Celular,
                        Email = contato.Email,
                        Telefone = contato.Telefone,
                    }
                };

                var endereco = model.Enderecos?.FirstOrDefault();
                
                if(endereco?.IdCidade == null || endereco.IdCidade == 0)
                    return new ValidationResult().Add("Cidade não informada.");
                
                proprietario.Enderecos = new List<ProprietarioEndereco>
                {
                    new ProprietarioEndereco
                    {
                        IdEmpresa = endereco.IdEmpresa,
                        Bairro = endereco.Bairro,
                        CEP = endereco.CEP,
                        Complemento = endereco.Complemento,
                        Endereco = endereco.Endereco,
                        IdCidade = endereco.IdCidade,
                        IdEstado = endereco.IdEstado,
                        IdPais = endereco.IdPais,
                        Numero = endereco.Numero
                    }
                };

                if (perfilUsuario != EPerfil.Administrador)
                    proprietario.IdEmpresa = idEmpresa.GetValueOrDefault();

                if (proprietario.IdProprietario > 0)
                {
                    var result = _proprietarioApp.Update(proprietario);
                    if (!result.IsValid) return result;

                }
                else
                {
                    var result = _proprietarioApp.Add(proprietario);
                    if (!result.IsValid) return result;
                }
                
                #region Dados pix e integracao no mscartao caso seja cadastro novo
                
                if (_userIdentity.Perfil == (int) EPerfil.Administrador && _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosPix(_userIdentity.IdUsuario))
                {
                    if(model.PermiteReceberPagamentoPix != null)
                    {
                        _parametrosApp.SetProprietarioPermiteReceberPagamentoPix(proprietario.CNPJCPF, proprietario.IdEmpresa,
                            model.PermiteReceberPagamentoPix.Value);
                    }

                    if (_parametrosApp.GetProprietarioPermiteReceberPagamentoPix(proprietario.CNPJCPF, proprietario.IdEmpresa) &&
                        model.DadosBancariosPix != null)
                    {
                        if (model.DadosBancariosPix.TipoChavePix == null) 
                            model.DadosBancariosPix.ChavePix = null;
                        
                        if (string.IsNullOrEmpty(model.DadosBancariosPix.ChavePix))
                        {
                            if (!string.IsNullOrEmpty(model.DadosBancariosPix.Agencia) || !string.IsNullOrEmpty(model.DadosBancariosPix.Conta) || !string.IsNullOrEmpty(model.DadosBancariosPix.CodBanco))
                            {
                                if(string.IsNullOrEmpty(model.DadosBancariosPix.Agencia) || string.IsNullOrEmpty(model.DadosBancariosPix.Conta) || string.IsNullOrEmpty(model.DadosBancariosPix.CodBanco)) 
                                    throw new InvalidOperationException("Caso deseje informar dados bancários em vez de uma chave Pix, é obrigatório informar conta, agência e banco.");
                            }
                        }

                        var cnpjEmpresa = _empresaService.GetCnpjById(proprietario.IdEmpresa);
                        
                        if (model.DadosBancariosPix.TipoChavePix != null && !string.IsNullOrWhiteSpace(model.DadosBancariosPix.ChavePix))
                        {
                            if (model.DadosBancariosPix.TipoChavePix == ETipoChavePix.Cpf &&
                                (model.DadosBancariosPix.ChavePix.Length != 11 ||
                                 model.DadosBancariosPix.ChavePix.OnlyNumbers().Length != 11))
                                throw new InvalidOperationException(
                                    "Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos.");
                            if (model.DadosBancariosPix.TipoChavePix == ETipoChavePix.Cnpj &&
                                (model.DadosBancariosPix.ChavePix.Length != 14 ||
                                 model.DadosBancariosPix.ChavePix.OnlyNumbers().Length != 14))
                                throw new InvalidOperationException(
                                    "Chave de tipo CNPJ com formato inválido. Deve ter 14 caracteres numéricos.");
                            if (model.DadosBancariosPix.TipoChavePix == ETipoChavePix.Phone &&
                                (model.DadosBancariosPix.ChavePix.Length != 11 ||
                                 model.DadosBancariosPix.ChavePix.OnlyNumbers().Length != 11))
                                throw new InvalidOperationException(
                                    "Chave de tipo Telefone com formato inválido. Deve ter 11 caracteres numéricos.");
                            if (model.DadosBancariosPix.TipoChavePix == ETipoChavePix.Email &&
                                (!model.DadosBancariosPix.ChavePix.Contains('@') ||
                                 !model.DadosBancariosPix.ChavePix.Contains('.')))
                                throw new InvalidOperationException(
                                    "Chave de tipo Email com formato inválido.");
                            if (model.DadosBancariosPix.TipoChavePix == ETipoChavePix.Evp &&
                                model.DadosBancariosPix.ChavePix.Length != 36)
                                throw new InvalidOperationException(
                                    "Chave de tipo Aleatoria com formato inválido. Deve ter 36 caracteres incluindo os hífens.");
                        }

                        var request = new IntegrarDadosBancariosPixModelRequest
                        {
                            DocumentoProprietario = proprietario.CNPJCPF,
                            DocumentoEmpresa = cnpjEmpresa,
                            Nome = proprietario.RazaoSocial,
                            NomeFantasia = proprietario.NomeFantasia,
                            TipoChavePix = model.DadosBancariosPix.TipoChavePix,
                            ChavePix = model.DadosBancariosPix.ChavePix,
                            Agencia = model.DadosBancariosPix.Agencia,
                            Conta = model.DadosBancariosPix.Conta,
                            CodBanco = model.DadosBancariosPix.CodBanco,
                            Endereco = new IntegrarDadosBancariosPixModelRequestEndereco()
                            {
                                Cep = endereco.CEP,
                                Bairro = endereco.Bairro,
                                Complemento = endereco.Complemento,
                                Numero = endereco.Numero.ToString(),
                                Logradouro = endereco.Endereco,
                            },
                            Info = new IntegrarDadosBancariosPixModelRequestInfo()
                            {
                                Telefone = contato.Telefone,
                                Celular = contato.Celular,
                                Email = contato.Email,
                                Rg = proprietario.RG,
                                NomeMae = proprietario.NomeMae,
                                NomePai = proprietario.NomePai,
                                TacEquiparado = proprietario.EquiparadoTac
                            }
                        };

                        var idEstado = model.Enderecos?.FirstOrDefault()?.IdEstado;
                        var idCidade = model.Enderecos?.FirstOrDefault()?.IdCidade;
                        if (idEstado != null && idEstado != 0 && idCidade != null && idCidade != 0)
                        {
                            var ibgeCidade = _cidadeApp.GetListaCidade(idEstado.Value)
                                .Where(c => c.IdCidade == idCidade).Select(c => c.IBGE).FirstOrDefault();
                            if (ibgeCidade != null && ibgeCidade != 0)
                            {
                                request.Endereco.CidadeIbge = ibgeCidade.Value;
                            }
                        }
                    
                        var resultadoIntegracao = _bizApiClient.PostDadosBancariosPix(request);
                        
                        if (!resultadoIntegracao.Success)
                            throw new InvalidOperationException("Não foi possível salvar os dados Pix do proprietário. " + resultadoIntegracao.Messages.FirstOrDefault());
                    }
                }
                
                #endregion

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Retorno<ConsultarTarifaProximaViagemResponse> ConsultarTarifaAnttProximaViagem(string token, string cnpjAplicacao, string cnpjEmpresa, string cnpjCpfProprietario,
            string rntrc, string placa)
        {
            var proprietarioApp = _proprietarioApp;
            var proprietario = proprietarioApp.GetPorCpfCnpj(cnpjCpfProprietario);
            if (proprietario == null)
                return new Retorno<ConsultarTarifaProximaViagemResponse>(false, "Não foi encontrado nenhum proprietário para o documento informado", null);

            var propAntt = proprietarioApp.EquiparadoTac(cnpjCpfProprietario, rntrc, cnpjEmpresa);
            if (propAntt.Retorno == ERetornoConsultaTAC.Erro)
                return new Retorno<ConsultarTarifaProximaViagemResponse>(false, propAntt.Mensagem, null);

            var empresaSerice = _empresaService;
            var tarifasMeioHomologado = empresaSerice.GetTarifasMeioHomologado(cnpjEmpresa);

            var result = new ConsultarTarifaProximaViagemResponse
            {
                GerarCiot = propAntt.Retorno == ERetornoConsultaTAC.Equiparado,
                GerarTarifa = propAntt.Retorno == ERetornoConsultaTAC.Equiparado,
                EquiparadoTac = propAntt.Retorno == ERetornoConsultaTAC.Equiparado,
                TipoProprietario = propAntt.TipoTransportador,
                VeiculoPertenceFrota = null, // Tirar null quando necessitar criar a integração com ANTT.
                QuantidadeTotalTarifas = tarifasMeioHomologado.QuantidadeTotal,
                ValorTotalTarifas = tarifasMeioHomologado.ValorTotal,
                Fonte = propAntt.FalhaComunicacaoAntt ? "Offline" : "ANTT"
            };

            result.MensagemInformativa =
                "Este transportador {0}está equiparado a Transportador Autônomo de Cargas e portanto, {0}está OBRIGADO a cumprir as disposições da Resolução ANTT 3658/11."
                    .FormatEx(result.EquiparadoTac ? "" : "NÃO ");

            return new Retorno<ConsultarTarifaProximaViagemResponse>
            {
                Sucesso = true,
                Objeto = result
            };
        }

        public Retorno<ConsultarParametroCartaoDTO> ConsultarParametrosCartaoProprietario(string token, string cnpjAplicacao, string cnpjEmpresa, string cnpjCpfProprietario)
        {
            var empService = _empresaService;
            var parametroService = _parametrosApp;

            var emp = 0;// empService.GetIdPorCNPJ(cnpjEmpresa);

            var prop = _proprietarioApp.GetIdPorCpfCnpj(cnpjCpfProprietario, emp);

            if (prop == null)
                return new Retorno<ConsultarParametroCartaoDTO>(false, $"Proprietário com CPF/CNPJ {cnpjCpfProprietario} não localizado!", null);

            var retorno = parametroService.GetPercentualTransferenciaCartaoProprietario(cnpjCpfProprietario);

            return new Retorno<ConsultarParametroCartaoDTO>(true, retorno);
        }

        public Retorno<object> AtualizarParametrosCartaoProprietario(AtualizarParametroCartaoProprietarioRequest request)
        {
            var empService = _empresaService;
            var parametroService = _parametrosApp;

            var empId = 0;// empService.GetIdPorCNPJ(request.CNPJEmpresa);

            var prop = _proprietarioApp.GetPorCpfCnpj(request.CnpjCpfProprietario, empId);
            if (prop == null)
                return new Retorno<object>(false, $"Proprietário com CPF/CNPJ {request.CnpjCpfProprietario} não localizado para empresa {empId}!", null);

            var requisicao = new PercentualTransferenciaFreteViagemParametro();

            requisicao.Adiantamento = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.Adiantamento)).Select(c => c.Valor).FirstOrDefault();
            requisicao.Abastecimento = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.Abastecimento)).Select(c => c.Valor).FirstOrDefault();
            requisicao.Abono = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.Abono)).Select(c => c.Valor).FirstOrDefault();
            requisicao.Estadia = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.Estadia)).Select(c => c.Valor).FirstOrDefault();
            requisicao.Saldo = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.Saldo)).Select(c => c.Valor).FirstOrDefault();
            requisicao.TarifaAntt = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.TarifaAntt)).Select(c => c.Valor).FirstOrDefault();
            requisicao.RPA = request.PercentuaisTransferenciaMotorista.Where(c => c.Chave == nameof(requisicao.RPA)).Select(c => c.Valor).FirstOrDefault();

            var result = parametroService.SetPercentualTransferenciaFreteProprietario(requisicao, request.CnpjCpfProprietario);

            return new Retorno<object>(
                result.IsValid,
                result.IsValid ? null : result.Errors.FirstOrDefault()?.Message,
                null);
        }

        public AtualizaBaseEquiparadoTacResponse AtualizarBaseEquiparadoTac(string cnpjcpf = null)
        {
            return _proprietarioApp.AtualizarBaseEquiparadoTac(cnpjcpf);
        }

        public AtualizaBaseEquiparadoTacResponse AtualizarCadastroServicoCartao(List<string> cnpjcpf)
        {
            return _proprietarioApp.AtualizarCadastroServicoCartao(cnpjcpf);
        }

        public ConsultarSituacaoVeiculosAnttResponse ConsultarSituacaoVeiculosAntt(string cnpjEmpresa, string cpfCnpjProprietario, List<string> placas)
        {
            var proprietarioAnttDto = _proprietarioApp.GetDadosProprietarioAntt(cpfCnpjProprietario, cnpjEmpresa);

            if(proprietarioAnttDto == null)
                return new ConsultarSituacaoVeiculosAnttResponse(false, $"Proprietário com CPF/CNPJ {cpfCnpjProprietario} não localizado para empresa {cnpjEmpresa}!");

            var request = new ConsultarFrotaTransportadorRequest
            {
                CpfCnpjInteressado = cnpjEmpresa,
                CpfCnpjTransportador = cpfCnpjProprietario,
                RntrcTransportador = proprietarioAnttDto.Rntrc,
                Placa = new ObservableCollection<string>()
            };
            placas.ForEach(placa => request.Placa.Add(placa));

            var responseAntt = _ciotV3App.ConsultarFrotaTransportador(request);

            if (responseAntt.Sucesso == true)
                return new ConsultarSituacaoVeiculosAnttResponse(true, "Consulta realizada com sucesso")
                {
                    Objeto = new SituacaoVeiculosAnttResponseDto
                    {
                        IdProprietario = proprietarioAnttDto.IdProprietario,
                        RntrcAtivo = responseAntt.RntrcAtivo == true,
                        Placas = responseAntt.VeiculoTransportador
                            .Select(veiculo => new PlacaStatusFrotaTransportadorDto(veiculo.PlacaVeiculo, veiculo.SituacaoVeiculoFrotaTransportador))
                            .ToList()
                    }
                };

            return new ConsultarSituacaoVeiculosAnttResponse(false, responseAntt.Excecao.Mensagem);
        }

        public Retorno<object> PermiteTransferenciaSemCartaFrete(string cnpjcpf)
        {
            var valido = _proprietarioApp.PermiteTransferenciaSemCartaFrete(cnpjcpf);
            return new Retorno<object>(valido);
        }

        public Retorno SetProprietarioPercentual(PercentualProprietarioRequestDTO request)
        {
            var validacaoChamada = request.ValidaRequest();

            if (!validacaoChamada.IsValid)
                return new Retorno(false, validacaoChamada.ToString());

            var parametroApp = _parametrosApp;

            var parametro = new PercentualTransferenciaFreteViagemParametro();

            parametro.Abastecimento = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Abastecimento).Select(c => c.Percentual).FirstOrDefault();
            parametro.Abono = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Abono).Select(c => c.Percentual).FirstOrDefault();
            parametro.Adiantamento = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Adiantamento).Select(c => c.Percentual).FirstOrDefault();
            parametro.Estadia = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Estadia).Select(c => c.Percentual).FirstOrDefault();
            parametro.Saldo = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Saldo).Select(c => c.Percentual).FirstOrDefault();
            parametro.TarifaAntt = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).Select(c => c.Percentual).FirstOrDefault();
            parametro.RPA = request.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.RPA).Select(c => c.Percentual).FirstOrDefault();

            var proprietarioParametrosTran = parametroApp.SetPercentualTransferenciaFreteProprietario(parametro, request.DocumentoProprietario);

            return new Retorno(proprietarioParametrosTran);
        }

        public Retorno SetProprietarioMotoristaPercentual(PercentualProprietarioMotoristaRequestDTO request)
        {
            var validacaoChamada = request.ValidaRequest();

            if (!validacaoChamada.IsValid)
                return new Retorno(false, validacaoChamada.ToString());

            var result = new ValidationResult();

            var parametroApp = _parametrosApp;

            var parametro = new PercentualTransferenciaFreteViagemParametro();

            foreach (var motorista in request.Motoristas)
            {
                parametro.Abastecimento = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Abastecimento).Select(c => c.Percentual).FirstOrDefault();
                parametro.Abono = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Abono).Select(c => c.Percentual).FirstOrDefault();
                parametro.Adiantamento = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Adiantamento).Select(c => c.Percentual).FirstOrDefault();
                parametro.Estadia = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Estadia).Select(c => c.Percentual).FirstOrDefault();
                parametro.Saldo = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.Saldo).Select(c => c.Percentual).FirstOrDefault();
                parametro.TarifaAntt = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).Select(c => c.Percentual).FirstOrDefault();
                parametro.RPA = motorista.PercentuaisTransferencias.Where(c => c.TipoEventoViagem == ETipoEventoViagem.RPA).Select(c => c.Percentual).FirstOrDefault();

                var proprietarioParametrosTran = parametroApp.SetPercentualTransferenciaFreteProprietarioMotorista(parametro, request.DocumentoProprietario, motorista.DocumentoMotorista);

                result.Add(proprietarioParametrosTran);
            }

            return new Retorno(result);
        }
    }
}

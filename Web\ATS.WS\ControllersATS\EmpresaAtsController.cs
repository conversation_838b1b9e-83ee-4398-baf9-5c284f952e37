﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Empresa;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.ControllersATS
{
    public class EmpresaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvEmpresa _srvEmpresa;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IModuloApp _moduloApp;
        private readonly IBloqueioGestorValorApp _bloqueioGestorValor;
        private readonly IPlanoApp _planoApp;
        private readonly IUsuarioPermissaoFinanceiroService _usuarioPermissaoFinanceiroService;

        public EmpresaAtsController(IUserIdentity userIdentity, IParametrosApp parametrosApp, SrvEmpresa srvEmpresa, IUsuarioApp usuarioApp, IEmpresaApp empresaApp, 
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IModuloApp moduloApp, IBloqueioGestorValorApp bloqueioGestorValor, IPlanoApp planoApp, IUsuarioPermissaoFinanceiroService usuarioPermissaoFinanceiroService)
        {
            _userIdentity = userIdentity;
            _parametrosApp = parametrosApp;
            _srvEmpresa = srvEmpresa;
            _usuarioApp = usuarioApp;
            _empresaApp = empresaApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _moduloApp = moduloApp;
            _bloqueioGestorValor = bloqueioGestorValor;
            _planoApp = planoApp;
            _usuarioPermissaoFinanceiroService = usuarioPermissaoFinanceiroService;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [Validate2FA]
        [EnableLogAudit]
        [EnableLogRequest]
        public JsonResult Cadastrar(EmpresaCreateRequest model)
        {
            try
            {
                if (model.IdEmpresa.HasValue)
                {
                    var retornoEdit = _srvEmpresa.Editar(model, _userIdentity.CpfCnpj, _userIdentity.Nome, model.AdministradoraPlataforma);
                    //IntegrarPlataformaMicroServicos
                    return retornoEdit.IsValid
                        ? ResponderSucesso("Empresa atualizada com sucesso!")
                        : ResponderErro(retornoEdit.Errors.FirstOrDefault()?.Message);
                }

                if(!_parametrosApp.GetPermitirEdicaoDadosAdministrativosEmpresa(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");

                var retorno = _srvEmpresa.Cadastrar(model, _userIdentity.CpfCnpj, _userIdentity.Nome, model.AdministradoraPlataforma);

                return retorno.IsValid
                    ? ResponderSucesso("Empresa cadastrada com sucesso!")
                    : ResponderErro(retorno.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ConsultarPorId(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var empresa = _srvEmpresa.ConsultarPorId(idEmpresa, _userIdentity);

                return empresa == null
                    ? ResponderErro("Empresa não encontrada")
                    : ResponderSucesso(string.Empty, empresa);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ConsultarDadosPorId(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");
                
                var empresa = _srvEmpresa.ConsultarPorId(idEmpresa, _userIdentity);

                return empresa == null
                    ? ResponderErro("Empresa não encontrada")
                    : ResponderSucesso(string.Empty, new
                    {
                        empresa.CNPJ,
                        empresa.RazaoSocial
                    });
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ConsultarParametroMeioHomologado(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var empresa = _srvEmpresa.ConsultarParametroMeioHomologado(idEmpresa);

                return empresa == null
                    ? ResponderErro("Empresa não encontrada")
                    : ResponderSucesso(string.Empty, empresa);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ConsultarTodas()
        {
            try
            {
                var empresas = _empresaApp.ConsultarTodas(true);

                return ResponderSucesso(empresas);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar()
        {
            try
            {
                var empresas = _empresaApp
                    .Consultar(null, _userIdentity.IdUsuario)
                    .Where(x => x.Ativo)
                    .Select(x => new
                    {
                        x.IdEmpresa,
                        x.RazaoSocial,
                        x.CNPJ
                    }).ToList();

                return ResponderSucesso(empresas);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarIdNome()
        {
            try
            {
                if(!_userIdentity.IdEmpresa.HasValue)
                    return ResponderSucesso("Usuário não possui empresa.");
                
                var empresas = _empresaApp
                    .All()
                    .Where(x => x.IdEmpresa == _userIdentity.IdEmpresa)
                    .Select(x => new
                    {
                        x.IdEmpresa,
                        x.RazaoSocial
                    }).FirstOrDefault();

                return ResponderSucesso(empresas);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            if (order == null) 
                throw new ArgumentNullException(nameof(order));
            
            try
            {
                List<int> idsEstabelecimentoBase = null;

                if (_userIdentity.Perfil == (int) EPerfil.Estabelecimento)
                {
                    idsEstabelecimentoBase = _usuarioApp.GetComEstabelecimentos(_userIdentity.IdUsuario)
                        .UsuarioEstabelecimentos
                        .Select(x => x.IdEstabelecimento)
                        .ToList();
                }

                var empresasGrid = _empresaApp.ConsultarGrid(take, page, order, filters, idsEstabelecimentoBase, _userIdentity.IdEmpresa);
                return ResponderSucesso(empresasGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetLogoEmpresa(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var logo = _empresaApp.GetLogoPorId(idEmpresa);
                return ResponderSucesso(string.Empty, logo);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult FiltrarProdutosMestre(int? idEmpresa)
        {
            try
            {
                if (idEmpresa == null && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;
                
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var app = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario, true, idEmpresa ?? _userIdentity.IdEmpresa);
                var result = app.FiltrarProdutosMestre();
                return ResponderSucesso(result);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetModulos()
        {
            try
            {
                var modulos = _moduloApp.All();

                foreach (var modulo in modulos)
                    modulo.Ativo = false;

                return ResponderSucesso(string.Empty, modulos);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridEmpresa(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var retorno = _empresaApp.ConsultarGridEmpresa(idEmpresa, take, page, order, filters);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridEmpresas(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa ?? 0;

            var report = _srvEmpresa.GerarRelatorioGridEmpresas(filtrosGridModel.IdEmpresa, filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de empresa.{filtrosGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult AlterarStatus(int idEmpresa)
        {
            try
            {
                if(!_parametrosApp.GetPermitirEdicaoDadosAdministrativosEmpresa(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");
                
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var retorno = _empresaApp.
                    AlterarStatus(idEmpresa);

                return !retorno.IsValid
                    ? ResponderErro(retorno.Errors.FirstOrDefault()?.Message)
                    : ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult UsuarioPermiteModuloAlcadas()
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Administrador)
                    return ResponderSucesso(string.Empty, true);

                if (_userIdentity.Perfil != (int) EPerfil.Empresa)
                    return ResponderSucesso(string.Empty, false);

                var usuario = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(_userIdentity.IdUsuario);

                return ResponderSucesso(string.Empty, usuario.PermiteEmpresa);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult UsuarioPermiteEdicaoDadosAdministrativosEmpresa()
        {
            try
            {
                var usuario = _parametrosApp.GetPermitirEdicaoDadosAdministrativosEmpresa(_userIdentity.IdUsuario);

                return ResponderSucesso(string.Empty, usuario);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPlanosAtivos()
        {
            try
            {
                var empresas = _planoApp.GetPlanosAtivos();

                return ResponderSucesso(empresas);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetBloqueioGestorValor(int? idEmpresa, int? idFilial)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                return ResponderSucesso(string.Empty, _bloqueioGestorValor.GetBloqueioGestorValor(idEmpresa,idFilial));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaDetalhesEmpresaMeioHomologado(string cnpj, int cartao, int produto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(cnpj))
                    throw new InvalidOperationException("CNPJ é obrigatório.");
                
                var app = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario, true, _userIdentity.IdEmpresa);
                var infoAdministradora = app.AdministradoraCartao(cartao, produto);

                var cartoesAppNovo = CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(),
                        _userIdentity.IdEmpresa ?? infoAdministradora.EmpresaId, _userIdentity.CpfCnpj, _userIdentity.Nome);

                var resposta = cartoesAppNovo.ConsultarEmpresa(cnpj);
                
                if(resposta.Sucesso)
                    return ResponderSucesso(resposta);

                return ResponderErro(resposta.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult ConsultarSaldo(int idEmpresa)
        {
            try
            {
                if(idEmpresa == 0)
                    return ResponderErro("Não foi possível consultar o saldo da empresa!");

                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa != idEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, idEmpresa, _userIdentity?.CpfCnpj, _userIdentity?.Nome);
                var cnpj = _empresaApp.GetCnpj(idEmpresa);

                var resposta = cartoesApp.ConsultarSaldoEmpresa(cnpj);
                
                if(resposta.Status == ConsultarSaldoEmpresaResponseStatus.Sucesso)
                    return ResponderSucesso(resposta.SaldoConta.FormatMoney());

                return ResponderErro(resposta.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult SaldoTopbar()
        {
            try
            {
                if (_userIdentity?.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar o saldo.");

                var permissao = _usuarioPermissaoFinanceiroService.GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, 
                    EBloqueioFinanceiroTipo.permiteVisualizarSaldoFreteTopbar);
                
                if(permissao == null || !permissao.DesbloquearFinanceiro)
                    return ResponderErro("Sem permissão para consultar o saldo.");

                var idEmpresa = _userIdentity.IdEmpresa.Value;

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, idEmpresa, _userIdentity?.CpfCnpj, _userIdentity?.Nome);
                var cnpj = _empresaApp.GetCnpj(idEmpresa);

                var resposta = cartoesApp.ConsultarSaldoEmpresa(cnpj);
                
                if(resposta.Status == ConsultarSaldoEmpresaResponseStatus.Sucesso)
                    return ResponderSucesso("", (resposta.SaldoConta ?? 0).ToString("C2"));

                return ResponderErro(resposta.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetParametrosViagem(int idEmpresa)
        {
            try
            {
                return ResponderSucesso(_empresaApp.GetParametrosViagem(idEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridEmpresasHubPedagio(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var empresasGrid = _empresaApp.ConsultarGridEmpresasHubPedagio(take, page, order, filters);
                return ResponderSucesso(empresasGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetUtilizaRelatoriosOfx()
        {
            try
            {
                if (_userIdentity.IdEmpresa == null) return ResponderErro("Dados inválidos.");
                var parametro = _parametrosApp.GetUtilizaRelatoriosOfx(_userIdentity.IdEmpresa.Value);
                return ResponderSucesso(parametro);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro("Erro ao consultar parâmetro.");
            }
        }
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ConjuntoCarretaMap : EntityTypeConfiguration<ConjuntoCarreta>
    {
        public ConjuntoCarretaMap()
        {
            ToTable("CONJUNTO_CARRETA");

            HasKey(t => new {t.IdConjuntoCarreta});

            Property(t => t.IdConjuntoCarreta)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Placa)
                .HasMaxLength(7);

            HasRequired(x => x.Conjunto)
                .WithMany(x => x.Carreta<PERSON>)
                .HasForeignKey(x => x.IdConjunto);

            HasRequired(t => t.TipoCarreta)
                .WithMany(t => t.ConjuntoCarretas)
                .HasForeignKey(t => t.IdTipoCarreta);
        }
    }
}
﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class BloqueioGestorValorApp : AppBase, IBloqueioGestorValorApp
    {
        private readonly IBloqueioGestorValorService _bloqueioGestorValorService;

        public BloqueioGestorValorApp(IBloqueioGestorValorService bloqueioGestorValorService)
        {
            _bloqueioGestorValorService = bloqueioGestorValorService;
        }

        public IQueryable<BloqueioGestorValor> GetAll()
        {
            return _bloqueioGestorValorService.GetAll();
        }

        public BloqueioGestorValorDto GetBloqueioGestorValor(int? idEmpresa, int? idFilial)
        {
            return _bloqueioGestorValorService.PegarBloqueioGestorValor(idEmpresa, idFilial);
        }

        public void IntegrarValores(BloqueioGestorValorDto valores)
        {
            _bloqueioGestorValorService.IntegrarValores(valores);
        }

        public decimal? ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa,EBloqueioOrigemTipo? origem)
        {
            return _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(bloqueioGestorTipo, idEmpresa,origem);
        }

        public decimal? ValorLimiteConfiguradoFilial(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa, int? idFilial,EBloqueioOrigemTipo? origem)
        {
            return _bloqueioGestorValorService.ValorLimiteConfiguradoFilial(bloqueioGestorTipo, idEmpresa,idFilial,origem);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.Viagem.ConsultaViagem;

namespace ATS.CrossCutting.Reports.Pedagio.PedagioAvulso
{
    public class RelatorioConsultaPedagioAvulsoType
    {
        // ReSharper disable once InconsistentNaming
        public IList<RelatorioPedagioAvulsoItemDataType> items { get; set; }

        // ReSharper disable once InconsistentNaming
        public int totalItems { get; set; }
    }

    public class RelatorioPedagioAvulsoItemDataType
    { 
        public DateTime? DataInicioFrete { get; set; }
        public DateTime? DataFimFrete { get; set; }
        public DateTime? DataEmissao { get; set; }
        public string NumeroDocumentoCliente { get; set; }
        public string ValorPedagio { get; set; }
        public string NomeEmpresa { get; set; }
        public string CnpjFilial { get; set; }
        public string NomeFilial { get; set; }
        public string Placa { get; set; }
        public string CnpjCpfProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string CpfMotorista { get; set; }
        public string StatusVpo { get; set; }
        public string NomeMotorista { get; set; }
        public int IdViagem { get; set; }
        public string FornecedorPedagio { get; set; }
        public string FornecedorCnpj { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
    }
}
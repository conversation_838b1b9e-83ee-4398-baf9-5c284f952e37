﻿using ATS.CrossCutting.IoC.Models;

namespace ATS.CrossCutting.IoC.Interfaces
{
    public interface IUserIdentity
    {
        /// <summary>
        /// Chave primária do usuário na base
        /// </summary>
        int IdUsuario { get; set; }

        string Nome { get; set; }

        /// <summary>
        /// Documento do usuário (CPF ou CNPJ), não formatado
        /// </summary>
        string CpfCnpj { get; set; }

        /// <summary>
        /// Perfil do usuário
        /// </summary>
        int Perfil { get; set; }

        /// <summary>
        /// Código da empresa do usuário, quando o mesmo estiver vinculado a tal
        /// </summary>
        int? IdEmpresa { get; set; }

        /// <summary>
        /// Cnpj da empresa do usuário, quando o mesmo estiver vinculado a tal
        /// </summary>
        string CnpjEmpresa { get; set; }

        string IpUsuario { get; set; }

        /// <summary>
        /// Origem do 
        /// </summary>
        EUserOrigin? Origem { get; set; }
        
        public decimal Latitude { get; set; }
        
        public decimal Longitude { get; set; }
        
        public string Cidade { get; set; }
        
        public string Provedor { get; set; }
        
        public string Estado { get; set; }
    }
}

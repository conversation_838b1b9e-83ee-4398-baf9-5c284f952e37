﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Interface.Service;
using ATS.Domain.Enum;

namespace ATS.Application.Application
{
    public class GrupoUsuarioMenuApp
    {
        private readonly IGrupoUsuarioMenuService _grupoUsuarioMenuService;

        public GrupoUsuarioMenuApp(IGrupoUsuarioMenuService grupoUsuarioMenuService)
        {
            _grupoUsuarioMenuService = grupoUsuarioMenuService;
        }

        public IQueryable<GrupoUsuarioMenu> GetAllPorIdMenu(int idMenu)
        {
            return _grupoUsuarioMenuService.GetAllPorIdMenu(idMenu);
        }

        public ValidationResult DeletePorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _grupoUsuarioMenuService.DeletePorIdGrupoUsuario(idGrupoUsuario);
        }

        public IQueryable<GrupoUsuarioMenu> GetAllMenusPorGrupoUsuario(int idGrupoUsuario)
        {
            return _grupoUsuarioMenuService.GetAllMenusPorGrupoUsuario(idGrupoUsuario);
        }

        public bool HasMenuLiberado(int idGrupoUsuario, EMenu idMenu)
        {
            return _grupoUsuarioMenuService.HasMenuLiberado(idGrupoUsuario, idMenu);
        }
    }
}

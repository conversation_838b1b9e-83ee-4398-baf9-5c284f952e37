﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Service;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class IconeApp : AppBase, IIconeApp
    {
        private readonly IIconeService _iconeService;

        public IconeApp(IIconeService iconeService)
        {
            _iconeService = iconeService;
        }

        public IQueryable<Icone> Consultar()
        {
            return _iconeService.Consultar();
        }

        public Icone GetFirst(EIconePara? iconePara)
        {
            return _iconeService.GetFirst(iconePara);
        }

        public Icone Get(int idIcone)
        {
            return _iconeService.Get(idIcone);
        }
    }
}

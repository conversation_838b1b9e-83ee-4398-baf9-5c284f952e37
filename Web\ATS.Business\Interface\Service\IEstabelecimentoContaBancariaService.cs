﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IEstabelecimentoContaBancariaService : IService<EstabelecimentoContaBancaria>
    {
        EstabelecimentoContaBancaria GetById(int id);

        List<object> GetContasBancariasByEstabalecimento(int idEstabelecimento);

        void DeleteListContasBancarias(List<int> idsContasBancarias);
    }
}

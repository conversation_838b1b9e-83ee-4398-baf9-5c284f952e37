﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.Reports.Credenciamento;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Service
{
    public interface ICredenciamentoService : IService<Credenciamento>
    {
        ValidationResult Add(Credenciamento credenciamento, int administradoraPlataforma);
        ValidationResult Update(Credenciamento credenciamento, int administradoraPlataforma);
        IEnumerable<Credenciamento> GetPorEmpresa(int? idEmpresa);
        IQueryable<Credenciamento> GetQuery();
        List<int> GetIdsEmpresaSolicitadoCredenciamento(int idEstabelecimentoBase);
        object GetDetalhesRejeicaoCredenciamento(int idCredenciamento);
        bool HasDocumentacaoVencendo(int idCredenciamento);
        bool EstabelecimentoCredenciado(int idEstabelecimento, int idEmpresa);
        ValidationResult AprovarCredenciamento(int idCredenciamento, string linkWebNovo, int administradoraPlataforma);
        ValidationResult AtualizaEmailEnviado(List<int> listaIdCredenciamento);
        
        #region Uso de EstabelecimentoBaseDocumentoService
        void AddCredenciamentoAnexo(int idEstabelecimento, int? idDocumento, string token, DateTime? dataValidade, string descricao);
        void UpdateCredenciamentoAnexo(int idEstabelecimento, int? idDocumento, string token, DateTime? dataValidade, string descricao);
        ValidationResult AtualizarStatusDocumentacaoPorEstabelecimento(int idEstabelecimento, int idEmpresa, EStatusDocumentacaoCredenciamento statusDocumentacao, int administradoraPlataforma);
        #endregion

        ValidationResult EnviarEmailCredenciamentoCadastroUsuario(string nomeEstab, string emailDestinatario, string nomeEmpresa,
            byte[] logoEmpresa, string nomeAplicativo, string chave, int idEstab, DateTime? dataValidadeChave, string linkAtsWebNovo,
            int administradoraPlataforma);

        object ConsultarImagemPorToken(string token);
        Credenciamento Get(int id);
        Credenciamento Get(int idEmpresa, int idEstabelecimento);
        object ConsultarGridPendentes(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        object ConsultarGridAprovados(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult Cancelar(int idCredenciamento);
        object ConsultarAnexosCredenciamento(int idEstabelecimento);

        List<object> ConsultarCredenciamentosPorEmpresa(int idEmpresa, DateTime? dtInicio, DateTime? dtFim, int administradoraPlataforma,
            bool aberto = false, bool aprovado = false, bool rejeitado = false, bool bloqueado = false, bool regular = true, bool irregular = true,
            bool aguardando = true, int? idEstabelecimento = null);

        DataModel<CredenciamentoModel> ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> Filters);
        void RejeitarCredenciamento(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma);
        void Descredenciar(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma);
        void DesbloquearCredenciamento(int idCredenciamento);
        Credenciamento GetCredenciamentoPorProtocolo(int idProtocolo);
        bool VerificarEstabelecimentoBaseAssociado(int idEstabelecimentoBase);
        void ReenviarEmailCredenciamentoUsuarioCadastro(int id, string linkWebNovo, int administradoraPlataforma);
        bool ChaveValida(string chaveEmail);
        Credenciamento GetByChaveEmail(string chaveEmail);
        List<CredenciamentoMotivo> GetAllMotivos(int idCredencimento_);

        byte[] GerarRelatorioGridCredenciamento(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,
            string tipoArquivo, string logo);

        void BloquearCredenciamento(int idCredenciamento);
        void EnviarCredenciamentoWebHook(Credenciamento credenciamento);
        EstabelecimentoCredenciadoWebHook OrganizarObjetoEstabelecimentoCredenciadoWebHook(Credenciamento credenciamento);
        List<Credenciamento> GetCredenciadosByIdEstabelecimentoBase(int idEstabelecimentoBase);
        
        bool AtualizarStatusDocumentacao(int idCredenciamento_, EStatusDocumentacaoCredenciamento status_, int administradoraPlataforma,
            string motivo_ = null);
        
        int? GetIdCredenciamento(int idEstabelecimento, int? idEmpresa = null);
        object GetStatus(int idEmpresa, int idEstabelecimento);
        object GetStatusDocumentacao(int idEmpresa, int idEstabelecimento);
        EStatusDocumentacaoCredenciamento? GetCodeStatusDocumentacao(int idEmpresa, int idEstabelecimento);
        EStatusCredenciamento? GetCodeStatus(int idEmpresa, int idEstabelecimento);
        EStatusCredenciamento? GetCodeStatusBase(int idEmpresa, int idEstabelecimento);
        List<Credenciamento> GetCredenciamentosAprovadosParaEstabBase(int idEstabelecimentoBase);
        List<Credenciamento> GetCredsAprovadosPorIdProdutoBase(int idProdutoBase);

        ValidationResult EnviarEmailAprovacaoCredenciamento(string nomeEstab, string emailDestinatario, string nomeEmpresa, byte[] logoEmpresa,
            string nomeAplicativo, int administradoraPlataforma);

        /// <summary>
        /// Envia E-mail para reprovação ou descredenciamento do estabelecimento
        /// </summary>
        /// <param name="nomeEstab"></param>
        /// <param name="emailDestinatario"></param>
        /// <param name="nomeEmpresa"></param>
        /// <param name="reprovacao">Determina se o e-mail será de reprovação ou descredenciamento</param>
        /// <param name="logoEmpresa"></param>
        ValidationResult EnviarEmailReprovacaoCredenciamento(string nomeEstab, string emailDestinatario, string nomeEmpresa, bool reprovacao,
            byte[] logoEmpresa, int administradoraPlataforma, string motivo = null, string detalhamento = null, string nomeAplicativo = null);

        ValidationResult EnviarEmailSolicitacaoCredenciamento(string nomeEmp, string nomeEstab, string emailDestinatario, byte[] logoEmpresa,
            string nomeAplicativo, int administradoraPlataforma);

        ValidationResult EnviarEmailDocumentacaoRejeitada(string nomeEstab, string emailDestinatario, string nomeEmpresa,
            int administradoraPlataforma, byte[] logoEmpresa, string motivo = null, string nomeAplicativo = null);
        
        List<Credenciamento> GetByIdEstabelecimentoBase(int? idEstabelecimentoBase);
        IQueryable<Credenciamento> GetQueryByIdEstabelecimentoBase(int idEstabelecimentoBase, int? idEmpresa);
        bool GetAllCredenciamentosIrregulares(List<int> idsEstabelecimentos_);

        ValidationResult EnviarEmailDocumentoAnexadoParaAnalise(string nomeEmp, string nomeEstab, string emailDestinatario, byte[] logoEmpresa,
            string nomeAplicativo);
    }
}
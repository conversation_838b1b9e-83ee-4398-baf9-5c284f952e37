﻿using System;

namespace ATS.Domain.Entities.Common
{
    public class EstabelecimentoBaseClass
    {
        public int IdEstabelecimento { get; set; }
        public string Descricao { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public DateTime? DataUltimaAtualizacao { get; set; }
        public int IdTipoEstabelecimento { get; set; }
        public int IdPais { get; set; }
        public int IdEstado { get; set; }
        public int IdCidade { get; set; }
        public string Bairro { get; set; }
        public string Logradouro { get; set; }
        public int? Numero { get; set; }
        public string Complemento { get; set; }
        public string CEP { get; set; }
        public string Email { get; set; }
        public string EmailProtocolo { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool Ativo { get; set; } = true;
        public string Telefone { get; set; }

    }
}

﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class PagamentoConfiguracaoProcesso
    {
        /// <summary>
        /// Código
        /// </summary>
        public int IdConfiguracao { get; set; }
        
        public int IdDocumento { get; set; }

        public EProcessoPgtoFrete Processo { get; set; }
    
        #region Referência
        /// <summary>
        /// Configuração da para a empresa
        /// </summary>
        public virtual PagamentoConfiguracao Configuracao { get; set; }
        public virtual  Documento Documento { get; set; }

        #endregion
    }
}
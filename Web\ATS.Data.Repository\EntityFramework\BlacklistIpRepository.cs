﻿using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class BlacklistIpRepository : Repository<BlacklistIp>, IBlacklistIpRepository
    {
        public BlacklistIpRepository(AtsContext context) : base(context)
        {
        }

        public IQueryable<BlacklistIp> GetByIpv4(string ipv4)
        {
            return Where(c => c.Ipv4.Equals(ipv4));
        }
    }
}
﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class MotoristaMovel
    {
        public int IdMotorista { get; set; }
        public int IdMovel { get; set; }
        public string IMEI { get; set; }

        #region Referências

        [ForeignKey("IdMotorista")]
        public virtual Motorista Motorista { get; set; }

        #endregion
    }
}
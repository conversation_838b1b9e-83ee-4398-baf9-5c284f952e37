﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class ViagemEstabelecimentoService : ServiceBase, IViagemEstabelecimentoService
    {
        private readonly IViagemEstabelecimentoRepository _viagemEstabelecimentoRepository;

        public ViagemEstabelecimentoService(IViagemEstabelecimentoRepository viagemEstabelecimentoRepository)
        {
            _viagemEstabelecimentoRepository = viagemEstabelecimentoRepository;
        }

        public List<ViagemEstabelecimento> GetPorViagem(int idViagem)
        {
            return _viagemEstabelecimentoRepository
                .Find(x => x.IdViagem == idViagem)
                .ToList();
        }
    }
}

﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioDocumentoRepository : Repository<UsuarioDocumento>, IUsuarioDocumentoRepository
    {
        public UsuarioDocumentoRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna todas as avaliações daquele respectiva motorista
        /// </summary>
        /// <param name="idUsuario">Código do motorista</param>
        /// <returns></returns>
        public IQueryable<UsuarioDocumento> GetDocumentos(int idUsuario)
        {
            return (from doc in All()
                    .Include(x => x.Usuario)
                    .Include(x => x.TipoDocumento)
                    where doc.IdUsuario == idUsuario
                    select doc);
        }

        public UsuarioDocumento GetDocCNH(int idUsuario)
        {
            var idTipoDocCNH = (from tp in ((AtsContext)Context).TipoDocumento
                                where tp.Descricao.ToLower() == "cnh"
                                select tp).FirstOrDefault()?.IdTipoDocumento;

            if (!idTipoDocCNH.HasValue)
                return null;

            var docCNH = from dox in All()
                         where dox.IdUsuario == idUsuario
                         && dox.IdTipoDocumento == idTipoDocCNH.Value
                         select dox;

            return docCNH.FirstOrDefault();
        }

        public UsuarioDocumento GetDocCNH(int idUsuario, int idTipoDoc)
        {
            var docCNH = from dox in All()
                         where dox.IdUsuario == idUsuario
                         && dox.IdTipoDocumento == idTipoDoc
                         select dox;

            return docCNH.FirstOrDefault();
        }

        public List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc)
        {
            var retorno = new List<Tuple<int, DateTime?>>();

            var validades = (from p in All()
                             where usuariosMotoristas.Contains(p.IdUsuario)
                             && p.IdTipoDocumento == tpDoc
                             select p).ToList();

            foreach (var doc in validades)
                retorno.Add(new Tuple<int, DateTime?>(doc.IdUsuario, doc.Validade));

            return retorno;
        }
    }
}
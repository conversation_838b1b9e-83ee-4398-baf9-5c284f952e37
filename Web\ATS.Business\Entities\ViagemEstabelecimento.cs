﻿using ATS.Domain.Enum;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemEstabelecimento
    {
        /// <summary>
        /// Código da viagem relacionado ao estabelecimento
        /// </summary>
        public int IdViagemEstabelecimento { get; set; }

        /// <summary>
        /// Código do estabelecimento empresa
        /// </summary>
        public int IdEstabelecimento { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Tipo de evento da viagem
        /// </summary>
        public ETipoEventoViagem TipoEventoViagem { get; set; }

        #region Virtual Fields
        public virtual Estabelecimento Estabelecimento { get; set; }
        public virtual Viagem Viagem { get; set; }
        #endregion
    }
}
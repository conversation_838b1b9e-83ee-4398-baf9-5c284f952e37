﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Request.Motorista
{
    public class MotoristaIntegrarRequest
    {
        public int? IdMotorista { get; set; }
        public int? IdEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public string Nome { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string Rg { get; set; }
        public string RgOrgaoExpedidor { get; set; }
        public string Cpf { get; set; }
        public string Sexo { get; set; }
        public string Cnh { get; set; }
        public string CnhCategoria { get; set; }
        public DateTime? ValidadeCnh { get; set; }
        public string Celular { get; set; }
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Frota;
        public string Email { get; set; }
        public string Imagem { get; set; }
        public bool Ativo { get; set; } = true;

        #region Endereço

        public string Cep { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IdPais { get; set; }
        public int IdCidade { get; set; }
        public int IdEstado { get; set; }

        #endregion

        #region Tabelas Filhas

        public List<MotoristaAvaliacaoIntegrarRequest> AvaliacoesAdicionar { get; set; }
        public List<MotoristaAvaliacaoIntegrarRequest> AvaliacoesRemover { get; set; }

        #endregion
    }

    public class MotoristaAvaliacaoIntegrarRequest
    {
        public int? IdAvaliacao { get; set; }
        public decimal Nota { get; set; }
        public string Observacao { get; set; }
    }
}

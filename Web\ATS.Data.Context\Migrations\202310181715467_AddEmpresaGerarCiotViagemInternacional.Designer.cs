﻿// <auto-generated />
namespace ATS.Data.Context.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class AddEmpresaGerarCiotViagemInternacional : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddEmpresaGerarCiotViagemInternacional));
        
        string IMigrationMetadata.Id
        {
            get { return "202310181715467_AddEmpresaGerarCiotViagemInternacional"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}

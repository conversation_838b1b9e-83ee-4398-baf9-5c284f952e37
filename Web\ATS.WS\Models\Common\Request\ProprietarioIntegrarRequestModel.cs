﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Validation;
using Autofac;

namespace ATS.WS.Models.Common.Request
{
    public class ProprietarioIntegrarRequestModel : RequestBase
    {
        public int? IdProprietario { get; set; } = null;
        public string CnpjCpf { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
        public ETipoContrato? TipoContrato { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string Endereco { get; set; }
        public string Inss { get; set; }
        public string Referencia1 { get; set; }
        public string Referencia2 { get; set; }        
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public bool HabilitarContratoCiotAgregado { get; set; }
        public ProprietarioCartaoIntegrarRequestModel Cartao { get; set; }

        public List<ProprietarioIntegrarContatoRequestModel> Contatos { get; set; }
        public List<ProprietarioIntegrarEnderecoRequestModel> Enderecos { get; set; }

        public ValidationResult ValidarEntrada()
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var cidadeApp = scope.Resolve<ICidadeApp>();
                var validation = new ValidationResult();

                if (string.IsNullOrEmpty(CnpjCpf))
                    validation.Add("Documento não informado no cadastro do proprietário.", EFaultType.Error);
                else if (CnpjCpf.Length > 14)
                    validation.Add("Documento não pode ter mais de 14 caracteres no cadastro do proprietário.", EFaultType.Error);
                else if (!CnpjCpf.ValidateDocument())
                    validation.Add("Documento inválido no cadastro do proprietário");

                if (string.IsNullOrEmpty(RazaoSocial))
                    validation.Add("Razão Social do cadastro do proprietário não informada.", EFaultType.Error);
                else if (RazaoSocial.Length > 100)
                    validation.Add("Razão Social do cadastro do proprietário não pode ter mais que 100 caracteres", EFaultType.Error);

                if (string.IsNullOrEmpty(NomeFantasia))
                    validation.Add("Nome Fantasia do cadastro do proprietário não informada.", EFaultType.Error);
                else if (NomeFantasia.Length > 100)
                    validation.Add("Nome Fantasia do cadastro do proprietário não pode ter mais que 100 caracteres", EFaultType.Error);

                if (Enderecos == null || !Enderecos.Any())
                    validation.Add("Nenhum endereço foi informado no cadastro do proprietário.", EFaultType.Error);
                else
                    foreach (var endereco in Enderecos)
                    {
                        if (endereco.IBGECidade == 0)
                            validation.Add("Código do IBGE cidade não informado no endereço do cadastro do proprietário.", EFaultType.Error);
                        else if (!cidadeApp.ValidarIbgeCadastrado(endereco.IBGECidade))
                            validation.Add("Código do IBGE cidade informado no endereço do cadastro do proprietário inválido.", EFaultType.Error);

                        if (string.IsNullOrEmpty(endereco.CEP))
                            validation.Add("CEP não informado no endereço do cadastro do proprietário.", EFaultType.Error);
                        else if (endereco.CEP.Length > 8)
                            validation.Add("CEP do endereço do cadastro do proprietário não pode ter mais de 8 caracteres.", EFaultType.Error);

                        if (string.IsNullOrEmpty(endereco.Endereco))
                            validation.Add("Endereço do cadastro do proprietário não foi informado.", EFaultType.Error);
                        else if (endereco.Endereco.Length > 100)
                            validation.Add("Endereço no cadastro do proprietario não pode ter mais de 100 caracteres.", EFaultType.Error);

                        if (string.IsNullOrEmpty(endereco.Bairro))
                            validation.Add("Bairro não informado no endereço do cadastro do proprietário.", EFaultType.Error);
                        else if (endereco.Bairro.Length > 100)
                            validation.Add("Bairro não pode ter mais de 100 caracteres no endereço do cadastro do proprietario.", EFaultType.Error);
                    }

                if (Contatos == null || !Contatos.Any())
                    validation.Add("Nenhum contato foi informado no cadastro do proprietário.", EFaultType.Error);
                else
                    foreach (var contato in Contatos)
                        if (string.IsNullOrEmpty(contato.Telefone))
                            validation.Add("Telefone não informado no contato do cadastro do proprietário.", EFaultType.Error);

                if (Cartao != null)
                    if (Cartao.NumeroCartao == 0)
                        validation.Add("Número do cartão não informado no cadastro do proprietário.", EFaultType.Error);

                return validation;
            }
        }
    }

    public class ProprietarioCartaoIntegrarRequestModel
    {
        public int NumeroCartao { get; set; }
        public bool RealizarTrocaCartao { get; set; }
    }
}
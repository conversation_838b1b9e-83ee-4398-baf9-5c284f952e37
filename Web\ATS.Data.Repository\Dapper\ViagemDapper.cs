using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using Dapper;

namespace ATS.Data.Repository.Dapper
{
    public class ViagemDapper: DapperFactory<Viagem>, IViagemDapper
    {
        private readonly IUserIdentity _userIdentity;

        public ViagemDapper(IUserIdentity userIdentity)
        {
            _userIdentity = userIdentity;
        }

        public IQueryable<ConsultaPagamentosItemModel> GetPagamentos(int idempresa, DateTime datainicio, DateTime datafim)
        {
            var query = @$"SELECT 
                            e.cnpj as 'CnpjEmpresa',
                            e.razaosocial as 'NomeEmpresa',
                            ve.idviagem AS 'IdViagem',
                            ve.idviagemevento AS 'IdViagemEvento',
                            NULL as 'IdCargaAvulsa',
                            v.datalancamento as 'DataLancamento',
                            ve.datahorapagamento AS 'DataHoraPagamento',
                            ve.valorpagamento AS 'ValorPagamento',
                            ve.habilitarpagamentocartao AS 'PagamentoCartao',
                            ve.status AS 'Status',
                            ve.tipoeventoviagem AS 'Tipo',
                            NULL AS 'FornecedorPedagio',
                            v.documentocliente AS 'DocumentoCliente',
                            dc.ciot AS 'CIOT',
                            v.placa as 'Placa',
                            v.cpfmotorista AS 'CpfMotorista',
                            v.nomemotorista AS 'NomeMotorista',
                            v.cpfcnpjproprietario AS 'CpfCnpjProprietario',
                            v.nomeproprietario AS 'NomeProprietario',
                            v.idfilial AS 'IdFilial',
                            f.cnpj AS 'CnpjFilial',
                            f.razaosocial AS 'Filial'
                        FROM VIAGEM_EVENTO ve 
                        JOIN VIAGEM v ON v.idviagem = ve.idviagem
                        LEFT JOIN FILIAL f ON v.idfilial = f.idfilial
                        JOIN EMPRESA e ON v.idempresa = e.idempresa
                        LEFT JOIN DECLARACAO_CIOT dc ON v.iddeclaracaociot = dc.iddeclaracaociot
                        where v.idempresa = @idEmpresa
                        and v.datalancamento between @dataInicio and @dataFim

                        UNION

                        SELECT 
                            e.cnpj as 'CnpjEmpresa',
                            e.razaosocial as 'NomeEmpresa',
                            v.idviagem AS 'IdViagem',
                            NULL AS 'IdViagemEvento',
                            NULL as 'IdCargaAvulsa',
                            v.datalancamento as 'DataLancamento',
                            v.dataconfirmacaopedagio AS 'DataHoraPagamento',
                            v.valorpedagio AS 'ValorPagamento',
                            CASE WHEN vr.fornecedorpedagio = 1 THEN 1 ELSE 0 END AS 'PagamentoCartao',
                            CASE WHEN v.dataconfirmacaoestornopedagio IS NOT NULL THEN 3 
                                 WHEN v.dataconfirmacaopedagio IS NOT NULL THEN 2
                                 ELSE 0 END AS 'Status',
                            7 AS 'Tipo',
                            vr.fornecedorpedagio AS 'FornecedorPedagio',
                            v.documentocliente AS 'DocumentoCliente',
                            dc.ciot AS 'CIOT',
                            v.placa as 'Placa',
                            v.cpfmotorista AS 'CpfMotorista',
                            v.nomemotorista AS 'NomeMotorista',
                            v.cpfcnpjproprietario AS 'CpfCnpjProprietario',
                            v.nomeproprietario AS 'NomeProprietario',
                            v.idfilial AS 'IdFilial',
                            f.cnpj AS 'CnpjFilial',
                            f.razaosocial AS 'Filial'
                        FROM VIAGEM v 
                        JOIN VIAGEM_ROTA vr ON vr.idviagem = v.idviagem
                        LEFT JOIN FILIAL f ON v.idfilial = f.idfilial
                        JOIN EMPRESA e ON v.idempresa = e.idempresa
                        LEFT JOIN DECLARACAO_CIOT dc ON v.iddeclaracaociot = dc.iddeclaracaociot
                        where v.idempresa = @idEmpresa
                        and v.dataconfirmacaopedagio between @dataInicio and @dataFim

                        UNION

                        SELECT 
                            e.cnpj as 'CnpjEmpresa',
                            e.razaosocial as 'NomeEmpresa',
                            NULL AS 'IdViagem',
                            NULL AS 'IdViagemEvento',
                            ca.idcargaavulsa as 'IdCargaAvulsa',
                            ca.datacadastro as 'DataLancamento',
                            tc.dataconfirmacaomeiohomologado AS 'DataHoraPagamento',
                            ca.valor AS 'ValorPagamento',
                            1 AS 'PagamentoCartao',
                            CASE WHEN ca.statuscargaavulsa = 3 THEN 3 
                                 WHEN ca.statuscargaavulsa = 2 THEN 2
                                 ELSE 0 END AS 'Status',
                            99 AS 'Tipo',
                            NULL AS 'FornecedorPedagio',
                            ca.nrocontroleintegracao AS 'DocumentoCliente',
                            NULL AS 'CIOT',
                            ca.placacavalo as 'Placa',
                            ca.cpfmototista AS 'CpfMotorista',
                            ca.nomemotorista AS 'NomeMotorista',
                            NULL AS 'CpfCnpjProprietario',
                            NULL AS 'NomeProprietario',
                            ca.idfilial AS 'IdFilial',
                            f.cnpj AS 'CnpjFilial',
                            f.razaosocial AS 'Filial'
                        FROM CARGA_AVULSA ca 
                        LEFT JOIN FILIAL f ON ca.idfilial = f.idfilial
                        JOIN EMPRESA e ON ca.idempresa = e.idempresa
                        LEFT JOIN TRANSACAO_CARTAO tc on tc.idcargaavulsa = ca.idcargaavulsa 
                        where ca.idempresa = @idEmpresa
                        and tc.dataconfirmacaomeiohomologado between @dataInicio and @dataFim;";

            var @Params = new DynamicParameters();

            @Params.Add("@idEmpresa", idempresa);
            @Params.Add("@dataInicio", datainicio);
            @Params.Add("@dataFim", datafim);

            return RunQuery<ConsultaPagamentosItemModel>(query, @Params);
        }

        public IList<ConsultaViagemInternalResponseDTO> GetViagens(ConsultaViagemRequestDTO request)
        {
            string sSql = $@"SELECT
                            V.IdViagem As IdViagem,
                            V.datacoleta As DataColeta,
                            V.dataatualizacao As DataAtualizacao,
                            V.dataprevisaoentrega As DataPrevisaoEntrega,
                            V.dataemissao As DataEmissao,
                            V.datafinalizacao As DataFinalizacao,
                            V.datalancamento As DataIntegracao,
                            E.cnpj As EmpresaCnpj,
                            E.nomefantasia As EmpresaNome,
                            F.cnpj As FilialCnpj,
                            F.razaosocial As FilialNome,
                            CLI_ORIGEM.cnpjcpf As ClienteOrigemCnpjCpf,
                            CLI_ORIGEM.nomefantasia As ClienteOrigemNome,
                            CLI_DESTINO.cnpjcpf As ClienteDestinoCnpjCpf,
                            CLI_DESTINO.nomefantasia As ClienteDestinoNome,
                            V.placa As Placa,
                            V.nomeproprietario As ProprietarioNome,
                            V.cpfcnpjproprietario AS ProprietarioCnpjCpf,
                            V.rntrc AS ProprietarioRntrc,
                            PROP.razaosocial AS RazaoSocial,
                            V.nomemotorista AS MotoristaNome,
                            V.cpfmotorista AS MotoristaCpf,
                            V.documentocliente AS DocumentoCliente,
                            V.habilitardeclaracaociot AS CiotHabilitado,
                            (CASE WHEN V.habilitardeclaracaociot = 1 AND DC.iddeclaracaociot IS NOT NULL
                                  THEN 1
                                  ELSE 0 END) AS HasCIOT,
                            (CASE WHEN V.habilitardeclaracaociot = 1 THEN (CASE WHEN V.habilitardeclaracaociot = 1 AND DC.iddeclaracaociot IS NOT NULL
                                  THEN DC.ciot
                                  ELSE V.mensagemdeclaracaociot END)
                                ELSE '' END) AS CIOT,
                            DC.verificador as Verificador,
                            V.valorpedagio AS ValorPedagio,
                            COALESCE((SELECT SUM (VE.valorpagamento) FROM VIAGEM_EVENTO VE WHERE tipoeventoviagem = 0 AND VE.status <> 3 AND VE.idviagem = V.idviagem group by VE.tipoeventoviagem),0) AS ValorAdiantamento,
                            COALESCE((SELECT SUM (VE.valorpagamento) FROM VIAGEM_EVENTO VE WHERE tipoeventoviagem = 1 AND VE.status <> 3 AND VE.idviagem = V.idviagem group by VE.tipoeventoviagem),0) AS ValorSaldo,
                            COALESCE((SELECT SUM (VE.valorpagamento) FROM VIAGEM_EVENTO VE WHERE tipoeventoviagem = 4 AND VE.status <> 3 AND VE.idviagem = V.idviagem group by VE.tipoeventoviagem),0) AS ValorTarifa,
                            COALESCE((SELECT SUM (VE.valorpagamento) FROM VIAGEM_EVENTO VE WHERE tipoeventoviagem = 2 AND VE.status <> 3 AND VE.idviagem = V.idviagem group by VE.tipoeventoviagem),0) AS ValorEstadia,
                            COALESCE((SELECT SUM (VE.valorpagamento) FROM VIAGEM_EVENTO VE WHERE tipoeventoviagem = 5 AND VE.status <> 3 AND VE.idviagem = V.idviagem group by VE.tipoeventoviagem),0) AS ValorAbastecimento,
                            V.valorpedagio as ValorPedagio,
                            V.mensagemcomprapedagio as MensagemCompraPedagio,
                            V.statusviagem as StatusViagem,
                            V.inss as Inss,
                            V.irrpf as Irrf,
                            V.sestsenat as Sestsenat,
                            ROTA.fornecedorpedagio as FornecedorPedagio,
                            FCNPJ.cnpj as FornecedorCnpj,
                            V.protocoloenviovalepedagio as ProtocoloEnvioValePedagio,
                            V.protocolovalepedagio as ProtocoloValePedagio,
                            V.resultadocomprapedagio as ResultadoCompraPedagio
                            FROM VIAGEM V
                            LEFT JOIN DECLARACAO_CIOT DC ON V.iddeclaracaociot = DC.iddeclaracaociot
                            LEFT JOIN CLIENTE CLI_ORIGEM ON V.idclienteorigem = CLI_ORIGEM.idcliente
                            LEFT JOIN CLIENTE CLI_DESTINO ON V.idclientedestino = CLI_DESTINO.idcliente 
                            LEFT JOIN PROPRIETARIO PROP ON V.idproprietario = PROP.idproprietario 
                            LEFT JOIN FILIAL F ON V.idfilial = F.idfilial 
                            LEFT JOIN VIAGEM_ROTA ROTA ON V.idviagem = ROTA.idviagem
                            LEFT JOIN FORNECEDOR_PEDAGIO_CNPJ FCNPJ ON ROTA.fornecedorpedagio = FCNPJ.idfornecedor
                            JOIN EMPRESA E on V.idempresa = E.idempresa
                            where V.datalancamento BETWEEN'{request.DataInicial.StartOfDay():yyyyMMdd HH:mm:ss}' AND '{request.DataFinal.EndOfDay():yyyyMMdd HH:mm:ss}'";

            if (request.IdEmpresa.HasValue)
            {
                sSql += $" AND V.idempresa = {request.IdEmpresa.Value} ";
            }
            
            if (request.Filiais != null && request.Filiais.Any())
            {
                var inList = string.Join(", ", request.Filiais.Select(t => t.Codigo));
                
                sSql += $" AND V.idfilial IN ({inList}) ";
            }
            
            if (request.IsPedagioAvulso)
            {
                sSql += " AND V.ispedagioavulso = 1";
            }

            if (!string.IsNullOrWhiteSpace(request.Pesquisa))
            {
                var cpfCnpj = request.Pesquisa.FormatarCpfCnpj(false, true);

                if (!string.IsNullOrWhiteSpace(cpfCnpj) && request.Pesquisa.Length == 14)
                {
                    sSql += $" AND V.cpfcnpjproprietario LIKE '%{request.Pesquisa}%' ";
                }
                else if (!string.IsNullOrWhiteSpace(cpfCnpj) && request.Pesquisa.Length == 11)
                {
                    sSql += $" AND (V.cpfmotorista LIKE '%{request.Pesquisa}%' OR V.cpfcnpjproprietario LIKE '%{request.Pesquisa}%') ";
                }
                else
                {
                    sSql += $" AND (UPPER(V.nomemotorista) LIKE '%{request.Pesquisa.ToUpper()}%' OR UPPER(V.nomeproprietario) LIKE '%{request.Pesquisa.ToUpper()}%' OR UPPER(V.placa) LIKE '%{request.Pesquisa.ToUpper()}%') ";
                }
            }

            sSql += $" ORDER BY V.idviagem DESC ";
            
            var repository = this; 
            
            var viagens = repository.RunSelect<ConsultaViagemInternalResponseDTO>(sSql);
            
            return viagens.ToList();
        }
        
        public decimal ConsultarValorPedagiosCiotAgregado(int idContratoCiotAgregado)
        {
            var sql = $@"select sum(valorpedagio) from VIAGEM
                        where iddeclaracaociot in 
                        (
                            select iddeclaracaociot from DECLARACAO_CIOT 
                            where idcontratociotagregado = @IdContratoCiotAgregado
                        )
                        and statusviagem != 3";
            
            var parameters = new {IdContratoCiotAgregado = idContratoCiotAgregado};
            using (IDbConnection dbConnection = this.GetConnection())
            {
                var valorPedagios = dbConnection.Query<decimal?>(sql, parameters).FirstOrDefault();

                return valorPedagios.GetValueOrDefault();
            }
        }

        public ViagemDadosValePedagio GetDadosValePedagio(int idviagem)
        {
            var sql = $@"select
                            v.idempresa as IdEmpresa,
                            r.fornecedorpedagio as Fornecedor,
                            v.protocoloenviovalepedagio as ProtocoloEnvioValePedagio,
                            v.protocolovalepedagio as ProtocoloValePedagio,
                            v.nomeproprietario as NomeContratante,
                            v.cpfcnpjproprietario as CpfCnpjContratante
                        from VIAGEM v 
                        left join VIAGEM_ROTA r on v.idviagem = r.idviagem
                        where v.idviagem = @Idviagem";
            
            var parameters = new {Idviagem = idviagem};
            using (IDbConnection dbConnection = this.GetConnection())
            {
                var dadosPedagio = dbConnection.Query<ViagemDadosValePedagio>(sql, parameters).FirstOrDefault();

                return dadosPedagio;
            }
        }

        public ComprovanteCargaResponse GetDadosComprovanteCarga(int idviagem)
        {
            var sql = $@"select
                            v.idempresa as IdEmpresa,
                            r.fornecedorpedagio as Fornecedor,
                            v.protocoloenviovalepedagio as ProtocoloEnvioValePedagio,
                            v.protocolovalepedagio as ProtocoloValePedagio,
                            v.valorpedagio as ValorComprovanteCarga,
                            v.resultadocomprapedagio as StatusCompraPedagio
                        from VIAGEM v 
                        left join VIAGEM_ROTA r on v.idviagem = r.idviagem
                        where v.idviagem = @Idviagem";
            
            var parameters = new {Idviagem = idviagem};
            using (IDbConnection dbConnection = this.GetConnection())
            {
                var dadosPedagio = dbConnection.Query<ComprovanteCargaResponse>(sql, parameters).FirstOrDefault();

                return dadosPedagio;
            }
        }
    }
}
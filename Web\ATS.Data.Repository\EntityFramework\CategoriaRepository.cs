﻿using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CategoriaRepository : Repository<Categoria>, ICategoriaRepository
    {
        public CategoriaRepository(AtsContext context) : base(context)
        {
        }

        public List<int> TodosAtivos()
        {
            return Where(c => c.Ativo)
                .Select(c => c.IdCategoria)
                .ToList();
        }
    }
}
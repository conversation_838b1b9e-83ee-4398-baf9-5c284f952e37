﻿using System.Web.Script.Serialization;

namespace ATS.CrossCutting.IoC.Serialization
{
    public static class SerializatorJson
    {
        private static readonly JavaScriptSerializer Serializator = new JavaScriptSerializer();
        
        public static string Serialize(object obj)
        {
            return Serializator.Serialize(obj);
        }

        public static T Deserialize<T>(string json)
        {
            return Serializator.Deserialize<T>(json);
        }
    }
}

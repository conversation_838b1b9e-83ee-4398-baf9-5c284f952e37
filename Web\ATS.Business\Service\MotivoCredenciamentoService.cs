﻿using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.Reports.MotivoCredenciamento;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;

namespace ATS.Domain.Service
{
    public class MotivoCredenciamentoService : ServiceBase, IMotivoCredenciamentoService
    {
        private readonly IMotivoRepository _motivoRepository;
        private readonly ITipoMotivoRepository _tipoMotivoRepository;
        private readonly IUserIdentity _userIdentity;

        public MotivoCredenciamentoService(IMotivoRepository motivoRepository, ITipoMotivoRepository tipoMotivoRepository, IUserIdentity userIdentity)
        {
            _motivoRepository = motivoRepository;
            _tipoMotivoRepository = tipoMotivoRepository;
            _userIdentity = userIdentity;
        }
        
        public Motivo ValidarEntidade(Motivo motivo)
        {
            return motivo;
        }

        public ValidationResult Add(Motivo motivo)
        {
            try
            {
                motivo.DataCadastro = DateTime.Now;
                motivo.Ativo = true;

                ValidarEntidade(motivo);
                _motivoRepository.Add(motivo);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return new ValidationResult().Add(mensagem);
            }
        }

        public ValidationResult Update(Motivo motivo, List<ETipoMotivo> tipos)
        {
            var validationResult = new ValidationResult();
            try
            {
                validationResult.Add(UpdateTipoMotivo(motivo.IdMotivo, tipos));

                if (!validationResult.IsValid)
                    return validationResult;

                _motivoRepository.Update(motivo);

                return validationResult;
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return validationResult.Add(mensagem);
            }
        }

        public ValidationResult UpdateTipoMotivo(int idMotivo, List<ETipoMotivo> tipos)
        {
            try
            {
                var tiposDb = _tipoMotivoRepository.GetAll()
                    .Where(x => x.IdMotivo == idMotivo)
                    .Select(x => x.Tipo).ToList();
                
                var listToAdd = tipos.Except(tiposDb).ToList();
                foreach (var tipo in listToAdd)
                    _tipoMotivoRepository.Add(new TipoMotivo(){IdMotivo = idMotivo, Tipo = tipo});
                
                var listToDelete = tiposDb.Except(tipos).ToList();
                foreach (var tipo in listToDelete)
                    _tipoMotivoRepository.Delete(tipo, idMotivo);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return new ValidationResult().Add(mensagem);
            }
        }

        public ValidationResult Reativar(int idMotivoCredenciamento)
        {
            try
            {
                var grupoCheckList = _motivoRepository.Get(idMotivoCredenciamento);

                if (_userIdentity.Perfil != (int)EPerfil.Administrador &&
                    grupoCheckList.IdEmpresa != _userIdentity.IdEmpresa)
                    throw new InvalidOperationException("Usuário não autenticado.");

                grupoCheckList.Ativo = true;

                _motivoRepository.Update(grupoCheckList);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return new ValidationResult().Add(mensagem);
            }
        }

        public ValidationResult Inativar(int idMotivoCredenciamento)
        {
            try
            {
                var grupoCheckList = _motivoRepository.Get(idMotivoCredenciamento);

                if (_userIdentity.Perfil != (int)EPerfil.Administrador &&
                    grupoCheckList.IdEmpresa != _userIdentity.IdEmpresa)
                    throw new InvalidOperationException("Usuário não autenticado.");
                
                grupoCheckList.Ativo = false;

                _motivoRepository.Update(grupoCheckList);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return new ValidationResult().Add(mensagem);
            }
        }

        public Motivo Get(int idMotivoCredenciamento, bool include = true)
        {
            if(include)
                return _motivoRepository
                    .Include(x => x.Empresa)
                    .Include(x => x.Filial)
                    .Include(x => x.TipoMotivo)
                    .FirstOrDefault(x => x.IdMotivo == idMotivoCredenciamento);
            
            return _motivoRepository.FirstOrDefault(x => x.IdMotivo == idMotivoCredenciamento);
        }

        public IQueryable<Motivo> GetAll()
        {
            return _motivoRepository.GetAll();
        }

        public DataModel<MotivoCredenciamentoModel> ConsultarGrid(int? idEmpresa, int? idFilial, string descricao,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            IQueryable<Motivo> motivos;
            
            var tipoQuery = filters?.Where(x => x.Campo == "TipoMotivo").Select(x => x.Valor).ToList();
            ETipoMotivo? tipo = null;
            if(tipoQuery != null && tipoQuery.Any()) tipo = (ETipoMotivo) Convert.ToInt32(tipoQuery.First());
            
            if (idEmpresa.HasValue)
            {
                int idEmpresaNotNull = idEmpresa ?? default(int);
                
                motivos = _motivoRepository.GetAtivos(idEmpresaNotNull, tipo)
                    .Include(x => x.Empresa)
                    .Where(x => x.IdEmpresa == idEmpresa.Value);
            }
            else
                motivos = _motivoRepository.GetAll();

            if (idFilial.HasValue) 
                motivos = motivos.Include(x => x.Filial)
                .Where(x => x.IdFilial == idFilial.Value);

            if (descricao != null) 
                motivos = motivos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                motivos = motivos.OrderBy(x => x.IdMotivo);
            else
                motivos = motivos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");


            // Aplica para todos os campos de string
            motivos = motivos.AplicarFiltrosDinamicos(filters);

            return new DataModel<MotivoCredenciamentoModel>
            {
                totalItems = motivos.Count(),
                items = motivos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new MotivoCredenciamentoModel
                    {
                        IdMotivo = x.IdMotivo,
                        Descricao = x.Descricao,
                        Ativo = x.Ativo,
                        RazaoSocialEmpresa = x.Empresa != null ? x.Empresa.RazaoSocial : string.Empty,
                        RazaoSocialFilial = x.Filial != null ? x.Filial.RazaoSocial : string.Empty
                    })
            };
        }
        
        public object ConsultarGridTodos(int? idEmpresa,
                                    int? idFilial,
                                    string descricao,
                                    int take,
                                    int page,
                                    OrderFilters orderFilters,
                                    List<QueryFilters> filters)
        {
            var motivos = _motivoRepository.GetAll()
                .Include(x => x.Empresa)
                .Include(x => x.Filial);

            if (idEmpresa.HasValue) motivos = motivos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idFilial.HasValue) motivos = motivos.Where(x => x.IdFilial == idFilial.Value);

            if (descricao != null) motivos = motivos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                motivos = motivos.OrderBy(x => x.IdMotivo);
            else
                motivos = motivos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            // Aplica para todos os campos de string
            motivos = motivos.AplicarFiltrosDinamicos(filters);

            return new DataModel<MotivoCredenciamentoModel>
            {
                totalItems = motivos.Count(),
                items = motivos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new MotivoCredenciamentoModel
                {
                    IdMotivo = x.IdMotivo,
                    Descricao = x.Descricao,
                    Ativo = x.Ativo,
                    RazaoSocialEmpresa = x.Empresa != null ? x.Empresa.RazaoSocial : string.Empty,
                    RazaoSocialFilial = x.Filial != null ? x.Filial.RazaoSocial : string.Empty
                })
            };
        }

        public byte[] GerarRelatorioGridMotivo(int? idEmpresa, int? idFilial, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var motivos = ConsultarGrid(idEmpresa, idFilial, descricao, int.MaxValue, 1, orderFilters, filters);

            return new RelatorioMotivos().GetReport(motivos.items, tipoArquivo, logo);
        }
    }
}

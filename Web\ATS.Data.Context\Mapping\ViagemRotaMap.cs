using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemRotaMap: EntityTypeConfiguration<ViagemRota>
    {
        public ViagemRotaMap()
        {
            ToTable("VIAGEM_ROTA");

            HasKey(t => t.IdViagemRota);

            Property(c => c.FornecedorPedagio).IsRequired();
            Property(c => c.TipoVeiculo).IsRequired();
            Property(c => c.IdentificadorHistorico).IsOptional();

            HasMany(c => c.<PERSON>)
                .WithRequired(c => c.ViagemRota)
                .HasForeignKey(c => c.IdViagemRota)
                .WillCascadeOnDelete();
        }
    }
}
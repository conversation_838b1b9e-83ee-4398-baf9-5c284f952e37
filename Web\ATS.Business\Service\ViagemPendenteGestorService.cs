﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class ViagemPendenteGestorService : ServiceBase, IViagemPendenteGestorService
    {
        private readonly IViagemPendenteGestorRepository _viagemPendenteGestorRepository;

        public ViagemPendenteGestorService(IViagemPendenteGestorRepository viagemPendenteGestorRepository)
        {
            _viagemPendenteGestorRepository = viagemPendenteGestorRepository;
        }

        public List<ViagemPendenteGestor> GetPendenciasByIdViagem( int idViagem, int idEmpresa)
        {
            return _viagemPendenteGestorRepository.GetAll()
                .Where(vp => vp.IdViagem == idViagem && vp.IdEmpresa == idEmpresa).ToList();
        }

        public IQueryable<ViagemPendenteGestor> GetQuery()
        {
            return _viagemPendenteGestorRepository.GetAll();
        }

        public IQueryable<ViagemPendenteGestor> GetQueryIncluded()
        {
            var ioc = _viagemPendenteGestorRepository;
            ;
            return ioc.Include(x=> x.BloqueioGestorTipo).Union(ioc.Include(s => s.UsuarioDesbloqueio));
        }

        public ViagemPendenteGestor Save(ViagemPendenteGestor pendencia)
        {
           var repository = _viagemPendenteGestorRepository;

            if (repository.Any(c => c.IdViagem == pendencia.IdViagem && c.IdBloqueioGestorTipo == pendencia.IdBloqueioGestorTipo))
            {
               
                repository.Update(pendencia);
            }
            else
            {
                repository.Add(pendencia);
            }

            return pendencia;
        }

        public void Update(ViagemPendenteGestor pendencia)
        {
            _viagemPendenteGestorRepository.Update(pendencia);
        }

        public EBloqueioGestorStatus? GetStatusViagem(int idviagem)
        {
            var valorQuery = _viagemPendenteGestorRepository.Where(c => c.IdViagem == idviagem).Select(c => c.Status);

            if (valorQuery.Any())
                return (EBloqueioGestorStatus) valorQuery.First();

            return null;
        }
    }
}
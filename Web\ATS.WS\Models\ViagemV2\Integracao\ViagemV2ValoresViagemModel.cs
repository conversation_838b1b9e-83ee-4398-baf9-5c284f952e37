using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2ValoresViagemModel
    {
        public ViagemV2ValoresViagemModel()
        {
            Irrpf = 0;
            Inss = 0;
            SestSenat = 0;
        }
        
        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal Irrpf { get; set; }

        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal Inss { get; set; }

        /// <summary>
        /// Valor do imposto
        /// </summary>
        public decimal SestSenat { get; set; }

        /// <summary>
        /// Peso do frete detalhado no documento do transporte
        /// </summary>
        public decimal? PesoSaida { get; set; }

        /// <summary>
        /// Valor total da mercadoria
        /// </summary>
        public decimal? ValorMercadoria { get; set; }

        /// <summary>
        /// Quantidade referente a carga
        /// </summary>
        public decimal Quantidade { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (Irrpf < 0)
                return new ValidationResult().Add("IRRPF não pode conter um valor negativo.", EFaultType.Error);
            
            if (Inss < 0)
                return new ValidationResult().Add("INSS não pode conter um valor negativo.", EFaultType.Error);
            
            if (SestSenat < 0)
                return new ValidationResult().Add("Sest Senat não pode conter um valor negativo.", EFaultType.Error);
            
            if (PesoSaida.HasValue)
                if (PesoSaida.Value < 0)
                    return new ValidationResult().Add("Peso de saída não pode conter um valor negativo.", EFaultType.Error);
            
            if (ValorMercadoria.HasValue)
                if (ValorMercadoria.Value < 0)
                    return new ValidationResult().Add("Valor da mercadoria não pode conter um valor negativo.", EFaultType.Error);
            
            if (Quantidade < 0)
                return new ValidationResult().Add("Quantidade não pode conter um valor negativo.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
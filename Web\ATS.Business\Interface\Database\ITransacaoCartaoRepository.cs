﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Database
{
    public interface ITransacaoCartaoRepository : IRepository<TransacaoCartao>
    {
        int GetCountByIdEvento(int idevento);
        List<TransacaoCartao> GetByIdEvento(int idevento);
        List<TransacaoCartao> GetByIdCarga(int idcarga);
        TransacaoCartao GetByIdEventoAndTipoProcessamento(int idevento, ETipoProcessamentoCartao tipoProcessamentoCartao);
        TransacaoCartao GetById(int idtransacaocartao);
        TransacaoCartao GetByResgate(int idresgate);
        TransacaoCartao GetByIdIncludeViagem(int idtransacaoCartao);
    }
}

﻿using System;
using System.Collections.Generic;
using System.Web.Script.Serialization;
using Newtonsoft.Json;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico
{
    public class RelatorioConciliacaoAnaliticoDataType
    {
        [JsonIgnore, ScriptIgnore]
        public bool Sucesso { get; set; }
        [JsonIgnore, ScriptIgnore]
        public string Mensagem { get; set; }
        // ReSharper disable once InconsistentNaming
        public IList<RelatorioConciliacaoAnaliticoTransacaoDataType> items { get; set; }

        // ReSharper disable once InconsistentNaming
        public int totalItems { get; set; }

        public RelatorioConciliacaoAnaliticoResumoDataType Resumo { get; set; }
    }

    public enum EStatusTransacaoConciliacao
    {
        Existente = 0,
        Inexistente = 1,
        Pendente = 2
    }

    public class RelatorioConciliacaoAnaliticoTransacaoDataType
    {
        public string NumeroRecibo { get; set; }
        public string Ciot { get; set; }
        public string TipoEvento { get; set; }
        public string Data { get; set; }
        public DateTime? DataDt { get; set; }
        public string Valor { get; set; }
        public decimal? ValorDecimal { get; set; }
        public EStatusTransacaoConciliacao StatusAts { get; set; }
        public EStatusTransacaoConciliacao StatusMeioHomologado { get; set; }
        public EStatusTransacaoConciliacao StatusProcessadora { get; set; }
        public EStatusTransacaoConciliacao StatusGeral { get; set; }
        public string MensagemRetorno { get; set; }
        public string TipoTransacaoDescricao { get; set; }
        public ConciliacaoItemTipoProcessadora? TipoTransacao { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public long? ProtocoloRequisicaoPedagio { get; set; }     
        public string NumeroCartao { get; set; }     
        public int? Conta { get; set; }
        public string DocumentoPortador { get; set; }     
        public string NomePortador { get; set; }     
        public string Placa { get; set; }
        public string Filial { get; set; }
        public string DocumentoUsuario { get; set; }
        public string NomeUsuario { get; set; }
        public string Informacoes { get; set; }
        public int? IdCargaAvulsa { get; set; }
        
        [JsonIgnore, ScriptIgnore]
        public object ConciliacaoItem { get; set; }

        public int? IdParaCsv { get; set; }
                
        [JsonIgnore, ScriptIgnore]
        public int? TipoEventoViagem { get; set; }
    }

    public class RelatorioConciliacaoAnaliticoResumoDataType
    {
        public string SaldoInicialConta { get; set; }
        public string SaldoFinalConta { get; set; }
        public string ValorTotalCargaTarifa { get; set; }
        public string ValorTotalEstornoTarifa { get; set; }
        public string ValorTotalCargaAdiantamento { get; set; }
        public string ValorTotalEstornoAdiantamento { get; set; }
        public string ValorTotalCargaSaldo { get; set; }
        public string ValorTotalEstornoSaldo { get; set; }
        public string ValorTotalCargaRpa { get; set; }
        public string ValorTotalEstornoRpa { get; set; }
        public string ValorTotalCargaEstadia { get; set; }
        public string ValorTotalEstornoEstadia { get; set; }
        public string ValorTotalCargaPedagio { get; set; }
        public string ValorTotalEstornoPedagio { get; set; }
        public string ValorTotalCargaAvulsa { get; set; }
        public string ValorTotalEstornoAvulsa { get; set; }

        public string ValorTotalAts { get; set; }
        public string ValorTotalMeioHomologado { get; set; }
        public string ValorTotalProcessadora { get; set; }
        public string ValorTotalDiferenca { get; set; }
        
        public string ValorTotalCargaAbastecimento { get; set; }
        public string ValorTotalEstornoAbastecimento { get; set; }
    }
}
﻿using System;

namespace ATS.Domain.Entities
{
    public class GestorUsuario 
    {
        public int Id { get; set; }
        
        /// <summary>
        /// Id do Usuário (de perfil Empresa) com a permissão de aprovar/reprovar solicitações de adiantamento
        /// </summary>
        public int IdGestor { get; set; }
        
        /// <summary>
        /// Id do Usuário (de perfil Motorista ou Proprietario) que será gerido
        /// </summary>
        public int IdSubordinado { get; set; }
        
        /// <summary>
        /// Id do Usuário que criou o registro
        /// </summary>
        public int IdUsuarioCadastro { get; set; }
        
        public DateTime DataCadastro { get; set; }
        
        #region Propriedades de Navegacao
        
        public virtual Usuario UsuarioGestor { get; set; }
        
        public virtual Usuario UsuarioSubordinado { get; set; }
        
        public virtual Usuario UsuarioCadastro { get; set; }
        
        #endregion
    }
}

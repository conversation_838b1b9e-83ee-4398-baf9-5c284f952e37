﻿using ATS.Application.Application;
using ATS.Domain.Enum;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class IconeAtsController : DefaultController
    {
        private readonly IDataMediaServerApp _dataMediaServerApp;
        private readonly IIconeApp _iconeApp;

        public IconeAtsController(IDataMediaServerApp dataMediaServerApp, IIconeApp iconeApp)
        {
            _dataMediaServerApp = dataMediaServerApp;
            _iconeApp = iconeApp;
        }

        [HttpGet]
        public JsonResult Consultar(EIconePara? iconePara)
        {
            try
            {
                if (!iconePara.HasValue)
                    iconePara = EIconePara.CheckList;

                var retorno = new List<object>();
                var icones = _iconeApp.Consultar().Where(x => x.IconePara == iconePara.Value);

                foreach (var icone in icones)
                {
                    var imagem = string.Empty;
                    if (!string.IsNullOrWhiteSpace(icone.TokenIcone))
                        imagem = "data:image/png;base64," + _dataMediaServerApp.GetMedia(icone.TokenIcone)?.Data;

                    var objeto = new
                    {
                        icone.Descricao,
                        icone.IdIcone,
                        Imagem = imagem
                    };
                    retorno.Add(objeto);
                }

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        public JsonResult GetFirst(EIconePara? iconePara)
        {
            try
            {
                var icone = _iconeApp.GetFirst(iconePara);
                var imagem = string.Empty;
                if (!string.IsNullOrWhiteSpace(icone.TokenIcone))
                    imagem = "data:image/png;base64," + _dataMediaServerApp.GetMedia(icone.TokenIcone)?.Data;

                return ResponderSucesso(new
                {
                    icone.Descricao,
                    icone.IdIcone,
                    Imagem = imagem
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
    }
}
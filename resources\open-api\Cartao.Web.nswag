﻿{
  "runtime": "WinX64",
  "defaultVariables": null,
  "swaggerGenerator": {
    "fromSwagger": {
      "json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Cartoes/Web\"\r\n  },\r\n  \"basePath\": \"/Cartoes/Web\",\r\n  \"paths\": {\r\n    \"/AcessoPortador/AcessarPaginaSaldoExtrato\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"AcessoPortador\"\r\n        ],\r\n        \"summary\": \"\",\r\n        \"operationId\": \"AcessoPortadorAcessarPaginaSaldoExtratoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/AcessarPaginaSaldoExtratoRequest\"\r\n            }\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/AcessarPaginaSaldoExtratoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/AcessoPortador/ConsultarExtrato\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"AcessoPortador\"\r\n        ],\r\n        \"summary\": \"Consulta um extrato do cartão\",\r\n        \"operationId\": \"AcessoPortadorConsultarExtratoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"gridRequest\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false\r\n          },\r\n          {\r\n            \"name\": \"Cartao.Identificador\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"Cartao.Produto\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"DataInicio\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"DataFim\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"Tipo\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"ExibirMetadados\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"boolean\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DataSourceResult\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/AcessoPortador/ConsultarSaldo\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"AcessoPortador\"\r\n        ],\r\n        \"summary\": \"Retorna o saldo inicial do período da consulta\",\r\n        \"operationId\": \"AcessoPortadorConsultarSaldoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarExtratoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/AcessoPortador/ConsultarSaldoCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"AcessoPortador\"\r\n        ],\r\n        \"summary\": \"Consultar o saldo do cartão\",\r\n        \"operationId\": \"AcessoPortadorConsultarSaldoCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de consulta de saldo\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Atualizar/GetScript\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Atualizar\"\r\n        ],\r\n        \"operationId\": \"AtualizarGetScriptGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Atualizar/Executar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Atualizar\"\r\n        ],\r\n        \"operationId\": \"AtualizarExecutarPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Atualizar/ExecutarSeed\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Atualizar\"\r\n        ],\r\n        \"operationId\": \"AtualizarExecutarSeedPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Atualizar/PossuiAtualizacaoPendente\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Atualizar\"\r\n        ],\r\n        \"operationId\": \"AtualizarPossuiAtualizacaoPendentePost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Auth/Login/{usuario}/{senha}\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"AuthWeb\"\r\n        ],\r\n        \"summary\": \"\",\r\n        \"operationId\": \"AuthLoginByUsuarioBySenhaPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"usuario\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"senha\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"administradoraRequired\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"boolean\"\r\n          },\r\n          {\r\n            \"name\": \"empresaRequired\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"boolean\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/LoginWebResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/{identificador}/{produto}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Busca cartão através do identificador e produto.\\r\\nEsta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.\\r\\nOu seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.\",\r\n        \"operationId\": \"CartoesByIdentificadorByProdutoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"identificador\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Número identificador do cartão\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"produto\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Id do tipo de produto. Maiores detalhes na documentação da api \\\"/Produtos\\\".\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Dados do cartão com o identificador e produto indicado.\\r\\n<li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão\\\"</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCartaoResponse\"\r\n            }\r\n          },\r\n          \"204\": {\r\n            \"description\": \"Não existe cartão com os dados indicados\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Buscar todos os cartões da administradora\",\r\n        \"operationId\": \"CartoesGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"data\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/CartaoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/Cadastrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Cadastra um novo cartão\",\r\n        \"operationId\": \"CartoesCadastrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cartaoCadastrarRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"DTO de integração contendo dados de pessoa e cartão\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoCadastrarRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoCadastrarResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/CadastrarCartaoVirtual\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.\",\r\n        \"operationId\": \"CartoesCadastrarCartaoVirtualPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cartaoCadastrarRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"cartao a ser cadastrado\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoVirtualCadastrarRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoVirtualCadastrarResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/ImportarCartoes\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Método responsável por importar cartoes a  partir de um arquivo\",\r\n        \"operationId\": \"CartoesImportarCartoesPost\",\r\n        \"consumes\": [\r\n          \"multipart/form-data\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"administradoraId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"file\",\r\n            \"in\": \"formData\",\r\n            \"description\": \"Upload File\",\r\n            \"required\": true,\r\n            \"type\": \"file\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/GetAdministradoras\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Buscar todas as administradoras\",\r\n        \"operationId\": \"CartoesGetAdministradorasGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/Administradora\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Cartoes/ConsultarCartaoProcessadora\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"CartaoWeb\"\r\n        ],\r\n        \"summary\": \"Realiza a consulta dos dados na processadora a partir de um Hash de 16 dígitos criptografado\",\r\n        \"operationId\": \"CartoesConsultarCartaoProcessadoraPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarCartaoProcessadoraRequest\"\r\n            }\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarCartaoProcessadoraResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Dashboard/IndicadoresTransacoesSemana\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Dashboard\"\r\n        ],\r\n        \"operationId\": \"DashboardIndicadoresTransacoesSemanaGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"offsetWeeks\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"administradoraId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"empresaId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"produtoId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IndicadorTransacaoSemanaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Dashboard/IndicadoresTransacoesSemanaGrafico\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Dashboard\"\r\n        ],\r\n        \"operationId\": \"DashboardIndicadoresTransacoesSemanaGraficoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"offsetWeeks\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"administradoraId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"empresaId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"produtoId\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IndicadorTransacaoSemanaGraficoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Dashboard/GetSaldoEmpresa\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Dashboard\"\r\n        ],\r\n        \"summary\": \"Consultar saldo atual da empresa na BIZ\",\r\n        \"operationId\": \"DashboardGetSaldoEmpresaGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/ConsultarSaldoEmpresa/{cnpj}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Consultar saldo existente na conta de adiantamneto da empresa na processadora de cartões\",\r\n        \"operationId\": \"EmpresasConsultarSaldoEmpresaByCnpjGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cnpj\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Busca todas as pessoas cadastradas (Somente para testes)\",\r\n        \"operationId\": \"PessoasGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/ConsultarPessoaResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Processadora/Integrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Processadora\"\r\n        ],\r\n        \"summary\": \"Integra a Processadora\",\r\n        \"operationId\": \"ProcessadoraIntegrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"processadora\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto da Processadora\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ProcessadoraDTORequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/WebIntegrationResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Processadora\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Processadora\"\r\n        ],\r\n        \"summary\": \"Busca todas as processadoras\",\r\n        \"operationId\": \"ProcessadoraGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/ProcessadoraDTOResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"Processadora\"\r\n        ],\r\n        \"summary\": \"Exclui uma Processadora\",\r\n        \"operationId\": \"ProcessadoraDelete\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Id da processadora\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/WebActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Processadora/{id}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Processadora\"\r\n        ],\r\n        \"summary\": \"Busca uma processadora por Id\",\r\n        \"operationId\": \"ProcessadoraByIdGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ProcessadoraDTOResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/TipoTransacao/Integrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"TipoTransacao\"\r\n        ],\r\n        \"summary\": \"Integra um Tipo de Transação\",\r\n        \"operationId\": \"TipoTransacaoIntegrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"tipoTransacao\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto do tipo de transação\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TipoTransacaoDTORequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/WebIntegrationResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/TipoTransacao\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"TipoTransacao\"\r\n        ],\r\n        \"summary\": \"Busca todos os Tipos de Transação cadastrados\",\r\n        \"operationId\": \"TipoTransacaoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/TipoTransacaoDTOResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"TipoTransacao\"\r\n        ],\r\n        \"summary\": \"Remove um Tipo de Transacao\",\r\n        \"operationId\": \"TipoTransacaoDelete\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Id da transacao a ser removida\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"format\": \"int32\",\r\n              \"type\": \"integer\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/WebActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/TipoTransacao/{id}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"TipoTransacao\"\r\n        ],\r\n        \"summary\": \"Busca um Tipo de Transacao com base no Id\",\r\n        \"operationId\": \"TipoTransacaoByIdGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Id do tipo de transacao\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-web-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação para front end\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TipoTransacaoDTOResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"AcessarPaginaSaldoExtratoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senha\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AcessarPaginaSaldoExtratoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tokenSessao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IdentificadorCartaoWeb\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ApiProcessingStateOnServer\": {\r\n      \"required\": [\r\n        \"state\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"state\": {\r\n          \"enum\": [\r\n            \"Ok\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"errorMessage\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"IdentificadorCartaoWeb\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"codigoProduto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nomeProduto\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DataSourceRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"page\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"pageSize\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"sorts\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/SortDescriptor\"\r\n          }\r\n        },\r\n        \"filters\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IFilterDescriptor\"\r\n          }\r\n        },\r\n        \"groups\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/GroupDescriptor\"\r\n          }\r\n        },\r\n        \"aggregates\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/AggregateDescriptor\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"SortDescriptor\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"member\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sortDirection\": {\r\n          \"enum\": [\r\n            \"Ascending\",\r\n            \"Descending\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IFilterDescriptor\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {}\r\n    },\r\n    \"GroupDescriptor\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"memberType\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"displayContent\": {\r\n          \"type\": \"object\"\r\n        },\r\n        \"aggregateFunctions\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/AggregateFunction\"\r\n          },\r\n          \"readOnly\": true\r\n        },\r\n        \"member\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sortDirection\": {\r\n          \"enum\": [\r\n            \"Ascending\",\r\n            \"Descending\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AggregateDescriptor\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"aggregates\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/AggregateFunction\"\r\n          },\r\n          \"readOnly\": true\r\n        },\r\n        \"member\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AggregateFunction\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"aggregateMethodName\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"caption\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sourceField\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"functionName\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"memberType\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"resultFormatString\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DataSourceResult\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"data\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"object\"\r\n          }\r\n        },\r\n        \"total\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"aggregateResults\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/AggregateResult\"\r\n          }\r\n        },\r\n        \"errors\": {\r\n          \"type\": \"object\"\r\n        }\r\n      }\r\n    },\r\n    \"AggregateResult\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"value\": {\r\n          \"type\": \"object\",\r\n          \"readOnly\": true\r\n        },\r\n        \"member\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"formattedValue\": {\r\n          \"type\": \"object\",\r\n          \"readOnly\": true\r\n        },\r\n        \"itemCount\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"caption\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"functionName\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"aggregateMethodName\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarExtratoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"exibirMetadados\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"IdentificadorCartao\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valorLimiteCredito\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorSaldoDisponivel\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"LoginWebResponse\": {\r\n      \"required\": [\r\n        \"sucesso\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/WebProcessingStateOnServer\"\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sessionToken\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"urlImagemCartao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"WebProcessingStateOnServer\": {\r\n      \"required\": [\r\n        \"state\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"state\": {\r\n          \"enum\": [\r\n            \"Ok\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"errorMessage\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCartaoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"$ref\": \"#/definitions/CartaoStatus\"\r\n        },\r\n        \"contaVirtual\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"motivoDesvinculo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/Pessoa\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        },\r\n        \"cartaoProcessadora\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartaoProcessadoraResponse\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CartaoStatus\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"abreviado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"id\": {\r\n          \"enum\": [\r\n            \"AguardandoRemessaParaCliente\",\r\n            \"AguardandoRemessaParaPontoDistribuicao\",\r\n            \"EmTransitoParaPontoDistribuicao\",\r\n            \"EmTransitoRemessaRejeitada\",\r\n            \"AguardandoVinculacao\",\r\n            \"Vinculado\",\r\n            \"CanceladoExtravio\",\r\n            \"CanceladoTrocaCartao\",\r\n            \"CanceladoManual\",\r\n            \"RejeitadoPontoDistribuicao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Pessoa\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sexo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidadeId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"ativo\": {\r\n          \"type\": \"boolean\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataAtivacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesativacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidade\": {\r\n          \"$ref\": \"#/definitions/Cidade\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEndereco\"\r\n        },\r\n        \"info\": {\r\n          \"$ref\": \"#/definitions/PessoaInfo\"\r\n        },\r\n        \"contasBancarias\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/PessoaContaBancaria\"\r\n          }\r\n        },\r\n        \"tipos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/PessoaTipo\"\r\n          }\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ProdutoResponse\": {\r\n      \"required\": [\r\n        \"id\",\r\n        \"nome\",\r\n        \"isMultiplasContas\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"isMultiplasContas\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoProcessadoraResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"key\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"Cidade\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ibge\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"estadoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"estado\": {\r\n          \"$ref\": \"#/definitions/Estado\"\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaEndereco\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"pessoaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"logradouro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numero\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"bairro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cep\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"complemento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidadeId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cidade\": {\r\n          \"$ref\": \"#/definitions/Cidade\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/Pessoa\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaInfo\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"pessoaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"rg\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeMae\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePai\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataNascimento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/Pessoa\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaContaBancaria\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"pessoaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nomeConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBacenBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"digitoConta\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoConta\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/Pessoa\"\r\n        },\r\n        \"banco\": {\r\n          \"$ref\": \"#/definitions/Banco\"\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaTipo\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"pessoaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"empresaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoPessoaId\": {\r\n          \"enum\": [\r\n            \"PortadorCartao\",\r\n            \"PontoDistribuicao\",\r\n            \"Estabelecimento\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/Pessoa\"\r\n        },\r\n        \"administradora\": {\r\n          \"$ref\": \"#/definitions/Administradora\"\r\n        },\r\n        \"empresa\": {\r\n          \"$ref\": \"#/definitions/Empresa\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"$ref\": \"#/definitions/TipoPessoa\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Estado\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sigla\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ibge\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"paisId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"pais\": {\r\n          \"$ref\": \"#/definitions/Pais\"\r\n        },\r\n        \"cidades\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/Cidade\"\r\n          }\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Banco\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"url\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"id\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Administradora\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"urlImagemCartao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ativo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"Empresa\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"TipoPessoa\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"abreviado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"id\": {\r\n          \"enum\": [\r\n            \"PortadorCartao\",\r\n            \"PontoDistribuicao\",\r\n            \"Estabelecimento\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Pais\": {\r\n      \"required\": [\r\n        \"id\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sigla\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bacen\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"prefixoTelefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estados\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/Estado\"\r\n          }\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"usuarioCadastroId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"usuarioAlteracaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"portador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoCadastrarRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"parametros\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"agrupadorProdutos\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conteudoImportacao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoCadastrarResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVirtualCadastrarRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"parametros\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVirtualCadastrarResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"IFormFile\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"contentType\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"contentDisposition\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"headers\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          \"readOnly\": true\r\n        },\r\n        \"length\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\",\r\n          \"readOnly\": true\r\n        },\r\n        \"name\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"fileName\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarCartaoProcessadoraRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"hashCartao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarCartaoProcessadoraResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"cartaoRelacionado\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"documentoPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePortador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IndicadorTransacaoSemanaResponse\": {\r\n      \"required\": [\r\n        \"sucesso\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/WebProcessingStateOnServer\"\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"indicadores\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IndicadorItem\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"IndicadorItem\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"indicador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Informacao\",\r\n            \"TotalizadorCargaCredito\",\r\n            \"TotalizadorEstornoCargaCredito\",\r\n            \"TotalizadorSaldo\",\r\n            \"Agrupador\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"segunda\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"terca\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"quarta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"quinta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"sexta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"sabado\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"domingo\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"totalSemana\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\",\r\n          \"readOnly\": true\r\n        },\r\n        \"totalMes\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"segundaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"tercaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"quartaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"quintaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"sextaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"sabadoFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"domingoFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"totalSemanaFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"totalMesFormatted\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"IndicadorTransacaoSemanaGraficoResponse\": {\r\n      \"required\": [\r\n        \"sucesso\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/WebProcessingStateOnServer\"\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicioSemanaAtual\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFimSemanaAtual\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicioSemanaAnterior\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFimSemanaAnterior\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"indicadores\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/TransacaoDiaGraficoitem\"\r\n          }\r\n        },\r\n        \"indicadoresPeriodo\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/TransacaoDiaGraficoitemPeriodo\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"TransacaoDiaGraficoitem\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"periodo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valorSemanaAtual\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorSemanaAnterior\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorMesmaHoraSemanaAnterior\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"TransacaoDiaGraficoitemPeriodo\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"periodo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldoConta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sexo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ativo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"cidade\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ProcessadoraDTORequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"url\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"WebIntegrationResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/WebProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/WebResponseValidation\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"WebResponseValidation\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"type\": {\r\n          \"enum\": [\r\n            \"Information\",\r\n            \"Warning\",\r\n            \"Validation\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"message\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"WebActionResponse\": {\r\n      \"required\": [\r\n        \"sucesso\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/WebProcessingStateOnServer\"\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ProcessadoraDTOResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"url\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"TipoTransacaoDTORequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"transacaoContrariaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoTransacaoApiSuportada\": {\r\n          \"enum\": [\r\n            \"Indefinida\",\r\n            \"CargaCartao\",\r\n            \"OnUs\",\r\n            \"Agrupador\",\r\n            \"Moedeiro\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"enum\": [\r\n            \"NaoAplicado\",\r\n            \"Estornar\",\r\n            \"Reenviar\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"itens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/TipoTransacaoItemDTORequest\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"TipoTransacaoItemDTORequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"tipoTransacaoItem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"TipoTransacaoDTOResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"transacaoContrariaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"apiSuportada\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"itens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/TipoTransacaoItemDTOResponse\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"TipoTransacaoItemDTOResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"lineId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoTransacaoItem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}",
      "url": "http://*************/Cartoes/Web/swagger/v1/swagger.json",
      "output": null
    }
  },
  "codeGenerators": {
    "swaggerToCSharpClient": {
      "clientBaseClass": "SistemaInfoMicroServiceBaseClient",
      "configurationClass": "HttpContext",
      "generateClientClasses": true,
      "generateClientInterfaces": false,
      "generateDtoTypes": true,
      "injectHttpClient": false,
      "disposeHttpClient": true,
      "protectedMethods": [],
      "generateExceptionClasses": true,
      "exceptionClass": "SwaggerException",
      "wrapDtoExceptions": true,
      "useHttpClientCreationMethod": true,
      "httpClientType": "System.Net.Http.HttpClient",
      "useHttpRequestMessageCreationMethod": true,
      "useBaseUrl": true,
      "generateBaseUrlProperty": true,
      "generateSyncMethods": true,
      "exposeJsonSerializerSettings": false,
      "clientClassAccessModifier": "public",
      "typeAccessModifier": "public",
      "generateContractsOutput": false,
      "contractsNamespace": null,
      "contractsOutputFilePath": null,
      "parameterDateTimeFormat": "s",
      "generateUpdateJsonSerializerSettingsMethod": true,
      "serializeTypeInformation": false,
      "queryNullValue": "",
      "className": "{controller}Client",
      "operationGenerationMode": "MultipleClientsFromPathSegments",
      "additionalNamespaceUsages": [
        "System.Web",
        "ATS.Data.Repository.External.SistemaInfo"
      ],
      "additionalContractNamespaceUsages": [],
      "generateOptionalParameters": false,
      "generateJsonMethods": true,
      "enforceFlagEnums": false,
      "parameterArrayType": "System.Collections.Generic.IEnumerable",
      "parameterDictionaryType": "System.Collections.Generic.IDictionary",
      "responseArrayType": "System.Collections.Generic.List",
      "responseDictionaryType": "System.Collections.Generic.Dictionary",
      "wrapResponses": false,
      "wrapResponseMethods": [],
      "generateResponseClasses": true,
      "responseClass": "SwaggerResponse",
      "namespace": "SistemaInfo.MicroServices.Rest.Cartao.WebClient",
      "requiredPropertiesMustBeDefined": true,
      "dateType": "System.DateTime",
      "jsonConverters": null,
      "dateTimeType": "System.DateTime",
      "timeType": "System.TimeSpan",
      "timeSpanType": "System.TimeSpan",
      "arrayType": "System.Collections.Generic.List",
      "arrayInstanceType": null,
      "dictionaryType": "System.Collections.Generic.Dictionary",
      "dictionaryInstanceType": null,
      "arrayBaseType": "System.Collections.Generic.List",
      "dictionaryBaseType": "System.Collections.Generic.Dictionary",
      "classStyle": "Inpc",
      "generateDefaultValues": true,
      "generateDataAnnotations": true,
      "excludedTypeNames": [],
      "handleReferences": false,
      "generateImmutableArrayProperties": false,
      "generateImmutableDictionaryProperties": false,
      "jsonSerializerSettingsTransformationMethod": null,
      "templateDirectory": null,
      "typeNameGeneratorType": null,
      "propertyNameGeneratorType": null,
      "enumNameGeneratorType": null,
      "serviceHost": null,
      "serviceSchemes": null,
      "output": "./Sources/Cartao.WebClient.cs"
    }
  }
}
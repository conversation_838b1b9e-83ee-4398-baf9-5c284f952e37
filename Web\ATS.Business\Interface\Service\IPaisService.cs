﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IPaisService : IService<Pais>
    {
        Pais Get(int id);
        Pais Get(string sigla);
        ValidationResult Add(Pais pais);
        ValidationResult Update(Pais pais);
        Pais GetPorCodigoBACEN(int nBACEN);
        IQueryable<Pais> Consultar(string nome);
        ValidationResult Inativar(int idPais);
        ValidationResult Reativar(int idPais);
        Pais BuscarBrasil();

        int? GetIdPais(string sigla);

        int GetIdPaisPorBACEN(int nBACEN);
        Pais GetPorNome(string pais);

        object ConsultarGrid(string nome, string sigla, int? bacen, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        bool VerificarBacenCadastrado(int codigo);
    }
}
﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using ATS.Domain.Extensions;

namespace ATS.Domain.Validation
{

    public class Fault
    {
        public EFaultType Type { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
    }

    public class ValidationResult
    {
        private readonly List<ValidationError> _erros;
        private readonly List<ValidationError> _alert;
        private readonly List<KeyValuePair<string, string>> _faultsCode;
        private readonly List<KeyValuePair<string, EFaultType>> _faultsType;

        public ValidationResult()
        {
            _erros = new List<ValidationError>();
            _alert = new List<ValidationError>();
            _faultsCode = new List<KeyValuePair<string, string>>();
            _faultsType = new List<KeyValuePair<string, EFaultType>>();
        }

        public bool IsValid
        {
            get { return !_erros.Any(); }
        }

        public IEnumerable<ValidationError> Errors
        {
            get { return _erros; }
        }


        public IEnumerable<ValidationError> Alerts
        {
            get { return _alert; }
        }

        public ValidationResult Add(string errorMessage)
        {
            if (String.IsNullOrWhiteSpace(errorMessage))
                return this;

            _erros.Add(new ValidationError(errorMessage));

            return this;
        }

        public ValidationResult Add(string errorMessage, EFaultType faultType, string faultCode = "")
        {
            if (String.IsNullOrWhiteSpace(errorMessage))
                return this;

            if (faultType == EFaultType.Alert)
                _alert.Add(new ValidationError(errorMessage));
            else
                _erros.Add(new ValidationError(errorMessage));

            _faultsCode.Add(new KeyValuePair<string, string>(errorMessage, faultCode));

            return this;
        }

        public ValidationResult Add(ValidationError error, Boolean tagHtml = true)
        {
            if (error == null)
                return this;
            if (tagHtml)
                error.Message += " </br> ";
            _erros.Add(error);

            return this;
        }

        public ValidationResult Add(params ValidationResult[] validationResults)
        {
            if (validationResults == null)
                return this;

            foreach (ValidationResult result in validationResults.Where(r => r != null))
                _erros.AddRange(result.Errors);

            return this;
        }

        public ValidationResult AddRange(IEnumerable<ValidationError> errors)
        {
            if (errors == null || !errors.Any())
                return this;

            foreach (ValidationError error in errors)
                _erros.Add(error);

            return this;
        }

        public ValidationResult Remove(ValidationError error)
        {
            if (_erros.Contains(error))
                _erros.Remove(error);

            return this;
        }

        public List<Fault> GetFaults()
        {
            List<Fault> faults = new List<Fault>();

            foreach (var error in _erros)
            {

                var faultCode = _faultsCode.FirstOrDefault(x => x.Key == error.Message).Value;
                var faultType = _faultsType.FirstOrDefault(x => x.Key == error.Message).Value;

                if (faultCode != null)
                {
                    faults.Add(new Fault()
                    {
                        Code = faultCode,
                        Type = faultType,
                        Message = error.Message
                    });
                }
            }

            return faults;
        }

        public override string ToString()
        {
            var stringbuilder = new StringBuilder();
            foreach (var validationError in this.Errors)
                stringbuilder.AppendLine(validationError.Message);

            foreach (var validationError in Alerts)
                stringbuilder.AppendLine(validationError.Message);

            return stringbuilder.ToString();
        }
    }

    public class ValidationResult<TValidationType>
    {
        private readonly List<ValidationError> _erros;
        private readonly List<ValidationError> _alert;
        private readonly List<KeyValuePair<string, string>> _faultsCode;
        private readonly List<KeyValuePair<string, EFaultType>> _faultsType;
        
        public ValidationResult()
        {
            _erros = new List<ValidationError>();
            _alert = new List<ValidationError>();
            _faultsCode = new List<KeyValuePair<string, string>>();
            _faultsType = new List<KeyValuePair<string, EFaultType>>();
        }
        
        public bool IsValid => !_erros.Any();
        public IEnumerable<ValidationError> Errors => _erros;
        public IEnumerable<ValidationError> Alerts => _alert;
        
        public ValidationResult<TValidationType> Add(TValidationType errorCodesType, EFaultType faultType)
        {
            if (!typeof(TValidationType).IsEnum)
                throw new InvalidEnumArgumentException();
            
            if (!System.Enum.IsDefined(typeof(TValidationType), errorCodesType))
                throw new InvalidEnumArgumentException();
            
            if (faultType == EFaultType.Alert)
                _alert.Add(new ValidationError(errorCodesType.DescriptionAttr()));
            else
                _erros.Add(new ValidationError(errorCodesType.DescriptionAttr()));

            _faultsCode.Add(
                new KeyValuePair<string, string>(errorCodesType.DescriptionAttr(), Convert.ToInt32(errorCodesType).ToString()));
            
            _faultsType.Add(new KeyValuePair<string, EFaultType>(errorCodesType.DescriptionAttr(), faultType));
            
            return this;
        }
        
        public ValidationResult<TValidationType> Add(TValidationType errorCodesType, EFaultType faultType, object[] stringFormatArgs)
        {
            if (!typeof(TValidationType).IsEnum)
                throw new InvalidEnumArgumentException();
            
            if (!System.Enum.IsDefined(typeof(TValidationType), errorCodesType))
                throw new InvalidEnumArgumentException();

            var message = string.Format(errorCodesType.DescriptionAttr(), stringFormatArgs);
            
            if (faultType == EFaultType.Alert)
                _alert.Add(new ValidationError(message));
            else
                _erros.Add(new ValidationError(message));

            _faultsCode.Add(new KeyValuePair<string, string>(message, Convert.ToInt32(errorCodesType).ToString()));
            
            _faultsType.Add(new KeyValuePair<string, EFaultType>(message, faultType));
            
            return this;
        }
        
        public ValidationResult<TValidationType> Add(string errorMessage, EFaultType faultType, string faultCode = "")
        {
            if (string.IsNullOrWhiteSpace(errorMessage))
                return this;

            if (faultType == EFaultType.Alert)
                _alert.Add(new ValidationError(errorMessage));
            else
                _erros.Add(new ValidationError(errorMessage));

            _faultsCode.Add(new KeyValuePair<string, string>(errorMessage, faultCode));

            return this;
        }
        
        public List<Fault> GetFaults()
        {
            var faults = new List<Fault>();

            foreach (var error in _erros)
            {
                var faultCode = _faultsCode.FirstOrDefault(x => x.Key == error.Message).Value;
                var faultType = _faultsType.FirstOrDefault(x => x.Key == error.Message).Value;

                if (faultCode != null)
                {
                    faults.Add(new Fault()
                    {
                        Code = string.IsNullOrEmpty(faultCode) ? "Erro não catalogado" : faultCode,
                        Type = faultType,
                        Message = error.Message
                    });
                }
            }

            return faults;
        }
        
        public override string ToString()
        {
            var stringbuilder = new StringBuilder();
            foreach (var validationError in Errors)
                stringbuilder.AppendLine(validationError.Message);

            foreach (var validationError in Alerts)
                stringbuilder.AppendLine(validationError.Message);

            return stringbuilder.ToString();
        }
    }
}
using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using AutoMapper;
using Microsoft.Ajax.Utilities;
using NLog;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Domain.Entities
{
    public class ViagemActionDependencies
    {
        public IViagemEventoDapper ViagemEventoDapper { get; }
        public IViagemDapper ViagemDapper { get; }
        public IContratoCiotAgregadoRepository ContratoCiotAgregadoRepository { get; }
        public IProprietarioRepository ProprietarioRepository { get; }
        public IVeiculoRepository VeiculoRepository { get; }
        public IEmpresaService EmpresaService { get; }
        public IEmpresaRepository EmpresaRepository { get; }
        public ContratoCiotAgregadoActionDependencies ContratoCiotAgregadoActionDependencies { get; }
        
        public ViagemActionDependencies(IViagemEventoDapper viagemEventoDapper, IViagemDapper viagemDapper, IContratoCiotAgregadoRepository contratoCiotAgregadoRepository,
            IProprietarioRepository proprietarioRepository, IVeiculoRepository veiculoRepository, IEmpresaService empresaService, ContratoCiotAgregadoActionDependencies contratoCiotAgregadoActionDependencies, IEmpresaRepository empresaRepository)
        {
            ViagemEventoDapper = viagemEventoDapper;
            ViagemDapper = viagemDapper;
            ContratoCiotAgregadoRepository = contratoCiotAgregadoRepository;
            ProprietarioRepository = proprietarioRepository;
            VeiculoRepository = veiculoRepository;
            EmpresaService = empresaService;
            ContratoCiotAgregadoActionDependencies = contratoCiotAgregadoActionDependencies;
            EmpresaRepository = empresaRepository;
        }
    }
    
    [TrackChanges]
    public class Viagem
    {
        #region Campos

        /// <summary>
        /// Código da Viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da Viagem complemelntar
        /// </summary>
        public int? IdViagemComplementada { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código da Filial
        /// </summary>
        public int? IdFilial { get; set; }

        /// <summary>
        /// Código do proprietario
        /// </summary>
        public int? IdProprietario { get; set; }

        /// <summary>
        /// Placa que esta realizando o transporte
        /// </summary>
        public string Placa { get; set; }

        /// <summary>
        /// Código do Cliente de origem
        /// </summary>
        public int? IdClienteOrigem { get; set; }

        /// <summary>
        /// Código do Cliente de destino
        /// </summary>
        public int? IdClienteDestino { get; set; }

        /// <summary>
        /// Código do cliente que realiza o pagamento
        /// </summary>
        public int? IdClienteTomador { get; set; }

        /// <summary>
        /// Data de coleta da viagem
        /// </summary>
        public DateTime? DataColeta { get; set; }

        /// <summary>
        /// Endereço de coleta
        /// </summary>
        public string Coleta { get; set; }

        /// <summary>
        /// Endereço de entrega
        /// </summary>
        public string Entrega { get; set; }

        /// <summary>
        /// Data de previsão de entrega
        /// </summary>
        public DateTime DataPrevisaoEntrega { get; set; }

        /// <summary>
        /// Data de finalização da viagem
        /// </summary>
        public DateTime? DataFinalizacao { get; set; }

        /// <summary>
        /// Peso apresentado durante a saida da viagem
        /// </summary>
        public decimal? PesoSaida { get; set; }

        /// <summary>
        /// Peso apresentado durante a chegada da viagem
        /// </summary>
        public decimal? PesoChegada { get; set; }

        /// <summary>
        /// Informa o peso de chegada original, caso tenha sofrido alterações durante o processo de pagamento
        /// </summary>
        public decimal? PesoChegadaOriginal { get; set; }

        /// <summary>
        /// Diferenca entre o peso de saida e chegada da viagem
        /// </summary>
        public decimal? PesoDiferenca { get; set; }

        /// <summary>
        /// Valor de frete da viagem
        /// </summary>
        public decimal? ValorMercadoria { get; set; }

        /// <summary>
        /// Valor de quebra de tarifa da viagem
        /// </summary>
        public decimal? DifFreteMotorista { get; set; }

        /// <summary>
        /// Valor de quebra de mercadoria da viagem
        /// </summary>
        public decimal? ValorQuebraMercadoria { get; set; }

        /// <summary>
        /// Valor de quebra de mercadoria calculo
        /// </summary>
        public decimal? ValorQuebraMercadoriaCalculado { get; set; }

        /// <summary>
        /// Data de lancamento
        /// </summary>
        public DateTime? DataLancamento { get; set; }

        /// <summary>
        /// Nome do motorista que esta realizando a viagem
        /// </summary>
        public string NomeMotorista { get; set; }

        /// <summary>
        /// CPF do motorista que esta realizando a viagem
        /// </summary>
        public string CPFMotorista { get; set; }

        /// <summary>
        /// Nome do proprietaario
        /// </summary>
        public string NomeProprietario { get; set; }

        /// <summary>
        /// CPF do motorista que esta realizando a viagem
        /// </summary>
        public string CPFCNPJProprietario { get; set; }

        /// <summary>
        /// Código de RNTRC do proprietario
        /// </summary>
        public int? RNTRC { get; set; }

        /// <summary>
        /// CNH do motorista que esta realizando a viagem
        /// </summary>
        public string CNHMotorista { get; set; }

        /// <summary>
        /// Status da viagem
        /// </summary>
        public EStatusViagem StatusViagem { get; set; } = EStatusViagem.Aberto;

        /// <summary>
        /// Numero do documento
        /// </summary>
        public String NumeroDocumento { get; set; }

        public string NumeroNota { get; set; }

        /// <summary>
        /// Numero do documento complementado
        /// </summary>
        public String NumeroDocumentoComplementado { get; set; }

        /// <summary>
        /// Data de emissão
        /// </summary>
        public DateTime? DataEmissao { get; set; }

        /// <summary>
        /// CNPJFiliall
        /// </summary>
        public string CNPJFilial { get; set; }

        /// <summary>
        /// Razão social da filial
        /// </summary>
        public string RazaoSocialFilial { get; set; }

        /// <summary>
        /// Produto
        /// </summary>
        [SkipTracking] 
        public string Produto { get; set; }

        /// <summary>
        /// Código da natureza da carga referente ao produto principal sendo transportado na viagem
        /// </summary>
        public int? NaturezaCarga { get; set; }

        /// <summary>
        /// Unidade de medida
        /// </summary>
        public EUnidadeMedida Unidade { get; set; }

        /// <summary>
        /// Unidade de medida
        /// </summary>
        public string Origem { get; set; }

        /// <summary>
        /// Quantidade
        /// </summary>
        public decimal Quantidade { get; set; }

        /// <summary>
        /// Status da integração do registro entre o ATS e TMS
        /// </summary>
        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Pendente;

        public string DocumentoCliente { get; set; }

        /// <summary>
        /// Número do cartão utilizado para o pagamento do pedágio da viagem
        /// </summary>
        public string NumeroCartao { get; set; }

        // Impostos
        public decimal IRRPF { get; set; } = 0;
        public decimal INSS { get; set; } = 0;
        public decimal SESTSENAT { get; set; } = 0;

        public decimal ValorPedagio { get; set; } = 0;

        /// <summary>
        /// Indica se o pedágio já é considerado pago ou não.
        /// Influencia no valor que é instruido o posto para trocar a carta frete do motorista.
        /// Caso não for token de adiantamento, não pagando no cartão:
        /// -  PedagioBaixado = false, incluir o valor do pedágio no pagamento do adiantamento do posto. O protocolo gerado posteriormente também deve respeitar a adição do valor do pedágio no adiantamento.
        /// - PedagioBaixado = true, paga apenas o valor do adiantamento. O protoclo gerado posteriormente deve ter apenas o valor do adiantamento.
        ///  Utilizar função "DeveIncluirPedagioJuntoComPagamentoDoEvento" na classe "PagamentoFreteService" para auxiliar na utilização desta regra.
        /// </summary>
        public bool PedagioBaixado { get; set; } = false;

        /// <summary>
        /// Indica que a nossa plataforma deve realizar a gestão do CIOT, veriricar a obrigatoriedade e declarar a operação caso positivo.
        /// O cliente consumidor da plataforma possui a opção de enviar este campo como falso, pois o mesmo pode ter realizado o CIOT por outro fornecedor, e estar consumindo nosso serviço apenas para o pagamento de cartão.
        /// </summary>
        public bool HabilitarDeclaracaoCiot { get; set; }

        public EResultadoDeclaracaoCiot? ResultadoDeclaracaoCiot { get; set; } = EResultadoDeclaracaoCiot.NaoHabilitado;
        [SkipTracking] public string MensagemDeclaracaoCiot { get; set; }

        //Pedagio
        public EResultadoCompraPedagio ResultadoCompraPedagio { get; set; } = EResultadoCompraPedagio.NaoRealizado;

        public DateTime? DataConfirmacaoCreditoPedagio { get; set; }

        public DateTime? DataConfirmacaoEstornoPedagio { get; set; }

        [SkipTracking] public string MensagemCompraPedagio { get; set; }

        /// <summary>
        /// Representa o código da compra gerada no serviço de pedágio
        /// </summary>
        public long? NumeroProtocoloPedagio { get; set; }

        /// <summary>
        /// Primeira viagem do dia do motorista (independente de empresa) irá solicitar a restituição do saldo residual caso o parametro do proprietário/empresa estiver configurado para isto.
        /// </summary>
        public bool? EstornoSaldoResidualPedagioSolicitado { get; set; }

        public DateTime? DataConfirmacaoPedagio { get; set; }
        public DateTime? DataCancelamentoPedagio { get; set; }

        /// <summary>
        /// Código da declaração de viagem CIOT
        /// </summary>
        public int? IdDeclaracaoCiot { get; set; }

        /// <summary>
        /// Campo responsável por realizar uma tratativa no ERP integrador, validando se a viagem já foi integrada ou não para a empresa
        /// </summary>
        [SkipTracking]
        public string NumeroControle { get; set; }

        /// <summary>
        /// Data de descarga
        /// </summary>
        public DateTime? DataDescarga { get; set; }

        public int? CodigoTipoCarga { get; set; }

        public int? DistanciaViagem { get; set; }

        public string CepOrigem { get; set; }

        public string CepDestino { get; set; }

        public bool AltoDesempenho { get; set; }

        public bool DestinacaoComercial { get; set; }

        public bool FreteRetorno { get; set; }

        public string CepRetorno { get; set; }

        public int? DistanciaRetorno { get; set; }

        public EViagemFormaPagamento FormaPagamento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
        public bool? ValePedagioSolicitado { get; set; } = false;
        public string ProtocoloValePedagio { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public string ContaCorrente { get; set; }
        public string Agencia { get; set; }
        public EViagemFormaPagamento? FormaPagamentoSemCartao { get; set; }
        public int? IdBanco { get; set; }
        public string DescricaoBanco { get; set; }
        public ETipoConta? TipoConta { get; set; } = ETipoConta.ContaCorrente;
        public bool IsPedagioAvulso { get; set; } = false;

        #endregion

        #region Navegação

        #region Tabelas filhas

        public virtual ICollection<ViagemRegra> ViagemRegras { get; set; }

        /// <summary>
        /// Checks (status) da viagem
        /// </summary>
        public virtual ICollection<ViagemCheck> ViagemChecks { get; set; }

        /// <summary>
        /// Check-ins vinculados a viagem
        /// </summary>
        public virtual ICollection<CheckIn> CheckIns { get; set; }

        /// <summary>
        /// Check-ins vinculados a viagem
        /// </summary>
        public virtual ICollection<CheckinResumo> CheckinResumo { get; set; }


        /// <summary>
        /// Cargas vinculadas a viagem
        /// </summary>
        public virtual ICollection<ViagemCarga> ViagemCargas { get; set; }

        #endregion

        // Reverse navigation
       public virtual ICollection<ViagemCarreta> ViagemCarretas { get; set; }
        public virtual ICollection<ViagemEstabelecimento> ViagemEstabelecimentos { get; set; }
        public virtual ICollection<ViagemEvento> ViagemEventos { get; set; } = new List<ViagemEvento>();

        public virtual ICollection<Viagem> ViagensComplementar { get; set; }

//        public virtual ICollection<DeclaracaoCiot> DeclaracaoCiots { get; set; }
        public virtual ICollection<ViagemDocumentoFiscal> ViagemDocumentosFiscais { get; set; }
        public virtual ICollection<DeclaracaoCiot> CIOTs { get; set; }

        // Foreign keys
        public virtual Cliente ClienteDestino { get; set; }
        public virtual Cliente ClienteOrigem { get; set; }
        public virtual Cliente ClienteTomador { get; set; }
        public virtual Filial Filial { get; set; }
        public virtual Empresa Empresa { get; set; }
        public virtual Proprietario Proprietario { get; set; }
        public virtual Viagem ViagemComplementada { get; set; }

        /// <summary>
        /// Vinculo com a declaração de CIOT referente a viagem.
        /// Para viagem padrão: Relacionamento 1 para 1 e FK recursiva entre Viagem.IdDeclaracaoCiot e DeclaracaoCiot.IdViagemOrigem.
        /// Para viagem contrato agregado: Relacionamento N para 1. Existirão N viagens com o mesmo IdDeclaracaoCiot, porém o campo DeclaracaoCiot.IdViagem representa apenas a primeira viagem vinculada ao contrato.
        /// </summary>
        public virtual DeclaracaoCiot DeclaracaoCiot { get; set; }

        public virtual ViagemPagamentoConta ViagemPagamentoConta { get; set; }

        #endregion

        #region Regras de negócio

        #region Versão 2 ANTT
        
        /*
         * A VERSÃO 2 DA ANTT É COMO CHAMAMOS A VERSÃO DA ANTT ANTERIOR AO CIOT PARA TODOS.
         * ESTA VERSÃO ENCONTRA-SE ATUALMENTE (2021) APENAS EM AMBIENTE DE PRODUÇÃO.
         * ELA AINDA PERMITE:
         *  - NÃO EQUIPARADOS NÃO PRECISAM ENVIAR CIOT
         *  - HÁ RETIFICAÇÃO DO CIOT PADRÃO
         *  - NÃO HÁ NECESSIDADE DE ENVIO DE VALOR PARA A ANTT
         *  - NÃO HÁ VALIDAÇÃO COM RELAÇÃO A VALOR PAGO NA ANTT
         */

        public DeclararCiotResult DeclararCiotV2(IContratoCiotAgregadoService contratoCiotAgregadoService, IVeiculoService veiculoService, ICiotV2Service ciotV2Service, IProprietarioService proprietarioService,
            IViagemRepository viagemRepository, IDeclaracaoCiotRepository declaracaoCiotRepository, ViagemActionDependencies dependencies, string cnpjEmpresa,
            bool retificar, bool forcarCiotNaoEquiparado, bool habilitarDeclaracaoCiot, bool? gerarCiotTacAgregado)
        {
            if (!IdDeclaracaoCiot.HasValue && !habilitarDeclaracaoCiot)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.NaoHabilitado,
                        Declarado = false,
                        Mensagem =
                            "CIOT não processado. Tag 'HabilitarDeclaracaoCiot' desabilitada pelo consumidor."
                    };
            
            var viagem = this;

            try
            {
                #region CIOT já declarado

                if (IdDeclaracaoCiot != null)
                {
                    var declaracaoCiot = ciotV2Service.GetQueryDeclaracaoCiotByViagem(viagem.IdViagem, viagem.IdEmpresa)
                        ?.FirstOrDefault();
                    var ciotIntegrado = declaracaoCiot?.GetCiotResultV2();

                    if (ciotIntegrado != null)
                    {
                        if (retificar)
                        {
                            if (declaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Padrao)
                            {
                                var retificarOperacaoTransporteReponse = viagem.RetificarCiotPadraoV2(ciotV2Service,
                                    declaracaoCiotRepository, viagemRepository, dependencies);
                                if (retificarOperacaoTransporteReponse.Sucesso.HasValue &&
                                    !retificarOperacaoTransporteReponse.Sucesso.Value)
                                    LogManager.GetCurrentClassLogger()
                                        .Info(
                                            $"Erro ao retificar CIOT: {retificarOperacaoTransporteReponse.Excecao?.Mensagem}");

                                return ciotIntegrado;
                            }

                            return ciotIntegrado;
                        }
                        
                        if (ciotIntegrado.Resultado == EResultadoDeclaracaoCiot.Sucesso)
                        {
                            ciotIntegrado.Mensagem = "Ciot atualizado com sucesso";
                            return ciotIntegrado;
                        }
                    }
                }

                if (!habilitarDeclaracaoCiot)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.NaoHabilitado,
                        Declarado = false,
                        Mensagem = "CIOT não processado. Tag 'HabilitarDeclaracaoCiot' desabilitada pelo consumidor."
                    };
                
                #endregion
                
                #region Verifica o Proprietário

                if (viagem.CPFCNPJProprietario.IsNullOrWhiteSpace())
                    throw new CiotAtsException(
                        $"CPF/CNPJ de proprietário não informado na viagem {viagem.IdViagem} para declarar CIOT.");

                if (viagem.RNTRC == null || viagem.RNTRC == 0)
                    throw new CiotAtsException($"RNTRC não informado na viagem {viagem.IdViagem} para declarar CIOT.");

                var retornoTac = proprietarioService.EquiparadoTac(viagem.CPFCNPJProprietario,
                    viagem.RNTRC.Value.ToString().PadLeft(9, '0'), cnpjEmpresa);

                // Atualiza o proprietario se ele é ou não equiparado a TAC                
                var proprietario = proprietarioService.Repository.GetPorCpfCnpj(viagem.CPFCNPJProprietario, viagem.IdEmpresa);

                if (proprietario == null)
                    throw new Exception(
                        $"O proprietário {viagem.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados");

                #endregion

                #region Gerenciamento do CIOT
                
                if (retornoTac.Retorno != ERetornoConsultaTAC.Erro)
                {
                    if (proprietario.EquiparadoTac != (retornoTac.Retorno == ERetornoConsultaTAC.Equiparado))
                    {
                        proprietario.EquiparadoTac = retornoTac.Retorno == ERetornoConsultaTAC.Equiparado;
                        proprietarioService.Update(proprietario);
                    }

                    if (retornoTac.Retorno == ERetornoConsultaTAC.Equiparado || forcarCiotNaoEquiparado)
                    {
                        #region TAC Agregado

                         // SE É POR CONTRATO, VERIFICAR SE EXISTE CONTRATO VIGENTE E ABRE CASO NECESSÁRIO
                        // VerificarECriarContrato(CnpjProp, placa)
                        // VincularViagemContrato

                        //Quando o contratante não possui RNTRC ele é do tipo indústria e não transportadora,
                        //ainda assim deve ser permitido emitir CIOT, porém só do tipo padrão
                        var possuiRntrc = dependencies.EmpresaRepository.Query(viagem.IdEmpresa).Any(x => x.CNTRC.HasValue);

                        if (gerarCiotTacAgregado.HasValue)
                            proprietarioService.AtualizaEquiparadoTac(proprietario.IdProprietario,
                                gerarCiotTacAgregado.Value);

                        if (possuiRntrc && ciotV2Service.IsDeclaracaoTacAgregado(viagem.Placa, viagem.IdEmpresa,
                            proprietario.HabilitarContratoCiotAgregado) || gerarCiotTacAgregado == true)
                        {
                            #region CIOT Contrato de agregado
                            
                            var eventos = viagem.ViagemEventos.Where(ve => ve.Status != EStatusViagemEvento.Cancelado).ToList();
                            var tarifas = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                            var abastecimentos = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
                            var idVeiculoViagem = GetVeiculo(veiculoService, viagem.Placa, viagem.IdEmpresa);

                            if (!idVeiculoViagem.HasValue)
                                throw new Exception(
                                    $"Veículo {viagem.Placa.ToPlacaFormato()} não encontrado na base de dados.");

                            var contratoCiotAgregado =
                                contratoCiotAgregadoService.GetContratoVigente(viagem.IdEmpresa, proprietario.RNTRC);

                            // Se tem contrato aberto com outra placa sem ciot e sem viagens, o contrato é cancelado para declarar o ciot com a placa da viagem atual
                            if (contratoCiotAgregado != null && !contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                            {
                                if (contratoCiotAgregado.ContratoCiotAgregadoVeiculos.All(x =>
                                    x.IdVeiculo != idVeiculoViagem))
                                {
                                    contratoCiotAgregadoService.CancelarContrato(contratoCiotAgregado,
                                        "Contrato não possui viagem para o veículo e será substituído por um novo contrato");
                                    contratoCiotAgregado = null;
                                }
                            }

                            if (contratoCiotAgregado == null)
                            {
                                // Criar novo contrato e declara CIOT na ANTT
                                var contratoAberturaModel = new ContratoAberturaModel
                                {
                                    Veiculos = new List<VeiculoModelAgregado>
                                    {
                                        new VeiculoModelAgregado
                                        {
                                            IdVeiculo = idVeiculoViagem.Value,
                                            Placa = viagem.Placa
                                        }
                                    },
                                    IdEmpresa = viagem.IdEmpresa,
                                    IdProprietario = proprietario.IdProprietario,
                                    DataInicio = DateTime.Today,
                                    DataFinal = DateTime.Today.AddDays(30).EndOfDayWithDelay(),
                                    QuantidadeTarifas = tarifas.Count,
                                    ValorTarifas = tarifas.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                                    ValorCombustivel =
                                        abastecimentos.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                                    ValorPedagio = viagem.ValorPedagio,
                                    ValorINSS = viagem.INSS,
                                    ValorIRRF = viagem.IRRPF,
                                    ValorSESTSENAT = viagem.SESTSENAT
                                };
                                
                                if (viagem.ViagemCarretas?.Any() == true)
                                    foreach (var carreta in viagem.ViagemCarretas)
                                        contratoAberturaModel.Veiculos.Add(new VeiculoModelAgregado
                                        {
                                            IdVeiculo = veiculoService.GetIdPorPlaca(carreta.Placa, viagem.IdEmpresa).GetValueOrDefault(),
                                            Placa = carreta.Placa
                                        });

                                var abrirContratoResult =
                                    contratoCiotAgregadoService.AbrirContratoCiotAgregado(contratoAberturaModel);

                                if (!viagem.IdDeclaracaoCiot.HasValue &&
                                    abrirContratoResult.CiotResult?.DeclaracaoCiot?.IdDeclaracaoCiot > 0)
                                    viagem.IdDeclaracaoCiot =
                                        abrirContratoResult.CiotResult.DeclaracaoCiot.IdDeclaracaoCiot;

                                return !abrirContratoResult.Sucesso || abrirContratoResult.CiotResult == null
                                    ? new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Erro,
                                        Mensagem = abrirContratoResult.Mensagem
                                    }
                                    : new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Sucesso,
                                        Declarado = true,
                                        Dados = Mapper.Map<DeclararCiotResult.Declaracao>(abrirContratoResult.CiotResult
                                            .DeclaracaoCiot),
                                        Mensagem = abrirContratoResult.Mensagem
                                    };
                            }

                            // Se existir registro na tabela de contrato, que não possua CIOT gerado, tenta declara o CIOT para seguir o processo (Pode acontecer isso caso ANTT esteja fora durante o processo de declaração
                            if (contratoCiotAgregado.Status == EStatusContratoAgregado.Vigente &&
                                contratoCiotAgregado.IdDeclaracaoCiot == null)
                            {
                                //var resultDeclaracaoCiot = contratoCiotAgregado.VerificarEDeclararCiotAntt(agregadoService.Repository, ciotService, declaracaoCiotRepository);
                                var resultDeclaracaoCiot =
                                    contratoCiotAgregado.VerificarEDeclararCiotAntt(contratoCiotAgregadoService.Repository,
                                        ciotV2Service, null, EVersaoAntt.Versao2,
                                        declaracaoCiotRepository, dependencies.ContratoCiotAgregadoActionDependencies);

                                if (!viagem.IdDeclaracaoCiot.HasValue &&
                                    resultDeclaracaoCiot.DeclaracaoCiot?.IdDeclaracaoCiot > 0)
                                    viagem.IdDeclaracaoCiot = resultDeclaracaoCiot.DeclaracaoCiot.IdDeclaracaoCiot;

                                return resultDeclaracaoCiot.Sucesso != true ||
                                       resultDeclaracaoCiot.DeclaracaoCiot == null
                                    ? new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Erro,
                                        Mensagem = resultDeclaracaoCiot.Excecao?.Mensagem
                                    }
                                    : new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Sucesso,
                                        Declarado = true,
                                        Dados = Mapper.Map<DeclararCiotResult.Declaracao>(resultDeclaracaoCiot
                                            .DeclaracaoCiot),
                                        Mensagem = resultDeclaracaoCiot.AvisoTransportador
                                    };
                            }

                            //Verifica se existe algum veículo diferente na viagem e insere no contrato, retificando CIOT
                            Collection<VeiculoModelAgregado> novosVeiculosList;
                            var retificaPorPlaca = ValidaRetificacaoPorPlacaV2(contratoCiotAgregado, viagem,
                                idVeiculoViagem.Value, cnpjEmpresa,
                                veiculoService, ciotV2Service, out novosVeiculosList);

                            //Verifica se a placa existe no contrato
                            if (retificaPorPlaca || tarifas.Any() || abastecimentos.Any())
                            {
                                var idViagemEventosRequest = eventos.Select(x => x.IdViagemEvento).ToList();
                                var freteRequest = eventos.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();

                                // Pega os eventos do banco e retira da lista o que está vindo na request que já estava no banco antes
                                var eventosJaExistentes = dependencies.ViagemEventoDapper.ConsultarEventosCiotAgregado(contratoCiotAgregado.IdContratoCiotAgregado);
                                eventosJaExistentes = eventosJaExistentes.Where(x => !idViagemEventosRequest.Contains(x.IdViagemEvento)).ToList();

                                var tarifasJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                                var abastecimentosJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
                                var freteJaExistentes = eventosJaExistentes.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();

                                // Atualizar impressão
                                contratoCiotAgregado.ValorPedagio = dependencies.ViagemDapper.ConsultarValorPedagiosCiotAgregado(contratoCiotAgregado.IdContratoCiotAgregado);
                                contratoCiotAgregado.ValorFrete = freteRequest.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                                  + freteJaExistentes.Sum(ve => ve.Valor);
                                contratoCiotAgregado.ValorFretePago = freteRequest.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                                      + freteJaExistentes.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.Valor);
                                
                                // Campos retificação ANTT
                                contratoCiotAgregado.ValorCombustivel = abastecimentos.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                                                   + abastecimentosJaExistentes.Sum(ve => ve.Valor);
                                contratoCiotAgregado.QuantidadeTarifas = tarifas.Count + tarifasJaExistentes.Count;
                                contratoCiotAgregado.ValorTarifas = tarifas.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                                        + tarifasJaExistentes.Sum(x => x.Valor);


                                //Retifica para incluir o novo veículo
                                var retificarResult = ciotV2Service.RetificarCiot(contratoCiotAgregadoService, contratoCiotAgregado,
                                    dependencies.ContratoCiotAgregadoRepository, novosVeiculosList);

                                if (retificarResult.Resultado == EResultadoDeclaracaoCiot.Erro)
                                    return new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Erro,
                                        Declarado = true,
                                        Mensagem = retificarResult.Mensagem,
                                        Dados = retificarResult.Dados
                                    };
                            }

                            var ciotAgregadoResult = contratoCiotAgregado.DeclaracaoCiot.GetCiotResultV2();
                            if (contratoCiotAgregado.DeclaracaoCiot.IdViagem == null)
                                contratoCiotAgregado.DeclaracaoCiot.SetIdViagem(viagem.IdViagem,
                                    declaracaoCiotRepository);

                            if (viagem.IdDeclaracaoCiot == null && contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                                SetDeclaracaoCiotV2(viagemRepository, contratoCiotAgregado.IdDeclaracaoCiot.Value);

                            return new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Sucesso,
                                Declarado = true,
                                Mensagem = "Contrato de agregado não necessita declarar CIOT novamente",
                                Dados = ciotAgregadoResult.Dados
                            };

                            #endregion
                        }

                        #endregion
                        
                        #region CIOT Padrão

                        var ciotResponse = DeclararCiotPadraoAnttV2(ciotV2Service, dependencies, viagemRepository);
                        if (!ciotResponse.Sucesso.GetValueOrDefault(false))
                        {
                            return new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Erro,
                                Declarado = false,
                                Mensagem = ciotResponse.Excecao?.Mensagem
                            };
                        }

                        if (viagem.IdDeclaracaoCiot == null && ciotResponse.DeclaracaoCiot != null)
                            SetDeclaracaoCiotV2(viagemRepository, ciotResponse.DeclaracaoCiot.IdDeclaracaoCiot);

                        return ciotResponse.DeclaracaoCiot?.GetCiotResultV2();

                        #endregion
                    }

                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.NaoObrigatorio,
                        Declarado = false,
                        Mensagem = "O proprietário não está equiparado."
                    };
                }

                #endregion

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = retornoTac.Mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro declarar CIOT para viagem Id: {viagem.IdViagem} / Empresa: {viagem.IdEmpresa}.");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.GetBaseException().Message;

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = msg
                };
            }
        }

        #region Métodos Privados
        
        private bool ValidaRetificacaoPorPlacaV2(ContratoCiotAgregado contratoCiotAgregado, Viagem viagem, int idVeiculoViagem, string cnpjEmpresa, IVeiculoService veiculoService, ICiotV2Service ciotV2Service, out Collection<VeiculoModelAgregado> novosVeiculosList)
        {
            novosVeiculosList = new Collection<VeiculoModelAgregado>();
            var retificaPorPlaca = false;
            
            var veiculosPersistidos = new Collection<Veiculo>();

            var idVeiculosPersistidosList = contratoCiotAgregado.ContratoCiotAgregadoVeiculos
                .Select(v => v.IdVeiculo).ToList();
            
            foreach (var id in idVeiculosPersistidosList)
                veiculosPersistidos.Add(veiculoService.Get(id));

            var placaPersistidasList = veiculosPersistidos.Select(v => v.Placa).ToList();
            var carretasNovas = viagem.ViagemCarretas?.Select(c => c.Placa).Except(placaPersistidasList).ToList();

            //Se a placa cavalo da viagem já existe no banco e não tem carreta nova já retorna false para retificação por placa
            if (placaPersistidasList.Contains(viagem.Placa) && carretasNovas?.Any() != true)
            {
                novosVeiculosList = null;
                return false;
            }
            
            if (!viagem.RNTRC.HasValue)
                return false;

            var viagemRntrc = viagem.RNTRC.Value.ToString().PadLeft(9, '0');
            
            if (!placaPersistidasList.Contains(viagem.Placa))
            {
                var consultaFrota = ciotV2Service.ConsultarFrotaTransportador(new ConsultarFrotaTransportadorRequest
                {
                    Placa = new ObservableCollection<string> {viagem.Placa},
                    RntrcTransportador = viagemRntrc,
                    CpfCnpjTransportador = viagem.CPFCNPJProprietario,
                    CpfCnpjInteressado = cnpjEmpresa
                });

                //Se deu erro na consulta ou retornou que a carreta é do proprietário, informa na retificação, caso contrário apenas vai salvar no banco
                var inclusoCiot = consultaFrota.Sucesso != true || 
                                  consultaFrota.FalhaComunicacaoAntt != false || 
                                  !consultaFrota.VeiculoTransportador.Any() || 
                                  consultaFrota.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true;

                if (inclusoCiot)
                    retificaPorPlaca = true;
                
                novosVeiculosList.Add(new VeiculoModelAgregado
                {
                    IdVeiculo = idVeiculoViagem,
                    Placa = viagem.Placa, 
                    InclusoCiot = inclusoCiot
                });
            }

            if (carretasNovas?.Any() == true)
                foreach(var carreta in carretasNovas)
                {
                    var consultaFrota = ciotV2Service.ConsultarFrotaTransportador(new ConsultarFrotaTransportadorRequest
                    {
                        Placa = new ObservableCollection<string> {carreta},
                        RntrcTransportador = viagem.RNTRC.Value.ToString().PadLeft(9, '0'),
                        CpfCnpjTransportador = viagem.CPFCNPJProprietario,
                        CpfCnpjInteressado = cnpjEmpresa
                    });

                    //Se deu erro na consulta ou retornou que a carreta é do proprietário, informa na retificação, caso contrário apenas vai salvar no banco
                    var inclusoCiot = consultaFrota.Sucesso != true || 
                                      consultaFrota.FalhaComunicacaoAntt != false || 
                                      !consultaFrota.VeiculoTransportador.Any() || 
                                      consultaFrota.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true;

                    if (inclusoCiot)
                        retificaPorPlaca = true;
                    
                    var idVeiculoCarreta = veiculoService.GetIdPorPlaca(carreta, IdEmpresa);
                    if(idVeiculoCarreta.HasValue)
                        novosVeiculosList.Add(new VeiculoModelAgregado
                        {
                            IdVeiculo = idVeiculoCarreta.Value, 
                            Placa = carreta, 
                            InclusoCiot = inclusoCiot
                        });
                }

            if (!retificaPorPlaca)
            {
                novosVeiculosList = null;
                return false;
            }

            //Popula a lista de veiculos que já estavam no contrato porque para retificar placa sempre são enviados todos, os já existentes e os novos
            var veiculosModelAgregadoPersistidos = Mapper.Map<Collection<VeiculoModelAgregado>>(veiculosPersistidos);
            foreach (var novoVeiculo in veiculosModelAgregadoPersistidos)
                novoVeiculo.InclusoCiot = contratoCiotAgregado.ContratoCiotAgregadoVeiculos
                    .Where(c => c.IdVeiculo == novoVeiculo.IdVeiculo)
                    .Select(c => c.InclusoCiot)
                    .First();

            novosVeiculosList.AddRange(veiculosModelAgregadoPersistidos);
            
            return true;
        }

        private int? GetVeiculo(IVeiculoService veiculoService, string placa, int idEmpresa)
        {
            return veiculoService.GetIdPorPlaca(placa, idEmpresa);
        }

        private void SetDeclaracaoCiotV2(IViagemRepository repository, int idDeclaracaoCiot)
        {
            if (idDeclaracaoCiot == 0)
                throw new Exception("IdDeclaracaoCiot não informado para atribuir a viagem id " + IdViagem);
            
            IdDeclaracaoCiot = idDeclaracaoCiot;            
            repository.Update(this);            
        }
        
        private DeclararOperacaoTransporteModel DeclararCiotPadraoAnttV2(ICiotV2Service ciotService, ViagemActionDependencies dependecies, IViagemRepository viagemRepository)
        {            
            try
            {
                var viagem = this;

                var dadosCiotV2Dto = viagemRepository.GetDadosCiotV2(viagem.IdViagem);

                // Carrega proprietário a partir do CNPJ informado na integração da viagem. Padronizado todas leitura de proprietário neste campo.
                var proprietarioRepository = dependecies.ProprietarioRepository;
                var veiculoRepository = dependecies.VeiculoRepository;

                var propQy = proprietarioRepository
                    .AsNoTracking()
                    .Include(p => p.Enderecos)
                    .Include(p => p.Enderecos)
                    .Include(p => p.Enderecos.Select(e => e.Cidade))
                    .Include(p => p.Contatos);

                var prop = propQy
                    .FirstOrDefault(p => p.CNPJCPF == viagem.CPFCNPJProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (prop == null)
                    prop = propQy
                        .FirstOrDefault(p => p.IdProprietario == viagem.IdProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (prop == null)
                    throw new CiotAtsException($"Não foi possível localizar o proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");

                var rntrcPlacaCavaloCadastrada = veiculoRepository.GetRntrcProprietarioVeiculo(viagem.Placa, viagem.IdEmpresa);
                var rntrcProprietarioViagem = prop.RNTRC.PadLeft(9, '0');
                var propEnd = prop.Enderecos.FirstOrDefault();
                
                if (propEnd == null)
                    throw new CiotAtsException($"Não foi possível localizar o endereço do proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");
                
                var propContato = prop.Contatos.FirstOrDefault();

                var request = new DeclararOperacaoTransporteRequest();

                #region Construir ANTT request
                
                // Frete
                request.Frete = new FreteRequest
                {
                    DataTerminoFrete = viagem.DataPrevisaoEntrega < DateTime.Now ? DateTime.Now.AddDays(1) : viagem.DataPrevisaoEntrega,
                    DadosComplementares =
                        "Documento cliente: " + viagem.DocumentoCliente + " / Número: " + viagem.NumeroDocumento,
                    DocumentoCliente = viagem.DocumentoCliente,
                    SubContratacao = false,
                    TipoViagem = ETipoDeclaracao.Padrao.ToIntNullable(),
                };

                
                request.Frete.DataInicioFrete = viagem.DataColeta ?? DateTime.Now.AddHours(1); // viagem.DataEmissao, // Viagens podem ser emitidas num dia pelo ERP, e programadas a viagem no outro
                request.Frete.PesoCarga = viagem.PesoChegada.HasValue && viagem.PesoChegada > 0
                    ? viagem.PesoChegada
                    : viagem.PesoSaida ?? 0;
                request.Frete.CodigoNaturezaCarga = viagem.NaturezaCarga?.ToString();
                request.Frete.CodigoMunicipioOrigem = dadosCiotV2Dto.Remetente.IBGE;
                request.Frete.CodigoMunicipioDestino = dadosCiotV2Dto.Destinatario.IBGE;

                request.Frete.Proprietario = new ProprietarioRequest
                {
                    CpfCnpj = viagem.CPFCNPJProprietario.DefaultIfEmpty(prop.CNPJCPF),
                    NomeRazaoSocial = prop.RazaoSocial,
                    NomeFantasia = prop.NomeFantasia,
                    Rntrc = viagem.RNTRC.HasValue && viagem.RNTRC.Value > 0 ? viagem.RNTRC.Value.ToString().PadLeft(9, '0') : rntrcProprietarioViagem,
                    TipoPessoa = prop.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = propEnd.Cidade.IBGE,
                        Logradouro = propEnd.Endereco,
                        Cep = propEnd.CEP,
                        Numero = propEnd.Numero?.ToString(),
                        Complemento = propEnd.Complemento,
                        Bairro = propEnd.Bairro,
                        Telefone = propContato?.Telefone,
                        Email = propContato?.Email
                    }
                };
                request.Frete.Motorista = new MotoristaRequest
                {
                    CpfCnpj = viagem.CPFMotorista,
                    Nome = viagem.NomeMotorista,
                    
                    // Campo comentado pois não é obrigatório na ANTT
                    //NumeroCNH = viagem.CNHMotorista
                };

                // Empresa contratante
                request.Contratante = new ContratanteRequest
                {
                    CpfCnpj = dadosCiotV2Dto.Contratante.CNPJ,
                    NomeRazaoSocial = dadosCiotV2Dto.Contratante.RazaoSocial,
                    NomeFantasia = dadosCiotV2Dto.Contratante.NomeFantasia,
                    TipoPessoa = dadosCiotV2Dto.Contratante.CNPJ.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV2Dto.Contratante.IBGE,
                        Cep = dadosCiotV2Dto.Contratante.CEP,
                        Bairro = dadosCiotV2Dto.Contratante.Bairro,
                        Complemento = dadosCiotV2Dto.Contratante.Complemento,
                        Email = dadosCiotV2Dto.Contratante.Email,
                        Logradouro = dadosCiotV2Dto.Contratante.Endereco,
                        Numero = dadosCiotV2Dto.Contratante.Numero?.ToString(),
                        Telefone = dadosCiotV2Dto.Contratante.Telefone
                    }
                };
                
                // Remetente
                request.Remetente = new RemetenteRequest
                {
                    CpfCnpj = dadosCiotV2Dto.Remetente.CNPJCPF,
                    NomeRazaoSocial = dadosCiotV2Dto.Remetente.RazaoSocial,
                    NomeFantasia = dadosCiotV2Dto.Remetente.NomeFantasia,
                    TipoPessoa = dadosCiotV2Dto.Remetente.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV2Dto.Remetente.IBGE,
                        Logradouro = dadosCiotV2Dto.Remetente.Endereco,
                        Cep = dadosCiotV2Dto.Remetente.CEP,
                        Numero = dadosCiotV2Dto.Remetente.Numero?.ToString(),
                        Complemento = dadosCiotV2Dto.Remetente.Complemento,
                        Bairro = dadosCiotV2Dto.Remetente.Bairro,
                        Telefone = dadosCiotV2Dto.Remetente.Celular,
                        Email = dadosCiotV2Dto.Remetente.Email
                    }
                };

                // Destinatário
                request.Destinatario = new DestinatarioRequest
                {
                    CpfCnpj = dadosCiotV2Dto.Destinatario.CNPJCPF,
                    NomeRazaoSocial = dadosCiotV2Dto.Destinatario.RazaoSocial,
                    NomeFantasia = dadosCiotV2Dto.Destinatario.NomeFantasia,
                    TipoPessoa = dadosCiotV2Dto.Destinatario.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV2Dto.Destinatario.IBGE,
                        Logradouro = dadosCiotV2Dto.Destinatario.Endereco,
                        Cep = dadosCiotV2Dto.Destinatario.CEP,
                        Numero = dadosCiotV2Dto.Destinatario.Numero?.ToString(),
                        Complemento = dadosCiotV2Dto.Destinatario.Complemento,
                        Bairro = dadosCiotV2Dto.Destinatario.Bairro,
                        Telefone = dadosCiotV2Dto.Destinatario.Celular,
                        Email = dadosCiotV2Dto.Destinatario.Email
                    }
                };
                
                // Veículos
                request.Veiculos = new ObservableCollection<VeiculoRequest>
                {
                    new VeiculoRequest
                    {
                        Placa = viagem.Placa,
                        Rntrc = !string.IsNullOrWhiteSpace(rntrcPlacaCavaloCadastrada) 
                            ? rntrcPlacaCavaloCadastrada 
                            : viagem.RNTRC > 0
                                ? viagem.RNTRC.Value.ToString().PadLeft(9, '0') 
                                : rntrcProprietarioViagem,
                    }
                };

                if (viagem.ViagemCarretas != null)
                    foreach (var carreta in viagem.ViagemCarretas)
                    {
                        var veiculoRntrc = ciotService.ObterVeiculoPorPlaca(carreta, viagem.IdEmpresa);

                        request.Veiculos.Add(!string.IsNullOrWhiteSpace(carreta.Rntrc) ? 
                            new VeiculoRequest {Placa = carreta.Placa, Rntrc = carreta.Rntrc.PadLeft(9, '0')}
                            : !string.IsNullOrWhiteSpace(veiculoRntrc?.RNTRC) 
                                ? new VeiculoRequest {Placa = carreta.Placa, Rntrc = veiculoRntrc.RNTRC.PadLeft(9, '0')} 
                                : new VeiculoRequest {Placa = carreta.Placa, Rntrc = rntrcProprietarioViagem});
                    }

                var eventos = viagem.ViagemEventos.Where(o => o.Status != EStatusViagemEvento.Cancelado).ToList();
                var eventosFrete = eventos.Where(o => o.TipoEventoViagem != ETipoEventoViagem.Abastecimento && 
                                                      o.TipoEventoViagem != ETipoEventoViagem.TarifaAntt).ToList();
                var eventosTarifas = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                
                var valorFrete = eventosFrete.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorFretePago = eventosFrete.Where(o => o.Status == EStatusViagemEvento.Baixado)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                
                var valorAbastecimentos = eventos.Where(o => o.TipoEventoViagem == ETipoEventoViagem.Abastecimento)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorTarifas = eventosTarifas.Sum(e => e.ValorTotalPagamento ?? e.ValorPagamento);
                var qtdeTarifas = eventosTarifas.Count();
                var valorPedagio = !viagem.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado)
                    ? viagem.ValorPedagio
                    : 0;

                request.Valores = new ValoresFreteRequest
                {
                    ValorFrete = valorFrete,
                    ValorFretePago = valorFretePago,
                    TotalImposto = viagem.SESTSENAT + viagem.INSS + viagem.IRRPF,
                    ValorSESTSENAT = viagem.SESTSENAT,
                    ValorINSS = viagem.INSS,
                    ValorIRRF = viagem.IRRPF,
                    TotalPegadio = valorPedagio,
                    ValorCombustivel = valorAbastecimentos,
                    ValorDespesas = valorFrete + valorAbastecimentos + valorTarifas + valorPedagio,
                    QuantidadeTarifas = qtdeTarifas,
                    ValorTarifas = valorTarifas
                };

                request.Pagamento = new PagamentoRequest
                {
                    FormaPagmento = 1,
                    Parcelas = new ObservableCollection<ParcelaPagamentoRequest>()
                };

                var codigoParcela = 1;
                foreach (var evento in viagem.ViagemEventos.OrderBy(v => v.IdViagemEvento))
                {
                    request.Pagamento.Parcelas.Add(new ParcelaPagamentoRequest
                    {
                        CodigoParcela = codigoParcela.ToString(),
                        ValorParcela = evento.ValorTotalPagamento ?? 0,
                        Vencimento = evento.DataHoraPagamento ?? evento.DataValidade
                    });
                    codigoParcela++;
                }
                
                #endregion

                // Declarar CIOT
                var result = ciotService.DeclararOperacaoTransporte(request, this, null);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao declarar CIOT da viagem Id: {IdViagem} / Empresa: {IdEmpresa}");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                return new DeclararOperacaoTransporteModel
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = msg,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    }
                };
            }
        }
        
        private RetificarOperacaoTransporteReponse RetificarCiotPadraoV2(ICiotV2Service ciotService, IDeclaracaoCiotRepository declaracaoCiotRepository, IViagemRepository viagemRepository,
            ViagemActionDependencies dependencies)
        {
            try
            {
                var proprietarioRepository = dependencies.ProprietarioRepository;
                var veiculoRepository = dependencies.VeiculoRepository;

                var viagem = this;
                if (viagem.DeclaracaoCiot == null && viagem.IdDeclaracaoCiot.HasValue)
                    viagem.DeclaracaoCiot = declaracaoCiotRepository.Get(viagem.IdDeclaracaoCiot.Value);

                if (viagem.DeclaracaoCiot == null)
                    return new RetificarOperacaoTransporteReponse
                    {
                        Sucesso = false,
                        Excecao = new ExcecaoResponse
                        {
                            Mensagem = "Dados da declaração de CIOT da viagem {0} não localizados para retificar."
                                .FormatEx(IdViagem)
                        }
                    };

                // Carrega proprietário a partir do CNPJ informado na integração da viagem. Padronizado todas leitura de proprietário neste campo.
                var propQy = proprietarioRepository
                    .AsNoTracking()
                    .Include(p => p.Enderecos.Select(e => e.Cidade))
                    .Include(p => p.Contatos);

                var prop = propQy
                               .FirstOrDefault(p => p.CNPJCPF == viagem.CPFCNPJProprietario && p.IdEmpresa == viagem.IdEmpresa) ?? propQy
                               .FirstOrDefault(p => p.IdProprietario == viagem.IdProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (prop == null)
                    throw new CiotAtsException($"Não foi possível localizar o proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");

                var rntrcPlacaCavaloCadastrada = veiculoRepository.GetRntrcProprietarioVeiculo(viagem.Placa, viagem.IdEmpresa);
                var rntrcProprietarioViagem = prop.RNTRC.PadLeft(9, '0');
                var eventosParaRetificar = viagem.ViagemEventos.Where(o => o.Status != EStatusViagemEvento.Cancelado).ToList();
                var eventosTarifas = eventosParaRetificar.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                var eventosFrete = eventosParaRetificar.Where(o => o.TipoEventoViagem != ETipoEventoViagem.Abastecimento && o.TipoEventoViagem != ETipoEventoViagem.TarifaAntt).ToList();

                var valorFrete = eventosFrete.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorFretePago = eventosFrete.Where(o => o.Status == EStatusViagemEvento.Baixado)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var combustivel = eventosParaRetificar.Where(o => o.TipoEventoViagem == ETipoEventoViagem.Abastecimento)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorTarifas = eventosTarifas.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var qtdeTarifas = eventosTarifas.Count();
                
                var request = new RetificarOperacaoTransporteRequest
                {
                    Ciot = viagem.DeclaracaoCiot.Ciot,
                    SenhaAlteracao = viagem.DeclaracaoCiot.Senha,
                    QuantidadeTarifas = qtdeTarifas,
                    ValorTarifas = valorTarifas,
                    ValorCombustivel = 0,
                    ValorPedagio = viagem.ValorPedagio,
                    ValorFrete = valorFrete,
                    ValorFretePago = valorFretePago
                };
                
                request.PesoCarga = viagem.PesoChegada.HasValue && viagem.PesoChegada > 0
                    ? viagem.PesoChegada
                    : viagem.PesoSaida ?? 0;
                request.CodigoMunicipioOrigem = viagemRepository.GetIbgeOrigemCiot(viagem.IdViagem);
                request.CodigoMunicipioDestino = viagemRepository.GetIbgeDestinoCiot(viagem.IdViagem);
                request.CodigoNaturezaCarga = viagem.NaturezaCarga?.ToString();
                request.DocumentoCliente = viagem.DocumentoCliente;
                request.DataFimViagem = viagem.DataPrevisaoEntrega < DateTime.Now ? DateTime.Now.AddDays(1) : viagem.DataPrevisaoEntrega;                    
                request.DataInicioViagem = viagem.DataColeta;
                request.Veiculos = new ObservableCollection<VeiculoRequest>
                {
                    new VeiculoRequest
                    {
                        Placa = viagem.Placa,
                        Rntrc = !string.IsNullOrWhiteSpace(rntrcPlacaCavaloCadastrada)
                            ? rntrcPlacaCavaloCadastrada
                            : viagem.RNTRC > 0
                                ? viagem.RNTRC.Value.ToString().PadLeft(9, '0')
                                : rntrcProprietarioViagem
                    }
                };

                foreach (var evento in eventosParaRetificar.Where(x => x.TipoEventoViagem != ETipoEventoViagem.Abastecimento && x.TipoEventoViagem != ETipoEventoViagem.TarifaAntt))
                    request.ValorFrete += evento.ValorTotalPagamento ?? evento.ValorPagamento;
                
                foreach (var evento in eventosParaRetificar.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Abastecimento))
                    request.ValorCombustivel += evento.ValorTotalPagamento ?? evento.ValorPagamento;
               
                if (viagem.ViagemCarretas != null)
                    foreach (var carreta in viagem.ViagemCarretas)
                    {
                        var veiculoRntrc = ciotService.ObterVeiculoPorPlaca(carreta, viagem.IdEmpresa);

                        request.Veiculos.Add(!string.IsNullOrWhiteSpace(carreta.Rntrc) ? 
                            new VeiculoRequest {Placa = carreta.Placa, Rntrc = carreta.Rntrc.PadLeft(9, '0')}
                            : !string.IsNullOrWhiteSpace(veiculoRntrc?.RNTRC) 
                                ? new VeiculoRequest {Placa = carreta.Placa, Rntrc = veiculoRntrc.RNTRC.PadLeft(9, '0')} 
                                : new VeiculoRequest {Placa = carreta.Placa, Rntrc = rntrcProprietarioViagem});
                    }

                // Retificar CIOT
                var result = ciotService.CiotRepository.RetificarOperacaoTransporte(request);
                if (!result.Sucesso.GetValueOrDefault(false))
                    return new RetificarOperacaoTransporteReponse
                    {
                        Sucesso = false,
                        Excecao = new ExcecaoResponse
                        {
                            Mensagem = result.ExceptionMessage ?? 
                                       $"Erro ao retificar CIOT da viagem: {IdViagem} - {result.Excecao?.Mensagem}"
                        }
                    };
                                
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao declarar CIOT da viagem Id: {IdViagem} / Empresa: {IdEmpresa}");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                return new RetificarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = msg,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    }
                };
            }
        }

        #endregion

        #endregion

        #region Versão 3 ANTT

        /*
         * A VERSÃO 3 DA ANTT É COMO CHAMAMOS O CIOT PARA TODOS.
         * ESTA VERSÃO É UMA ALTERAÇÃO DA ANTT QUE ENVOLVE ALGUMAS MUDANÇAS NAS REGRAS DO CIOT.
         * ELA ENCONTRA-SE ATUALMENTE (2021) APENAS EM HOMOLOGAÇÃO E NÃO HÁ PREVISÃO PARA LANÇAMENTO EM PRD.
         * ENTRE AS MUDANÇAS ALGUMAS SÃO:
         *  - OBRIGATORIEDADE DE ENVIO DE CIOT PARA TODOS OS PROPRIETÁRIOS.
         *  - NÃO HÁ MAIS RETIFICAÇÃO DO CIOT PADRÃO. SEMPRE QUE HOUVER ALTERAÇÃO, UM NOVO CIOT É ENVIADO.
         *  - É OBRIGATÓRIO ENVIAR VALORES PAGOS E DISTANCIA DA VIAGEM, QUE SERÁ VALIDADA DE ACORDO COM A TABELA DE FRETE DA ANTT.
         */
        
        public DeclararCiotResult DeclararCiotV3(IContratoCiotAgregadoService contratoCiotAgregadoService, IVeiculoService veiculoService, DeclararCiotModel model,
            ViagemActionDependencies dependencies, ContratoCiotAgregadoActionDependencies contratoCiotAgregadoActionDependencies)
        {
        
            if (!IdDeclaracaoCiot.HasValue && !model.HabilitarDeclaracaoCiot)
                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.NaoHabilitado,
                    Declarado = false,
                    Mensagem =
                        "CIOT não processado. Tag 'HabilitarDeclaracaoCiot' desabilitada pelo consumidor."
                };
            
            var ciotPadraoRetificar = false;
            var viagem = this;
            try
            {
                #region CIOT já declarado

                if (IdDeclaracaoCiot != null)
                {
                    var declaracaoCiot = model.CiotV3Service
                        .GetQueryDeclaracaoCiotByViagem(viagem.IdViagem, viagem.IdEmpresa)?.FirstOrDefault();
                    var ciotIntegrado = declaracaoCiot?.GetCiotResultV3();

                    if (ciotIntegrado != null)
                    {
                        if (declaracaoCiot.Status == EStatusDeclaracaoCiot.Cancelado)
                            return ciotIntegrado;
                        
                        if (!model.Retificar)
                            return ciotIntegrado;

                        switch (declaracaoCiot.TipoDeclaracao)
                        {
                            case ETipoDeclaracao.Agregado:
                            {
                                var resultado = RetificarCiotTacAgregadoV3(viagem, model.CiotV3Service, declaracaoCiot, dependencies);

                                if (!resultado.Sucesso.GetValueOrDefault())
                                    return new DeclararCiotResult
                                    {
                                        Resultado = EResultadoDeclaracaoCiot.Erro,
                                        Declarado = false,
                                        Mensagem = resultado.ExceptionMessage
                                    };

                                return ciotIntegrado;
                            }

                            case ETipoDeclaracao.Padrao:
                            {
                                var deveEmitirNovoCiot =
                                    model.ParametrosService.GetReemiteCiotPadraoAlteracaoViagem(viagem.IdEmpresa);

                                if (!deveEmitirNovoCiot.HasValue || !deveEmitirNovoCiot.Value)
                                    return ciotIntegrado;

                                if (ciotIntegrado.Dados.EmContigencia)
                                    model.CiotV3Service.CancelarCiot(viagem);
                                else
                                {
                                    model.CiotV3Service.EncerrarCiotPadrao(viagem, declaracaoCiot);
                                }

                                ciotPadraoRetificar = true;
                                break;
                            }

                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                }
                
                if (!model.HabilitarDeclaracaoCiot)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.NaoHabilitado,
                        Declarado = false,
                        Mensagem =
                            "CIOT não processado. Tag 'HabilitarDeclaracaoCiot' desabilitada pelo consumidor."
                    };
                
                #endregion
                
                #region Verifica o Proprietário

                if (viagem.CPFCNPJProprietario.IsNullOrWhiteSpace())
                    throw new CiotAtsException(
                        $"CPF/CNPJ de proprietário não informado na viagem {viagem.IdViagem} para declarar CIOT.");

                if (viagem.RNTRC == null || viagem.RNTRC == 0)
                    throw new CiotAtsException(
                        $"RNTRC não informado na viagem {viagem.IdViagem} para declarar CIOT.");

                var cnpjEmpresa = model.CnpjEmpresa;
                var retornoTac = model.ProprietarioService.EquiparadoTac(viagem.CPFCNPJProprietario,
                    viagem.RNTRC.Value.ToString().PadLeft(9, '0'), cnpjEmpresa);

                // Atualiza o proprietario se ele é ou não equiparado a TAC                
                var proprietario =
                    model.ProprietarioService.Repository.GetPorCpfCnpj(viagem.CPFCNPJProprietario, viagem.IdEmpresa);

                if (proprietario == null)
                    throw new Exception(
                        $"O proprietário {viagem.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados");

                if (retornoTac.Retorno != ERetornoConsultaTAC.Erro)
                    if (proprietario.EquiparadoTac != (retornoTac.Retorno == ERetornoConsultaTAC.Equiparado))
                    {
                        proprietario.EquiparadoTac = retornoTac.Retorno == ERetornoConsultaTAC.Equiparado;
                        model.ProprietarioService.Update(proprietario);
                    }

                var possuiRntrc = dependencies.EmpresaRepository.Query(viagem.IdEmpresa).Any(x => x.CNTRC.HasValue);

                if (model.GerarCiotTacAgregado.HasValue)
                    model.ProprietarioService.AtualizaEquiparadoTac(proprietario.IdProprietario,
                        model.GerarCiotTacAgregado.Value);

                #endregion

                #region Declaração de CIOT do tipo TAC Agregado

                if (possuiRntrc && model.CiotV3Service.IsDeclaracaoTacAgregado(viagem.Placa, viagem.IdEmpresa, proprietario.HabilitarContratoCiotAgregado) || model.GerarCiotTacAgregado == true)
                {
                    #region CIOT Contrato de agregado
                    
                    var eventos = viagem.ViagemEventos.Where(ve => ve.Status != EStatusViagemEvento.Cancelado).ToList();
                    var tarifas = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                    var abastecimentos = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
                    var idVeiculoViagem = GetVeiculo(veiculoService, viagem.Placa, viagem.IdEmpresa);

                    if (!idVeiculoViagem.HasValue)
                        throw new Exception(
                            $"Veículo {viagem.Placa.ToPlacaFormato()} não encontrado na base de dados.");

                    var contratoCiotAgregado =
                        contratoCiotAgregadoService.GetContratoVigente(viagem.IdEmpresa, proprietario.RNTRC);

                    // Se tem contrato aberto com outra placa sem ciot e sem viagens, o contrato é cancelado para declarar o ciot com a placa da viagem atual
                    if (contratoCiotAgregado != null && !contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                    {
                        if (contratoCiotAgregado.ContratoCiotAgregadoVeiculos.All(x =>
                            x.IdVeiculo != idVeiculoViagem))
                        {
                            contratoCiotAgregadoService.CancelarContrato(contratoCiotAgregado,
                                "Contrato não possui viagem para o veículo e será substituído por um novo contrato");
                            contratoCiotAgregado = null;
                        }
                    }

                    if (contratoCiotAgregado == null)
                    {
                        // Criar novo contrato e declara CIOT na ANTT
                        var contratoAberturaModel = new ContratoAberturaModel
                        {
                            Veiculos = new List<VeiculoModelAgregado>
                            {
                                new VeiculoModelAgregado
                                {
                                    IdVeiculo = idVeiculoViagem.Value,
                                    Placa = viagem.Placa
                                }
                            },
                            IdEmpresa = viagem.IdEmpresa,
                            IdProprietario = proprietario.IdProprietario,
                            DataInicio = DateTime.Today,
                            DataFinal = DateTime.Today.AddDays(30).EndOfDayWithDelay(),
                            QuantidadeTarifas = tarifas.Count,
                            ValorTarifas = tarifas.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                            ValorCombustivel =
                                abastecimentos.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                            ValorPedagio = viagem.ValorPedagio,
                            ValorINSS = viagem.INSS,
                            ValorIRRF = viagem.IRRPF,
                            ValorSESTSENAT = viagem.SESTSENAT
                        };
                        
                        if (viagem.ViagemCarretas?.Any() == true)
                            foreach (var carreta in viagem.ViagemCarretas)
                                contratoAberturaModel.Veiculos.Add(new VeiculoModelAgregado
                                {
                                    IdVeiculo = veiculoService.GetIdPorPlaca(carreta.Placa, viagem.IdEmpresa).GetValueOrDefault(),
                                    Placa = carreta.Placa
                                });

                        var abrirContratoResult =
                            contratoCiotAgregadoService.AbrirContratoCiotAgregado(contratoAberturaModel);

                        if (!viagem.IdDeclaracaoCiot.HasValue &&
                            abrirContratoResult.CiotResult?.DeclaracaoCiot?.IdDeclaracaoCiot > 0)
                            viagem.IdDeclaracaoCiot =
                                abrirContratoResult.CiotResult.DeclaracaoCiot.IdDeclaracaoCiot;

                        return !abrirContratoResult.Sucesso || abrirContratoResult.CiotResult == null
                            ? new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Erro,
                                Mensagem = abrirContratoResult.Mensagem
                            }
                            : new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Sucesso,
                                Declarado = true,
                                Dados = Mapper.Map<DeclararCiotResult.Declaracao>(abrirContratoResult.CiotResult
                                    .DeclaracaoCiot),
                                Mensagem = abrirContratoResult.Mensagem
                            };
                    }

                    // Se existir registro na tabela de contrato, que não possua CIOT gerado, tenta declara o CIOT para seguir o processo (Pode acontecer isso caso ANTT esteja fora durante o processo de declaração
                    if (contratoCiotAgregado.Status == EStatusContratoAgregado.Vigente &&
                        contratoCiotAgregado.IdDeclaracaoCiot == null)
                    {
                        var resultDeclaracaoCiot = contratoCiotAgregado.VerificarEDeclararCiotAntt(
                            contratoCiotAgregadoService.Repository, null, model.CiotV3Service, EVersaoAntt.Versao3,
                            model.DeclaracaoCiotRepository, contratoCiotAgregadoActionDependencies);

                        if (!viagem.IdDeclaracaoCiot.HasValue &&
                            resultDeclaracaoCiot.DeclaracaoCiot?.IdDeclaracaoCiot > 0)
                            viagem.IdDeclaracaoCiot = resultDeclaracaoCiot.DeclaracaoCiot.IdDeclaracaoCiot;

                        return resultDeclaracaoCiot.Sucesso != true || resultDeclaracaoCiot.DeclaracaoCiot == null
                            ? new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Erro,
                                Mensagem = resultDeclaracaoCiot.Excecao?.Mensagem
                            }
                            : new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Sucesso,
                                Declarado = true,
                                Dados = Mapper.Map<DeclararCiotResult.Declaracao>(resultDeclaracaoCiot
                                    .DeclaracaoCiot),
                                Mensagem = resultDeclaracaoCiot.AvisoTransportador
                            };
                    }

                    //Verifica se existe algum veículo diferente na viagem e insere no contrato, retificando CIOT
                    Collection<VeiculoModelAgregado> novosVeiculosList;
                    var retificaPorPlaca = ValidaRetificacaoPorPlacaV3(contratoCiotAgregado, viagem,
                        idVeiculoViagem.Value, cnpjEmpresa,
                        veiculoService, model.CiotV3Service, out novosVeiculosList);

                    //Verifica se a placa existe no contrato
                    if (retificaPorPlaca || tarifas.Any() || abastecimentos.Any())
                    {
                        var idViagemEventosRequest = eventos.Select(x => x.IdViagemEvento).ToList();
                        var freteRequest = eventos.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();

                        // Pega os eventos do banco e retira da lista o que está vindo na request que já estava no banco antes
                        var eventosJaExistentes = dependencies.ViagemEventoDapper.ConsultarEventosCiotAgregado(contratoCiotAgregado.IdContratoCiotAgregado);
                        eventosJaExistentes = eventosJaExistentes.Where(x => !idViagemEventosRequest.Contains(x.IdViagemEvento)).ToList();

                        var tarifasJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                        var abastecimentosJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
                        var freteJaExistentes = eventosJaExistentes.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();

                        // Atualizar impressão
                        contratoCiotAgregado.ValorPedagio = dependencies.ViagemDapper.ConsultarValorPedagiosCiotAgregado(contratoCiotAgregado.IdContratoCiotAgregado);
                        contratoCiotAgregado.ValorFrete = freteRequest.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                          + freteJaExistentes.Sum(ve => ve.Valor);
                        contratoCiotAgregado.ValorFretePago = freteRequest.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                              + freteJaExistentes.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.Valor);
                        
                        // Campos retificação ANTT
                        contratoCiotAgregado.ValorCombustivel = abastecimentos.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                                + abastecimentosJaExistentes.Sum(ve => ve.Valor);
                        contratoCiotAgregado.QuantidadeTarifas = tarifas.Count + tarifasJaExistentes.Count;
                        contratoCiotAgregado.ValorTarifas = tarifas.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                                            + tarifasJaExistentes.Sum(x => x.Valor);

                        //Retifica para incluir o novo veículo
                        var retificarResult = model.CiotV3Service.RetificarCiot(contratoCiotAgregadoService, contratoCiotAgregado,
                            dependencies.ContratoCiotAgregadoRepository, novosVeiculosList);

                        if (retificarResult.Resultado == EResultadoDeclaracaoCiot.Erro)
                            return new DeclararCiotResult
                            {
                                Resultado = EResultadoDeclaracaoCiot.Erro,
                                Declarado = true,
                                Mensagem = retificarResult.Mensagem,
                                Dados = retificarResult.Dados
                            };
                    }

                    var ciotAgregadoResult = contratoCiotAgregado.DeclaracaoCiot.GetCiotResultV3();
                    if (contratoCiotAgregado.DeclaracaoCiot.IdViagem == null)
                        contratoCiotAgregado.DeclaracaoCiot.SetIdViagem(viagem.IdViagem,
                            model.DeclaracaoCiotRepository);

                    if (viagem.IdDeclaracaoCiot == null && contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                        SetDeclaracaoCiotV3(model.ViagemRepository, contratoCiotAgregado.IdDeclaracaoCiot.Value);

                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Sucesso,
                        Declarado = true,
                        Mensagem = "Contrato de agregado não necessita declarar CIOT novamente",
                        Dados = ciotAgregadoResult.Dados
                    };

                    #endregion
                }

                #endregion

                #region Declaração de CIOT do tipo Padrão

                #region CIOT Padrão

                var ciotResponse = DeclararCiotPadraoAnttV3(model.CiotV3Service, model.ViagemRepository, dependencies);
                if (!ciotResponse.Sucesso.GetValueOrDefault(false))
                {
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = ciotResponse.Excecao?.Mensagem
                    };
                }

                if (ciotPadraoRetificar)
                    viagem.IdDeclaracaoCiot = null;

                if (viagem.IdDeclaracaoCiot == null && ciotResponse.DeclaracaoCiot != null)
                    SetDeclaracaoCiotV3(model.ViagemRepository, ciotResponse.DeclaracaoCiot.IdDeclaracaoCiot);

                if (ciotResponse.DeclaracaoCiot != null)
                    return ciotResponse.DeclaracaoCiot.GetCiotResultV3();

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = ciotResponse.AvisoTransportador
                };

                #endregion

                #endregion
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro declarar CIOT para viagem Id: {viagem.IdViagem} / Empresa: {viagem.IdEmpresa}.");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.GetBaseException().Message;

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = msg
                };
            }
        }

        #region Métodos privados
        
        private void SetDeclaracaoCiotV3(IViagemRepository repository, int idDeclaracaoCiot)
        {
            if (idDeclaracaoCiot == 0)
                throw new Exception("IdDeclaracaoCiot não informado para atribuir a viagem id " + IdViagem);

            IdDeclaracaoCiot = idDeclaracaoCiot;
            repository.Update(this);
        }

        private DeclararOperacaoTransporteModel DeclararCiotPadraoAnttV3(ICiotV3Service ciotService, IViagemRepository viagemRepository, ViagemActionDependencies dependencies)
        {
            try
            {
                var viagem = this;
                var dadosCiotV3Dto = viagemRepository.GetDadosCiotV3(viagem.IdViagem);

                // Carrega proprietário a partir do CNPJ informado na integração da viagem. Padronizado todas leitura de proprietário neste campo.
                var proprietarioRepository = dependencies.ProprietarioRepository;
                var veiculoRepository = dependencies.VeiculoRepository;

                var propQy = proprietarioRepository
                    .AsNoTracking()
                    .Include(p => p.Enderecos)
                    .Include(p => p.Enderecos)
                    .Include(p => p.Enderecos.Select(e => e.Cidade))
                    .Include(p => p.Contatos);

                var prop = propQy
                    .FirstOrDefault(p => p.CNPJCPF == viagem.CPFCNPJProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (prop == null)
                    prop = propQy
                        .FirstOrDefault(p =>
                            p.IdProprietario == viagem.IdProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (prop == null)
                    throw new CiotAtsException(
                        $"Não foi possível localizar o proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");

                var rntrcPlacaCavaloCadastrada = veiculoRepository.GetRntrcProprietarioVeiculo(viagem.Placa, viagem.IdEmpresa);
                var rntrcProprietarioViagem = prop.RNTRC.PadLeft(9, '0');
                var propEnd = prop.Enderecos.FirstOrDefault();

                if (propEnd == null)
                    throw new CiotAtsException(
                        $"Não foi possível localizar o endereço do proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");

                var propContato = prop.Contatos.FirstOrDefault();

                var request = new DeclararOperacaoTransporteRequest();

                #region Construir ANTT request

                // Frete
                request.Frete = new FreteRequest
                {
                    DataTerminoFrete = viagem.DataPrevisaoEntrega < DateTime.Now
                        ? DateTime.Now.AddDays(1)
                        : viagem.DataPrevisaoEntrega,
                    DadosComplementares =
                        "Documento cliente: " + viagem.DocumentoCliente + " / Número: " + viagem.NumeroDocumento,
                    DocumentoCliente = viagem.DocumentoCliente,
                    SubContratacao = false,
                    TipoViagem = ETipoDeclaracao.Padrao.ToIntNullable(),

                    CodigoTipoCarga = viagem.CodigoTipoCarga,
                    DistanciaPercorrida = viagem.DistanciaViagem,
                    CepOrigem = viagem.CepOrigem,
                    CepDestino = viagem.CepDestino,
                    AltoDesempenho = viagem.AltoDesempenho,
                    DestinacaoComercial = viagem.DestinacaoComercial
                };

                if (viagem.FreteRetorno)
                    request.Frete.FreteRetorno = new FreteRetornoRequest
                    {
                        CepRetorno = viagem.CepRetorno,
                        DistanciaRetorno = viagem.DistanciaRetorno
                    };

                request.Frete.DataInicioFrete =
                    viagem.DataColeta ??
                    DateTime.Now.AddHours(1); // viagem.DataEmissao, // Viagens podem ser emitidas num dia pelo ERP, e programadas a viagem no outro
                request.Frete.PesoCarga = viagem.PesoChegada.HasValue && viagem.PesoChegada > 0
                    ? viagem.PesoChegada
                    : viagem.PesoSaida ?? 0;
                request.Frete.CodigoNaturezaCarga = viagem.NaturezaCarga?.ToString().PadLeft(4, '0');
                request.Frete.CodigoMunicipioOrigem = dadosCiotV3Dto.Remetente.IBGE;
                request.Frete.CodigoMunicipioDestino = dadosCiotV3Dto.Destinatario.IBGE;

                request.Frete.Proprietario = new ProprietarioRequest
                {
                    CpfCnpj = viagem.CPFCNPJProprietario.DefaultIfEmpty(prop.CNPJCPF),
                    NomeRazaoSocial = prop.RazaoSocial,
                    NomeFantasia = prop.NomeFantasia,
                    Rntrc = viagem.RNTRC.HasValue && viagem.RNTRC.Value > 0
                        ? viagem.RNTRC.Value.ToString().PadLeft(9, '0')
                        : rntrcProprietarioViagem,
                    TipoPessoa = prop.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = propEnd.Cidade.IBGE,
                        Logradouro = propEnd.Endereco,
                        Cep = propEnd.CEP,
                        Numero = propEnd.Numero?.ToString(),
                        Complemento = propEnd.Complemento,
                        Bairro = propEnd.Bairro,
                        Telefone = propContato?.Telefone,
                        Email = propContato?.Email
                    }
                };
                request.Frete.Motorista = new MotoristaRequest
                {
                    CpfCnpj = viagem.CPFMotorista,
                    Nome = viagem.NomeMotorista,
                    
                    // Campo comentado pois não é obrigatório na ANTT
                    //NumeroCNH = viagem.CNHMotorista
                };

                // Empresa contratante
                request.Contratante = new ContratanteRequest
                {
                    CpfCnpj = dadosCiotV3Dto.Contratante.CNPJ,
                    NomeRazaoSocial = dadosCiotV3Dto.Contratante.RazaoSocial,
                    NomeFantasia = dadosCiotV3Dto.Contratante.NomeFantasia,
                    TipoPessoa = dadosCiotV3Dto.Contratante.CNPJ.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV3Dto.Contratante.IBGE,
                        Cep = dadosCiotV3Dto.Contratante.CEP,
                        Bairro = dadosCiotV3Dto.Contratante.Bairro,
                        Complemento = dadosCiotV3Dto.Contratante.Complemento,
                        Email = dadosCiotV3Dto.Contratante.Email,
                        Logradouro = dadosCiotV3Dto.Contratante.Endereco,
                        Numero = dadosCiotV3Dto.Contratante.Numero?.ToString(),
                        Telefone = dadosCiotV3Dto.Contratante.Telefone
                    }
                };

                // Remetente
                request.Remetente = new RemetenteRequest
                {
                    CpfCnpj = dadosCiotV3Dto.Remetente.CNPJCPF,
                    NomeRazaoSocial = dadosCiotV3Dto.Remetente.RazaoSocial,
                    NomeFantasia = dadosCiotV3Dto.Remetente.NomeFantasia,
                    TipoPessoa = dadosCiotV3Dto.Remetente.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV3Dto.Remetente.IBGE,
                        Logradouro = dadosCiotV3Dto.Remetente.Endereco,
                        Cep = dadosCiotV3Dto.Remetente.CEP,
                        Numero = dadosCiotV3Dto.Remetente.Numero?.ToString(),
                        Complemento = dadosCiotV3Dto.Remetente.Complemento,
                        Bairro = dadosCiotV3Dto.Remetente.Bairro,
                        Telefone = dadosCiotV3Dto.Remetente.Celular,
                        Email = dadosCiotV3Dto.Remetente.Email
                    }
                };

                // Destinatário
                request.Destinatario = new DestinatarioRequest
                {
                    CpfCnpj = dadosCiotV3Dto.Destinatario.CNPJCPF,
                    NomeRazaoSocial = dadosCiotV3Dto.Destinatario.RazaoSocial,
                    NomeFantasia = dadosCiotV3Dto.Destinatario.NomeFantasia,
                    TipoPessoa = dadosCiotV3Dto.Destinatario.CNPJCPF.Length > 11 ? "J" : "F",
                    Endereco = new PessoaEnderecoRequest
                    {
                        CodigoMunicipio = dadosCiotV3Dto.Destinatario.IBGE,
                        Logradouro = dadosCiotV3Dto.Destinatario.Endereco,
                        Cep = dadosCiotV3Dto.Destinatario.CEP,
                        Numero = dadosCiotV3Dto.Destinatario.Numero?.ToString(),
                        Complemento = dadosCiotV3Dto.Destinatario.Complemento,
                        Bairro = dadosCiotV3Dto.Destinatario.Bairro,
                        Telefone = dadosCiotV3Dto.Destinatario.Celular,
                        Email = dadosCiotV3Dto.Destinatario.Email
                    }
                };

                // Veículos
                request.Veiculos = new ObservableCollection<VeiculoRequest>
                {
                    new VeiculoRequest
                    {
                        Placa = viagem.Placa, 
                        Rntrc = !string.IsNullOrWhiteSpace(rntrcPlacaCavaloCadastrada) 
                            ? rntrcPlacaCavaloCadastrada 
                            : viagem.RNTRC > 0
                                ? viagem.RNTRC.Value.ToString().PadLeft(9, '0') 
                                : rntrcProprietarioViagem
                    }
                };

                if (viagem.ViagemCarretas != null)
                    foreach (var carreta in viagem.ViagemCarretas)
                    {
                        var veiculoRntrc = ciotService.ObterVeiculoPorPlaca(carreta, viagem.IdEmpresa);

                        request.Veiculos.Add(!string.IsNullOrWhiteSpace(carreta.Rntrc) ? 
                            new VeiculoRequest {Placa = carreta.Placa, Rntrc = carreta.Rntrc}
                            : !string.IsNullOrWhiteSpace(veiculoRntrc?.RNTRC) 
                            ? new VeiculoRequest {Placa = carreta.Placa, Rntrc = veiculoRntrc.RNTRC.PadLeft(9, '0')} 
                            : new VeiculoRequest {Placa = carreta.Placa, Rntrc = rntrcProprietarioViagem});
                    }

                var eventos = viagem.ViagemEventos.Where(o => o.Status != EStatusViagemEvento.Cancelado).ToList();
                var eventosFrete = eventos.Where(o => o.TipoEventoViagem != ETipoEventoViagem.Abastecimento && 
                                                      o.TipoEventoViagem != ETipoEventoViagem.TarifaAntt).ToList();
                var eventosTarifas = eventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                
                var valorFrete = eventosFrete.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorFretePago = eventosFrete.Where(o => o.Status == EStatusViagemEvento.Baixado)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                
                var valorAbastecimentos = eventos.Where(o => o.TipoEventoViagem == ETipoEventoViagem.Abastecimento)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
                var valorTarifas = eventosTarifas.Sum(e => e.ValorTotalPagamento ?? e.ValorPagamento);
                var qtdeTarifas = eventosTarifas.Count();
                var valorPedagio = !viagem.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CancelamentoSolicitado, EResultadoCompraPedagio.CancelamentoConfirmado)
                    ? viagem.ValorPedagio
                    : 0;

                request.Valores = new ValoresFreteRequest
                {
                    ValorFrete = valorFrete,
                    ValorFretePago = valorFretePago,
                    TotalImposto = viagem.SESTSENAT + viagem.INSS + viagem.IRRPF,
                    ValorSESTSENAT = viagem.SESTSENAT,
                    ValorINSS = viagem.INSS,
                    ValorIRRF = viagem.IRRPF,
                    TotalPegadio = valorPedagio,
                    ValorCombustivel = valorAbastecimentos,
                    ValorDespesas = valorFrete + valorAbastecimentos + valorTarifas + valorPedagio,
                    QuantidadeTarifas = qtdeTarifas,
                    ValorTarifas = valorTarifas
                };

                request.InformacoesPagamento = new InformacoesPagamentoRequest
                {
                    FormaPagamento = ObterDeParaInformacoesPagamentoV3(viagem.FormaPagamento),
                    DadosBancarios = new DadosBancariosRequest
                    {
                        CpfCnpjConta = viagem.ViagemPagamentoConta?.CpfCnpjConta,
                        CodigoBACEN = viagem.ViagemPagamentoConta?.CodigoBacenBanco,
                        Agencia = viagem.ViagemPagamentoConta?.Agencia,
                        Conta = viagem.ViagemPagamentoConta?.Conta
                    }
                };

                #endregion

                // Declarar CIOT
                var result = ciotService.DeclararOperacaoTransporte(request, this, null);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao declarar CIOT da viagem Id: {IdViagem} / Empresa: {IdEmpresa}");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;
                
                return new DeclararOperacaoTransporteModel
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = msg,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    }
                };
            }
        }

        private RetificarOperacaoTransporteReponse RetificarCiotTacAgregadoV3(Viagem viagem, ICiotV3Service ciotService, DeclaracaoCiot declaracaoCiot,
            ViagemActionDependencies dependencies)
        {
            var proprietarioRepository = dependencies.ProprietarioRepository;
            var veiculoRepository = dependencies.VeiculoRepository;
            
            // Carrega proprietário a partir do CNPJ informado na integração da viagem. Padronizado todas leitura de proprietário neste campo.
            var proprietarioQuery = proprietarioRepository.AsNoTracking();

            var proprietario = proprietarioQuery
                                   .FirstOrDefault(p =>
                                       p.CNPJCPF == viagem.CPFCNPJProprietario && p.IdEmpresa == viagem.IdEmpresa) ??
                               proprietarioQuery
                                   .FirstOrDefault(p =>
                                       p.IdProprietario == viagem.IdProprietario && p.IdEmpresa == viagem.IdEmpresa);

            if (proprietario == null)
                throw new CiotAtsException(
                    $"Não foi possível localizar o proprietário da viagem {viagem.IdViagem} para declarar o CIOT!");

            var rntrcPlacaCavaloCadastrada = veiculoRepository.GetRntrcProprietarioVeiculo(viagem.Placa, viagem.IdEmpresa);
            
            var eventosViagemRequest = viagem.ViagemEventos.Where(ve => ve.Status != EStatusViagemEvento.Cancelado).ToList();
            var idViagemEventosRequest = eventosViagemRequest.Select(x => x.IdViagemEvento).ToList();
            
            var tarifasRequest = eventosViagemRequest.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
            var abastecimentosRequest = eventosViagemRequest.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
            var freteRequest = eventosViagemRequest.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();

            List<EventoCiotAgregadoDto> tarifasJaExistentes = null;
            List<EventoCiotAgregadoDto> abastecimentosJaExistentes = null;
            List<EventoCiotAgregadoDto> freteJaExistentes = null;
            decimal valorPedagios = 0;
            if (declaracaoCiot.IdContratoCiotAgregado.HasValue)
            {
                // Pega os eventos do banco e retira da lista o que está vindo na request que já estava no banco antes
                var eventosJaExistentes = dependencies.ViagemEventoDapper.ConsultarEventosCiotAgregado(declaracaoCiot.IdContratoCiotAgregado.Value);
                eventosJaExistentes = eventosJaExistentes.Where(x => !idViagemEventosRequest.Contains(x.IdViagemEvento)).ToList();

                tarifasJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
                abastecimentosJaExistentes = eventosJaExistentes.Where(x => x.TipoEventoViagem == ETipoEventoViagem.Abastecimento).ToList();
                freteJaExistentes = eventosJaExistentes.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento).ToList();
                valorPedagios = dependencies.ViagemDapper.ConsultarValorPedagiosCiotAgregado(declaracaoCiot.IdContratoCiotAgregado.Value);
            }

            var request = new RetificarOperacaoTransporteRequest
            {
                Ciot = declaracaoCiot.Ciot,
                SenhaAlteracao = declaracaoCiot.Senha,
                Veiculos = new ObservableCollection<VeiculoRequest>(),
                QuantidadeTarifas = tarifasRequest.Count + tarifasJaExistentes.Count,
                ValorTarifas = tarifasRequest.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento)  + tarifasJaExistentes.Sum(x => x.Valor),
                ValorCombustivel = abastecimentosRequest.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                                   + abastecimentosJaExistentes?.Sum(ve => ve.Valor),
                ValorPedagio = valorPedagios,
                ValorFrete = freteRequest.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento) 
                             + freteJaExistentes?.Sum(ve => ve.Valor),
                ValorFretePago = freteRequest.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento)
                                 + freteJaExistentes?.Where(ve => ve.Status == EStatusViagemEvento.Baixado).Sum(ve => ve.Valor)
            };
            
            request.Veiculos.Add(new VeiculoRequest
            {
                Placa = viagem.Placa,
                Rntrc = !string.IsNullOrWhiteSpace(rntrcPlacaCavaloCadastrada) ? rntrcPlacaCavaloCadastrada : proprietario.RNTRC.PadLeft(9, '0')
            }); 

            foreach (var carreta in viagem.ViagemCarretas)
            {
                var rntrcProprietarioViagem = proprietario.RNTRC.PadLeft(9, '0');
                var veiculoRntrc = ciotService.ObterVeiculoPorPlaca(carreta, viagem.IdEmpresa);

                if (!string.IsNullOrEmpty(veiculoRntrc?.RNTRC))
                {
                    request.Veiculos.Add(new VeiculoRequest {Placa = carreta.Placa, Rntrc = veiculoRntrc.RNTRC});
                    continue;
                }

                request.Veiculos.Add(new VeiculoRequest {Placa = carreta.Placa, Rntrc = rntrcProprietarioViagem});
            }

            var resultado = ciotService.CiotRepository.RetificarOperacaoTransporte(request);
            if (!resultado.Sucesso.GetValueOrDefault(false))
                return new RetificarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Mensagem = resultado.ExceptionMessage ?? 
                            $"Erro ao retificar CIOT da viagem : {IdViagem} - {resultado.Excecao?.Codigo} - {resultado.Excecao?.Mensagem}"
                    }
                };

            return resultado;
        }

        private InformacoesPagamentoRequestFormaPagamento ObterDeParaInformacoesPagamentoV3(EViagemFormaPagamento formaPagamento)
        {
            switch (formaPagamento)
            {
                case EViagemFormaPagamento.Cartao:
                    return InformacoesPagamentoRequestFormaPagamento.Cartao;
                case EViagemFormaPagamento.Outros:
                    return InformacoesPagamentoRequestFormaPagamento.Outros;
                case EViagemFormaPagamento.ContaCorrente:
                    return InformacoesPagamentoRequestFormaPagamento.ContaCorrente;
                case EViagemFormaPagamento.ContaPoupanca:
                    return InformacoesPagamentoRequestFormaPagamento.ContaPoupanca;
                case EViagemFormaPagamento.ContaPagamento:
                    return InformacoesPagamentoRequestFormaPagamento.ContaPagamento;
                default:
                    throw new ArgumentOutOfRangeException(nameof(formaPagamento), formaPagamento, null);
            }
        }
        
        private bool ValidaRetificacaoPorPlacaV3(ContratoCiotAgregado contratoCiotAgregado, Viagem viagem, int idVeiculoViagem, string cnpjEmpresa, IVeiculoService veiculoService, 
            ICiotV3Service ciotV3Service, out Collection<VeiculoModelAgregado> novosVeiculosList)
        {
            novosVeiculosList = new Collection<VeiculoModelAgregado>();
            var retificaPorPlaca = false;
            
            var veiculosPersistidos = new Collection<Veiculo>();

            var idVeiculosPersistidosList = contratoCiotAgregado.ContratoCiotAgregadoVeiculos
                .Select(v => v.IdVeiculo).ToList();
            
            foreach (var id in idVeiculosPersistidosList)
                veiculosPersistidos.Add(veiculoService.Get(id));

            var placaPersistidasList = veiculosPersistidos.Select(v => v.Placa).ToList();
            var carretasNovas = viagem.ViagemCarretas?.Select(c => c.Placa).Except(placaPersistidasList).ToList();

            //Se a placa cavalo da viagem já existe no banco e não tem carreta nova já retorna false para retificação por placa
            if (placaPersistidasList.Contains(viagem.Placa) && carretasNovas?.Any() != true)
            {
                novosVeiculosList = null;
                return false;
            }
            
            if (!viagem.RNTRC.HasValue)
                return false;

            var viagemRntrc = viagem.RNTRC.Value.ToString().PadLeft(9, '0');
            
            if (!placaPersistidasList.Contains(viagem.Placa))
            {
                var consultaFrota = ciotV3Service.ConsultarFrotaTransportador(new ConsultarFrotaTransportadorRequest
                {
                    Placa = new ObservableCollection<string> {viagem.Placa},
                    RntrcTransportador = viagemRntrc,
                    CpfCnpjTransportador = viagem.CPFCNPJProprietario,
                    CpfCnpjInteressado = cnpjEmpresa
                });

                //Se deu erro na consulta ou retornou que a carreta é do proprietário, informa na retificação, caso contrário apenas vai salvar no banco
                var inclusoCiot = consultaFrota.Sucesso != true || 
                                  consultaFrota.FalhaComunicacaoAntt != false || 
                                  !consultaFrota.VeiculoTransportador.Any() || 
                                  consultaFrota.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true;

                if (inclusoCiot)
                    retificaPorPlaca = true;
                
                novosVeiculosList.Add(new VeiculoModelAgregado
                {
                    IdVeiculo = idVeiculoViagem,
                    Placa = viagem.Placa, 
                    InclusoCiot = inclusoCiot
                });
            }

            if (carretasNovas?.Any() == true)
                foreach(var carreta in carretasNovas)
                {
                    var consultaFrota = ciotV3Service.ConsultarFrotaTransportador(new ConsultarFrotaTransportadorRequest
                    {
                        Placa = new ObservableCollection<string> {carreta},
                        RntrcTransportador = viagem.RNTRC.Value.ToString().PadLeft(9, '0'),
                        CpfCnpjTransportador = viagem.CPFCNPJProprietario,
                        CpfCnpjInteressado = cnpjEmpresa
                    });

                    //Se deu erro na consulta ou retornou que a carreta é do proprietário, informa na retificação, caso contrário apenas vai salvar no banco
                    var inclusoCiot = consultaFrota.Sucesso != true || 
                                      consultaFrota.FalhaComunicacaoAntt != false || 
                                      !consultaFrota.VeiculoTransportador.Any() || 
                                      consultaFrota.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true;

                    if (inclusoCiot)
                        retificaPorPlaca = true;
                    
                    var idVeiculoCarreta = veiculoService.GetIdPorPlaca(carreta, IdEmpresa);
                    if(idVeiculoCarreta.HasValue)
                        novosVeiculosList.Add(new VeiculoModelAgregado
                        {
                            IdVeiculo = idVeiculoCarreta.Value, 
                            Placa = carreta, 
                            InclusoCiot = inclusoCiot
                        });
                }

            if (!retificaPorPlaca)
            {
                novosVeiculosList = null;
                return false;
            }

            //Popula a lista de veiculos que já estavam no contrato porque para retificar placa sempre são enviados todos, os já existentes e os novos
            var veiculosModelAgregadoPersistidos = Mapper.Map<Collection<VeiculoModelAgregado>>(veiculosPersistidos);
            foreach (var novoVeiculo in veiculosModelAgregadoPersistidos)
                novoVeiculo.InclusoCiot = contratoCiotAgregado.ContratoCiotAgregadoVeiculos
                    .Where(c => c.IdVeiculo == novoVeiculo.IdVeiculo)
                    .Select(c => c.InclusoCiot)
                    .First();

            novosVeiculosList.AddRange(veiculosModelAgregadoPersistidos);
            
            return true;
        }

        #endregion

        #endregion

        #endregion
    }

    // Parametros da empresa relacionados a viagem
    [SuppressMessage("ReSharper", "InconsistentNaming")]
    public enum ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado
    {
        /// <summary>
        /// Gerar registro pendente de cancelamento no serviço de pedágio e retornar sucesso, executando o estorno do ciot/cartão frete (5.1).
        /// </summary>
        Sucesso_EstornandoFreteCiot = 0,

        /// <summary>
        /// Gerar registro pendente de cancelamento no serviço de pedágio e retornar falha, executando o estorno do ciot/cartão frete (5.2).
        /// </summary>
        Falha_EstornandoFreteCiot = 1,

        /// <summary>
        /// Gerar registro pendente de cancelamento no serviço de pedágio e retornar falha, não executando estorno do ciot/cartão frete (5.3).
        /// </summary>
        Falha_NaoEstornandoFreteCiot = 2,

        /// <summary>
        /// Bloquear cancelamento e não realizar nenhum ação, NÃO gerando registro de pendência de cancelamento no serviço de pedágio e nem estornar ciot/cartão frete (5.4).
        /// Para conseguir cancelar a viagem, o motorista deverá estar na frente do operador e plugar o cartão no leitor.
        /// Será possível estornar a ultima carga realizada no moedeiro, somente através do comando de estorno manual iniciado pelo client moedeiro.
        /// </summary>
        Falha_BloqueioTotal = 3
    }
}
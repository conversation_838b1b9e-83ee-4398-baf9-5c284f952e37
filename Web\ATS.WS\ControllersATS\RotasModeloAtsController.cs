﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.CadastroRotas;
using ATS.Domain.Grid;
using ATS.Domain.Models.DestinoRotaModelo;
using ATS.WS.Models.Webservice.Request.CadastroRotas;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class RotasModeloAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IRotaModeloApp _rotaModeloApp;


        public RotasModeloAtsController(IUserIdentity userIdentity, IRotaModeloApp rotaModeloApp)
        {
            _userIdentity = userIdentity;
            _rotaModeloApp = rotaModeloApp;
        }


        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Integrar(RotasModeloIntegrarRequest @params)
        {
            try
            {
                if(@params.Destinos.Count <= 1)
                    return ResponderErro("Não é possível cadastrar uma rota sem ao menos uma origem e um destino!");

                if(@params.CodigosIbge == null || @params.CodigosIbge.Count <= 1)
                    return ResponderErro("Não foi possível encontrar o número de ibge referente a estes destinos!");

                var rotaPadrao = Mapper.Map<RotasModeloIntegrarRequest, RotaModelo>(@params);
                var validationResult = @params.IdRotaModelo.HasValue ? _rotaModeloApp.Editar(rotaPadrao) : _rotaModeloApp.Add(rotaPadrao);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());
                
                return ResponderSucesso("Rota inserida com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!idEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa;

                var rotas = _rotaModeloApp.ConsultarGrid(idEmpresa, take, page, order, filters);

                return ResponderSucesso(rotas);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idRota)
        {
            try
            {
                var result = _rotaModeloApp.GetById(idRota);
                
                if(!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GetByIdOrNomeRota(int idRota,string nomeRota,int idEmpresa)
        {
            try
            {
                var rota = _rotaModeloApp.GetByIdOrNomeRota(idRota,nomeRota,idEmpresa);
                
                return ResponderSucesso(rota);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhes(int idRota)
        {
            try
            {
                var rota = _rotaModeloApp.GetWithChilds(idRota);
                
                if(rota == null)
                    return ResponderSucesso(new RotaModeloDetalhesResponse());
                
                var listaRotaDetalhes = _rotaModeloApp.ConsultarDetalhes(rota);
                var retornoRotaDetalhes = new RotaModeloDetalhesResponse()
                {
                    Pontos = listaRotaDetalhes,
                    Descricao = rota.NomeRota
                };
                
                return ResponderSucesso(retornoRotaDetalhes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarParametros()
        {
            try
            {
                var parametros = _rotaModeloApp.ConsultarParametros(_userIdentity.IdEmpresa ?? 0);
                
                if(!parametros.Success)
                    return ResponderErro(parametros.Messages.FirstOrDefault());
                
                return ResponderSucesso(parametros.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
using System;

namespace ATS.Domain.Models
{
    public class ConsultaSituacaoTransportadorResponse
    {
        public bool Sucesso { get; set; }

        public string Mensagem { get; set; }

        public ConsultaSituacaoTransportadorObjetoResponse Objeto { get; set; }
        
        public ConsultaSituacaoTransportadorResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public ConsultaSituacaoTransportadorResponse()
        {
            
        }
    }

    public class ConsultaSituacaoTransportadorObjetoResponse
    {
        public int IdProprietario { get; set; }

        public bool RntrcAtivo { get; set; }

        public string DataValidadeRntrc { get; set; }

        public string TipoTransportador { get; set; }

        public bool EquiparadoTac { get; set; }
    }
}
﻿using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.WS.Attributes;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.ControllersATS
{
    public class MotivoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IMotivoCredenciamentoService _motivoCredenciamentoService;
        private readonly IMotivoApp _motivoApp;

        public MotivoAtsController(IUserIdentity userIdentity, IMotivoCredenciamentoService motivoCredenciamentoService, IMotivoApp motivoApp)
        {
            _userIdentity = userIdentity;
            _motivoCredenciamentoService = motivoCredenciamentoService;
            _motivoApp = motivoApp;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public System.Web.Mvc.JsonResult ConsultarGrid(int? idEmpresa, int? idFilial, string descricao, int take, 
            int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador) idEmpresa = _userIdentity.IdEmpresa;

                var consultarGrid = _motivoCredenciamentoService.ConsultarGrid(idEmpresa, idFilial, descricao, take, page, order, filters);

                return ResponderSucesso(consultarGrid);

            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public System.Web.Mvc.JsonResult GetMotivos(int? tipoMotivo = null)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderSucesso(new { });

                ETipoMotivo? tipo = tipoMotivo.HasValue ? (ETipoMotivo) tipoMotivo : (ETipoMotivo?) null; 
                    
                var motivos = _motivoApp.GetAtivos(_userIdentity.IdEmpresa.Value, tipo)
                    ?.Select(x => new
                    {
                        x.IdMotivo,
                        x.Descricao
                    }).ToList();

                return ResponderSucesso(motivos);
            }
            catch (Exception e)
            {
                Logger.Fatal(e);
                return ResponderErro(e);
            }
        }
    }
}
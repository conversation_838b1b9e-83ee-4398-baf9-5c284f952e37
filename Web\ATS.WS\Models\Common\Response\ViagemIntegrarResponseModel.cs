using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Models.Common.Response
{
    public class ViagemIntegrarResponseModel
    {
        public int IdViagem { get; set; }
        public EBloqueioGestorStatus? BloqueioGestor { get; set; }
        public Retorno<BloqueioGestorValorResponseDTO> BloqueioGestorNew { get; set; }
        public string NumeroDocumento { get; set; }
        public IList<int> IdsViagemEstabelecimento { get; set; }
        public IList<ViagemIntegrarEventoResponseModel> Eventos { get; set; }
        public DeclararCiotResult CIOT { get; set; }
        public SolicitarCompraPedagioResponseDTO Pedagio { get; set; } //protocolo
        public decimal IRRPF { get; set; }
        public decimal INSS { get; set; }
        public decimal SESTSENAT { get; set; }
        public string Avisos { get; set; }
    }

    public class ViagemIntegrarEventoResponseModel
    {
        public StatusOperacaoCartaoModel OperacaoCartao { get; set; }
        public int IdViagemEvento { get; set; }
        public string NumeroControle { get; set; }
        public string Token { get; set; }
        public IList<ViagemIntegrarEventoDocumentosResponse> IdsViagemDocumento { get; set; }
        public IList<int> IdsViagemVlAdicional { get; set; }
        public IList<int> IdsViagemOutrosDescontos { get; set; }
        public IList<ViagemIntegrarEventoOutrosAcrescimosDescontos> ViagemOutrosDescontos { get; set; }
        public IList<int> IdsViagemOutrosAcrescimos { get; set; }
        public IList<ViagemIntegrarEventoOutrosAcrescimosDescontos> ViagemOutrosAcrescimos { get; set; }
        public ETipoEventoViagem TipoEventoViagem { get; set; }
        public decimal ValorBruto { get; set; }
    }

    public class ViagemIntegrarEventoDocumentosResponse
    {
        public int IdViagemDocumento { get; set; }
        public int NumeroDocumento { get; set; }
    }

    public class ViagemIntegrarEventoOutrosAcrescimosDescontos
    {
        public int IdViagemOutrosDescontos { get; set; }
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
        public long? CodigoERP { get; set; }
    }
}
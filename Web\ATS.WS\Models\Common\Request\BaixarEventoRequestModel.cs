﻿using System;
using ATS.WS.Models.Common.Request.Base;
using System.Text;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request
{
    public class BaixarEventoRequestModel : RequestBase
    {
        public new string DocumentoUsuarioAudit 
        {
            get { return !string.IsNullOrWhiteSpace(_documentoUsuarioAudit) ? _documentoUsuarioAudit : CpfUsuario.OnlyNumbers(); }
            set {_documentoUsuarioAudit = value.OnlyNumbers();}
        }
        private string _documentoUsuarioAudit { get; set; }
        [JsonIgnore]
        //[Obsolete("Utilizar os campos de auditoria da request base")]
        public string CpfUsuario { get; set; }

        public new string NomeUsuarioAudit 
        {
            get { return !string.IsNullOrWhiteSpace(_nomeUsuarioAudit) ? _nomeUsuarioAudit : NomeUsuario.RemoveAccents().RemoveSpecialCaracter(true); }
            set {_nomeUsuarioAudit = value.RemoveAccents().RemoveSpecialCaracter(true);}
        }
        private string _nomeUsuarioAudit { get; set; }

        [JsonIgnore]
       // [Obsolete("Utilizar os campos de auditoria da request base")]
        public string NomeUsuario { get; set; }
        public int? IdViagem { get; set; }
        public int? IdViagemEvento { get; set; }
        public bool? HabilitarPagamentoCartao { get; set; }
        public bool? HabilitarPagamentoPix { get; set; } = null;
        public string NumeroControleViagem { get { return _numeroControleViagem;} set { _numeroControleViagem = value?.Trim(); } }
        public string NumeroControleEvento { get { return _numeroControleEvento;} set { _numeroControleEvento = value?.Trim(); } }
        private string _numeroControleViagem { get; set; }
        private string _numeroControleEvento { get; set; }
        public DateTime? DataAtualizacao { get; set; }

        public string Valida()
        {
            var erros = new StringBuilder();
            
            if (string.IsNullOrWhiteSpace(DocumentoUsuarioAudit) && string.IsNullOrWhiteSpace(NomeUsuarioAudit))
                erros.AppendLine("Os campos DocumentoUsuarioAudit e NomeUsuarioAudit são obrigatórios e não foram preenchidos");
            if (string.IsNullOrWhiteSpace(DocumentoUsuarioAudit))
                erros.AppendLine("O campo DocumentoUsuarioAudit é obrigatório e não foi preenchido");
            if (string.IsNullOrWhiteSpace(NomeUsuarioAudit))
                erros.AppendLine("O campo NomeUsuarioAudit é obrigatório e não foi preenchido");
            
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleViagem) && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("É obrigatório o preenchimento de ao menos dois campos: NumeroControleViagem e NumeroControleEvento ou IdViagem e IdViagemEvento");
            
            if (IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleViagem) && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("O campo IdViagemEvento é obrigatório e não foi preenchido");
            
            if (!IdViagem.HasValue && IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleViagem) && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("O campo IdViagem é obrigatório e não foi preenchido");
            
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && !string.IsNullOrWhiteSpace(NumeroControleViagem) && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("O campo NumeroControleEvento é obrigatório e não foi preenchido");
            
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleViagem) && !string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("O campo NumeroControleViagem é obrigatório e não foi preenchido");
            
            if (IdViagem.HasValue && !string.IsNullOrWhiteSpace(NumeroControleEvento) && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleViagem))
                erros.AppendLine("É obrigatório o preenchimento de ao menos dois campos: NumeroControleViagem e NumeroControleEvento ou IdViagem e IdViagemEvento");
            
            if (IdViagemEvento.HasValue && !string.IsNullOrWhiteSpace(NumeroControleViagem) && !IdViagem.HasValue && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("É obrigatório o preenchimento de ao menos dois campos: NumeroControleViagem e NumeroControleEvento ou IdViagem e IdViagemEvento");
            
            if (IdViagemEvento.HasValue  && !string.IsNullOrWhiteSpace(NumeroControleEvento) && !IdViagem.HasValue &&  string.IsNullOrWhiteSpace(NumeroControleViagem))
                erros.AppendLine("É obrigatório o preenchimento de ao menos dois campos: NumeroControleViagem e NumeroControleEvento ou IdViagem e IdViagemEvento");
            
            if (IdViagem.HasValue  && !string.IsNullOrWhiteSpace(NumeroControleViagem) && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControleEvento))
                erros.AppendLine("É obrigatório o preenchimento de ao menos dois campos: NumeroControleViagem e NumeroControleEvento ou IdViagem e IdViagemEvento");

            if (erros.Length > 0)
                return erros.ToString();
                
            if (NomeUsuarioAudit.Length > 100)
                erros.AppendLine("O campo NomeUsuario não pode conter mais de 100 caracteres");
            else if(NomeUsuarioAudit.Length < 3)
                erros.AppendLine("O campo NomeUsuario não pode conter menos de 3 caracteres");
            
            if (DocumentoUsuarioAudit.Length != 11)
                erros.AppendLine("O campo CpfUsuario deve conter 11 caracteres");
            
            if (CNPJEmpresa.OnlyNumbers().Length != 14)
                erros.AppendLine("O campo CNPJEmpresa deve conter 14 caracteres");
            
            if (CNPJAplicacao.OnlyNumbers().Length != 14)
                erros.AppendLine("O campo CNPJAplicacao deve conter 14 caracteres");
            
            if (!string.IsNullOrWhiteSpace(NumeroControleViagem) && NumeroControleViagem.Length > 300)
                erros.AppendLine("O campo NumeroControleViagem não pode conter mais de 300 caracteres");
            
            if (!string.IsNullOrWhiteSpace(NumeroControleEvento) && NumeroControleEvento.Length > 300)
                erros.AppendLine("O campo NumeroControleEvento não pode conter mais de 300 caracteres");
            
            if (erros.Length > 0)
                return erros.ToString();
            
            return string.Empty;
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class TipoMotivoRepository : Repository<TipoMotivo>, ITipoMotivoRepository
    {
        public TipoMotivoRepository(AtsContext context) : base(context)
        {
        }
        
        public void Delete(ETipoMotivo tipo, int idMotivo)
        {
            try
            {
                var query = Context.Set<TipoMotivo>()
                    .Where(x => x.Tipo == tipo && x.IdMotivo == idMotivo);

                if (!query.Any()) return;

                Context.Set<TipoMotivo>().Remove(query.First());
                Context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{e.Message} {e.InnerException}");
                throw;
            }
        }
    }
}
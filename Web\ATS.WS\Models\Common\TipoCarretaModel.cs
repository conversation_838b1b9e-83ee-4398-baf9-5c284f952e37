﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class TipoCarretaModel : RequestBase
    {
        public int IdTipoCarreta { get; set; }
        public string Nome { get; set; }
        public ECategoriaTipoCarreta Categoria { get; set; }
        public string CategoriaDescricao { get; set; }
        public int? Capacidade { get; set; }
        public bool Destacar { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? StatusIntegracao { get; set; }

        public bool Ativo { get; set; } = true;
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;

namespace ATS.Application.Application
{
    public class AutorizacaoEmpresaApp : AppBase, IAutorizacaoEmpresaApp
    {
        private readonly IAutorizacaoEmpresaService _autorizacaoEmpresaService;
        public AutorizacaoEmpresaApp(IAutorizacaoEmpresaService autorizacaoEmpresaService)
        {
            _autorizacaoEmpresaService = autorizacaoEmpresaService;
        }

        public ValidationResult Add(List<AutorizacaoEmpresa> autorizacoesEmpresa)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var autorizacoes = _autorizacaoEmpresaService.Get(autorizacoesEmpresa.FirstOrDefault()?.IdEmpresa ?? 0);
                foreach (var autorizacaoEmpresa in autorizacoes)
                {
                    _autorizacaoEmpresaService.Delete(autorizacaoEmpresa);
                }

                foreach (var autorizacaoEmpresa in autorizacoesEmpresa)
                {
                    var validationResult = _autorizacaoEmpresaService.Add(autorizacaoEmpresa);

                    if (!validationResult.IsValid)
                        return validationResult;
                }

                transaction.Complete();
            }

            return new ValidationResult();
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _autorizacaoEmpresaService.ConsultarGridAutorizacoesEmpresa(take, page, order, filters);
        }

        public List<AutorizacaoEmpresa> Get(int idEmpresa)
        {
            return _autorizacaoEmpresaService.Get(idEmpresa);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Globalization;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Validation;

namespace ATS.Domain.Models
{
    public class CargaAvulsaAddResponseModel
    {
        public int? CargaAvulsaId { get; set; }

        public int? StatusCargaAvulsa { get; set; }

        public EStatusPagamentoCartao? StatusPagamentoCartao { get; set; }
        
        public string MensagemProcessamentoWs { get; set; }
        
        public bool? CargaAvulsaDuplicada { get; set; }
        
        public int? PeriodoCargaAvulsaDuplicada { get; set; }
        
        public List<CargaAvulsaAddItemDuplicadoResponseModel> CargasAvulsasDuplicadas { get; set; }
        
        public ValidationResult<EValidationCargaAvulsa> ValidationResult { get; set; }

        public CargaAvulsaAddResponseModel()
        {
            
        }

        public CargaAvulsaAddResponseModel(int? cargaAvulsaId, int? statusCargaAvulsa, ValidationResult<EValidationCargaAvulsa> validationResult)
        {
            CargaAvulsaId = cargaAvulsaId;
            StatusCargaAvulsa = statusCargaAvulsa;
            ValidationResult = validationResult;
        }
    }

    public class CargaAvulsaAddItemDuplicadoResponseModel
    {
        public int Codigo { get; set; }
        public string DataCadastro => DataCadastroDateTime.ToString("yyyy-MM-dd HH:mm:ss");
        public DateTime DataCadastroDateTime { get; set; }
        public string Valor => ValorDecimal.ToString("C", new CultureInfo("pt-BR"));
        public decimal ValorDecimal { get; set; }
        public string MotoristaDocumento { get; set; }
        public string MotoristaNome { get; set; }
        public string TipoTitulo { get; set; }
        public string CnpjFilial { get; set; }
    }
}
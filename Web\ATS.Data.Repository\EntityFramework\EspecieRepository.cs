﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;
using ATS.Domain.Entities;

namespace ATS.Data.Repository.EntityFramework
{
    public class EspecieRepository : Repository<Especie>, IEspecieRepository
    {
        public EspecieRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna as espécies a partir dos dados de filtro
        /// </summary>
        /// <param name="descricao">Descrição da espécie</param>
        /// <returns>IQueryable de Especie</returns>
        public IQueryable<Especie> Consultar(string descricao)
        {
            return from especie in All()
                   where especie.Descricao.Contains(descricao)
                   select especie;
        }

        /// <summary>
        /// Retorna 
        /// </summary>
        /// <param name="dataHora"></param>
        /// <returns></returns>
        public IEnumerable<Especie> GetIdsEspeciesAtualizadas(DateTime dataHora)
        {
            return from especie in All()
                   where especie.DataHoraUltimaAtualizacao >= dataHora
                   select especie;
        }
    }
}
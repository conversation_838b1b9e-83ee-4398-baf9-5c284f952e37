﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Database
{
    public interface IEstabelecimentoRepository : IRepository<Estabelecimento>
    {
        /// <summary>
        /// Obter todos os estabelecimentos no raio da coordenada indicada
        /// </summary>
        /// <param name="latitude"></param>
        /// <param name="longitude"></param>
        /// <param name="raioKm"></param>
        /// <returns></returns>
        IEnumerable<int> GetIdsEstabelecimentoNoRaio(decimal latitude, decimal longitude, decimal raioKm);

        /// <summary>
        /// Obter todos os estabelecimentos do raio do percurso indicado
        /// </summary>
        /// <param name="latitudeOrigem"></param>
        /// <param name="longitudeOrigem"></param>
        /// <param name="latitudeDestino"></param>
        /// <param name="longitudeDestino"></param>
        /// <param name="raioKm"></param>
        /// <returns></returns>
        IEnumerable<int> GetIdsEstabelecimentoNoPercurso(
            decimal latitudeOrigem, decimal longitudeOrigem,
            decimal latitudeDestino, decimal longitudeDestino,
            decimal raioKm);

        int? GetIdPorCnpj(string cnpj, int idEmpresa);
    }
}

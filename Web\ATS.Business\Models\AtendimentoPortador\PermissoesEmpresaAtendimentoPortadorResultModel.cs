using ATS.Domain.Models.Comum;

namespace ATS.Domain.Models.AtendimentoPortador
{
    public class PermissoesEmpresaAtendimentoPortadorResultModel : BaseResultModel
    {
        public PermissoesUsuarioAtendimentoPortador Permissoes { get; set; }

        public PermissoesEmpresaAtendimentoPortadorResultModel()
        {
            Sucesso = true;
        }

        public PermissoesEmpresaAtendimentoPortadorResultModel(string mensagemFalha) : base(mensagemFalha)
        {
            
        }
    }
}
﻿{
  "runtime": "WinX64",
  "defaultVariables": null,
  "documentGenerator": {
    "fromDocument": {
      "json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Ciot/Api\",\r\n    \"description\": \"API's para integração de CIOT com ANTT.\\r\\n\\r\\nTodos os métodos seguem o mesmo padrão de autenticação, sendo necessário indicar no header da requisição os seguintes valores:\\r\\n<li>x-auth-token: Token de identificação da empresa requisitando a operação</li>\\r\\n<li>x-audit-user-doc: Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação</li>\\r\\n<li>x-audit-user-name: Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</li>\\r\\n\\r\\nOs retornos de métodos respeitam o padrão do protocolo REST/HTTP, fazendo uso do \\\"Http Code\\\" adequado a situação:\\r\\n<li>http code 200: Sucesso na operação do servidor, com retorno de dados</li>\\r\\n<li>http code 204: A requisição foi processada com sucesso no servidor, porém não há informação para retornar. Geralmente utilizado em consultas de registros. Na especificação dos métodos abaixo estará descrito se este código é possível de retorno pela API</li>\\r\\n<li>http code 4XX: Erro de requisição por conteúdo mal formatado pelo consumidor. Geralmente JSON em formato inválidio enviado pelo cliente. O servidor não realizou nenhuma operação</li>\\r\\n<li>http code 5XX: Erro interno no servidor de aplicação, neste caso o conteúdo da requisição está OK, e algo não planejado ocorreu na API</li>\\r\\n\\r\\nPelas definições abaixo é possiviel verificar os métodos que retornam a informação \\\"processingStateOnServer\\\". Esta subpropriedade é apenas informação técnica, não representando sucesso da execução de regras de negócios. Ela indica que o servidor executou a procedimento sem problemas técnicos, através do indicador \\\"State\\\" com os valores \\\"Ok\\\" ou \\\"Error\\\", sendo adicionado mensagens em caso de erros na propriedade \\\"ErrorMessage\\\".\"\r\n  },\r\n  \"basePath\": \"/Ciot/Api\",\r\n  \"paths\": {\r\n    \"/Antt/ConsultarTiposCarga\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Consulta os tipos de cargas válidas para a ANTT realizar uma declaração de CIOT\",\r\n        \"operationId\": \"AnttConsultarTiposCargaGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarTiposCargaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/ConsultarSituacaoTransportador\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Consultar dados do transportador na ANTT.\\r\\nPor este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)\",\r\n        \"operationId\": \"AnttConsultarSituacaoTransportadorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSituacaoTransportadorRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSituacaoTransportadorReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/ConsultarFrotaTransportador\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado\",\r\n        \"operationId\": \"AnttConsultarFrotaTransportadorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarFrotaTransportadorRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarFrotaTransportadorReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/DeclararOperacaoTransporte\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Registrar operação de transporte na ANTT.\\r\\nEm caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.\",\r\n        \"operationId\": \"AnttDeclararOperacaoTransportePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DeclararOperacaoTransporteRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DeclararOperacaoTransporteReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/CancelarOperacaoTransporte\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.\",\r\n        \"operationId\": \"AnttCancelarOperacaoTransportePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarOperacaoTransporteRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarOperacaoTransporteReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/RetificarOperacaoTransporte\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.\",\r\n        \"operationId\": \"AnttRetificarOperacaoTransportePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RetificarOperacaoTransporteRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RetificarOperacaoTransporteReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/EncerrarOperacaoTransporte\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Encerrar a operação de transporte na ANTT.\\r\\nOperações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.\\r\\nOperações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.\\r\\nOperações canceladas não permitem o encerramento.\",\r\n        \"operationId\": \"AnttEncerrarOperacaoTransportePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EncerrarOperacaoTransporteRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EncerrarOperacaoTransporteReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Antt/ReenviarContingencia\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Antt\"\r\n        ],\r\n        \"summary\": \"Reenviar ciot que está em contingencia a ANTT\",\r\n        \"operationId\": \"AnttReenviarContingenciaPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/DeclararOperacaoTransporteReponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Consultas/SituacaoCiot\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Consultar\"\r\n        ],\r\n        \"summary\": \"Consultar situação da declaração de transporte na base de dados do meio homologado\",\r\n        \"operationId\": \"ConsultasSituacaoCiotPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSituacaoCiotRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSituacaoCiotReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Consultas/EncerramentosPendentes\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Consultar\"\r\n        ],\r\n        \"summary\": \"Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado\",\r\n        \"operationId\": \"ConsultasEncerramentosPendentesPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarOperacaoTacAgregadoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarOperacaoTacAgregadoReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Consultas/ConsultarCiots\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Consultar\"\r\n        ],\r\n        \"summary\": \"Consultar ciots\",\r\n        \"operationId\": \"ConsultasConsultarCiotsPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarListaCiotsRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarListaCiotsResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"ConsultarTiposCargaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"tipos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/DadosTipoCargaResponse\"\r\n          }\r\n        },\r\n        \"falhaComunicacaoAntt\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ExcecaoResponse\": {\r\n      \"required\": [\r\n        \"tipo\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Autorizacao\",\r\n            \"Validacao\",\r\n            \"Negocio\",\r\n            \"Aplicacao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DadosTipoCargaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigo\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSituacaoTransportadorRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjInteressado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcTransportador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSituacaoTransportadorReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"cpfCnpjTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeRazaoSocialTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcAtivo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"dataValidadeRNTRC\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"equiparadoTAC\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"falhaComunicacaoAntt\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarFrotaTransportadorRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjInteressado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarFrotaTransportadorReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"cpfCnpjTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeRazaoSocialTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcAtivo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"veiculoTransportador\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/VeiculoFrotaTransportadorResponse\"\r\n          }\r\n        },\r\n        \"falhaComunicacaoAntt\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"VeiculoFrotaTransportadorResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"placaVeiculo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"situacaoVeiculoFrotaTransportador\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"DeclararOperacaoTransporteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"frete\": {\r\n          \"$ref\": \"#/definitions/FreteRequest\"\r\n        },\r\n        \"contratante\": {\r\n          \"$ref\": \"#/definitions/ContratanteRequest\"\r\n        },\r\n        \"destinatario\": {\r\n          \"$ref\": \"#/definitions/DestinatarioRequest\"\r\n        },\r\n        \"consignatario\": {\r\n          \"$ref\": \"#/definitions/ConsignatarioRequest\"\r\n        },\r\n        \"remetente\": {\r\n          \"$ref\": \"#/definitions/RemetenteRequest\"\r\n        },\r\n        \"veiculos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/VeiculoRequest\"\r\n          }\r\n        },\r\n        \"valores\": {\r\n          \"$ref\": \"#/definitions/ValoresFreteRequest\"\r\n        },\r\n        \"pagamento\": {\r\n          \"$ref\": \"#/definitions/PagamentoRequest\"\r\n        },\r\n        \"informacoesPagamento\": {\r\n          \"$ref\": \"#/definitions/InformacoesPagamentoRequest\"\r\n        },\r\n        \"ciotAjuste\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"FreteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigoMunicipioOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"codigoMunicipioDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cepOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cepDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"distanciaPercorrida\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"codigoTipoCarga\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataInicioFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataTerminoFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dadosComplementares\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"proprietario\": {\r\n          \"$ref\": \"#/definitions/ProprietarioRequest\"\r\n        },\r\n        \"motorista\": {\r\n          \"$ref\": \"#/definitions/MotoristaRequest\"\r\n        },\r\n        \"codigoNaturezaCarga\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pesoCarga\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"tipoViagem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"subContratacao\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"ciotPrincipal\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"altoDesempenho\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"destinacaoComercial\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"freteRetorno\": {\r\n          \"$ref\": \"#/definitions/FreteRetornoRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"ContratanteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"rntrc\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeRazaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEnderecoRequest\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DestinatarioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeRazaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEnderecoRequest\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsignatarioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeRazaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEnderecoRequest\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RemetenteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeRazaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEnderecoRequest\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"VeiculoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrc\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ValoresFreteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"valorFrete\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorCombustivel\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorDespesas\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"totalImposto\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"totalPegadio\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"quantidadeTarifas\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valorTarifas\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"PagamentoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"formaPagmento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"parcelas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ParcelaPagamentoRequest\"\r\n          }\r\n        },\r\n        \"parcelaUnica\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"infoPagamento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bancoPagamento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agenciaPagamento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"contaPagamento\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"InformacoesPagamentoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"formaPagamento\": {\r\n          \"enum\": [\r\n            \"Cartao\",\r\n            \"ContaCorrente\",\r\n            \"ContaPoupanca\",\r\n            \"ContaPagamento\",\r\n            \"Outros\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"dadosBancarios\": {\r\n          \"$ref\": \"#/definitions/DadosBancariosRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"ProprietarioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"rntrc\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeRazaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/PessoaEnderecoRequest\"\r\n        },\r\n        \"tipoPessoa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"MotoristaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroCNH\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"FreteRetornoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cepRetorno\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"distanciaRetorno\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaEnderecoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cep\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoMunicipio\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"logradouro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numero\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"complemento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bairro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"telefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"email\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ParcelaPagamentoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigoParcela\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valorParcela\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"vencimento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DadosBancariosRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBACEN\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DeclararOperacaoTransporteReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoVerificador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"emContingencia\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"protocoloErro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAlteracao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarOperacaoTransporteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"motivoCancelamento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAlteracao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarOperacaoTransporteReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCancelamento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloCancelamento\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RetificarOperacaoTransporteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAlteracao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"veiculos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/VeiculoRequest\"\r\n          }\r\n        },\r\n        \"quantidadeTarifas\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valorTarifas\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"codigoNaturezaCarga\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pesoCarga\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"dataInicioViagem\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFimViagem\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoMunicipioOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"codigoMunicipioDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valorFrete\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"RetificarOperacaoTransporteReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"codigoRetorno\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRetificacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EncerrarOperacaoTransporteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pesoCarga\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"viagensOperacaoTransporte\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/EncerrarOperacaoTransporteViagemRequest\"\r\n          }\r\n        },\r\n        \"valoresEfetivos\": {\r\n          \"$ref\": \"#/definitions/ValoresFreteRequest\"\r\n        },\r\n        \"senhaAlteracao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EncerrarOperacaoTransporteViagemRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigoMunicipioOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"codigoMunicipioDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cepOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cepDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoNaturezaCarga\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pesoCarga\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"quantidadeViagens\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"EncerrarOperacaoTransporteReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloEncerramento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataEncerramento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSituacaoCiotRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAlteracao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSituacaoCiotReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoVerificador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"situacao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"enviadoEmContingencia\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"erroContingencia\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"nomeProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicioFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataTerminoFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"valorFrete\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorEfetivoFrete\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorDespesas\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"totalImposto\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"totalPegadio\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"tipoViagem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"encerrado\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"situacaoDeclaracao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarOperacaoTacAgregadoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjContratante\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjTransportador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarOperacaoTacAgregadoReponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"viagensPendentes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaOperacaoTacAgregadoViagemResponse\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaOperacaoTacAgregadoViagemResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoVerificador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataTerminoViagem\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDeclaracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"quantidadeTarifas\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valorTarifas\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarListaCiotsRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjContratante\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"ciot\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CiotStruct\"\r\n          }\r\n        },\r\n        \"tipos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"enum\": [\r\n              \"Padrao\",\r\n              \"Fracionado\",\r\n              \"TacAgregado\"\r\n            ],\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"cnpjProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rntrcProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfMotorista\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"verificador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarListaCiotsResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"excecao\": {\r\n          \"$ref\": \"#/definitions/ExcecaoResponse\"\r\n        },\r\n        \"quantidadeCiots\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"ciotsAbertos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"ciotsEmContingencia\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"ciots\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaCiot\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCiot\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ciot\": {\r\n          \"$ref\": \"#/definitions/CiotStruct\"\r\n        },\r\n        \"dataEmissao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicioFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFimFrete\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"CiotGerado\",\r\n            \"Contingencia\",\r\n            \"Cancelado\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"encerrado\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"avisoAoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valores\": {\r\n          \"$ref\": \"#/definitions/ValoresCiotStruct\"\r\n        },\r\n        \"modal\": {\r\n          \"$ref\": \"#/definitions/ModalCiotStruct\"\r\n        },\r\n        \"rota\": {\r\n          \"$ref\": \"#/definitions/RotaCiotStruct\"\r\n        },\r\n        \"clientes\": {\r\n          \"$ref\": \"#/definitions/ClientesCiotStruct\"\r\n        }\r\n      }\r\n    },\r\n    \"ValoresCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"valorTotalFrete\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTotalPedagio\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTotalImpostos\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"pesoCarga\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ModalCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"motoristaCpf\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"motoristaNome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"proprietarioCnpjCpf\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"proprietarioNome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"proprietarioRntrc\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"RotaCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"origem\": {\r\n          \"$ref\": \"#/definitions/LocalCiotStruct\"\r\n        },\r\n        \"destino\": {\r\n          \"$ref\": \"#/definitions/LocalCiotStruct\"\r\n        }\r\n      }\r\n    },\r\n    \"ClientesCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"contratante\": {\r\n          \"$ref\": \"#/definitions/DadosClienteCiotStruct\"\r\n        },\r\n        \"remetente\": {\r\n          \"$ref\": \"#/definitions/DadosClienteCiotStruct\"\r\n        },\r\n        \"destinatario\": {\r\n          \"$ref\": \"#/definitions/DadosClienteCiotStruct\"\r\n        },\r\n        \"consignatario\": {\r\n          \"$ref\": \"#/definitions/DadosClienteCiotStruct\"\r\n        }\r\n      }\r\n    },\r\n    \"LocalCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pais\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DadosClienteCiotStruct\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}",
      "url": "http://localhost:50500/Ciot/Api/swagger/v1/swagger.json",
      "output": null
    }
  },
  "codeGenerators": {
    "openApiToCSharpClient": {
      "clientBaseClass": "SistemaInfoMicroServiceBaseClient",
      "configurationClass": "HttpContext",
      "generateClientClasses": true,
      "generateClientInterfaces": false,
      "injectHttpClient": false,
      "disposeHttpClient": true,
      "protectedMethods": [],
      "generateExceptionClasses": true,
      "exceptionClass": "SwaggerException",
      "wrapDtoExceptions": true,
      "useHttpClientCreationMethod": true,
      "httpClientType": "System.Net.Http.HttpClient",
      "useHttpRequestMessageCreationMethod": true,
      "useBaseUrl": true,
      "generateBaseUrlProperty": true,
      "generateSyncMethods": true,
      "exposeJsonSerializerSettings": false,
      "clientClassAccessModifier": "public",
      "typeAccessModifier": "public",
      "generateContractsOutput": false,
      "contractsNamespace": null,
      "contractsOutputFilePath": null,
      "parameterDateTimeFormat": "s",
      "generateUpdateJsonSerializerSettingsMethod": true,
      "serializeTypeInformation": false,
      "queryNullValue": "",
      "className": "{controller}Client",
      "operationGenerationMode": "MultipleClientsFromPathSegments",
      "additionalNamespaceUsages": [
        "System.Web",
        "ATS.Data.Repository.External.SistemaInfo"
      ],
      "additionalContractNamespaceUsages": [],
      "generateOptionalParameters": false,
      "generateJsonMethods": true,
      "enforceFlagEnums": false,
      "parameterArrayType": "System.Collections.Generic.IEnumerable",
      "parameterDictionaryType": "System.Collections.Generic.IDictionary",
      "responseArrayType": "System.Collections.Generic.List",
      "responseDictionaryType": "System.Collections.Generic.Dictionary",
      "wrapResponses": false,
      "wrapResponseMethods": [],
      "generateResponseClasses": true,
      "responseClass": "SwaggerResponse",
      "namespace": "SistemaInfo.MicroServices.Rest.Ciot.ApiClient",
      "requiredPropertiesMustBeDefined": true,
      "dateType": "System.DateTime",
      "jsonConverters": null,
      "anyType": "object",
      "dateTimeType": "System.DateTime",
      "timeType": "System.TimeSpan",
      "timeSpanType": "System.TimeSpan",
      "arrayType": "System.Collections.ObjectModel.ObservableCollection",
      "arrayInstanceType": null,
      "dictionaryType": "System.Collections.Generic.Dictionary",
      "dictionaryInstanceType": null,
      "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection",
      "dictionaryBaseType": "System.Collections.Generic.Dictionary",
      "classStyle": "Inpc",
      "generateDefaultValues": true,
      "generateDataAnnotations": true,
      "excludedTypeNames": [],
      "excludedParameterNames": [],
      "handleReferences": false,
      "generateImmutableArrayProperties": false,
      "generateImmutableDictionaryProperties": false,
      "jsonSerializerSettingsTransformationMethod": null,
      "inlineNamedArrays": false,
      "inlineNamedDictionaries": false,
      "inlineNamedTuples": true,
      "inlineNamedAny": false,
      "generateDtoTypes": true,
      "templateDirectory": null,
      "typeNameGeneratorType": null,
      "propertyNameGeneratorType": null,
      "enumNameGeneratorType": null,
      "serviceHost": null,
      "serviceSchemes": null,
      "output": "Sources/Ciot.ApiClient.cs"
    }
  }
}
﻿using System.Web.Mvc;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Attributes;

namespace ATS.WS.ControllersATS
{
    public class LogSmsAtsController : DefaultController
    {
        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GerarRelatorio(EPerfil perfil, string extensao, int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa = null)
        {
            /*try
            {
                if (perfil == EPerfil.Administrador && idEmpresa == 0)
                {
                    return ResponderSucesso(new
                    {
                        totalItems = 0,
                        items = new List<object>()

                    });
                }
                else if (perfil == EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var dadosLogSmsFiltrados = new SrvLogSms().ConsultarGrid(take, page, order, filters, true, idEmpresa);

                return ResponderSucesso(new RelatorioLogSms().GetReport(extensao, dadosLogSmsFiltrados.items));
            }
            catch (Exception ex)
            {
                return ResponderErro(ex.Message);
            }*/
            return ResponderErro(string.Empty);
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(EPerfil perfil, int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa = null)
        {
            /*try
            {
                if (perfil == EPerfil.Administrador && idEmpresa == 0)
                {
                    return ResponderSucesso(new
                    {
                        totalItems = 0,
                        items = new List<object>()

                    });
                }                    
                else if (perfil == EPerfil.Empresa)
                    idEmpresa = usuarioLogado.IdEmpresa;

                return ResponderSucesso(new SrvLogSms().ConsultarGrid(take, page, order, filters, false, idEmpresa));
            }
            catch (Exception ex)
            {
                return ResponderErro(ex.Message);
            }*/
            
            return ResponderErro(string.Empty);
        
        }
    }
}
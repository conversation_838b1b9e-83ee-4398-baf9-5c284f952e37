﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioFilialService
    {
        IQueryable<UsuarioFilial> GetFiliaisPorIdUsuario(int? idUsuario);
        int GetFilialPorIdUsuario(int? idUsuario);
    }
}

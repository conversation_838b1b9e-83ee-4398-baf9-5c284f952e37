﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System.Collections.Generic;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Models.Common
{
    public class ProprietarioModel
    {
        public int IdProprietario { get; set; }
        public string CnpjCpf { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
        public string CNH { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EStatusIntegracao StatusIntegracao { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ETipoContrato TipoContrato { get; set; }

        public string DataNascimentoStr { get; set; }

        public string Endereco { get; set; }

        public string Inss { get; set; }

        public string Referencia1 { get; set; }

        public string Referencia2 { get; set; }

        public bool Ativo { get; set; } = true;

        public bool HasCadastro { get; set; } = false;

        public List<ConjuntoRequestModel> Conjuntos { get; set; } = null;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<ProprietarioContatoModel> Contatos { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<ProprietarioEnderecoModel> Enderecos { get; set; }
    }

    public class ProprietarioCartaoModel
    {
        
    }
}
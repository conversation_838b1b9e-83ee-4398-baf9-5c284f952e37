using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class BloqueioOrigemTipoMap : EntityTypeConfiguration<BloqueioOrigemTipo>
    {
        public BloqueioOrigemTipoMap()
        {
            ToTable("BLOQUEIO_ORIGEM_TIPO");

            HasKey(t => t.IdBloqueioOrigemTipo);
            Property(t => t.IdBloqueioOrigemTipo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            
            Property(t => t.Descricao).IsRequired();
        }
    }
}
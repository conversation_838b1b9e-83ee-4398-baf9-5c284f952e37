﻿using ATS.Application.Application;
using ATS.MongoDB.Context.Entities;
using ATS.MongoDB.Enum;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Documento;
using ATS.WS.Models.Webservice.Response.Documento;
using Microsoft.AspNet.SignalR;
using Sistema.Framework.Util.Enumerate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using ATS.Application.Interface;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Services
{
    public class SrvDocumento : SrvBase
    {
        public readonly SrvDataMediaServer _appLayer;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IEmpresaApp _empresaApp;
        private readonly IPagamentoConfiguracaoService _pagamentoConfiguracaoService;
        private readonly IViagemDocumentoService _viagemDocumentoService;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IDocumentoService _documentoService;

        public SrvDocumento(SrvDataMediaServer appLayer, IViagemEventoService viagemEventoService, IEmpresaApp empresaApp, IPagamentoConfiguracaoService pagamentoConfiguracaoService, 
            IViagemDocumentoService viagemDocumentoService, IEstabelecimentoApp estabelecimentoApp, IDocumentoService documentoService)
        {
            _appLayer = appLayer;
            _viagemEventoService = viagemEventoService;
            _empresaApp = empresaApp;
            _pagamentoConfiguracaoService = pagamentoConfiguracaoService;
            _viagemDocumentoService = viagemDocumentoService;
            _estabelecimentoApp = estabelecimentoApp;
            _documentoService = documentoService;
        }

        public Retorno<List<ConsultarDocumentoResponse>> ConsultarDocumento(ConsultarDocumentosRequest @params)
        {
            try
            {
                var listaDocumentos = new List<ConsultarDocumentoResponse>();
                var validarRequest = ValidarRequestConsultarDocumentos(@params);

                if (!validarRequest.IsValid)
                    return new Retorno<List<ConsultarDocumentoResponse>>(false,
                        validarRequest.Errors.FirstOrDefault()?.Message, null);

                if (@params.Processo == EProcessoDocumento.PagamentoFrete)
                {
                    var evento = _viagemEventoService.GetByToken(@params.TokenEvento);

                    if (evento == null)
                        return new Retorno<List<ConsultarDocumentoResponse>>(false, @"Evento não encontrado.", null);

                    listaDocumentos.AddRange(from viagemDocumento in evento.ViagemDocumentos
                                             let media =
                                                 viagemDocumento.TokenAnexo != null
                                                     ? _appLayer.GetMedia(viagemDocumento.TokenAnexo)
                                                     : new Media()
                                             select new ConsultarDocumentoResponse
                                             {
                                                 IdViagemDocumento = viagemDocumento.IdViagemDocumento,
                                                 CodigoProcesso = evento.Token,
                                                 Descricao = viagemDocumento?.Descricao,
                                                 IdentificacaoProcesso = EProcessoDocumento.PagamentoFrete,
                                                 Obrigatorio = viagemDocumento.ObrigaAnexo,
                                                 ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal,
                                                 Documento = media?.Data,
                                                 Tipo = media?.Type ?? EMediaType.PNG
                                             });

                    return new Retorno<List<ConsultarDocumentoResponse>>(true, listaDocumentos);
                }

                var itemsToken = @params.TokenEvento.Split('-');
                if (itemsToken.Count() < 2)
                    return new Retorno<List<ConsultarDocumentoResponse>>(false, "QR Code no formato incorreto! ", null);
                var cnpjEmpresa = itemsToken[1];
                var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);

                var configuracao =
                    _pagamentoConfiguracaoService.GetPorEmpresa(idEmpresa ?? 0, null).FirstOrDefault(o => o.Ativo);

                if (configuracao != null)
                    listaDocumentos.AddRange(
                        from pagamentoConfiguracaoProcesso in configuracao.PagamentoConfiguracoesProcesso
                        where pagamentoConfiguracaoProcesso.Processo == EProcessoPgtoFrete.Protocolo
                        select new ConsultarDocumentoResponse
                        {
                            IdDocumento = pagamentoConfiguracaoProcesso.IdDocumento,
                            CodigoProcesso = @params.TokenEvento,
                            Descricao = pagamentoConfiguracaoProcesso.Documento?.Descricao,
                            IdentificacaoProcesso = EProcessoDocumento.GeracaoProtocolo,
                            Obrigatorio = true,
                            Documento = null,
                        });

                return new Retorno<List<ConsultarDocumentoResponse>>(true, listaDocumentos);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<ConsultarDocumentoResponse>>($"{nameof(ConsultarDocumento)} >> {e.Message}");
            }
        }

        public Retorno<string> EnviarDocumento(EnviarDocumentoRequest @params)
        {
            try
            {
                var validarRequest = ValidarRequestEnviarDocumento(@params);

                if (!validarRequest.IsValid)
                    return new Retorno<string>(false, validarRequest.ToString(), null);

                if (@params.Processo == EProcessoDocumento.PagamentoFrete)
                {
                    var viagemDocumento = _viagemDocumentoService.Get(@params.IdViagemDocumento ?? 0);

                    if (viagemDocumento == null)
                        return new Retorno<string>(false, "Viagem documento não encontrada", null);

                    var objectId = _appLayer.Add((int)@params.Type, @params.DocumentoBase64,
                        @params.NomeArquivo, Convert.ToString(EnumHelper.GetDescription(@params.Type)));

                    Thread.Sleep(1000);

                    var hubContext = GlobalHost.ConnectionManager.GetHubContext("pagamentoFreteHub");
                    hubContext.Clients.Group(@params.TokenEvento)
                        .EnviarAnexo(new { Token = objectId.ToString(), @params.IdViagemDocumento });

                    viagemDocumento.TokenAnexo = objectId.ToString();
                    var resultUpdate = _viagemDocumentoService.Update(viagemDocumento);

                    return resultUpdate.IsValid
                        ? new Retorno<string>(true, objectId.ToString())
                        : new Retorno<string>(false, resultUpdate.ToString(), null);
                }
                else if (@params.Processo == EProcessoDocumento.GeracaoProtocolo)
                {
                    var objectId = _appLayer.Add((int)@params.Type, @params.DocumentoBase64,
                        @params.NomeArquivo, Convert.ToString(EnumHelper.GetDescription(@params.Type)));

                    var hubContext = GlobalHost.ConnectionManager.GetHubContext("pagamentoFreteHub");
                    hubContext.Clients.Group(@params.TokenEvento)
                        .EnviarAnexo(new { Token = objectId.ToString(), @params.IdDocumento });

                    return new Retorno<string>(true, objectId.ToString());
                }

                return new Retorno<string>(false, "Nenhum processo valido informado.", null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<string>(false, e.Message, null);
            }
        }

        private static ValidationResult ValidarRequestConsultarDocumentos(ConsultarDocumentosRequest @params)
        {
            var validation = new ValidationResult();

            if (@params.Processo == 0)
                return validation.Add("O processo não foi informado.");

            if (!Enum.IsDefined(typeof(EProcessoDocumento), @params.Processo))
                return validation.Add("Tipo de processo inválido.");

            if (string.IsNullOrEmpty(@params.TokenEvento))
                return validation.Add("O Token não foi informado.");

            return new ValidationResult();
        }

        private static ValidationResult ValidarRequestEnviarDocumento(EnviarDocumentoRequest @params)
        {
            var validation = new ValidationResult();

            if (@params.Processo == 0)
                return validation.Add("O processo não foi informado.");

            if (!Enum.IsDefined(typeof(EProcessoDocumento), @params.Processo))
                return validation.Add("Tipo de processo inválido.");

            if (@params.Processo == EProcessoDocumento.PagamentoFrete)
                if (!@params.IdViagemDocumento.HasValue)
                    return validation.Add("IdViagemDocumento não informado.");
            if (@params.Processo == EProcessoDocumento.GeracaoProtocolo && !@params.IdDocumento.HasValue)
                return validation.Add("Código do documento é obrigatório para digitalização no processo de geração de protocolo");

            if (string.IsNullOrEmpty(@params.TokenEvento))
                return validation.Add("Token não informado.");

            if (string.IsNullOrEmpty(@params.DocumentoBase64))
                return validation.Add("Documento não informado.");

            if (@params.Type == 0)
                return validation.Add("Tipo do documento não informado.");

            if (!Enum.IsDefined(typeof(EMediaType), @params.Type))
                return validation.Add("Tipo do documento inválido.");

            if (string.IsNullOrEmpty(@params.NomeArquivo))
                return validation.Add("Nome do arquivo não informado.");

            return validation;
        }

        private Estabelecimento GetEstabelecimento(int idEstabelecimentoBase, string cnpjEmpresa)
        {
            var empresa = _empresaApp.Get(cnpjEmpresa);

            var estabelecimento = _estabelecimentoApp.GetByIdEstabelecimentoBase(idEstabelecimentoBase, empresa.IdEmpresa);
            return estabelecimento;
        }

        public byte[] GerarRelatorioGridDocumentos(int? idEmpresa, string descricao, int Take, int Page, OrderFilters Order, List<QueryFilters> Filters, string extensao)
        {
            return _documentoService.GerarRelatorioGridDocumento(idEmpresa, descricao, Take, Page, Order, Filters, extensao, GetLogo(idEmpresa));
        }
    }
}
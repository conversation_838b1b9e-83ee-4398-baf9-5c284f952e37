﻿using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Application.WS;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Models;
using ATS.Data.Context;
using ATS.Data.Repository.Dapper.Common;
using ATS.Data.Repository.EntityFramework;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Interfaces;
using ATS.Data.Repository.External.Serpro;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Client;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Interface.Validators;
using ATS.Domain.Service;
using ATS.Domain.Validators;
using Autofac;

namespace ATS.CrossCutting.DI
{
	public class DependencyInjection
	{
		public static void RegisterServices(ContainerBuilder containerBuilder)
		{
			containerBuilder.RegisterGeneric(typeof(ContextManager<>)).InstancePerLifetimeScope();
			containerBuilder.RegisterType(typeof(AtsContext)).InstancePerLifetimeScope();

			//Configuramos uma instancia da user identity por requisição, para que assim, ela possa ser injetada e utilizada pelos projetos, identificando o usuário logado
			containerBuilder.RegisterType<UserIdentity>().As<IUserIdentity>()
				.InstancePerLifetimeScope();

			containerBuilder.RegisterType(typeof(DapperContext));

			RegisterApp(containerBuilder);
			RegisterDomainServices(containerBuilder);
			RegisterRepositories(containerBuilder);
			RegisterOthers(containerBuilder);
		}

        private static void RegisterApp(ContainerBuilder containerBuilder)
        {
            containerBuilder.RegisterType<BancoApp>().As<IBancoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AutorizacaoEmpresaApp>().As<IAutorizacaoEmpresaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CargaAvulsaApp>().As<ICargaAvulsaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CargaAvulsaLoteApp>().As<ICargaAvulsaLoteApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CidadeApp>().As<ICidadeApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ClienteApp>().As<IClienteApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EmpresaApp>().As<IEmpresaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PlanoApp>().As<IPlanoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<GrupoUsuarioApp>().As<IGrupoUsuarioApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<LayoutApp>().As<ILayoutApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioApp>().As<IUsuarioApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioDocumentoApp>().As<IUsuarioDocumentoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioFilialApp>().As<IUsuarioFilialApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<VeiculoApp>().As<IVeiculoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AtendimentoPortadorApp>().As<IAtendimentoPortadorApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PedagioRotaApp>().As<IPedagioRotaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ViagemApp>().As<IViagemApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CadastrosApp>().As<ICadastrosApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosApp>().As<IParametrosApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ProprietarioApp>().As<IProprietarioApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<FilialApp>().As<IFilialApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<MotoristaApp>().As<IMotoristaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ResgateCartaoAtendimentoApp>().As<IResgateCartaoAtendimentoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AdministradoraPlataformaApp>().As<IAdministradoraPlataformaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ValePedagioApp>().As<IValePedagioApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<SerproApp>().As<ISerproApp>().InstancePerLifetimeScope();

            containerBuilder.RegisterType<CiotV2App>().As<ICiotV2App>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CiotV3App>().As<ICiotV3App>().InstancePerLifetimeScope();

            containerBuilder.RegisterType<DeclaracaoCiotApp>().As<IDeclaracaoCiotApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ClienteProdutoEspecieApp>().As<IClienteProdutoEspecieApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CredenciamentoApp>().As<ICredenciamentoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EstabelecimentoBaseDocumentoApp>().As<IEstabelecimentoBaseDocumentoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PagamentoFreteApp>().As<IPagamentoFreteApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<TransacaoCartaoApp>().As<ITransacaoCartaoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PedagioViagemApp>().As<IPedagioViagemApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CheckinResumoApp>().As<ICheckinResumoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CheckInApp>().As<ICheckInApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioPermissoesConcedidasMobileApp>().As<IUsuarioPermissoesConcedidasMobileApp>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<DespesaUsuarioApp>().As<IDespesaUsuarioApp>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LocalizacaoUsuarioApp>().As<ILocalizacaoUsuarioApp>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LimiteTransacaoPortadorApp>().As<ILimiteTransacaoPortadorApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<RecursoMobileApp>().As<IRecursoMobileApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CampanhaApp>().As<ICampanhaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<BizWebhookApp>().As<IBizWebhookApp>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TagExtrattaApp>().As<ITagExtrattaApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioPermissaoCartaoApp>().As<IUsuarioPermissaoCartaoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PermissaoCartaoApp>().As<IPermissaoCartaoApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PrestacaoContasApp>().As<IPrestacaoContasApp>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ExtratoConsolidadoApp>().As<IExtratoConsolidadoApp>().InstancePerLifetimeScope();
        }

        private static void RegisterDomainServices(ContainerBuilder containerBuilder)
        {
            containerBuilder.RegisterType<BancoService>().As<IBancoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AutorizacaoEmpresaService>().As<IAutorizacaoEmpresaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CargaAvulsaService>().As<ICargaAvulsaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<LayoutService>().As<ILayoutService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AtendimentoPortadorService>().As<IAtendimentoPortadorService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PedagioRotaService>().As<IPedagioRotaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ViagemService>().As<IViagemService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ViagemEventoService>().As<IViagemEventoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CadastrosService>().As<ICadastrosService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ClienteService>().As<IClienteService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosService>().As<IParametrosService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosFilialService>().As<IParametrosFilialService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosAdministradoraMeioHomologadoService>().As<IParametrosAdministradoraMeioHomologadoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosAdministradoraPlataformaService>().As<IParametrosAdministradoraPlataformaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosEmpresaService>().As<IParametrosEmpresaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosUsuarioService>().As<IParametrosUsuarioService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosGenericoService>().As<IParametrosGenericoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosMobileGlobalService>().As<IParametrosMobileGlobalService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosProprietarioService>().As<IParametrosProprietarioService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosProprietarioMotoristaService>().As<IParametrosProprietarioMotoristaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ProprietarioService>().As<IProprietarioService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ResgateCartaoAtendimentoService>().As<IResgateCartaoAtendimentoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<UsuarioService>().As<IUsuarioService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AdministradoraPlataformaService>().As<IAdministradoraPlataformaService>().InstancePerLifetimeScope();

			containerBuilder.RegisterType<CiotV2Service>().As<ICiotV2Service>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CiotV3Service>().As<ICiotV3Service>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EmpresaService>().As<IEmpresaService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PlanoService>().As<IPlanoService>().InstancePerLifetimeScope();

            containerBuilder.RegisterType<DeclaracaoCiotService>().As<IDeclaracaoCiotService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PushService>().As<IPushService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EstabelecimentoBaseService>().As<IEstabelecimentoBaseService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EstabelecimentoService>().As<IEstabelecimentoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ParametrosEstabelecimentoService>().As<IParametrosEstabelecimentoService>().InstancePerLifetimeScope();

            containerBuilder.RegisterType<VersaoAnttLazyLoadService>().As<IVersaoAnttLazyLoadService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ContratoCiotAgregadoService>().As<IContratoCiotAgregadoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<VeiculoService>().As<IVeiculoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PagamentoFreteService>().As<IPagamentoFreteService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CheckinResumoService>().As<ICheckinResumoService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<LogSmsService>().As<ILogSmsService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<SMService>().As<ISMService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EmailService>().As<IEmailService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<WebhookService>().As<IWebhookService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<DataMediaServerService>().As<IDataMediaServerService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<WhiteListIPService>().As<IWhiteListIPService>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<DeclaracaoCiotService>().As<IDeclaracaoCiotService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PushService>().As<IPushService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EstabelecimentoBaseService>().As<IEstabelecimentoBaseService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EstabelecimentoService>().As<IEstabelecimentoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ParametrosEstabelecimentoService>().As<IParametrosEstabelecimentoService>().InstancePerLifetimeScope();

			containerBuilder.RegisterType<VersaoAnttLazyLoadService>().As<IVersaoAnttLazyLoadService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ContratoCiotAgregadoService>().As<IContratoCiotAgregadoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<VeiculoService>().As<IVeiculoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PagamentoFreteService>().As<IPagamentoFreteService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CheckinResumoService>().As<ICheckinResumoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LogSmsService>().As<ILogSmsService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<SMService>().As<ISMService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EmailService>().As<IEmailService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<WebhookService>().As<IWebhookService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<DataMediaServerService>().As<IDataMediaServerService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<WhiteListIPService>().As<IWhiteListIPService>().InstancePerLifetimeScope();

			containerBuilder.RegisterType<DespesaUsuarioService>().As<IDespesaUsuarioService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EmpresaModuloService>().As<IEmpresaModuloService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ClienteProdutoEspecieService>().As<IClienteProdutoEspecieService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<NotificacaoPushService>().As<INotificacaoPushService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ViagemSolicitacaoAbonoService>().As<IViagemSolicitacaoAbonoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EstabelecimentoBaseProdutoService>().As<IEstabelecimentoBaseProdutoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<RotaService>().As<IRotaService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LocalizacaoUsuarioService>().As<ILocalizacaoUsuarioService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LimiteTransacaoPortadorService>().As<ILimiteTransacaoPortadorService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CampanhaService>().As<ICampanhaService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<UsuarioPermissaoCartaoService>().As<IUsuarioPermissaoCartaoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<BizWebhookService>().As<IBizWebhookService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PermissaoCartaoService>().As<IPermissaoCartaoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<RsaCryptoService>().As<IRsaCryptoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PrestacaoContasService>().As<IPrestacaoContasService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TransacaoPixService>().As<ITransacaoPixService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<SolicitacaoChavePixService>().As<ISolicitacaoChavePixService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ExtratoConsolidadoService>().As<IExtratoConsolidadoService>().InstancePerLifetimeScope();
        }

		private static void RegisterRepositories(ContainerBuilder containerBuilder)
		{
			containerBuilder.RegisterType<LocalizacaoUsuarioAddModelValidator>().As<ILocalizacaoUsuarioAddModelValidator>().InstancePerLifetimeScope();

			containerBuilder.RegisterType<AutorizacaoEmpresaRepository>().As<IAutorizacaoEmpresaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CargaAvulsaRepository>().As<ICargaAvulsaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CidadeRepository>().As<ICidadeRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EmpresaRepository>().As<IEmpresaRepository>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PlanoRepository>().As<IPlanoRepository>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PlanoEmpresaRepository>().As<IPlanoEmpresaRepository>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<EstadoRepository>().As<IEstadoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LayoutRepository>().As<ILayoutRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PaisRepository>().As<IPaisRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<UsuarioRepository>().As<IUsuarioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<VeiculoRepository>().As<IVeiculoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<AtendimentoPortadorRepository>().As<IAtendimentoPortadorRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<AtendimentoPortadorTramiteRepository>().As<IAtendimentoPortadorTramiteRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PedagioRotaRepository>().As<IPedagioRotaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ViagemRepository>().As<IViagemRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ViagemEventoRepository>().As<IViagemEventoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ClienteRepository>().As<IClienteRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ParametrosRepository>().As<IParametrosRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ProprietarioRepository>().As<IProprietarioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<MotoristaRepository>().As<IMotoristaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TransacaoCartaoRepository>().As<ITransacaoCartaoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ViagemRotaRepository>().As<IViagemRotaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<FornecedorCnpjPedagioRepository>().As<IFornecedorCnpjPedagioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<AdministradoraPlataformaRepository>().As<IAdministradoraPlataformaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<DeclaracaoCiotRepository>().As<IDeclaracaoCiotRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ResgateCartaoAtendimentoRepository>().As<IResgateCartaoAtendimentoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EmpresaModuloService>().As<IEmpresaModuloService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ClienteProdutoEspecieService>().As<IClienteProdutoEspecieService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<NotificacaoPushService>().As<INotificacaoPushService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<ViagemSolicitacaoAbonoService>().As<IViagemSolicitacaoAbonoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<EstabelecimentoBaseProdutoService>().As<IEstabelecimentoBaseProdutoService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<RotaService>().As<IRotaService>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<WhiteListIPRepository>().As<IWhiteListIPRepository>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<AbastecimentoApiClient>().As<IAbastecimentoApiClient>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<DespesaUsuarioRepository>().As<IDespesaUsuarioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CategoriaRepository>().As<ICategoriaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LocalizacaoUsuarioRepository>().As<ILocalizacaoUsuarioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<LimiteTransacaoPortadorRepository>().As<ILimiteTransacaoPortadorRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TagRepository>().As<ITagRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<BlacklistIpRepository>().As<IBlacklistIpRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CampanhaRepository>().As<ICampanhaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<CampanhaRespostaRepository>().As<ICampanhaRespostaRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<UsuarioPermissaoCartaoRepository>().As<IUsuarioPermissaoCartaoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<GestorUsuarioRepository>().As<IGestorUsuarioRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PrestacaoContasRepository>().As<IPrestacaoContasRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<PrestacaoContasEventoRepository>().As<IPrestacaoContasEventoRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TransacaoPixRepository>().As<ITransacaoPixRepository>().InstancePerLifetimeScope();
			containerBuilder.RegisterType<TransacaoPixStatusRepository>().As<ITransacaoPixStatusRepository>().InstancePerLifetimeScope();
		}

        private static void RegisterOthers(ContainerBuilder containerBuilder)
        {
            containerBuilder.RegisterType<MobilePush>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ViagemActionDependencies>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ContratoCiotAgregadoActionDependencies>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<WebhookActionDependencies>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CartoesServiceArgs>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<PedagioAppFactoryDependencies>().AsSelf().InstancePerLifetimeScope();
            containerBuilder.RegisterType<CartoesAppFactoryDependencies>().AsSelf().InstancePerLifetimeScope();
            //containerBuilder.RegisterType<SerproClient>().As<ISerproClient>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<ExtrattaBizApiClient>().As<IExtrattaBizApiClient>().InstancePerLifetimeScope();
            containerBuilder.RegisterType<TagExtrattaExternalRepository>().As<ITagExtrattaExternalRepository>().InstancePerLifetimeScope();
        }
    }
}

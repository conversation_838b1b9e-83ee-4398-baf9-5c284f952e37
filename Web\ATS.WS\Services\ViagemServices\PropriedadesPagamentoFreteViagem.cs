using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.WS.Models.Common.Request;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;

namespace ATS.WS.Services.ViagemServices
{
    public class PropriedadesPagamentoFreteViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IMotivoCredenciamentoService _motivoCredenciamentoService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IExtrattaBizApiClient _bizApiClient;

        public PropriedadesPagamentoFreteViagem(IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp, IEmpresaRepository empresaRepository, ITransacaoCartaoApp transacaoCartaoApp, IEstabelecimentoApp estabelecimentoApp, IViagemEventoApp viagemEventoApp, IMotivoCredenciamentoService motivoCredenciamentoService, IParametrosEmpresaService parametrosEmpresaService, IParametrosProprietarioService parametrosProprietarioService, IExtrattaBizApiClient bizApiClient)
        {
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _empresaRepository = empresaRepository;
            _transacaoCartaoApp = transacaoCartaoApp;
            _estabelecimentoApp = estabelecimentoApp;
            _viagemEventoApp = viagemEventoApp;
            _motivoCredenciamentoService = motivoCredenciamentoService;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosProprietarioService = parametrosProprietarioService;
            _bizApiClient = bizApiClient;
        }

        public void SetarPropriedadesPagamentoFrete(ViagemIntegrarRequestModel @params, Viagem viagem, bool isInsert)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    SetarPropriedadesPagamentoFreteV2(@params, viagem, isInsert);
                    break;
                case EVersaoAntt.Versao3:
                    SetarPropriedadesPagamentoFreteV3(@params, viagem, isInsert);
                    break;
                default:
                    SetarPropriedadesPagamentoFreteV2(@params, viagem, isInsert);
                    break;
            }
        }
        
        private void SetarPropriedadesPagamentoFreteV2(ViagemIntegrarRequestModel @params, Viagem viagem, bool isInsert)
        {
            if (@params.ValorMercadoria.HasValue)
                viagem.ValorMercadoria = @params.ValorMercadoria;

            if (@params.Pedagio != null && @params.Pedagio.ValorPedagio.HasValue && @params.Pedagio.ValorPedagio.Value > 0)
                viagem.ValorPedagio = @params.Pedagio.ValorPedagio.Value;

            if (@params.PedagioBaixado.HasValue)
                viagem.PedagioBaixado = @params.PedagioBaixado.Value;

            viagem.CPFCNPJProprietario = @params.CPFCNPJProprietario.DefaultIfEmpty(viagem.CPFCNPJProprietario);
            viagem.NomeProprietario = @params.NomeProprietario.DefaultIfEmpty(viagem.NomeProprietario);
            if (@params.RNTRC.HasValue)
                viagem.RNTRC = @params.RNTRC;

            if (!string.IsNullOrWhiteSpace(@params.Coleta))
                viagem.Coleta = @params.Coleta;

            if (!string.IsNullOrWhiteSpace(@params.Entrega))
                viagem.Entrega = @params.Entrega;

            if (!string.IsNullOrWhiteSpace(@params.NumeroCartao))
                viagem.NumeroCartao = @params.NumeroCartao;

            switch (@params.StatusViagem)
            {
                case EStatusViagem.Aberto:
                    viagem.StatusViagem = EStatusViagem.Aberto;
                    break;
                case EStatusViagem.Baixada:
                    viagem.StatusViagem = EStatusViagem.Baixada;
                    break;
                case EStatusViagem.Bloqueada:
                    viagem.StatusViagem = EStatusViagem.Bloqueada;
                    break;
                case EStatusViagem.Programada:
                    viagem.StatusViagem = EStatusViagem.Programada;
                    break;
                case EStatusViagem.Cancelada:
                    viagem.StatusViagem = EStatusViagem.Cancelada;
                    break;
                default:
                    viagem.StatusViagem = EStatusViagem.Aberto;
                    break;
            }

            #region Regras da Viagem

            if (@params.ViagemRegra != null && @params.ViagemRegra.Any())
            {
                if (viagem.ViagemRegras == null) viagem.ViagemRegras = new List<ViagemRegra>();
                foreach (var viagemRegra in @params.ViagemRegra)
                {
                    if (viagemRegra.TipoQuebraMercadoria.HasValue && ((int) viagemRegra.TipoQuebraMercadoria.Value < 0 || (int) viagemRegra.TipoQuebraMercadoria.Value > 1))
                        throw new ApplicationException("Tipo de quebra de mercadoria informada é inválida. ");

                    if (viagemRegra.IdViagemRegra.HasValue)
                    {
                        var regra = viagem.ViagemRegras.FirstOrDefault(x => viagemRegra.IdViagemRegra != null && x.IdViagemRegra == viagemRegra.IdViagemRegra.Value);
                        if (regra == null)
                            throw new ApplicationException($"Não foi possível identificar a regra pelo código informado. Código: {viagemRegra.IdViagemRegra}");
                        if (viagemRegra.TarifaTonelada.HasValue)
                            regra.TarifaTonelada = viagemRegra.TarifaTonelada;
                        if (viagemRegra.TaxaAntecipacao.HasValue)
                            regra.TaxaAntecipacao = viagemRegra.TaxaAntecipacao;
                        if (viagemRegra.TipoQuebraMercadoria.HasValue)
                            regra.TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria.Value;
                        if (viagemRegra.ToleranciaPeso.HasValue)
                            regra.ToleranciaPeso = viagemRegra.ToleranciaPeso;
                        if (viagemRegra.TotalFrete.HasValue)
                            regra.TotalFrete = viagemRegra.TotalFrete;

                        regra.FreteLotacao = viagemRegra.FreteLotacao;
                    }
                    else
                    {
                        viagem.ViagemRegras.Add(new ViagemRegra
                        {
                            IdEmpresa = viagem.IdEmpresa,
                            TarifaTonelada = viagemRegra.TarifaTonelada,
                            TaxaAntecipacao = viagemRegra.TaxaAntecipacao,
                            TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria ??
                                                   ETipoQuebraMercadoria.Diferenca,
                            ToleranciaPeso = viagemRegra.ToleranciaPeso,
                            TotalFrete = viagemRegra.TotalFrete,
                            FreteLotacao = viagemRegra.FreteLotacao
                        });
                    }
                }
            }

            #endregion

            #region Estabelecimentos da viagem

            if (@params.ViagemEstabelecimentos != null && @params.ViagemEstabelecimentos.Any())
            {
                if (viagem.ViagemEstabelecimentos == null)
                    viagem.ViagemEstabelecimentos = new List<ViagemEstabelecimento>();
                foreach (var viagemEstabelecimento in @params.ViagemEstabelecimentos)
                    //Estamos editando um registro já existente
                    if (viagemEstabelecimento.IdViagemEstabelecimento.HasValue)
                    {
                        var estabelecimento = viagem.ViagemEstabelecimentos.FirstOrDefault(x => x.IdViagemEstabelecimento == viagemEstabelecimento.IdViagemEstabelecimento.Value);
                        if (estabelecimento == null)
                            throw new ApplicationException($"Não foi possível identificar o estabelecimento pelo código informado. Código: {viagemEstabelecimento.IdViagemEstabelecimento}");
                        if (viagemEstabelecimento.TipoEventoViagem.HasValue)
                            estabelecimento.TipoEventoViagem = viagemEstabelecimento.TipoEventoViagem.Value;
                        if (!viagemEstabelecimento.IdEstabelecimento.HasValue) continue;
                        var estabelecimentoEmpresa = _estabelecimentoApp.GetByIdEstabelecimentoEmpresa(viagemEstabelecimento.IdEstabelecimento.Value, viagem.IdEmpresa);

                        if (estabelecimentoEmpresa == null || !estabelecimentoEmpresa.Ativo)
                            throw new ApplicationException($"Estabelecimento de código {viagemEstabelecimento.IdEstabelecimento} inválido para a empresa da viagem. Verifique o credenciamento!");

                        estabelecimento.IdEstabelecimento = estabelecimentoEmpresa.IdEstabelecimento;
                    }
                    else //Estamos adicionando um novo registro
                    {
                        if (viagem.ViagemEstabelecimentos != null && viagem.ViagemEstabelecimentos.Any(x => x.IdEstabelecimento == viagemEstabelecimento.IdEstabelecimento && x.TipoEventoViagem == viagemEstabelecimento.TipoEventoViagem))
                            continue;
                        if (!viagemEstabelecimento.TipoEventoViagem.HasValue)
                            throw new ApplicationException("Tipo de evento da viagem não foi informado na integração de estabelecimento. ");
                        var estabelecimentoAdd = new ViagemEstabelecimento
                        {
                            TipoEventoViagem = viagemEstabelecimento.TipoEventoViagem.Value
                        };
                        if (!viagemEstabelecimento.IdEstabelecimento.HasValue)
                            throw new ApplicationException("Código do estabelecimento não foi informado.");

                        var estabelecimentoEmpresa = _estabelecimentoApp.GetByIdEstabelecimentoEmpresa(viagemEstabelecimento.IdEstabelecimento.Value, viagem.IdEmpresa);

                        if (estabelecimentoEmpresa == null || !estabelecimentoEmpresa.Ativo)
                            throw new ApplicationException($"Estabelecimento de código {viagemEstabelecimento.IdEstabelecimento} inválido para a empresa da viagem. Verifique o credenciamento!");

                        estabelecimentoAdd.IdEstabelecimento = estabelecimentoEmpresa.IdEstabelecimento;
                        estabelecimentoAdd.IdEmpresa = viagem.IdEmpresa;
                        viagem.ViagemEstabelecimentos.Add(estabelecimentoAdd);
                    }
            }

            #endregion

            #region Eventos da viagem

            if (@params.ViagemEventos == null || !@params.ViagemEventos.Any()) return;

            if (viagem.ViagemEventos == null) viagem.ViagemEventos = new List<ViagemEvento>();


            var agendamentos = @params.ViagemEventos.Where(x => x.Status == EStatusViagemEvento.Agendado || x.DataAgendamentoPagamento.HasValue).ToArray();
            if (agendamentos.Any())
            {
                var habilitarAgendamentoPagamentoFrete = _empresaRepository.HabilitarAgendamentoPagamentoFrete(viagem.IdEmpresa);
                if (!habilitarAgendamentoPagamentoFrete)
                    throw new ApplicationException("Agendamento de pagamento não disponível para a empresa.");

                var agendamentosSemData = agendamentos.Where(x => x.DataAgendamentoPagamento == null);
                var planejamentosSemStatus = agendamentos.Where(x => x.Status != EStatusViagemEvento.Agendado);
                var agendamentosComDataInvalida = agendamentos.Where(x => x.DataAgendamentoPagamento != null && x.DataAgendamentoPagamento <= DateTime.Now.EndOfDay());
                var agendamentoSemPagamentoCartao = agendamentos.Where(x => x.Status == EStatusViagemEvento.Agendado && !x.HabilitarPagamentoCartao);

                if (agendamentosSemData.Any())
                    throw new ApplicationException("Não é permitido status \"Agendado\" sem \"DataAgendamentoPagamento\".");
                if (planejamentosSemStatus.Any())
                    throw new ApplicationException("Não é permitido status diferente de \"Agendado\" ao informar \"DataAgendamentoPagamento\".");
                if (agendamentosComDataInvalida.Any())
                    throw new ApplicationException("Não é permitido agendar pagamento para data menor ou igual a hoje.");
                if (agendamentoSemPagamentoCartao.Any())
                    throw new ApplicationException("Não é permitido agendar pagamento com o campo \"HabilitarPagamentoCartao\" falso.");
            }

            var eventosIgnorados = new List<ViagemEventoIntegrarModel>();

            var empresaPix = _parametrosEmpresaService.GetPermiteRealizarPagamentoPix(viagem.IdEmpresa);
            var naoBaixarParcelasDeposito = empresaPix && _parametrosEmpresaService.GetNaoBaixarParcelasDeposito(viagem.IdEmpresa);
            var proprietarioPix = _parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(viagem.CPFCNPJProprietario, viagem.IdEmpresa);
            
            foreach (var viagemEvento in @params.ViagemEventos)
            {
                if (viagemEvento.TipoEvento.HasValue && (viagemEvento.TipoEvento.Value < 0 || (int) viagemEvento.TipoEvento.Value > 5))
                    throw new ApplicationException("Tipo de evento informado para a viagem é inválido. ");

                var habilitarPagamentocartao = viagemEvento.HabilitarPagamentoCartao;
                var habilitarPagamentoTicket = viagemEvento.DadosAbastecimento?.Fornecedor == EFornecedorPedagio.TicketLog;

                //Se veio do portal marcado como pix
                if (viagemEvento.HabilitarPagamentoPix == true)
                {
                    if (!empresaPix)
                        throw new ApplicationException("Empresa sem permissão para realizar pagamentos Pix. ");
                    if (!proprietarioPix)
                        throw new ApplicationException("Proprietário não informado ou sem permissão para receber pagamentos Pix pela empresa. ");
                }
                
                //Se veio do portal marcado como pix ou se caiu na regra da integracao de nao ser cartao e tiver os dois parametros
                var habilitarPagamentoPix = viagemEvento.HabilitarPagamentoPix ?? !viagemEvento.HabilitarPagamentoCartao && empresaPix && proprietarioPix;

                //Validacao do parametro de nao baixar parcelas deposito pra proprietarios sem chave cadastrada
                if (naoBaixarParcelasDeposito && !viagemEvento.IdViagemEvento.HasValue && !habilitarPagamentoPix && 
                    !habilitarPagamentocartao && viagemEvento.Status == EStatusViagemEvento.Baixado && empresaPix)
                {
                    var dadosPix = _bizApiClient.GetDadosBancariosPix(@params.CNPJEmpresa, viagem.CPFCNPJProprietario)?.Value;
                    if (string.IsNullOrWhiteSpace(dadosPix?.ChavePix))
                        viagemEvento.Status = EStatusViagemEvento.Aberto;
                }
                
                //Estamos editando o registro da viagem evento
                if (viagemEvento.IdViagemEvento.HasValue)
                {
                    var evento = viagem.ViagemEventos.FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento.Value);
                    if (evento == null)
                        throw new ApplicationException($"Não foi possível identificar o evento pelo código informado. Código: {viagemEvento.IdViagemEvento}");
                    
                    if (!string.IsNullOrWhiteSpace(viagemEvento.CpfUsuario))
                        evento.CpfUsuario = viagemEvento.CpfUsuario;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.NomeUsuario))
                        evento.NomeUsuario = viagemEvento.NomeUsuario;

                    if (evento.HabilitarPagamentoPix == true && evento.Status == EStatusViagemEvento.Bloqueado && viagemEvento.Status != EStatusViagemEvento.Bloqueado)
                        throw new Exception("Evento Pix com status 'Bloqueado' não permite alteração pois está pendente de análise do gestor.");

                    if (naoBaixarParcelasDeposito && evento.Status != EStatusViagemEvento.Baixado && evento.HabilitarPagamentoPix != true &&
                        !evento.HabilitarPagamentoCartao && empresaPix && viagemEvento.Status == EStatusViagemEvento.Baixado)
                    {
                        var dadosPix = _bizApiClient.GetDadosBancariosPix(@params.CNPJEmpresa, viagem.CPFCNPJProprietario)?.Value;
                        if (string.IsNullOrWhiteSpace(dadosPix?.ChavePix))
                            throw new Exception($"Empresa configurada para não baixar parcelas depósito para proprietários sem chave Pix cadastrada.");
                    }

                    if (evento.HabilitarPagamentoCartao || evento.HabilitarPagamentoPix == true)
                    {
                        if (evento.Status == EStatusViagemEvento.Cancelado &&
                            viagemEvento.Status != EStatusViagemEvento.Cancelado)
                            throw new Exception("Status do evento da viagem 'Cancelado' não permite alteração no meio homologado.");

                        if (evento.Status == EStatusViagemEvento.Baixado &&
                            (viagemEvento.Status == EStatusViagemEvento.Aberto ||
                             viagemEvento.Status == EStatusViagemEvento.Bloqueado))
                            throw new Exception($"Status do evento da viagem 'Baixado' não permite alteração no meio homologado para {viagemEvento.Status.GetDescription()}.");

                        if (evento.Status == viagemEvento.Status)
                        {
                            if(evento.Status == EStatusViagemEvento.Cancelado)
                                if (viagemEvento.ValorPagamento != (evento.ValorTotalPagamento ?? evento.ValorPagamento))
                                    throw new Exception($"O valor do evento {evento.TipoEventoViagem.DescriptionAttr()} não pode ser alterado quando o pagamento está cancelado.");
                                else
                                    eventosIgnorados.Add(viagemEvento);

                            if (evento.Status == EStatusViagemEvento.Baixado)
                                if (viagemEvento.ValorPagamento != (evento.ValorTotalPagamento ?? evento.ValorPagamento))
                                    throw new Exception($"O valor do evento {evento.TipoEventoViagem.DescriptionAttr()} não pode ser alterado quando o pagamento está efetuado.");
                                else
                                    eventosIgnorados.Add(viagemEvento);
                        }
                    }

                    #region Validacoes do meio homologado

                    if (viagemEvento.DadosAbastecimento != null && viagemEvento.DadosAbastecimento.Fornecedor == EFornecedorPedagio.TicketLog)
                    {
                        var eventoOriginal = _viagemEventoApp.GetQueryable(viagemEvento.IdViagemEvento.Value)
                            .Select(c => new
                            {
                                c.Status,
                                c.TipoEventoViagem,
                                c.IdAbastecimentoticket
                            }).FirstOrDefault();

                        switch (eventoOriginal.Status)
                        {
                            case EStatusViagemEvento.Aberto:
                            case EStatusViagemEvento.Agendado:
                                if (viagemEvento.Status != null)
                                    switch (viagemEvento.Status)
                                    {
                                        case EStatusViagemEvento.Aberto:
                                        case EStatusViagemEvento.Bloqueado:
                                        case EStatusViagemEvento.Cancelado:

                                            viagemEvento.DadosAbastecimento = null;
                                            break;
                                    }

                                break;

                            case EStatusViagemEvento.Baixado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Aberto' ou 'Bloqueado' quando o pagamento está efetuado.");

                                    case EStatusViagemEvento.Baixado:

                                        if (eventoOriginal?.IdAbastecimentoticket == null)
                                            viagemEvento.DadosAbastecimento = null;

                                        break;
                                }

                                break;

                            case EStatusViagemEvento.Bloqueado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                    case EStatusViagemEvento.Cancelado:

                                        viagemEvento.DadosAbastecimento = null;
                                        break;
                                }

                                break;

                            case EStatusViagemEvento.Cancelado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                    case EStatusViagemEvento.Baixado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Aberto', 'Bloqueado' ou 'Baixado' quando o pagamento está cancelado.");

                                    case EStatusViagemEvento.Cancelado:
                                        viagemEvento.DadosAbastecimento = null;
                                        break;
                                }

                                break;
                        }
                    }

                    if (viagemEvento.HabilitarPagamentoCartao)
                    {
                        var eventoOriginal = _viagemEventoApp.GetQueryable(viagemEvento.IdViagemEvento.Value)
                            .Select(c => new
                            {
                                c.Status,
                                c.TipoEventoViagem
                            }).FirstOrDefault();

                        switch (eventoOriginal.Status)
                        {
                            case EStatusViagemEvento.Aberto:
                            case EStatusViagemEvento.Agendado:
                                if (viagemEvento.Status != null)
                                    switch (viagemEvento.Status)
                                    {
                                        case EStatusViagemEvento.Aberto:
                                        case EStatusViagemEvento.Bloqueado:
                                        case EStatusViagemEvento.Cancelado:
                                            viagemEvento.HabilitarPagamentoCartao = false;
                                            break;
                                        case EStatusViagemEvento.Agendado:
                                        case EStatusViagemEvento.Baixado:
                                            viagemEvento.HabilitarPagamentoCartao = true;
                                            //viagemEvento.Status = EStatusViagemEvento.Aberto;
                                            break;
                                        default:
                                            throw new InvalidOperationException($"Ocorreu uma mudança de Status inesperada para o evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} de Id {viagemEvento.IdViagemEvento}");
                                    }

                                break;

                            case EStatusViagemEvento.Baixado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Aberto' ou 'Bloqueado' quando o pagamento está efetuado.");
                                    case EStatusViagemEvento.Cancelado:
                                        viagemEvento.HabilitarPagamentoCartao = true;
                                        break;
                                    case EStatusViagemEvento.Baixado:
                                        var transacaoExistente = _transacaoCartaoApp.GetAllByIdEvento(viagemEvento.IdViagemEvento.Value).FirstOrDefault();
                                        viagemEvento.HabilitarPagamentoCartao = transacaoExistente?.StatusPagamento != EStatusPagamentoCartao.Baixado;
                                        break;
                                    case EStatusViagemEvento.Agendado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Agendado' quando o pagamento está efetuado.");
                                    default:
                                        throw new InvalidOperationException($"Ocorreu uma mudança de Status inesperada para o evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} de Id {viagemEvento.IdViagemEvento}");
                                }

                                break;

                            case EStatusViagemEvento.Bloqueado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                    case EStatusViagemEvento.Cancelado:
                                        viagemEvento.HabilitarPagamentoCartao = false;
                                        break;
                                    case EStatusViagemEvento.Agendado:
                                    case EStatusViagemEvento.Baixado:
                                        viagemEvento.HabilitarPagamentoCartao = true;
                                        //viagemEvento.Status = EStatusViagemEvento.Aberto;
                                        break;
                                    default:
                                        throw new InvalidOperationException($"Ocorreu uma mudança de Status inesperada para o evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} de Id {viagemEvento.IdViagemEvento}");
                                }

                                break;

                            case EStatusViagemEvento.Cancelado:

                                switch (viagemEvento.Status)
                                {
                                    case EStatusViagemEvento.Aberto:
                                    case EStatusViagemEvento.Bloqueado:
                                    case EStatusViagemEvento.Baixado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Aberto', 'Bloqueado' ou 'Baixado' quando o pagamento está cancelado.");
                                    case EStatusViagemEvento.Agendado:
                                        throw new Exception($"O status do evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} não pode ser alterado para 'Agendado' quando o pagamento está cancelado.");
                                    case EStatusViagemEvento.Cancelado:
                                        viagemEvento.HabilitarPagamentoCartao = false;
                                        break;
                                    default:
                                        throw new InvalidOperationException($"Ocorreu uma mudança de Status inesperada para o evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} de Id {viagemEvento.IdViagemEvento}");
                                }

                                break;

                            default:
                                throw new InvalidOperationException($"Ocorreu uma mudança de Status inesperada para o evento {eventoOriginal.TipoEventoViagem.DescriptionAttr()} de Id {viagemEvento.IdViagemEvento}");
                        }
                    }

                    #endregion

                    if (string.IsNullOrWhiteSpace(evento.Token))
                        evento.Token = _viagemApp.GerarTokenViagemEvento();
                    
                    evento.HabilitarPagamentoCartao = habilitarPagamentocartao;
                    evento.HabilitaPagamentoTicket = habilitarPagamentoTicket;
                    evento.HabilitarPagamentoPix = habilitarPagamentoPix;
                    if (habilitarPagamentoPix) viagemEvento.HabilitarPagamentoPix = true;

                    if (viagemEvento.TipoEvento.HasValue)
                        evento.TipoEventoViagem = viagemEvento.TipoEvento.Value;

                    if (viagemEvento.ValorPagamento.HasValue)
                    {
                        if (isInsert || viagemEvento.Status != EStatusViagemEvento.Baixado)
                            evento.ValorPagamento = viagemEvento.ValorPagamento.Value;

                        if (viagemEvento.Status == EStatusViagemEvento.Baixado &&
                            (isInsert || evento.Status != EStatusViagemEvento.Baixado))
                        {
                            evento.ValorTotalPagamento = viagemEvento.ValorPagamento.Value;
                            evento.ValorPagamento = viagemEvento.ValorPagamento.Value;
                        }
                    }

                    if (viagemEvento.Status.HasValue && !evento.HabilitarPagamentoCartao)
                    {
                        if (viagemEvento.Status == EStatusViagemEvento.Cancelado && evento.Status == EStatusViagemEvento.Cancelado)
                            continue;
                        
                        if (evento.Status == EStatusViagemEvento.Cancelado)
                            throw new Exception("O status do evento cancelado não pode ser alterado.");
                        
                        if (viagemEvento.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado) &&
                            evento.Status == EStatusViagemEvento.Baixado)
                            throw new Exception($"O status do evento {evento.Status.DescriptionAttr()} não pode ser alterado para 'Aberto' ou 'Bloqueado' quando o pagamento está baixado.");

                        if (viagemEvento.Status == EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Baixado)
                        {
                            evento.DataHoraPagamento = DateTime.Now;
                            evento.ValorTotalPagamento = evento.ValorPagamento;
                        }
                        else if (viagemEvento.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                            evento.DataHoraCancelamento = DateTime.Now;

                        evento.Status = viagemEvento.Status.Value;
                    }

                    if (viagemEvento.DataValidade.HasValue)
                        evento.DataValidade = viagemEvento.DataValidade;
                    if (viagemEvento.IRRPF.HasValue)
                        evento.IRRPF = viagemEvento.IRRPF.Value;
                    if (viagemEvento.SESTSENAT.HasValue)
                        evento.SESTSENAT = viagemEvento.SESTSENAT.Value;
                    if (viagemEvento.INSS.HasValue)
                        evento.INSS = viagemEvento.INSS.Value;
                    if (viagemEvento.ValorBruto.HasValue)
                        evento.ValorBruto = viagemEvento.ValorBruto.Value;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.NumeroRecibo))
                        evento.NumeroRecibo = viagemEvento.NumeroRecibo;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.MotivoBloqueio))
                        evento.MotivoBloqueio = viagemEvento.MotivoBloqueio;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.Instrucao))
                        evento.Instrucao = viagemEvento.Instrucao;

                    if (viagemEvento.DataAgendamentoPagamento.HasValue)
                        evento.DataAgendamentoPagamento = viagemEvento.DataAgendamentoPagamento;
                    else if (viagemEvento.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Baixado)
                        evento.DataAgendamentoPagamento = null;

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado || viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        if (evento.Status != EStatusViagemEvento.Baixado && viagemEvento.Status != EStatusViagemEvento.Cancelado)
                            evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if ((viagemEvento.HabilitarPagamentoCartao || habilitarPagamentoTicket) && viagemEvento.Status == EStatusViagemEvento.Baixado)
                    {
                        if (isInsert && evento.Status != EStatusViagemEvento.Baixado)
                            evento.Status = EStatusViagemEvento.Aberto;
                    }
                    // inicialmente salva o evento como aberto para caso algum processo blocante impeça o pagamento, nao retorne como baixado
                    if (viagemEvento.HabilitarPagamentoPix == true)
                    {
                        evento.Status = EStatusViagemEvento.Aberto;
                    }
                    else if (viagemEvento.Status.HasValue)
                    {
                        evento.Status = viagemEvento.Status.Value;
                    }

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado || viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if (viagemEvento.IdMotivo.HasValue)
                    {
                        var motivo = _motivoCredenciamentoService.Get(viagemEvento.IdMotivo.Value);
                        if (motivo == null)
                            throw new ApplicationException("Motivo não cadastrado");
                        if (motivo.IdEmpresa != viagem.IdEmpresa)
                            throw new ApplicationException("Motivo não cadastrado");

                        if (viagem.IdFilial == null && motivo.IdFilial != null)
                            throw new ApplicationException("Motivo não cadastrado");
                        if (motivo.IdFilial != null && viagem.IdFilial != motivo.IdFilial)
                            throw new ApplicationException("Motivo não cadastrado");

                        evento.IdMotivo = motivo.IdMotivo;
                    }

                    #region Documentos do evento da viagem

                    //No caso da edição de um registro pai, devemos verificar se precisamos editar ou adicionar um registro filho.
                    if (viagemEvento.ViagemDocumentos != null && viagemEvento.ViagemDocumentos.Any())
                        foreach (var viagemDocumento in viagemEvento.ViagemDocumentos)
                        {
//                            if (viagemDocumento.TipoEvento.HasValue && ((int) viagemDocumento.TipoEvento.Value < 0 || (int) viagemDocumento.TipoEvento.Value > 2))
//                                throw new ApplicationException("Tipo de evento informado para o documento da viagem é inválido. ");

                            if (viagemDocumento.TipoDocumento.HasValue && ((int) viagemDocumento.TipoDocumento.Value < 0 || (int) viagemDocumento.TipoDocumento.Value > 4))
                                throw new ApplicationException("Tipo de documento informado é inválido. ");

                            //Caso seja informado a id do documento, significa que estamos editando-o
                            if (viagemDocumento.IdViagemDocumento.HasValue)
                            {
                                var documento = evento.ViagemDocumentos.FirstOrDefault(x => x.IdViagemDocumento == viagemDocumento.IdViagemDocumento.Value);
                                if (documento != null)
                                {
                                    if (viagemDocumento.TipoEvento.HasValue)
                                        documento.TipoEvento = viagemDocumento.TipoEvento.Value;
                                    if (viagemDocumento.TipoDocumento.HasValue)
                                        documento.TipoDocumento = viagemDocumento.TipoDocumento.Value;

                                    documento.Descricao = viagemDocumento.Descricao;
                                    documento.NumeroDocumento = viagemDocumento.NumeroDocumento;
                                    if (viagemDocumento.ObrigaAnexo.HasValue)
                                        documento.ObrigaAnexo = viagemDocumento.ObrigaAnexo.Value;

                                    if (viagemDocumento.ObrigaAnexoFilial.HasValue)
                                        documento.ObrigaAnexoFilial = viagemDocumento.ObrigaAnexoFilial.Value;

                                    if (viagemDocumento.ObrigaAnexoMatriz.HasValue)
                                        documento.ObrigaAnexoMatriz = viagemDocumento.ObrigaAnexoMatriz.Value;

                                    if (viagemDocumento.ObrigaDocOriginal.HasValue)
                                        documento.ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal.Value;
                                }
                            }
                            else
                            {
                                if (!viagemDocumento.TipoDocumento.HasValue)
                                    throw new ApplicationException("Tipo de documento do documento da viagem não informado. ");
                                if (!viagemDocumento.TipoEvento.HasValue)
                                    throw new ApplicationException("Tipo de evento do documento da viagem não informado. ");
                                if (!viagemDocumento.ObrigaAnexo.HasValue)
                                    throw new ApplicationException("Obrigatoriedade do documento não informado. ");

                                evento.ViagemDocumentos.Add(new ViagemDocumento
                                {
                                    TipoEvento = viagemDocumento.TipoEvento.Value,
                                    TipoDocumento = viagemDocumento.TipoDocumento.Value,
                                    Descricao = viagemDocumento.Descricao,
                                    NumeroDocumento = viagemDocumento.NumeroDocumento,
                                    ObrigaAnexo = viagemDocumento.ObrigaAnexo.Value,
                                    ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal.HasValue ? viagemDocumento.ObrigaDocOriginal.Value : false
                                });
                            }
                        }

                    #endregion

                    #region Valores adicionais do evento da viagem

                    if (viagemEvento.ViagemOutrosDescontos != null && viagemEvento.ViagemOutrosDescontos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosDescontos)
                            if (valorAdicional.IdViagemValorAdicional.HasValue)
                            {
                                var valor = evento.ViagemValoresAdicionais.FirstOrDefault(
                                    x => x.IdViagemValorAdicional == valorAdicional.IdViagemValorAdicional);
                                if (valor == null)
                                    throw new ApplicationException($"Não foi possível identificar o desconto pelo código informado. Código: {valorAdicional.IdViagemValorAdicional}");

                                valor.Descricao = valorAdicional.Descricao;
                                valor.NumeroDocumento = valorAdicional.NumeroDocumento;

                                valor.Valor = valorAdicional.Valor;
                                valor.Tipo = ETipoValorAdicional.Desconto;
                                if (valorAdicional.CodigoERP.HasValue)
                                    valor.CodigoERP = valorAdicional.CodigoERP;
                            }
                            else
                            {
                                evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                                {
                                    Descricao = valorAdicional.Descricao,
                                    NumeroDocumento = valorAdicional.NumeroDocumento,
                                    Valor = valorAdicional.Valor,
                                    Tipo = ETipoValorAdicional.Desconto,
                                    CodigoERP = valorAdicional.CodigoERP
                                });
                            }
                    }

                    if (viagemEvento.ViagemOutrosAcrescimos != null && viagemEvento.ViagemOutrosAcrescimos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosAcrescimos)
                            if (valorAdicional.IdViagemValorAdicional.HasValue)
                            {
                                var valor = evento.ViagemValoresAdicionais.FirstOrDefault(
                                    x => x.IdViagemValorAdicional == valorAdicional.IdViagemValorAdicional);
                                if (valor == null)
                                    throw new ApplicationException($"Não foi possível identificar o desconto pelo código informado. Código: {valorAdicional.IdViagemValorAdicional}");

                                valor.Descricao = valorAdicional.Descricao;
                                valor.NumeroDocumento = valorAdicional.NumeroDocumento;
                                if (valorAdicional.CodigoERP.HasValue)
                                    valor.CodigoERP = valorAdicional.CodigoERP;
                                valor.Valor = valorAdicional.Valor;
                                valor.Tipo = ETipoValorAdicional.Acrescimo;
                            }
                            else
                            {
                                evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                                {
                                    Descricao = valorAdicional.Descricao,
                                    NumeroDocumento = valorAdicional.NumeroDocumento,
                                    Valor = valorAdicional.Valor,
                                    Tipo = ETipoValorAdicional.Acrescimo,
                                    CodigoERP = valorAdicional.CodigoERP
                                });
                            }
                    }

                    #endregion
                }
                else //Estamos adicionando um novo evento
                {
                    if (!viagemEvento.TipoEvento.HasValue)
                        throw new ApplicationException("Tipo do evento não informado para um dos eventos da viagem.");

                    if (!viagemEvento.Status.HasValue)
                        throw new ApplicationException("Status do evento não informado para um dos eventos da viagem.");

                    var evento = new ViagemEvento
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        TipoEventoViagem = viagemEvento.TipoEvento.Value,
                        ValorPagamento = viagemEvento.ValorPagamento ?? 0,
                        Status = viagemEvento.Status.Value,
                        DataValidade = viagemEvento.DataValidade,
                        Token = _viagemApp.GerarTokenViagemEvento(),
                        INSS = viagemEvento.INSS ?? 0,
                        IRRPF = viagemEvento.IRRPF ?? 0,
                        SESTSENAT = viagemEvento.SESTSENAT ?? 0,
                        Instrucao = viagemEvento.Instrucao,
                        ValorBruto = viagemEvento.ValorBruto ?? 0,
                        NumeroRecibo = viagemEvento.NumeroRecibo,
                        MotivoBloqueio = viagemEvento.MotivoBloqueio,
                        NumeroControle = viagemEvento.NumeroControle,
                        CpfUsuario = viagemEvento.CpfUsuario,
                        NomeUsuario = viagemEvento.NomeUsuario,
                        DataAgendamentoPagamento = viagemEvento.DataAgendamentoPagamento
                    };

                    evento.HabilitarPagamentoCartao = habilitarPagamentocartao;
                    evento.HabilitaPagamentoTicket = habilitarPagamentoTicket;
                    evento.HabilitarPagamentoPix = habilitarPagamentoPix;
                    if (habilitarPagamentoPix) viagemEvento.HabilitarPagamentoPix = true;

                    if (evento.HabilitarPagamentoPix == true && (evento.Status != EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Aberto))
                        throw new ApplicationException("Integração de parcelas Pix permitida apenas com status 'Baixado' ou 'Aberto'. ");

                    // Usado para identificar o pagamento quando ainda não tem o IdViagemEvento na memória
                    viagemEvento.Token = evento.Token;

                    if ((viagemEvento.HabilitarPagamentoCartao || habilitarPagamentoTicket) && viagemEvento.Status == EStatusViagemEvento.Baixado)
                        evento.Status = EStatusViagemEvento.Aberto;

                    // inicialmente salva o evento como aberto para caso algum processo blocante impeça o pagamento, nao retorne como baixado
                    if (viagemEvento.HabilitarPagamentoPix == true)
                        evento.Status = EStatusViagemEvento.Aberto;

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado || viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        if (evento.Status != EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Cancelado)
                            evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if (evento.Status == EStatusViagemEvento.Baixado)
                    {
                        evento.DataHoraPagamento = DateTime.Now;
                        evento.ValorTotalPagamento = evento.ValorPagamento;
                    }
                    else if (evento.Status == EStatusViagemEvento.Cancelado)
                        evento.DataHoraCancelamento = DateTime.Now;

                    #region Documentos da viagem

                    if (viagemEvento.ViagemDocumentos != null && viagemEvento.ViagemDocumentos.Any())
                    {
                        if (evento.ViagemDocumentos == null)
                            evento.ViagemDocumentos = new List<ViagemDocumento>();
                        foreach (var viagemDocumento in viagemEvento.ViagemDocumentos)
                        {
                            if (!viagemDocumento.TipoEvento.HasValue)
                                throw new ApplicationException("Tipo do evento não informado para um dos documentos do evento da viagem. ");
                            if (!viagemDocumento.TipoDocumento.HasValue)
                                throw new ApplicationException("Tipo de documento não informado para um dos documentos do evento da viagem. ");
                            if ((int) viagemDocumento.TipoDocumento.Value < 0 || (int) viagemDocumento.TipoDocumento.Value > 4)
                                throw new ApplicationException("Tipo de documento informado é inválido. ");
                            if (!viagemDocumento.ObrigaAnexo.HasValue)
                                throw new ApplicationException("Obrigatoriedade de documento não informado para um dos documentos do evento da viagem. ");
                            evento.ViagemDocumentos.Add(new ViagemDocumento
                            {
                                TipoEvento = viagemDocumento.TipoEvento.Value,
                                TipoDocumento = viagemDocumento.TipoDocumento.Value,
                                Descricao = viagemDocumento.Descricao,
                                NumeroDocumento = viagemDocumento.NumeroDocumento,
                                ObrigaAnexo = viagemDocumento.ObrigaAnexo ?? false,
                                ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal ?? false,
                                ObrigaAnexoFilial = viagemDocumento.ObrigaAnexoFilial ?? false,
                                ObrigaAnexoMatriz = viagemDocumento.ObrigaAnexoMatriz ?? false
                            });
                        }
                    }

                    #endregion

                    #region Valores Adicionais do evento da viagem

                    if (viagemEvento.ViagemOutrosDescontos != null &&
                        viagemEvento.ViagemOutrosDescontos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null) evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosDescontos)
                            evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                            {
                                Descricao = valorAdicional.Descricao,
                                NumeroDocumento = valorAdicional.NumeroDocumento,
                                Valor = valorAdicional.Valor,
                                Tipo = ETipoValorAdicional.Desconto,
                                CodigoERP = valorAdicional.CodigoERP
                            });
                    }

                    if (viagemEvento.ViagemOutrosAcrescimos != null &&
                        viagemEvento.ViagemOutrosAcrescimos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null) evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosAcrescimos)
                            evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                            {
                                Descricao = valorAdicional.Descricao,
                                NumeroDocumento = valorAdicional.NumeroDocumento,
                                Valor = valorAdicional.Valor,
                                Tipo = ETipoValorAdicional.Acrescimo,
                                CodigoERP = valorAdicional.CodigoERP
                            });
                    }

                    #endregion

                    viagem.ViagemEventos.Add(evento);
                }
            }

            // Caso o evento não tenha alterações é removido da request para não cair em métodos de pagamento
            eventosIgnorados.ForEach(eventoIgnorado => @params.ViagemEventos.Remove(eventoIgnorado));
            #endregion
        }
        
        private void SetarPropriedadesPagamentoFreteV3(ViagemIntegrarRequestModel @params, Viagem viagem, bool isInsert)
        {
            if (@params.ValorMercadoria.HasValue)
                viagem.ValorMercadoria = @params.ValorMercadoria;

            if (@params.Pedagio != null && @params.Pedagio.ValorPedagio.HasValue &&
                @params.Pedagio.ValorPedagio.Value > 0)
                viagem.ValorPedagio = @params.Pedagio.ValorPedagio.Value;

            if (@params.PedagioBaixado.HasValue)
                viagem.PedagioBaixado = @params.PedagioBaixado.Value;

            viagem.CPFCNPJProprietario = @params.CPFCNPJProprietario.DefaultIfEmpty(viagem.CPFCNPJProprietario);
            viagem.NomeProprietario = @params.NomeProprietario.DefaultIfEmpty(viagem.NomeProprietario);
            if (@params.RNTRC.HasValue)
                viagem.RNTRC = @params.RNTRC;

            if (!string.IsNullOrWhiteSpace(@params.Coleta))
                viagem.Coleta = @params.Coleta;

            if (!string.IsNullOrWhiteSpace(@params.Entrega))
                viagem.Entrega = @params.Entrega;

            if (!string.IsNullOrWhiteSpace(@params.NumeroCartao))
                viagem.NumeroCartao = @params.NumeroCartao;

            switch (@params.StatusViagem)
            {
                case EStatusViagem.Aberto:
                    viagem.StatusViagem = EStatusViagem.Aberto;
                    break;
                case EStatusViagem.Baixada:
                    viagem.StatusViagem = EStatusViagem.Baixada;
                    break;
                case EStatusViagem.Bloqueada:
                    viagem.StatusViagem = EStatusViagem.Bloqueada;
                    break;
                case EStatusViagem.Programada:
                    viagem.StatusViagem = EStatusViagem.Programada;
                    break;
                case EStatusViagem.Cancelada:
                    viagem.StatusViagem = EStatusViagem.Cancelada;
                    break;
                default:
                    viagem.StatusViagem = EStatusViagem.Aberto;
                    break;
            }

            #region Regras da Viagem

            if (@params.ViagemRegra != null && @params.ViagemRegra.Any())
            {
                if (viagem.ViagemRegras == null) viagem.ViagemRegras = new List<ViagemRegra>();
                foreach (var viagemRegra in @params.ViagemRegra)
                {
                    if (viagemRegra.TipoQuebraMercadoria.HasValue &&
                        ((int) viagemRegra.TipoQuebraMercadoria.Value < 0 ||
                         (int) viagemRegra.TipoQuebraMercadoria.Value > 1))
                        throw new ApplicationException("Tipo de quebra de mercadoria informada é inválida. ");

                    if (viagemRegra.IdViagemRegra.HasValue)
                    {
                        var regra = viagem.ViagemRegras.FirstOrDefault(x =>
                            viagemRegra.IdViagemRegra != null && x.IdViagemRegra == viagemRegra.IdViagemRegra.Value);
                        if (regra == null)
                            throw new ApplicationException(
                                $"Não foi possível identificar a regra pelo código informado. Código: {viagemRegra.IdViagemRegra}");
                        if (viagemRegra.TarifaTonelada.HasValue)
                            regra.TarifaTonelada = viagemRegra.TarifaTonelada;
                        if (viagemRegra.TaxaAntecipacao.HasValue)
                            regra.TaxaAntecipacao = viagemRegra.TaxaAntecipacao;
                        if (viagemRegra.TipoQuebraMercadoria.HasValue)
                            regra.TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria.Value;
                        if (viagemRegra.ToleranciaPeso.HasValue)
                            regra.ToleranciaPeso = viagemRegra.ToleranciaPeso;
                        if (viagemRegra.TotalFrete.HasValue)
                            regra.TotalFrete = viagemRegra.TotalFrete;

                        regra.FreteLotacao = viagemRegra.FreteLotacao;
                    }
                    else
                    {
                        viagem.ViagemRegras.Add(new ViagemRegra
                        {
                            IdEmpresa = viagem.IdEmpresa,
                            TarifaTonelada = viagemRegra.TarifaTonelada,
                            TaxaAntecipacao = viagemRegra.TaxaAntecipacao,
                            TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria ??
                                                   ETipoQuebraMercadoria.Diferenca,
                            ToleranciaPeso = viagemRegra.ToleranciaPeso,
                            TotalFrete = viagemRegra.TotalFrete,
                            FreteLotacao = viagemRegra.FreteLotacao
                        });
                    }
                }
            }

            #endregion

            #region Estabelecimentos da viagem

            if (@params.ViagemEstabelecimentos != null && @params.ViagemEstabelecimentos.Any())
            {
                if (viagem.ViagemEstabelecimentos == null)
                    viagem.ViagemEstabelecimentos = new List<ViagemEstabelecimento>();
                foreach (var viagemEstabelecimento in @params.ViagemEstabelecimentos)
                    //Estamos editando um registro já existente
                    if (viagemEstabelecimento.IdViagemEstabelecimento.HasValue)
                    {
                        var estabelecimento = viagem.ViagemEstabelecimentos.FirstOrDefault(x =>
                            x.IdViagemEstabelecimento == viagemEstabelecimento.IdViagemEstabelecimento.Value);
                        if (estabelecimento == null)
                            throw new ApplicationException(
                                $"Não foi possível identificar o estabelecimento pelo código informado. Código: {viagemEstabelecimento.IdViagemEstabelecimento}");
                        if (viagemEstabelecimento.TipoEventoViagem.HasValue)
                            estabelecimento.TipoEventoViagem = viagemEstabelecimento.TipoEventoViagem.Value;
                        if (!viagemEstabelecimento.IdEstabelecimento.HasValue) continue;
                        var estabelecimentoEmpresa =
                            _estabelecimentoApp.GetByIdEstabelecimentoEmpresa(
                                viagemEstabelecimento.IdEstabelecimento.Value, viagem.IdEmpresa);

                        if (estabelecimentoEmpresa == null || !estabelecimentoEmpresa.Ativo)
                            throw new ApplicationException(
                                $"Estabelecimento de código {viagemEstabelecimento.IdEstabelecimento} inválido para a empresa da viagem. Verifique o credenciamento!");

                        estabelecimento.IdEstabelecimento = estabelecimentoEmpresa.IdEstabelecimento;
                    }
                    else //Estamos adicionando um novo registro
                    {
                        if (viagem.ViagemEstabelecimentos != null && viagem.ViagemEstabelecimentos.Any(x =>
                                x.IdEstabelecimento == viagemEstabelecimento.IdEstabelecimento &&
                                x.TipoEventoViagem == viagemEstabelecimento.TipoEventoViagem))
                            continue;
                        if (!viagemEstabelecimento.TipoEventoViagem.HasValue)
                            throw new ApplicationException(
                                "Tipo de evento da viagem não foi informado na integração de estabelecimento. ");
                        var estabelecimentoAdd = new ViagemEstabelecimento
                        {
                            TipoEventoViagem = viagemEstabelecimento.TipoEventoViagem.Value
                        };
                        if (!viagemEstabelecimento.IdEstabelecimento.HasValue)
                            throw new ApplicationException("Código do estabelecimento não foi informado.");

                        var estabelecimentoEmpresa =
                            _estabelecimentoApp.GetByIdEstabelecimentoEmpresa(
                                viagemEstabelecimento.IdEstabelecimento.Value, viagem.IdEmpresa);

                        if (estabelecimentoEmpresa == null || !estabelecimentoEmpresa.Ativo)
                            throw new ApplicationException(
                                $"Estabelecimento de código {viagemEstabelecimento.IdEstabelecimento} inválido para a empresa da viagem. Verifique o credenciamento!");

                        estabelecimentoAdd.IdEstabelecimento = estabelecimentoEmpresa.IdEstabelecimento;
                        estabelecimentoAdd.IdEmpresa = viagem.IdEmpresa;
                        viagem.ViagemEstabelecimentos.Add(estabelecimentoAdd);
                    }
            }

            #endregion

            #region Eventos da viagem

            if (@params.ViagemEventos == null || !@params.ViagemEventos.Any()) return;

            if (viagem.ViagemEventos == null) viagem.ViagemEventos = new List<ViagemEvento>();
            
            var eventosIgnorados = new List<ViagemEventoIntegrarModel>();
            foreach (var viagemEvento in @params.ViagemEventos)
            {
                if (viagemEvento.TipoEvento.HasValue &&
                    (viagemEvento.TipoEvento.Value < 0 || (int) viagemEvento.TipoEvento.Value > 5))
                    throw new ApplicationException("Tipo de evento informado para a viagem é inválido. ");

                var habilitarPagamentocartao = viagem.FormaPagamento == EViagemFormaPagamento.Cartao;

                //Estamos editando o registro da viagem evento
                if (viagemEvento.IdViagemEvento.HasValue)
                {
                    var evento =
                        viagem.ViagemEventos.FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento.Value);
                    if (evento == null)
                        throw new ApplicationException(
                            $"Não foi possível identificar o evento pelo código informado. Código: {viagemEvento.IdViagemEvento}");

                    if (!string.IsNullOrWhiteSpace(viagemEvento.CpfUsuario))
                        evento.CpfUsuario = viagemEvento.CpfUsuario;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.NomeUsuario))
                        evento.NomeUsuario = viagemEvento.NomeUsuario;

                    if (evento.HabilitarPagamentoCartao)
                    {
                        if (evento.Status == EStatusViagemEvento.Cancelado &&
                            viagemEvento.Status != EStatusViagemEvento.Cancelado)
                            throw new Exception("Status do evento da viagem 'Cancelado' não permite alteração no meio homologado.");

                        if (evento.Status == EStatusViagemEvento.Baixado &&
                            (viagemEvento.Status == EStatusViagemEvento.Aberto ||
                             viagemEvento.Status == EStatusViagemEvento.Bloqueado))
                            throw new Exception($"Status do evento da viagem 'Baixado' não permite alteração no meio homologado para {viagemEvento.Status.GetDescription()}.");

                        if (evento.Status == viagemEvento.Status)
                        {
                            if(evento.Status == EStatusViagemEvento.Cancelado)
                                if (viagemEvento.ValorPagamento != (evento.ValorTotalPagamento ?? evento.ValorPagamento))
                                    throw new Exception($"O valor do evento {evento.TipoEventoViagem.DescriptionAttr()} não pode ser alterado quando o pagamento está cancelado.");
                                else
                                    eventosIgnorados.Add(viagemEvento);

                            if (evento.Status == EStatusViagemEvento.Baixado)
                               if (viagemEvento.ValorPagamento != (evento.ValorTotalPagamento ?? evento.ValorPagamento))
                                   throw new Exception($"O valor do evento {evento.TipoEventoViagem.DescriptionAttr()} não pode ser alterado quando o pagamento está efetuado.");
                               else
                                   eventosIgnorados.Add(viagemEvento);
                        }
                    }

                    if (string.IsNullOrWhiteSpace(evento.Token))
                        evento.Token = _viagemApp.GerarTokenViagemEvento();

                    evento.HabilitarPagamentoCartao = habilitarPagamentocartao;

                    if (viagemEvento.TipoEvento.HasValue)
                        evento.TipoEventoViagem = viagemEvento.TipoEvento.Value;

                    if (viagemEvento.ValorPagamento.HasValue)
                    {
                        if (isInsert || viagemEvento.Status != EStatusViagemEvento.Baixado)
                            evento.ValorPagamento = viagemEvento.ValorPagamento.Value;

                        if (viagemEvento.Status == EStatusViagemEvento.Baixado &&
                            (isInsert || evento.Status != EStatusViagemEvento.Baixado))
                        {
                            evento.ValorTotalPagamento = viagemEvento.ValorPagamento.Value;
                            evento.ValorPagamento = viagemEvento.ValorPagamento.Value;
                        }
                    }

                    if (viagemEvento.Status.HasValue && !evento.HabilitarPagamentoCartao)
                    {
                        if (viagemEvento.Status == EStatusViagemEvento.Cancelado && evento.Status == EStatusViagemEvento.Cancelado)
                            continue;
                        
                        if (evento.Status == EStatusViagemEvento.Cancelado)
                            throw new Exception("O status do evento cancelado não pode ser alterado.");
                        
                        if (viagemEvento.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado) &&
                            evento.Status == EStatusViagemEvento.Baixado)
                            throw new Exception($"O status do evento {evento.Status.DescriptionAttr()} não pode ser alterado para 'Aberto' ou 'Bloqueado' quando o pagamento está baixado.");

                        if (viagemEvento.Status == EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Baixado)
                        {
                            evento.DataHoraPagamento = DateTime.Now;
                            evento.ValorTotalPagamento = evento.ValorPagamento;
                        }
                        else if (viagemEvento.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                            evento.DataHoraCancelamento = DateTime.Now;

                        evento.Status = viagemEvento.Status.Value;
                    }

                    if (viagemEvento.DataValidade.HasValue)
                        evento.DataValidade = viagemEvento.DataValidade;
                    if (viagemEvento.IRRPF.HasValue)
                        evento.IRRPF = viagemEvento.IRRPF.Value;
                    if (viagemEvento.SESTSENAT.HasValue)
                        evento.SESTSENAT = viagemEvento.SESTSENAT.Value;
                    if (viagemEvento.INSS.HasValue)
                        evento.INSS = viagemEvento.INSS.Value;
                    if (viagemEvento.ValorBruto.HasValue)
                        evento.ValorBruto = viagemEvento.ValorBruto.Value;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.NumeroRecibo))
                        evento.NumeroRecibo = viagemEvento.NumeroRecibo;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.MotivoBloqueio))
                        evento.MotivoBloqueio = viagemEvento.MotivoBloqueio;
                    if (!string.IsNullOrWhiteSpace(viagemEvento.Instrucao))
                        evento.Instrucao = viagemEvento.Instrucao;

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado ||
                        viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        if (evento.Status != EStatusViagemEvento.Baixado &&
                            viagemEvento.Status != EStatusViagemEvento.Cancelado)
                            evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if (viagem.FormaPagamento == EViagemFormaPagamento.Cartao &&
                        viagemEvento.Status == EStatusViagemEvento.Baixado)
                    {
                        if (isInsert && evento.Status != EStatusViagemEvento.Baixado)
                            evento.Status = EStatusViagemEvento.Aberto;
                    }
                    else if (viagemEvento.Status.HasValue)
                    {
                        evento.Status = viagemEvento.Status.Value;
                    }

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado ||
                        viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if (viagemEvento.IdMotivo.HasValue)
                    {
                        var motivo = _motivoCredenciamentoService.Get(viagemEvento.IdMotivo.Value);
                        if (motivo == null)
                            throw new ApplicationException("Motivo não cadastrado");
                        if (motivo.IdEmpresa != viagem.IdEmpresa)
                            throw new ApplicationException("Motivo não cadastrado");

                        if (viagem.IdFilial == null && motivo.IdFilial != null)
                            throw new ApplicationException("Motivo não cadastrado");
                        if (motivo.IdFilial != null && viagem.IdFilial != motivo.IdFilial)
                            throw new ApplicationException("Motivo não cadastrado");

                        evento.IdMotivo = motivo.IdMotivo;
                    }

                    #region Documentos do evento da viagem

                    //No caso da edição de um registro pai, devemos verificar se precisamos editar ou adicionar um registro filho.
                    if (viagemEvento.ViagemDocumentos != null && viagemEvento.ViagemDocumentos.Any())
                        foreach (var viagemDocumento in viagemEvento.ViagemDocumentos)
                        {
//                            if (viagemDocumento.TipoEvento.HasValue && ((int) viagemDocumento.TipoEvento.Value < 0 || (int) viagemDocumento.TipoEvento.Value > 2))
//                                throw new ApplicationException("Tipo de evento informado para o documento da viagem é inválido. ");

                            if (viagemDocumento.TipoDocumento.HasValue &&
                                ((int) viagemDocumento.TipoDocumento.Value < 0 ||
                                 (int) viagemDocumento.TipoDocumento.Value > 4))
                                throw new ApplicationException("Tipo de documento informado é inválido. ");

                            //Caso seja informado a id do documento, significa que estamos editando-o
                            if (viagemDocumento.IdViagemDocumento.HasValue)
                            {
                                var documento = evento.ViagemDocumentos.FirstOrDefault(x =>
                                    x.IdViagemDocumento == viagemDocumento.IdViagemDocumento.Value);
                                if (documento != null)
                                {
                                    if (viagemDocumento.TipoEvento.HasValue)
                                        documento.TipoEvento = viagemDocumento.TipoEvento.Value;
                                    if (viagemDocumento.TipoDocumento.HasValue)
                                        documento.TipoDocumento = viagemDocumento.TipoDocumento.Value;

                                    documento.Descricao = viagemDocumento.Descricao;
                                    documento.NumeroDocumento = viagemDocumento.NumeroDocumento;
                                    if (viagemDocumento.ObrigaAnexo.HasValue)
                                        documento.ObrigaAnexo = viagemDocumento.ObrigaAnexo.Value;

                                    if (viagemDocumento.ObrigaAnexoFilial.HasValue)
                                        documento.ObrigaAnexoFilial = viagemDocumento.ObrigaAnexoFilial.Value;

                                    if (viagemDocumento.ObrigaAnexoMatriz.HasValue)
                                        documento.ObrigaAnexoMatriz = viagemDocumento.ObrigaAnexoMatriz.Value;

                                    if (viagemDocumento.ObrigaDocOriginal.HasValue)
                                        documento.ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal.Value;
                                }
                            }
                            else
                            {
                                if (!viagemDocumento.TipoDocumento.HasValue)
                                    throw new ApplicationException(
                                        "Tipo de documento do documento da viagem não informado. ");
                                if (!viagemDocumento.TipoEvento.HasValue)
                                    throw new ApplicationException(
                                        "Tipo de evento do documento da viagem não informado. ");
                                if (!viagemDocumento.ObrigaAnexo.HasValue)
                                    throw new ApplicationException("Obrigatoriedade do documento não informado. ");

                                evento.ViagemDocumentos.Add(new ViagemDocumento
                                {
                                    TipoEvento = viagemDocumento.TipoEvento.Value,
                                    TipoDocumento = viagemDocumento.TipoDocumento.Value,
                                    Descricao = viagemDocumento.Descricao,
                                    NumeroDocumento = viagemDocumento.NumeroDocumento,
                                    ObrigaAnexo = viagemDocumento.ObrigaAnexo.Value,
                                    ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal.HasValue
                                        ? viagemDocumento.ObrigaDocOriginal.Value
                                        : false
                                });
                            }
                        }

                    #endregion

                    #region Valores adicionais do evento da viagem

                    if (viagemEvento.ViagemOutrosDescontos != null && viagemEvento.ViagemOutrosDescontos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosDescontos)
                            if (valorAdicional.IdViagemValorAdicional.HasValue)
                            {
                                var valor = evento.ViagemValoresAdicionais.FirstOrDefault(
                                    x => x.IdViagemValorAdicional == valorAdicional.IdViagemValorAdicional);
                                if (valor == null)
                                    throw new ApplicationException(
                                        $"Não foi possível identificar o desconto pelo código informado. Código: {valorAdicional.IdViagemValorAdicional}");

                                valor.Descricao = valorAdicional.Descricao;
                                valor.NumeroDocumento = valorAdicional.NumeroDocumento;

                                valor.Valor = valorAdicional.Valor;
                                valor.Tipo = ETipoValorAdicional.Desconto;
                                if (valorAdicional.CodigoERP.HasValue)
                                    valor.CodigoERP = valorAdicional.CodigoERP;
                            }
                            else
                            {
                                evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                                {
                                    Descricao = valorAdicional.Descricao,
                                    NumeroDocumento = valorAdicional.NumeroDocumento,
                                    Valor = valorAdicional.Valor,
                                    Tipo = ETipoValorAdicional.Desconto,
                                    CodigoERP = valorAdicional.CodigoERP
                                });
                            }
                    }

                    if (viagemEvento.ViagemOutrosAcrescimos != null && viagemEvento.ViagemOutrosAcrescimos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosAcrescimos)
                            if (valorAdicional.IdViagemValorAdicional.HasValue)
                            {
                                var valor = evento.ViagemValoresAdicionais.FirstOrDefault(
                                    x => x.IdViagemValorAdicional == valorAdicional.IdViagemValorAdicional);
                                if (valor == null)
                                    throw new ApplicationException(
                                        $"Não foi possível identificar o desconto pelo código informado. Código: {valorAdicional.IdViagemValorAdicional}");

                                valor.Descricao = valorAdicional.Descricao;
                                valor.NumeroDocumento = valorAdicional.NumeroDocumento;
                                if (valorAdicional.CodigoERP.HasValue)
                                    valor.CodigoERP = valorAdicional.CodigoERP;
                                valor.Valor = valorAdicional.Valor;
                                valor.Tipo = ETipoValorAdicional.Acrescimo;
                            }
                            else
                            {
                                evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                                {
                                    Descricao = valorAdicional.Descricao,
                                    NumeroDocumento = valorAdicional.NumeroDocumento,
                                    Valor = valorAdicional.Valor,
                                    Tipo = ETipoValorAdicional.Acrescimo,
                                    CodigoERP = valorAdicional.CodigoERP
                                });
                            }
                    }

                    #endregion
                }
                else //Estamos adicionando um novo evento
                {
                    if (!viagemEvento.TipoEvento.HasValue)
                        throw new ApplicationException("Tipo do evento não informado para um dos eventos da viagem.");

                    if (!viagemEvento.Status.HasValue)
                        throw new ApplicationException("Status do evento não informado para um dos eventos da viagem.");

                    var evento = new ViagemEvento
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        TipoEventoViagem = viagemEvento.TipoEvento.Value,
                        ValorPagamento = viagemEvento.ValorPagamento ?? 0,
                        Status = viagemEvento.Status.Value,
                        DataValidade = viagemEvento.DataValidade,
                        Token = _viagemApp.GerarTokenViagemEvento(),
                        INSS = viagemEvento.INSS ?? 0,
                        IRRPF = viagemEvento.IRRPF ?? 0,
                        SESTSENAT = viagemEvento.SESTSENAT ?? 0,
                        Instrucao = viagemEvento.Instrucao,
                        ValorBruto = viagemEvento.ValorBruto ?? 0,
                        NumeroRecibo = viagemEvento.NumeroRecibo,
                        MotivoBloqueio = viagemEvento.MotivoBloqueio,
                        NumeroControle = viagemEvento.NumeroControle,
                        CpfUsuario = viagemEvento.CpfUsuario,
                        NomeUsuario = viagemEvento.NomeUsuario
                    };

                    evento.HabilitarPagamentoCartao = habilitarPagamentocartao;
                    // Usado para identificar o pagamento quando ainda não tem o IdViagemEvento na memória
                    viagemEvento.Token = evento.Token;

                    if (viagem.FormaPagamento == EViagemFormaPagamento.Cartao &&
                        viagemEvento.Status == EStatusViagemEvento.Baixado)
                        evento.Status = EStatusViagemEvento.Aberto;

                    if (viagemEvento.Status == EStatusViagemEvento.Baixado ||
                        viagemEvento.Status == EStatusViagemEvento.Cancelado)
                        if (evento.Status != EStatusViagemEvento.Baixado &&
                            evento.Status != EStatusViagemEvento.Cancelado)
                            evento.OrigemPagamento = EOrigemIntegracao.TMS;

                    if (evento.Status == EStatusViagemEvento.Baixado)
                    {
                        evento.DataHoraPagamento = DateTime.Now;
                        evento.ValorTotalPagamento = evento.ValorPagamento;
                    }
                    else if (evento.Status == EStatusViagemEvento.Cancelado)
                        evento.DataHoraCancelamento = DateTime.Now;

                    #region Documentos da viagem

                    if (viagemEvento.ViagemDocumentos != null && viagemEvento.ViagemDocumentos.Any())
                    {
                        if (evento.ViagemDocumentos == null)
                            evento.ViagemDocumentos = new List<ViagemDocumento>();
                        foreach (var viagemDocumento in viagemEvento.ViagemDocumentos)
                        {
                            if (!viagemDocumento.TipoEvento.HasValue)
                                throw new ApplicationException(
                                    "Tipo do evento não informado para um dos documentos do evento da viagem. ");
                            if (!viagemDocumento.TipoDocumento.HasValue)
                                throw new ApplicationException(
                                    "Tipo de documento não informado para um dos documentos do evento da viagem. ");
                            if ((int) viagemDocumento.TipoDocumento.Value < 0 ||
                                (int) viagemDocumento.TipoDocumento.Value > 4)
                                throw new ApplicationException("Tipo de documento informado é inválido. ");
                            if (!viagemDocumento.ObrigaAnexo.HasValue)
                                throw new ApplicationException(
                                    "Obrigatoriedade de documento não informado para um dos documentos do evento da viagem. ");
                            evento.ViagemDocumentos.Add(new ViagemDocumento
                            {
                                TipoEvento = viagemDocumento.TipoEvento.Value,
                                TipoDocumento = viagemDocumento.TipoDocumento.Value,
                                Descricao = viagemDocumento.Descricao,
                                NumeroDocumento = viagemDocumento.NumeroDocumento,
                                ObrigaAnexo = viagemDocumento.ObrigaAnexo ?? false,
                                ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal ?? false,
                                ObrigaAnexoFilial = viagemDocumento.ObrigaAnexoFilial ?? false,
                                ObrigaAnexoMatriz = viagemDocumento.ObrigaAnexoMatriz ?? false
                            });
                        }
                    }

                    #endregion

                    #region Valores Adicionais do evento da viagem

                    if (viagemEvento.ViagemOutrosDescontos != null &&
                        viagemEvento.ViagemOutrosDescontos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosDescontos)
                            evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                            {
                                Descricao = valorAdicional.Descricao,
                                NumeroDocumento = valorAdicional.NumeroDocumento,
                                Valor = valorAdicional.Valor,
                                Tipo = ETipoValorAdicional.Desconto,
                                CodigoERP = valorAdicional.CodigoERP
                            });
                    }

                    if (viagemEvento.ViagemOutrosAcrescimos != null &&
                        viagemEvento.ViagemOutrosAcrescimos.Any())
                    {
                        if (evento.ViagemValoresAdicionais == null)
                            evento.ViagemValoresAdicionais = new List<ViagemValorAdicional>();
                        foreach (var valorAdicional in viagemEvento.ViagemOutrosAcrescimos)
                            evento.ViagemValoresAdicionais.Add(new ViagemValorAdicional
                            {
                                Descricao = valorAdicional.Descricao,
                                NumeroDocumento = valorAdicional.NumeroDocumento,
                                Valor = valorAdicional.Valor,
                                Tipo = ETipoValorAdicional.Acrescimo,
                                CodigoERP = valorAdicional.CodigoERP
                            });
                    }

                    #endregion

                    viagem.ViagemEventos.Add(evento);
                }
            }

            // Caso o evento não tenha alterações é removido da request para não cair em métodos de pagamento
            eventosIgnorados.ForEach(eventoIgnorado => @params.ViagemEventos.Remove(eventoIgnorado));
            #endregion
        }
    }
}
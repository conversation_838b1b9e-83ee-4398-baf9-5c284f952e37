﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System.Collections.Generic;

namespace ATS.WS.Models.Common.Request
{
    public class ProtocoloModel : RequestBase
    {
        public int? idProtocolo { get; set; }
        public int idEmpresa { get; set; }
        public int idAssociacao { get; set; }
        public int idEstabelecimentoBase { get; set; }
        public int IdEmpresaDestinatario { get; set; }
        public ETipoDestinatario tipoDest { get; set; }
        public List<ViagemEventosModel> eventos { get; set; }
        public List<ProtocoloAnexoModel> anexos { get; set; }

    }

    public class ViagemEventosModel : RequestBase
    {
        public int IdViagemEvento { get; set; }
        public decimal Valor { get; set; }
        public bool PagoCartao { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
    }

    public class ProtocoloAnexoModel
    {
        public string token { get; set; }
        public int idDocumento { get; set; }
    }
}
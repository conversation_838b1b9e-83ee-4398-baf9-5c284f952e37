﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Linq;

namespace ATS.Domain.Service
{
    public class TipoCombustivelService : ServiceBase, ITipoCombustivelService
    {
        private readonly ITipoCombustivelRepository _tipoCombustivelRepository;

        public TipoCombustivelService(ITipoCombustivelRepository tipoCombustivelRepository)
        {
            _tipoCombustivelRepository = tipoCombustivelRepository;
        }

        public TipoCombustivel Get(int id)
        {
            return _tipoCombustivelRepository.Get(id);
        }

        public IQueryable<TipoCombustivel> All()
        {
            return _tipoCombustivelRepository.All();
        }
    }
}
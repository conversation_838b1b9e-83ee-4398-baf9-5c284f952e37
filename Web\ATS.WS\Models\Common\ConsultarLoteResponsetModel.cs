﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class ConsultarLoteResponseModel
    {
     
        public int? IdLote { get; set; }
    
        public string Cliente { get; set; }
    
        public decimal IdCidadeOrigem { get; set; }
        
        public decimal IdCidadeDestino { get; set; }
    
        public int? ToneladasCarregadas { get; set; }
    
        public int? ToneladasTotais { get; set; }
 
        public EStatusLote Status { get; set; }
    
        public decimal Valor { get; set; }
    
        public EPedagioLote TipoPedagio { get; set; }

        public decimal? ValorPedagio { get; set; } 
    
        public decimal Distancia { get; set; }

    }
}
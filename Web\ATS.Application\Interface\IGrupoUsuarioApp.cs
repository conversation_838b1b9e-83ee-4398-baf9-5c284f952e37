﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IGrupoUsuarioApp : IAppBase<GrupoUsuario>
    {
        IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase);
        ValidationResult Add(GrupoUsuario entity);
        GrupoUsuario Get(int id);
        GrupoUsuario GetChilds(int id);
        ValidationResult Update(GrupoUsuario grupoUsuario, List<GrupoUsuarioMenu> grupoUsuarioMenus);
        ValidationResult Ativar(int id, int idUsuarioLogOn);
        ValidationResult Inativar(int id, int idUsuarioLogOn);
        IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase);

        void Atualizar(int idGrupoUsuario, int idEmpresa, string descricao, List<int> idMenusSelecionados);
        void Inserir(int idEmpresa, string descricao, List<int> idMenusSelecionados, int? idEstabelecimentoBase = null);
        object ConsultarGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool PertenceAEmpresa(int idEmpresa, int idGrupoUsuario);
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    class GruposUsuarioMap : EntityTypeConfiguration<GruposUsuarios>
    {
        public GruposUsuarioMap()
        {
            ToTable("MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO");
            HasKey(x => x.IdGrupoUsuario);
            Property(x => x.IdGrupoUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(x => x.MensagemGruposUsuarios)
                .WithMany(x => x.GrupoUsuarios)
                .HasForeignKey(x => x.IdMensagemGrupoUsuario);

            HasRequired(x => x.MensagemVinculada).WithMany(x => x.GruposUsuariosMensagens).HasForeignKey(x => x.IdMensagem);
        }
    }
}

﻿using System.Collections.Generic;
using ATS.Domain.Grid;

namespace ATS.Domain.Models.Grid.Base
{
    public class FiltrosGridBaseModel
    {
        public int? IdEmpresa { get; set; }

        public int Take { get; set; }

        public int Page { get; set; }

        public OrderFilters Order { get; set; }

        public List<QueryFilters> Filters { get; set; }

        public string Extensao { get; set; }
    }
}

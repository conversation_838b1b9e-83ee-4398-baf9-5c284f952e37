﻿using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Transactions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using NLog;

namespace ATS.Domain.Service
{
    public class PrestacaoContasService : ServiceBase, IPrestacaoContasService
    {
        private readonly IPrestacaoContasRepository _repository;
        private readonly IPrestacaoContasEventoRepository _eventoRepository;
        private readonly IGestorUsuarioRepository _gestorUsuarioRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly ICargaAvulsaService _cargaAvulsaService;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IUsuarioContatoRepository _usuarioContatoRepository;
        private readonly IEmailService _emailService;
        private readonly IEmpresaRepository _empresaRepository;

        public PrestacaoContasService(IPrestacaoContasRepository repository, IPrestacaoContasEventoRepository eventoRepository, IGestorUsuarioRepository gestorUsuarioRepository, IUserIdentity userIdentity, ICargaAvulsaService cargaAvulsaService, IUsuarioRepository usuarioRepository, IEmpresaRepository empresaRepository, IParametrosUsuarioService parametrosUsuarioService, IUsuarioContatoRepository usuarioContatoRepository, IEmailService emailService)
        {
            _repository = repository;
            _eventoRepository = eventoRepository;
            _gestorUsuarioRepository = gestorUsuarioRepository;
            _userIdentity = userIdentity;
            _cargaAvulsaService = cargaAvulsaService;
            _usuarioRepository = usuarioRepository;
            _empresaRepository = empresaRepository;
            _parametrosUsuarioService = parametrosUsuarioService;
            _usuarioContatoRepository = usuarioContatoRepository;
            _emailService = emailService;
        }

        public ValidationResult Novo(PrestacaoContasNovoRequest request, int idEmpresa)
        {
            if (!_parametrosUsuarioService.GetPermiteSolicitarAdiantamentoApp(request.IdUsuario))
                throw new InvalidOperationException("Usuário sem permissão para solicitar adiantamentos.");

            if (_repository.Any(c => c.IdUsuarioPrestacao == request.IdUsuario && c.Status == EStatusPrestacaoContas.Aberto))
                throw new InvalidOperationException("Você já possui uma prestação de contas em aberto");

            var prestacao = _repository.Add(new PrestacaoContas()
            {
                Status = EStatusPrestacaoContas.AguardandoAvaliacaoGestor,
                IdEmpresa = idEmpresa,
                IdUsuarioPrestacao = request.IdUsuario,
                Observacao = request.Observacao,
                Valor = request.Valor,
                DataCadastro = DateTime.Now
            });

            var eventoAbertura = _eventoRepository.Add(new PrestacaoContasEvento()
            {
                IdPrestacaoContas = prestacao.Id,
                DataCadastro = DateTime.Now,
                Status = EStatusPrestacaoContasEvento.Abertura
            });

            EnviarEmailNovaSolicitacaoAdiantamento(prestacao, request.IdUsuario);

            return new ValidationResult();
        }

        private void EnviarEmailNovaSolicitacaoAdiantamento(PrestacaoContas prestacao, int idSubordinado)
        {
            try
            {
                var gestores = _gestorUsuarioRepository
                    .AsNoTracking()
                    .Where(c => c.IdSubordinado == idSubordinado && c.UsuarioGestor.Ativo)
                    .Select(c => c.IdGestor)
                    .ToList();

                var emails = _usuarioContatoRepository
                    .AsNoTracking()
                    .Where(c => gestores.Contains(c.IdUsuario) && c.Email != null)
                    .Select(c => c.Email)
                    .ToList();

                if (!emails.Any()) return;

                var empresa = _empresaRepository
                    .AsNoTracking()
                    .Where(c => c.IdEmpresa == prestacao.IdEmpresa)
                    .Select(c => new
                    {
                        c.RazaoSocial,
                        c.CNPJ
                    })
                    .FirstOrDefault();

                if (empresa == null) return;

                var subordinado = _usuarioRepository
                    .AsNoTracking()
                    .Where(c => c.IdUsuario == idSubordinado)
                    .Select(c => new
                    {
                        c.Nome,
                        c.CPFCNPJ
                    })
                    .FirstOrDefault();

                if (subordinado == null) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Extratta – Nova solicitação de adiantamento",
                    Destinatarios = emails,
                    NomeVisualizacao = $"Extratta – Nova solicitação de adiantamento",
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory +
                                                 @"\Content\Email\nova-solicitacao-adiantamento.html"))
                {
                    var logoEmail =
                        new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = ms.ReadToEnd();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", empresa.RazaoSocial);
                    html = html.Replace("{EMPRESA_CNPJ}", empresa.CNPJ.FormatarCpfCnpj());
                    html = html.Replace("{VALOR}", "R$ " + prestacao.Valor.ToString("C"));
                    html = html.Replace("{USUARIO_CPF}", subordinado.CPFCNPJ.FormatarCpfCnpj());
                    html = html.Replace("{USUARIO_NOME}", subordinado.Nome);
                    html = html.Replace("{DATA}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                    html = html.Replace("{OBSERVACAO}", string.IsNullOrWhiteSpace(prestacao.Observacao) ? "--" : prestacao.Observacao);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "ERRO EMAIL GESTOR PRESTAÇÃO DE CONTAS");
            }
        }

        public bool PertenceAEmpresa(int prestacaoContasId, int? empresaId)
        {
            return _repository.Any(c => c.Id == prestacaoContasId && c.IdEmpresa == empresaId);
        }

        public ValidationResult AlterarStatus(int prestacaoContasId, EStatusPrestacaoContas status, string observacao = null)
        {
            if (!_parametrosUsuarioService.GetPermiteAprovarSolicitacaoAdiantamentoApp(_userIdentity.IdUsuario))
                throw new InvalidOperationException("Usuário não é gestor.");
            
            var usuariosSubordinados = _gestorUsuarioRepository
                .AsNoTracking()
                .Where(c => c.IdGestor == _userIdentity.IdUsuario)
                .Select(c => c.IdSubordinado)
                .ToList();

            var prestacaoContas = _repository
                .Where(c => c.Id == prestacaoContasId && usuariosSubordinados.Contains(c.IdUsuarioPrestacao))
                .FirstOrDefault();

            if (prestacaoContas == null)
                throw new InvalidOperationException("Registro não encontrado.");

            //Aprovação do gestor
            if (prestacaoContas.Status == EStatusPrestacaoContas.AguardandoAvaliacaoGestor)
            {
                if (status == EStatusPrestacaoContas.AguardandoAvaliacaoFinanceiro)
                {
                    UpdateStatusPrestacaoConta(prestacaoContas, status);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.AprovacaoGestor);
                    return new ValidationResult();
                }

                if (status == EStatusPrestacaoContas.RejeitadoGestor)
                {
                    prestacaoContas.DataConclusao = DateTime.Now;
                    UpdateStatusPrestacaoConta(prestacaoContas, status);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.RejeiacaoGestor);
                    return new ValidationResult();
                }

                throw new InvalidOperationException("Alteração de status inválida para esta prestação.");
            }

            //Aprovação do gestor financeiro
            if (prestacaoContas.Status == EStatusPrestacaoContas.AguardandoAvaliacaoFinanceiro)
            {
                if (!_parametrosUsuarioService.GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(_userIdentity.IdUsuario))
                    throw new InvalidOperationException("Usuário não é gestor financeiro.");

                //gerar carga avulsa
                if (status == EStatusPrestacaoContas.Aberto)
                {
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.AprovacaoFinanceiro);
                    try
                    { 
                        GerarCargaAvulsa(prestacaoContas);
                        UpdateStatusPrestacaoConta(prestacaoContas, status);
                        GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.CriacaoCargaAvulsa);
                        return new ValidationResult();
                    }
                    catch (Exception)
                    {
                        GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.ErroGerarCargaAvulsa);
                        throw;
                    }
                }

                if (status == EStatusPrestacaoContas.RejeitadoFinanceiro)
                {
                    prestacaoContas.DataConclusao = DateTime.Now;
                    UpdateStatusPrestacaoConta(prestacaoContas, status);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.RejeicaoFinanceiro);
                    return new ValidationResult();
                }

                throw new InvalidOperationException("Alteração de status inválida para esta prestação.");
            }

            //Finalização do registro pelo gestor financeiro
            if (prestacaoContas.Status == EStatusPrestacaoContas.Aberto)
            {
                if (!_parametrosUsuarioService.GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(_userIdentity.IdUsuario))
                    throw new InvalidOperationException("Usuário não é gestor financeiro.");
                
                prestacaoContas.DataConclusao = DateTime.Now;
                
                if (status == EStatusPrestacaoContas.ConcluidoAprovado)
                {
                    UpdateStatusPrestacaoConta(prestacaoContas, status, observacao);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.ConclusaoAprovada);
                    return new ValidationResult();
                }

                if (status == EStatusPrestacaoContas.ConcluidoRejeitado)
                {
                    if(string.IsNullOrWhiteSpace(observacao))
                        throw new InvalidOperationException("Motivo não informado.");
                    UpdateStatusPrestacaoConta(prestacaoContas, status, observacao);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.ConclusaoRejeitada);
                    return new ValidationResult();
                }

                if (status == EStatusPrestacaoContas.Cancelado)
                {
                    if(string.IsNullOrWhiteSpace(observacao))
                        throw new InvalidOperationException("Motivo não informado.");
                    UpdateStatusPrestacaoConta(prestacaoContas, status);
                    GerarEventoPrestacaoConta(prestacaoContas.Id, EStatusPrestacaoContasEvento.Cancelamento);
                    return new ValidationResult();
                }

                throw new InvalidOperationException("Alteração de status inválida para esta prestação.");
            }

            throw new InvalidOperationException("Alteração de status inválida para esta prestação.");
        }

        private void UpdateStatusPrestacaoConta(PrestacaoContas prestacaoContas, EStatusPrestacaoContas status, string observacao = null)
        {
            prestacaoContas.Status = status;
            if (!string.IsNullOrWhiteSpace(observacao)) prestacaoContas.Observacao = observacao;
            _repository.Update(prestacaoContas);
        }

        public void GerarEventoPrestacaoConta(int prestacaoContasId, EStatusPrestacaoContasEvento statusEvento)
        {
            var evento = new PrestacaoContasEvento()
            {
                IdPrestacaoContas = prestacaoContasId,
                DataCadastro = DateTime.Now,
                Status = statusEvento
            };
            
            if (_userIdentity.IdUsuario != 0) evento.IdUsuarioCadastro = _userIdentity.IdUsuario;
            
            _eventoRepository.Add(evento);
        }

        private void GerarCargaAvulsa(PrestacaoContas prestacaoContas)
        {
            var motorista = _usuarioRepository.AsNoTracking().FirstOrDefault(c => c.IdUsuario == prestacaoContas.IdUsuarioPrestacao);
            if (motorista == null) throw new InvalidOperationException("Motorista não encontrado.");
            var nomeUsuario = _usuarioRepository
                .Where(c => c.IdUsuario == _userIdentity.IdUsuario).Select(c => c.Nome)
                .FirstOrDefault();
            var carga = new CargaAvulsa
            {
                Observacao = $"Adiantamento solicitado via APP. EXT-{prestacaoContas.Id}",
                IdEmpresa = prestacaoContas.IdEmpresa,
                Valor = prestacaoContas.Valor,
                CPFMototista = motorista.CPFCNPJ,
                CPFCNPJUsuario = _userIdentity.CpfCnpj,
                NomeUsuario = nomeUsuario,
                NomeMotorista = motorista.Nome,
                IdUsuariocadastro = _userIdentity.IdUsuario,
                IdPrestacaoContas = prestacaoContas.Id,
                TipoCarga = ETipoCarga.Rapida
            };
            
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var response = _cargaAvulsaService.Add(carga);

                if (!response.ValidationResult.IsValid)
                    throw new InvalidOperationException(
                        response.ValidationResult.Errors.FirstOrDefault()?.Message ?? 
                        "Não foi possível gerar a carga avulsa, erro inesperado.");
                
                transaction.Complete();
            }
        }

        public PrestacaoContasGridResponse ConsultarGrid(int take, int page, OrderFilters order,
            List<QueryFilters> filters, EStatusPrestacaoContas[] status = null)
        {
            if (!_parametrosUsuarioService.GetPermiteAprovarSolicitacaoAdiantamentoApp(_userIdentity.IdUsuario))
                throw new InvalidOperationException("Usuário não é gestor.");
            
            var usuariosSubordinados = _gestorUsuarioRepository
                .Where(c => c.IdGestor == _userIdentity.IdUsuario)
                .Select(c => c.IdSubordinado)
                .ToList(); 

            var prestacoes = _repository
                .Where(c => usuariosSubordinados.Contains(c.IdUsuarioPrestacao));

            if (status != null && status.Any())
                prestacoes = prestacoes.Where(c => status.Contains(c.Status));

            prestacoes = string.IsNullOrWhiteSpace(order?.Campo)
                ? prestacoes.OrderByDescending(x => x.Status).ThenByDescending(c => c.Id)
                : prestacoes.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            var count = prestacoes.Count();

            var filtroDocumento = filters?.FirstOrDefault(c => c.Campo == "UsuarioDocumento");
            if (filtroDocumento != null)
            {
                prestacoes = prestacoes.Where(c => c.UsuarioPrestacao.CPFCNPJ.Contains(filtroDocumento.Valor));
                filters.Remove(filtroDocumento);
            }

            var filtroNome = filters?.FirstOrDefault(c => c.Campo == "UsuarioNome");
            if (filtroNome != null)
            {
                prestacoes = prestacoes.Where(c => c.UsuarioPrestacao.Nome.Contains(filtroNome.Valor));
                filters.Remove(filtroNome);
            }

            var filtroCodigo = filters?.FirstOrDefault(c => c.Campo == "Codigo");
            if (filtroCodigo != null)
            {
                var valido = int.TryParse(filtroCodigo.Valor, out var valor);
                if (valido) prestacoes = prestacoes.Where(c => c.Id == valor);
                filters.Remove(filtroCodigo);
            }

            /*var filtroStatus = filters?.FirstOrDefault(c => c.Campo == "Status");
            if (filtroStatus != null)
            {
                var filtroStatusValor = (EStatusPrestacaoContas) filtroStatus.Valor.ToInt();
                prestacoes = prestacoes.Where(c => c.Status == filtroStatusValor);
                filters.Remove(filtroStatus);
            }*/

            var prestacoesFiltradas = prestacoes
                .Skip((page - 1) * take)
                .Take(take)
                .Include(c => c.UsuarioPrestacao)
                .Select(c => new
                {
                    Id = c.Id,
                    Observacao = c.Observacao,
                    Valor = c.Valor,
                    Status = c.Status,
                    DataSolicitacao = c.DataCadastro,
                    DataConclusao = c.DataConclusao,
                    UsuarioDocumento = c.UsuarioPrestacao.CPFCNPJ,
                    UsuarioNome = c.UsuarioPrestacao.Nome
                })
                .ToList();
            
            var itens = prestacoesFiltradas
                .Select(c => new PrestacaoContasGridResponseItem()
                {
                    Id = c.Id,
                    Codigo = "EXT-" + c.Id,
                    Observacao = c.Observacao,
                    Valor = "R$ " + c.Valor.ToString("N"),
                    Status = (int)c.Status,
                    StatusDescricao = c.Status.GetDescription(),
                    DataSolicitacao = c.DataSolicitacao.ToString("dd/MM/yyyy HH:mm"),
                    DataConclusao = c.DataConclusao?.ToString("dd/MM/yyyy HH:mm"),
                    UsuarioDocumento = c.UsuarioDocumento.FormatMaskCpfCnpj(),
                    UsuarioNome = c.UsuarioNome
                })
                .ToList();

            return new PrestacaoContasGridResponse
            {
                totalItems = count,
                items = itens
            };
        }

        public int? VerificarPrestacaoContasAberta(int idUsuario)
        {
            var prestacao = _repository
                .AsNoTracking()
                .FirstOrDefault(c => c.IdUsuarioPrestacao == idUsuario && c.Status == EStatusPrestacaoContas.Aberto);

            return prestacao?.Id;
        }
    }
}

﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class NotificacaoPushItemMap : EntityTypeConfiguration<NotificacaoPushItem>
    {
        public NotificacaoPushItemMap()
        {
            ToTable("NOTIFICACAO_PUSH_ITEM");

            HasKey(x => new { x.IdHardware, x.IdNotificacaoPush });

            Property(x => x.Apelido)
                .IsRequired();

            Property(x => x.IdHardware)
                .IsRequired();

            HasRequired(x => x.NotificacaoPush)
                .WithMany(x => x.Items)
                .HasForeignKey(x => x.IdNotificacaoPush);
        }
    }
}

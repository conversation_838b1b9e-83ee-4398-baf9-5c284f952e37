﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Enum;

namespace ATS.Domain.Service
{
    public class AdressService
    {
        /// <summary>
        /// Obtém latitude e longitude de um endereço específico
        /// </summary>
        /// <param name="num"></param>
        /// <param name="endereco"></param>
        /// <param name="bairro"></param>
        /// <param name="cidade"></param>
        /// <param name="estado"></param>
        /// <param name="pais"></param>
        /// <param name="lat"></param>
        /// <param name="lng"></param>
        public void SetCoordinatesObject(int num, string endereco, string bairro, string cidade, string estado,
            string pais, out decimal lat, out decimal lng)
        {
            var end = string.Empty;

            if (num != 0)
                end += num + ",";
            if (!string.IsNullOrWhiteSpace(endereco))
                end += endereco + ",";
            if (!string.IsNullOrWhiteSpace(bairro))
                end += bairro + ",";

            if (!string.IsNullOrWhiteSpace(cidade))
                end += cidade + ",";

            if (!string.IsNullOrWhiteSpace(estado))
                end += estado + ",";

            if (!string.IsNullOrWhiteSpace(pais))
                end += pais + ",";

            var resultCoordinates = new WebServiceHelper().GetCoordinateFromAdress(end, EOrigemConsumoServicoExterno.ConsultaLatitudeLongitudePorEndereco);
            lat = resultCoordinates.Latitude;
            lng = resultCoordinates.Longitude;
        }
        
        

        public void VerificaCoordenadas(string descricao, int num, string endereco, string bairro, string cidade, string estado, string pais, out decimal latitude, out decimal longitude, EOrigemConsumoServicoExterno origemConsumoServicoExterno)
        {
            var end = string.Empty;

            if (!string.IsNullOrWhiteSpace(descricao))
                end += descricao + ",";
            if (num != 0)
                end += num + ",";
            if (!string.IsNullOrWhiteSpace(endereco))
                end += endereco + ",";
            if (!string.IsNullOrWhiteSpace(bairro))
                end += bairro + ",";

            if (!string.IsNullOrWhiteSpace(cidade))
                end += cidade + ",";

            if (!string.IsNullOrWhiteSpace(estado))
                end += estado + ",";

            if (!string.IsNullOrWhiteSpace(pais))
                end += pais + ",";

            var resultCoordinates = new WebServiceHelper().GetCoordinateFromAdress(end, origemConsumoServicoExterno);
            latitude = resultCoordinates.Latitude;
            longitude = resultCoordinates.Longitude;

        }
        public void VerificaCoordenadaSemNumero(string descricao, string endereco, string bairro, string cidade, string estado, string pais, out decimal latitude, out decimal longitude, EOrigemConsumoServicoExterno origemConsumoServicoExterno)
        {
            var end = string.Empty;

            if (!string.IsNullOrWhiteSpace(descricao))
                end += descricao + ",";
          
            if (!string.IsNullOrWhiteSpace(endereco))
                end += endereco + ",";
            if (!string.IsNullOrWhiteSpace(bairro))
                end += bairro + ",";

            if (!string.IsNullOrWhiteSpace(cidade))
                end += cidade + ",";

            if (!string.IsNullOrWhiteSpace(estado))
                end += estado + ",";

            if (!string.IsNullOrWhiteSpace(pais))
                end += pais + ",";

            var resultCoordinates = new WebServiceHelper().GetCoordinateFromAdress(end, origemConsumoServicoExterno);
            latitude = resultCoordinates.Latitude;
            longitude = resultCoordinates.Longitude;

        }

    
    }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Linq;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface IMensagemGrupoUsuarioService : IService<Mensagem>
    {
        IQueryable<MensagemGrupoUsuario> GetGruposDoUsuario(int idUsuario);
        void DeletarGrupo(int idGrupo);
        MensagemGrupoUsuario AddGrupo(MensagemGrupoUsuario grupoUsuarios);
        ValidationResult AddUsuarioParaGrupo(int idGrupo, int idUsuario);
        IEnumerable<object> GetUsuariosPeloGrupo(int idGrupo);
        void RemoverUsuarioDoGrupo(int idGrupo, int idUsuario);
        IEnumerable<int> GetIdsUsuariosPeloGrupo(int idGrupo);
    }
}
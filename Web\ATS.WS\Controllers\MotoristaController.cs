﻿using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using ATS.Domain.Enum;
using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;

namespace ATS.WS.Controllers
{
    public class MotoristaController : BaseController
    {
        private readonly SrvMotorista _srvMotorista;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaApp _empresaApp;

        public MotoristaController(BaseControllerArgs baseArgs, SrvMotorista srvMotorista, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaApp empresaApp) : base(baseArgs)
        {
            _srvMotorista = srvMotorista;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaApp = empresaApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Integrar(MotoristaIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvMotorista.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Alterar(MotoristaIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                     !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))

                    return new JsonResult().TokenInvalido();

                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (@params.TipoContrato == ETipoContrato.Terceiro)
                    idEmpresa = null;

                var retorno = _srvMotorista.Update(@params, idEmpresa);
                return new JsonResult().Responde(retorno);
            }
            catch(Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarPorCpf(MotoristaPorCpfRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvMotorista.ConsultarMotoristaPorCpf(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(MotoristaConsultarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvMotorista.Consultar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public string SalvarUsuario(MotoristaIntegrarRequestModel @params)
        {
            try
            {
                return new JsonResult().Responde(_srvMotorista.IntegrarUsuarioMotorista(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public string IntegrarMotoristaFila(MotoristaIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvMotorista.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Service
{
    public interface ITransacaoCartaoService : IService<TransacaoCartao>
    {
        TransacaoCartao Add(TransacaoCartao transacaoCartao);
        void Update(TransacaoCartao transacaoCartao);
        int GetCountByIdEvento(int idevento);
        IList<TransacaoCartao> GetByIdEvento(int idevento);
        TransacaoCartao GetByIdEventoAndTipoProcesso(int idevento, ETipoProcessamentoCartao tipoProcessamentoCartao);
        bool ExistsByIdEvento(int idevento);
        TransacaoCartao GetById(int transacaoCartaoId);
        TransacaoCartao GetByIdIncludeViagem(int idtransacaoCartao);
        bool AnyByTransacao(int idtransacaoCartao);
        IQueryable<TransacaoCartao> Find(Expression<Func<TransacaoCartao, bool>> predicate, bool @readonly = false);

        TransacaoCartao GetTransferenciaManualAdiantamento(string cpfCnpjOrigem, string cpfCnpjDestino,
            DateTime dataBaixaPrimeiroEventoViagem);

        IQueryable<TransacaoCartao> GetQuery();
        IQueryable<TransacaoCartao> GetQuery(int transacaoCartaoId);
        IList<TransacaoCartao> GetByIdCarga(int idCarga);
        int GetCountByIdCarga(int idCarga);
        TransacaoCartao GetByIdCargaAndTipoProcesso(int idCarga, ETipoProcessamentoCartao tipoProcessamentoCartao);
        TransacaoCartao GetByResgate(int resgateId);
        EStatusPagamentoCartao? GetStatusTransacaoByIdCargaAvulsa(int cargaAvulsaId);
    }
}

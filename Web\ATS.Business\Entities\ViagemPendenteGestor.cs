﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class ViagemPendenteGestor
    {
        [Index("IX_ViagemPendenteGestorUniqueKey", 1, IsUnique = true)]
        public int IdViagem { get; set; }

        [Index("IX_ViagemPendenteGestorUniqueKey", 2, IsUnique = true)]
        public int IdEmpresa { get; set; }
        
        [Index("IX_ViagemPendenteGestorUniqueKey", 3, IsUnique = true)]
        public int? IdFilial { get; set; }

        [Index("IX_ViagemPendenteGestorUniqueKey", 4, IsUnique = true)]
        public int IdBloqueioGestorTipo { get; set; }

        public int Status { get; set; }

        public int? IdUsuarioDesbloqueio { get; set; }

        public DateTime  DataCadastro { get; set; }

        public DateTime? DataStatus { get; set; }

        public string Ocorrencia { get; set; }

        public string Motivo { get; set; }

        #region Relacionamento

        public virtual Viagem Viagem { get; set; }
        public virtual BloqueioGestorTipo BloqueioGestorTipo { get; set; }
        public virtual Usuario UsuarioDesbloqueio { get; set; }
        public virtual Empresa Empresa { get; set; }
        public virtual Filial Filial { get; set; }

        #endregion
        //idviagem, IdBloqueioGestorTipo, usuário, motivo e data de desbloqueio, data de cadastro
    }
}
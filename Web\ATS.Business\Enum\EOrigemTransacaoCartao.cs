﻿using System.ComponentModel;
using System.Runtime.Serialization;
using System.Security.Cryptography;

namespace ATS.Domain.Enum
{
    public enum EOrigemTransacaoCartao
    {
        [EnumMember, Description("Automatico")]
        Automatico = 1,

        [EnumMember, Description("Manual pelo Aplicativo")]
        ManualAplicativo = 2,

        [EnumMember, Description("Avulso")]
        Avulso = 3,
        
        [EnumMember, Description("Atendimento")]
        Atendimento = 4
    }

    public static class EOrigemTransacaoCartaoExtensions
    {
        public static string GetTextoIntegracaoPlataformaCartoes(this EOrigemTransacaoCartao value)
        {
            switch (value)
            {
                case EOrigemTransacaoCartao.ManualAplicativo: return "Aplicativo";
                case EOrigemTransacaoCartao.Automatico: return "Automático";
                default: return value.ToString();
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Service
{
    public class ViagemCheckService : ServiceBase, IViagemCheckService
    {
        /// <summary>
        /// Validar os objetos de 
        /// </summary>
        /// <param name="viagem"></param>
        /// <returns></returns>
        public IEnumerable<ValidationError> IsValid(Viagem viagem)
        {
            if (!viagem.ViagemChecks.Any())
                return new List<ValidationError> { new ValidationError($"É necessário informar um status da carga.") };

            // Retorna o último registro salvo na base de dados relacionado a viagem
            ViagemCheck check = viagem.ViagemChecks.LastOrDefault(c => c.IdCheck != 0);

            if (check != null)
            {
                // Caso o status da viagem seja superior ao que se deseja adicionar, informa erro.
                if (Convert.ToInt32(check.Status) >= Convert.ToInt32(viagem.ViagemChecks.Last().Status))
                    return new List<ValidationError> { new ValidationError($"Acompanhamento já realizado.") };
            }

            return new List<ValidationError>();
        }
    }
}
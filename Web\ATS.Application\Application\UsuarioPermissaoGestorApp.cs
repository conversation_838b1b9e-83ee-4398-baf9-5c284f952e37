﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class UsuarioPermissaoGestorApp : AppBase, IUsuarioPermissaoGestorApp
    {
        private readonly IUsuarioPermissaoGestorService _usuarioPermissaoGestorService;

        public UsuarioPermissaoGestorApp(IUsuarioPermissaoGestorService usuarioPermissaoGestorService)
        {
            _usuarioPermissaoGestorService = usuarioPermissaoGestorService;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo,bool empresa, bool filial)
        {
            return _usuarioPermissaoGestorService.Integrar(idUsuario, idBloqueioGestorTipo, empresa, filial);
        }

        public ValidationResult Integrar(int idUsuario, Dictionary<EBloqueioGestorTipo, KeyValuePair<bool,bool>> permissoesBloqueioGestor)
        {
            var validationResult = new ValidationResult();
            
            foreach (var keyValuePair in permissoesBloqueioGestor)
            {
                validationResult = Integrar(idUsuario, keyValuePair.Key, keyValuePair.Value.Key, keyValuePair.Value.Value);

                if (!validationResult.IsValid)
                    return validationResult;
            }

            return validationResult;
        }

        public UsuarioPermissaoGestor GetParametroPermissaoGestor(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo)
        {
            return _usuarioPermissaoGestorService.GetParametroPermissaoGestor(idUsuario, idBloqueioGestorTipo);
        }
    }
}
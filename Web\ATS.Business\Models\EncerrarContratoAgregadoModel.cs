﻿using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class EncerrarContratoAgregadoModel
    {
        public int ContratoAgregadoId { get; set; }
//        public string Ciot { get; set; }
//        public decimal? PesoCarga { get; set; }
//        public List<ViagemAgregado> Viagens { get; set; }
//        public ValoresEfetivosAgregado ValoresEfetivos { get; set; }
//
//        public class ViagemAgregado
//        {
//            public int? CodigoMunicipioOrigem { get; set; }
//            public int? CodigoMunicipioDestino { get; set; }
//            public string CodigoNaturezaCarga { get; set; }
//            public decimal? PesoCarga { get; set; }
//            public int? QuantidadeViagens { get; set; }
//        }
//
//        public class ValoresEfetivosAgregado
//        {
//            public decimal? ValorFrete { get; set; }
//            public decimal? ValorCombustivel { get; set; }
//            public decimal? ValorDespesas { get; set; }
//            public decimal? TotalImposto { get; set; }
//            public decimal? TotalPegadio { get; set; }
//            public int? QuantidadeTarifas { get; set; }
//            public decimal? ValorTarifas { get; set; }
//        }
    }
}
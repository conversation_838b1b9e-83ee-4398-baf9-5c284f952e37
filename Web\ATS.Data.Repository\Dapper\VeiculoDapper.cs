﻿using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using ATS.Domain.DTO.Veiculo;
using Dapper;

namespace ATS.Data.Repository.Dapper
{
    public class VeiculoDapper : DapperFactory<Veiculo>, IVeiculoDapper
    {
        public IEnumerable<Veiculo> GetVeiculosVinculadosConjunto(int idVeiculoConjunto)
        {
            string sSql = string.Format(@"SELECT VEICULO.* FROM VEICULO INNER JOIN VEICULO_CONJUNTO ON (VEICULO.idveiculo = VEICULO_CONJUNTO.idveiculo) 
                                               WHERE VEICULO_CONJUNTO.idveiculo = {0}", idVeiculoConjunto);

            return this.RunSelect(sSql);
        }
        
        public VeiculoRntrcDTO GetVeiculoRntrc(string placa, int idEmpresa)
        {
            string sql = $@"select v.idveiculo, v.placa, p.rntrc, p.cnpjcpf as DocumentoProprietario from VEICULO v
                        left join proprietario p on v.idproprietario = p.idproprietario
                        where (v.idempresa = @idEmpresa or v.idempresa is null) and v.placa = @placa and v.ativo = 1
                        order by v.idempresa desc";
            
            var parameters = new
            {
                placa,
                idEmpresa  
            };

            using (IDbConnection dbConnection = this.GetConnection())
            {
                return dbConnection.Query<VeiculoRntrcDTO>(sql, parameters).FirstOrDefault();
            }
        }
        
        public VeiculoRntrcDTO GetVeiculoRntrc(int idVeiculo)
        {
            string sql = $@"select v.idveiculo, v.placa, p.rntrc, p.cnpjcpf as DocumentoProprietario from VEICULO v
                        left join proprietario p on v.idproprietario = p.idproprietario
                        where v.idveiculo = @idVeiculo
                        order by v.idempresa desc";
            
            var parameters = new { idVeiculo };

            using (IDbConnection dbConnection = this.GetConnection())
            {
                return dbConnection.Query<VeiculoRntrcDTO>(sql, parameters).FirstOrDefault();
            }
        }
        
    }
}

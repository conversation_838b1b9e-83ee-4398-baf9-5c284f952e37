using System;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioGridTriagemDTO : FiltrosGridBaseModel
    {
        public new int? IdEmpresa { get; set; }
        public int? IdEstabelecimento { get; set; }
        public int? IdAssociacao { get; set; }
        public DateTime? DataGeracaoInicial { get; set; }
        public DateTime? DataGeracaoFinal { get; set; }
        public DateTime? DataPagamentoInicial { get; set; }
        public DateTime? DataPagamentoFinal { get; set; }
    }
}
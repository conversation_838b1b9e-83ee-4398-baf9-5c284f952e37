﻿using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Webservice.Request.AutorizacaoEmpresa;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace ATS.WS.ControllersATS
{
    public class AutorizacaoEmpresaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IAutorizacaoEmpresaApp _autorizacaoEmpresaApp;
        private readonly IEmpresaApp _empresaApp;
        public AutorizacaoEmpresaAtsController(IUserIdentity userIdentity, IAutorizacaoEmpresaApp autorizacaoEmpresaApp, IEmpresaApp empresaApp)
        {
            _userIdentity = userIdentity;
            _autorizacaoEmpresaApp = autorizacaoEmpresaApp;
            _empresaApp = empresaApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(AutorizacaoEmpresaAtsGridFilter @params)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (@params.Filters == null)
                        @params.Filters = new List<QueryFilters>();

                    @params.Filters.Add(new QueryFilters
                    {
                        Campo = "IdEmpresa",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = _userIdentity.IdEmpresa.ToString()
                    });
                }

                var autorizacoes = _autorizacaoEmpresaApp.ConsultarGrid(@params.Take, @params.Page, @params.Order, @params.Filters);

                return ResponderSucesso(autorizacoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult Atualizar(AutorizacaoEmpresaAtsCadastrarAtualizarCls @params)
        {
            try
            {
                var emp = _empresaApp.GetWithChilds(@params.IdEmpresa);
                if (emp == null)
                    throw new Exception($"Nenhuma empresa encontrada para o id {@params.IdEmpresa}!");

                // Limpa os antigos
                var idsDelete = (from p in emp.Autorizacoes
                                 where !@params.Permissoes.Contains(p.IdMenu)
                                 select p.IdMenu).ToList();

                for (int i = 0; i <= idsDelete.Count - 1; i++)
                {
                    var toDeleteItem = emp.Autorizacoes.FirstOrDefault(x => x.IdMenu == idsDelete[i] && x.IdEmpresa == @params.IdEmpresa);
                    emp.Autorizacoes.Remove(toDeleteItem);
                }

                @params.Permissoes.ForEach(menuId =>
                {
                    if (!emp.Autorizacoes.Any(x => x.IdMenu == menuId))
                    {
                        // Então add..
                        emp.Autorizacoes.Add(new AutorizacaoEmpresa
                        {
                            IdMenu = menuId,
                            HasPermissao = true
                        });
                    }
                });

                var successUpdate = _empresaApp.Update(emp);
                if (!successUpdate.IsValid)
                    return ResponderErro(successUpdate.ToFormatedMessage());

                return ResponderSucesso("Autorização de empresa atualizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
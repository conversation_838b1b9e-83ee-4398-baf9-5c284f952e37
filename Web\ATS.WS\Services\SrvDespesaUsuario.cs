﻿using System;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Mobile.Request.Validators;
using AutoMapper;

namespace ATS.WS.Services
{
    public class SrvDespesaUsuario : SrvBase
    {
        private readonly IDespesaUsuarioApp _app;
        private readonly IDespesaCriarAplicativoModelValidator _validate;
        private readonly IDataMediaServerRepository _mongoRepository;

        public SrvDespesaUsuario(IDespesaUsuarioApp app, IDespesaCriarAplicativoModelValidator validate, IDataMediaServerRepository mongoRepository)
        {
            _app = app;
            _validate = validate;
            _mongoRepository = mongoRepository;
        }

        public BusinessResult Criar(DespesaCriarAplicativoModel @model)
        {
            var valid = _validate.Validate(@model);
            
            if (!valid.IsValid)
                return BusinessResult.Error(valid.Errors.Select(x => x.ErrorMessage).ToList());

            var modelApp = Mapper.Map<DespesaUsuarioAddModel>(@model);

            if (!string.IsNullOrWhiteSpace(@model.Despesa.FotoNotaBase64))
                modelApp.ImageToken = _mongoRepository.Add(1, @model.Despesa.FotoNotaBase64, Guid.NewGuid().ToString()).ToString();

            var result = _app.Add(modelApp);
            
            return !result.Success ? BusinessResult.Error(result.Messages) : BusinessResult.Valid();
        }
    }
}
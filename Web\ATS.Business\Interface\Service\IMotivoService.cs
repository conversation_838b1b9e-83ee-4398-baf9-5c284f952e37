﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IMotivoService : IService<Motivo>
    {
        IQueryable<Motivo> GetAtivos(int idEmpresa, ETipoMotivo? tipo = null);

        Motivo Get(int idMotivo);
    }
}

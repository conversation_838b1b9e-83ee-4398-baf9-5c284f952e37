﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class FilialContatoService : ServiceBase, IFilialContatoService
    {
        private readonly IFilialContatosRepository _filialContatosRepository;

        public FilialContatoService(IFilialContatosRepository filialContatosRepository)
        {
            _filialContatosRepository = filialContatosRepository;
        }

        /// <summary>
        /// Atualiza filial contato
        /// </summary>
        /// <param name="filialContato"></param>
        /// <returns></returns>
        public ValidationResult Update(FilialContatos filialContato)
        {
            try
            {
                _filialContatosRepository.Update(filialContato);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }
            
            return new ValidationResult();
        }

        /// <summary>
        /// Adiciona filial contato
        /// </summary>
        /// <param name="filialContato"></param>
        /// <returns></returns>
        public ValidationResult Add(FilialContatos filialContato)
        {
            try
            {
                _filialContatosRepository.Add(filialContato);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Remove filial contato
        /// </summary>
        /// <param name="filialContato"></param>
        /// <returns></returns>
        public ValidationResult Delete(FilialContatos filialContato)
        {
            try
            {
                _filialContatosRepository.Delete(filialContato);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using System.Linq.Expressions;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Proprietarios;

namespace ATS.Application.Interface
{
    public interface IProprietarioApp : IBaseApp<IProprietarioService>
    {
        Proprietario Get(int id);
        Proprietario GetAllChilds(int id);
        ValidationResult Add(Proprietario proprietario);
        ValidationResult Update(Proprietario proprietario);
        Proprietario GetPorCpfCnpj(string cpfCnpj);
        Proprietario GetPorCpfCnpj(string cpf, List<string> cnpjs);
        Proprietario GetPorCpfCnpj(string cpfCnpj, int? idEmpresa);
        int? GetIdPorCpfCnpj(string cpfCnpj, int? idEmpresa = null);
        int? GetIdByCpfCnpjWithEmpresa(string cpfCnpj, int? idEmpresa);
        ProprietarioConsultaDetalheViagemResponse ConsultaDetalheParaViagem(ProprietarioConsultaDetalheViagemRequest request);
        ValidationResult AtualizarRntrc(ProprietarioAtualizarRNTRCRequest request);
        AtualizaBaseEquiparadoTacResponse AtualizarBaseEquiparadoTac(string cnpjcpf = null);
        AtualizaBaseEquiparadoTacResponse AtualizarCadastroServicoCartao(List<string> cnpjcpf);
        IQueryable<Proprietario> All();
        IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, int? idUsuarioLogOn, bool? onlyAtivo);
        int? GetIdProprietario(int idEmpresa, string cpfCnpjEmpresa);
        ValidationResult AlterarStatus(int idProprietario);
        object ConsultarGridProprietarios(int? idEmpresa, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        byte[] GerarRelatorioGridProprietarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            string logo, string extensao);
        ConsultaProprietarioPorCnpjCpfResponseDTO ConsultarPorId(int idProprietario);
        ConsultaProprietarioPorCnpjCpfResponseDTO ConsultarPorCnpjCpf(string cnpjCpf, int idEmpresa);
        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario,
            string rntrcProprietario, string cnpjEmpresa);
        ConsultaSituacaoTransportadorResponse ConsultarSituacaoAntt(string cpfCnpj, string cnpjEmpresa);
        ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa);
        IQueryable<Proprietario> Find(Expression<Func<Proprietario, bool>> predicate, bool @readonly = false);
        bool Any(string cnpjCpf, int idEmpresa);
        bool PermiteTransferenciaSemCartaFrete(string cnpjCpf);
        bool PertenceAEmpresa(int idempresa, int idProprietario);
    }
}
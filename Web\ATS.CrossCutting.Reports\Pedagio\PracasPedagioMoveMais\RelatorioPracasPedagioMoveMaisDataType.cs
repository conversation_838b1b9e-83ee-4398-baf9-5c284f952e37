﻿using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Pedagio.PracasPedagioMoveMais
{
    public class RelatorioPracasPedagioMoveMaisDataType
    {
        public string ValorTotalPassagem { get; set; }
        public string ValorTotalEstimado { get; set; }
        public List<RelatorioPracasPedagioMoveMaisItemDataType> items { get; set; }
    }

    public class RelatorioPracasPedagioMoveMaisItemDataType
    { 
        public string PrecoEstimado { get; set; }
        public string PrecoPassagem { get; set; }
        public string Descricao { get; set; }
        public string DataPassagem { get; set; }
        public string Planejado { get; set; }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using System;
using System.Linq;
using ATS.Application.Interface;
using ATS.WS.Models;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class ProdutoController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IProdutoApp _produtoApp;

        public ProdutoController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaApp empresaApp, IProdutoApp produtoApp) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaApp = empresaApp;
            _produtoApp = produtoApp;
        }

        [System.Web.Http.HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string IntegrarAlterarProduto(IntegrarProdutoModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CnpjAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var emp = _empresaApp.GetIdPorCnpj(@params.CnpjEmpresa);
                if (!emp.HasValue)
                    throw new Exception($"Não foi possível encontrar uma empresa com o CNPJ {@params.CnpjEmpresa}!");

                if (string.IsNullOrWhiteSpace(@params.Descricao) || string.IsNullOrEmpty(@params.Descricao))
                    throw new Exception("O campo 'Descrição' é obrigatório!");

                if (!@params.IdProduto.HasValue)
                {
                    var prodToAdd = new Produto
                    {
                        Descricao = @params.Descricao,
                        Ativo = true,
                        IdEmpresa = emp.Value
                    };

                    var retorno = _produtoApp.Add(prodToAdd);
                    if (retorno.IsValid)
                    {

                        return new JsonResult().Responde(new Retorno<object>
                        {
                            Mensagem = $"Produto '{@params.Descricao}' inserido com sucesso!",
                            Sucesso = true,
                            Objeto = new
                            {
                                prodToAdd.IdProduto,
                                prodToAdd.Descricao,
                                prodToAdd.Ativo,
                                prodToAdd.IdEmpresa
                            }
                        });
                    }
                    throw new Exception(retorno.Errors.FirstOrDefault()?.Message);
                }


                var prod = _produtoApp.GetPorId(@params.IdProduto.Value);
                if (prod != null)
                {
                    prod.Descricao = @params.Descricao;
                    if (@params.Ativo.HasValue)
                        prod.Ativo = @params.Ativo.Value;

                    var retorno = _produtoApp.Update(prod);
                    if (retorno.IsValid)
                    {
                        return new JsonResult().Responde(new Retorno<object>
                        {
                            Mensagem = $"Produto '{prod.IdProduto}' atualizado com sucesso!",
                            Sucesso = true
                        });
                    }
                }

                return new JsonResult().Responde(new Retorno<object>
                {
                    Mensagem = $"Não foi possível localizar o produto {@params.IdProduto.Value}!",
                    Sucesso = false
                });
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [System.Web.Http.HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(string token, string cnpjAplicacao, string cnpjEmpresa, int? idProduto)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                var emp = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
                if (!emp.HasValue)
                    throw new Exception($"Não foi possível encontrar uma empresa com o CNPJ {cnpjEmpresa}!");

                var produtos = _produtoApp.ConsultarPorEmpresa(emp.Value);

                produtos.ForEach(prod =>
                {
                    prod.Empresa = null;
                });

                return new JsonResult().Responde(produtos);
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

    }
}
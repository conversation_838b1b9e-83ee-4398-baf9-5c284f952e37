﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.CrossCutting.IoC.Utils
{
    public static class DateUtils
    {
        public static DateTime StartOfDay(this DateTime aValue)
        {
            return aValue.Date;
        }

        public static DateTime GetDataOnly(this DateTime data)
        {
            return new DateTime(data.Year, data.Month, data.Day);
        }

        public static DateTime EndOfDay(this DateTime aValue)
        {
            return new DateTime(aValue.Year, aValue.Month, aValue.Day, 23, 59, 59, 999);
        }

        public static DateTime EndOfDayWithDelay(this DateTime aValue)
        {
            return new DateTime(aValue.Year, aValue.Month, aValue.Day, 23, 59, 00);
        }

        public static DateTime StartOfMonth(this DateTime aValue)
        {
            return new DateTime(aValue.Year, aValue.Month, 1);
        }

        public static DateTime EndOfMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, DateTime.DaysInMonth(dateTime.Year, dateTime.Month));
        }
    }
}
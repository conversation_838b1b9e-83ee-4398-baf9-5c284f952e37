﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class FilialAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvFilial _srvFilial;
        private readonly IFilialApp _filialApp;

        public FilialAtsController(IUserIdentity userIdentity, IParametrosApp parametrosApp, SrvFilial srvFilial, IFilialApp filialApp)
        {
            _userIdentity = userIdentity;
            _parametrosApp = parametrosApp;
            _srvFilial = srvFilial;
            _filialApp = filialApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(FilialIntegrarRequestModel model)
        {
            try
            {
                if (!_parametrosApp.GetPermitirEdicaoDadosAdministrativosFilial(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");
                        
                if (model.IdFilial.HasValue)
                {
                    if (_userIdentity.IdEmpresa.HasValue)
                    {
                        if (!_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, model.IdFilial.Value))
                            return ResponderErro("Registro não encontrado.");
                    }

                    var retornoEdit = _srvFilial.Editar(model);
                    return retornoEdit.IsValid
                        ? ResponderSucesso("Filial atualizada com sucesso!")
                        : ResponderErro(retornoEdit.Errors.FirstOrDefault()?.Message);
                }
                
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && model.IdEmpresa != _userIdentity.IdEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                var retorno = _srvFilial.Cadastrar(model);
                return retorno.IsValid
                    ? ResponderSucesso("Filial cadastrada com sucesso!")
                    : ResponderErro(retorno.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorEmpresa(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var filiais = _filialApp
                    .Consultar(null, idEmpresa ?? _userIdentity.IdEmpresa)
                    .Where(x => x.Ativo)
                    .Select(x => new
                    {
                        x.IdFilial,
                        x.RazaoSocial,
                        x.NomeFantasia,
                        x.CNPJ
                    });

                return ResponderSucesso(filiais);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var filiaisGrid = _filialApp.ConsultarGrid(idEmpresa, take, page, order, filters);

                return ResponderSucesso(filiaisGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult AlterarStatus(int idFilial)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idFilial))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _filialApp.AlterarStatus(idFilial);

                return !retorno.IsValid
                    ? ResponderErro(retorno.Errors.FirstOrDefault()?.Message)
                    : ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idFilial)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idFilial))
                        return ResponderErro("Registro não encontrado.");
                }

                var lFilial = _filialApp.GetFilialCadastro(idFilial);
                return lFilial == null ? ResponderErro("Filial não encontrada.") : ResponderSucesso(string.Empty, lFilial);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaFilialPorEmpresa(int? idEmpresa = null)
        {
            try
            {
                if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var filiais = _filialApp.GetListaFilialPorEmpresa(idEmpresa.GetValueOrDefault()).ToList()
                    .OrderBy(o => o.NomeFantasia)
                    .Select(o => new
                    {
                        Codigo = o.IdFilial,
                        Descricao = o.NomeFantasia,
                        o.Latitude,
                        o.Longitude
                    });

                var lResposta = ResponderSucesso(filiais);

                return lResposta;
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridFiliais(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _srvFilial.GerarRelatorioGridFiliais(filtrosGridModel.IdEmpresa,
                filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de filiais.{filtrosGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult UsuarioPermiteModuloAlcadas()
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Administrador)
                    return ResponderSucesso(string.Empty, true);

                if (_userIdentity.Perfil != (int) EPerfil.Empresa)
                    return ResponderSucesso(string.Empty, false);

                var usuario = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(_userIdentity.IdUsuario);

                return ResponderSucesso(string.Empty, usuario.PermiteFilial);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetBloqueioGestorValor(int? idEmpresa, int? idFilial)
        {
            try
            {
                if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idFilial ?? 0))
                        return ResponderErro("Registro não encontrado.");
                }

                return ResponderSucesso(string.Empty, _srvFilial.GetBloqueioGestorValor(idEmpresa, idFilial));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
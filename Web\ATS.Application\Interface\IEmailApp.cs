﻿using System;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Models;

namespace ATS.Application.Interface
{
    public interface IEmailApp
    {
        ValidationResult EnviarEmail(EmailModel emailMode, int? administradora = null);

        ValidationResult TestarEmail(string emailNome, string emailEndereco, int porta, bool usaSSL, string servidor, string logon, string senha);

        EmailConfiguration GetEmailConfiguration(int? idFilial, int idEmpresa);

        void EnviarEmailAsync(EmailModel emailModel, ConfiguracaoEnvioEmail config = null);
    }
   
}
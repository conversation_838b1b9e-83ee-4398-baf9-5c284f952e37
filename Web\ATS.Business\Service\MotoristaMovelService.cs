﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System.Linq;

namespace ATS.Domain.Service
{
    public class MotoristaMovelService : ServiceBase, IMotoristaMovelService
    {
        private readonly IMotoristaMovelRepository _motoristaMovelRepository;

        public MotoristaMovelService(IMotoristaMovelRepository motoristaMovelRepository)
        {
            _motoristaMovelRepository = motoristaMovelRepository;
        }

        /// <summary>
        /// Retorna todos os moveis cadastrados para um determinado motorista
        /// </summary>
        /// <param name="idMotorista">Código do motorista</param>
        /// <returns></returns>
        public IQueryable<MotoristaMovel> GetPorMotorista(int idMotorista)
        {
            return _motoristaMovelRepository.Find(x => x.IdMotorista == idMotorista);            
        }

        /// <summary>
        /// Validar o movel do motorista para os processos de CRUD
        /// </summary>
        /// <param name="movel">Dados do movel</param>
        /// <returns></returns>
        public ValidationResult IsValid(MotoristaMovel movel)
        {
            ValidationResult validationResult = new ValidationResult();
            validationResult.Add(AssertionConcern.AssertArgumentLength(movel.IMEI, 10, 15, @"IMEI deve ser válido."));

            return validationResult;
        }

        public MotoristaMovel Get(int id)
        {
            throw new System.NotImplementedException();
        }
    }
}
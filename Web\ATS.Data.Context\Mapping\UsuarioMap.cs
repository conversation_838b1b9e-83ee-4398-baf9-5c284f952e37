using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioMap : EntityTypeConfiguration<Usuario>
    {
        public UsuarioMap()
        {
            ToTable("USUARIO");
            
            HasKey(t => t.IdUsuario);

            Property(t => t.IdUsuario)
                .HasColumnOrder(0)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa)
                .IsOptional()
                .HasIndex("IX_USUARIO_CPFCNPJ_ATIVO_IDEMPRESA", true, 0);

            Property(t => t.IdGrupoUsuario)
                .IsOptional();

            Property(t => t.IdFacebook)
                .IsOptional();
            
            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Login)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Senha)
                .IsRequired()
                .HasMaxLength(50);

            Property(x => x.UsoAppEmpresa)
                .IsOptional();

            Property(t => t.CPFCNPJ)
                .IsOptional()
                .HasMaxLength(14)
                .HasIndex("IX_USUARIO_CPFCNPJ_ATIVO_IDEMPRESA", true, 1);

            Property(t => t.IdPush)
                .IsOptional()
                .HasMaxLength(300);

            Property(t => t.RNTRC)
                .IsOptional()
                .HasMaxLength(8);

            Property(t => t.RG)
                .IsOptional();

            Property(t => t.Ativo)
                .HasIndex("IX_USUARIO_CPFCNPJ_ATIVO_IDEMPRESA", true, 2);

            Property(t => t.RGOrgaoExpedidor)
                .IsOptional();

            Property(t => t.Carreteiro)
                .IsRequired();

            Property(t => t.PermiteResponderChat)
                .IsRequired();

            Property(t => t.DataCadastro)
                .IsRequired();

            Property(t => t.DataUltimoAcessoWeb)
                .IsOptional();

            Property(t => t.DataUltimoAcessoAplicativo)
                .IsOptional();
            
            Property(t => t.RecebeEmailGestao)
                .IsRequired();

            HasOptional(t => t.Empresa)
                .WithMany(t => t.Usuarios)
                .HasForeignKey(d => d.IdEmpresa);

            HasOptional(t => t.GrupoUsuario)
                .WithMany(t => t.Usuarios)
                .HasForeignKey(d => d.IdGrupoUsuario);

            HasMany(t => t.CampanhaRespostas)
                .WithRequired(t => t.Usuario)
                .HasForeignKey(d => d.IdUsuario);

            Property(x => x.Gestor)
                .IsRequired();

            Property(x => x.Login)
                .IsOptional();

            Property(t => t.ReceberRelatorioOC)
               .IsRequired();

            Property(o => o.VistoriadorMaster)
                .IsRequired();
            
            Property(t => t.NomeMae)
                .IsOptional()
                .HasMaxLength(100);
            
            Property(t => t.NomePai)
                .IsOptional()
                .HasMaxLength(100);

            Property(o => o.DataUltimaAberturaAplicativo)
                .IsOptional();

            Property(o => o.UsuarioMasterEstabelecimento)
                .IsOptional();

            Property(o => o.MostrarVideosTreinamento)
                .IsOptional();

            Property(o => o.KeyCodeTransaction)
                .IsOptional()
                .HasMaxLength(100);

            HasMany(t => t.BannersUsuario)
                .WithRequired(t => t.Usuario)
                .HasForeignKey(d => d.IdUsuario);

            HasMany(t => t.BannersCadastrados)
                .WithOptional(t => t.UsuarioCadastro)
                .HasForeignKey(d => d.IdUsuarioCadastro);

            HasMany(t => t.BannersAtivados)
                .WithOptional(t => t.UsuarioAtivacao)
                .HasForeignKey(d => d.IdUsuarioAtivacao);

            HasMany(t => t.BannersDesativados)
                .WithOptional(t => t.UsuarioDesativacao)
                .HasForeignKey(d => d.IdUsuarioDesativacao);

            Property(o => o.QuantidadeErroSenha)
                .IsOptional();
        }
    }
}

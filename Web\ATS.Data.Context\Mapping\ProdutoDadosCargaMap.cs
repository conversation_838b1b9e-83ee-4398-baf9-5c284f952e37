﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ProdutoDadosCargaMap : EntityTypeConfiguration<ProdutoDadosCarga>
    {
        public ProdutoDadosCargaMap()
        {
            ToTable("PRODUTO_DADOSCARGA");

            HasKey(t => new { t.IdDadosCarga });

            Property(t => t.IdDadosCarga)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdProduto).IsOptional();
            Property(t => t.NumeroPedido).HasMaxLength(100).IsOptional();
            Property(t => t.OrdemCompra).HasMaxLength(100).IsOptional();
            Property(t => t.Protocolo).HasMaxLength(100).IsOptional();
            Property(t => t.Formula).HasMaxLength(100).IsOptional();
            Property(t => t.Quantidade).HasMaxLength(100).IsOptional();
            Property(t => t.Armazem).HasMaxLength(150).IsOptional();
            
            HasOptional(x => x.Produto)
                .WithMany(x => x.ProdutoDadosCarga)
                .HasForeignKey(x => x.IdProduto);
        }
    }
}

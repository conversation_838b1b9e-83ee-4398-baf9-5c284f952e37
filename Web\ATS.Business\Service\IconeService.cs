﻿using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Enum;
using System;

namespace ATS.Domain.Service
{
    public class IconeService : ServiceBase, IIconeService
    {
        private readonly IIconeRepository _iconeRepository;

        public IconeService(IIconeRepository iconeRepository)
        {
            _iconeRepository = iconeRepository;
        }

        public IQueryable<Icone> Consultar()
        {
            var icones = _iconeRepository.Find(x => x.Ativo);

            return icones;
        }

        public Icone GetFirst(EIconePara? iconePara)
        {
            if (iconePara.HasValue)
                return _iconeRepository.FirstOrDefault(x => x.IconePara == iconePara);

            return _iconeRepository.FirstOrDefault();
        }

        public Icone Get(int idIcone)
        {
            return _iconeRepository.FirstOrDefault(x => x.IdIcone == idIcone);
        }
    }
}

﻿using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.Helpers;

namespace ATS.Domain.DTO.Pix
{
    public class ValidarPlanilhaSolicitacaoChavePixResponse
    {
        public int Total { get; set; }
        public int TotalInvalidos { get; set; }
        public int TotalValidos { get; set; }
        public string CodigoPlanilhaImportada { get; set; }
        public List<ValidarPlanilhaSolicitacaoChavePixResponseItem> Registros { get; set; }
    }
    
    public class ValidarPlanilhaSolicitacaoChavePixResponseItem
    {
        public bool Valido { get; set; }
        public string NomeSheet { get; set; }
        public int Linha { get; set; }
        public string MensagemValidacao { get; set; }
        public string DocumentoTitular { get; set; }
        public ETipoChavePix? TipoChave { get; set; }
        public string TipoChaveString => TipoChave.GetDescription();
        public string Chave { get; set; }
    }
}
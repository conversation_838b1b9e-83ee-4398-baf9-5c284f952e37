using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    public class Estado
    {
        /// <summary>
        /// Código do Estado
        /// </summary>
        public int IdEstado { get; set; }

        /// <summary>
        /// Código do país
        /// </summary>
        public int IdPais { get; set; }

        /// <summary>
        /// Nome
        /// </summary>
        public string Nome { get; set; }

        /// <summary>
        /// Sigla
        /// </summary>
        public string Sigla { get; set; }

        /// <summary>
        /// Código do IBGE
        /// </summary>
        public int? IBGE { get; set; }

        /// <summary>
        /// Ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Data de alteração do registro
        /// </summary>
        public DateTime DataAlteracao { get; set; }

        /// <summary>
        /// Região do Brasil que pertence o estado
        /// </summary>
        public ERegiaoBrasil Regiao { get; set; } = ERegiaoBrasil.NaoEncontrada;

        #region Referências

        public virtual Pais Pais { get; set; }

        #endregion

        #region Navegação inversa

        public virtual ICollection<ClienteEndereco> ClienteEnderecos { get; set; }

        public virtual ICollection<Cidade> Cidades { get; set; }

        public virtual ICollection<Filial> Filiais { get; set; }
        public virtual ICollection<Estabelecimento> Estabelecimentos { get; set; }
        public virtual ICollection<EstabelecimentoBase> EstabelecimentosBase { get; set; }
        public virtual ICollection<Veiculo> Veiculo { get; set; }

        #endregion
    }
}
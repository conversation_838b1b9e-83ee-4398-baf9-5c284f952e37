﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Estado;
using AutoMapper;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class EstadoController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEstadoApp _estadoApp;

        public EstadoController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEstadoApp estadoApp) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _estadoApp = estadoApp;
        }

        /// <summary>
        /// Retorna as cidades atualizadas a partir da data base informada
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string Consultar(ConsultarEstadoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(new Retorno<List<EstadoModel>>(true, string.Empty,
                    Mapper.Map<List<Estado>, List<EstadoModel>>(_estadoApp
                        .GetEstadosAtualizados(@params.DataBase, @params.Regiao).ToList())));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string ConsultarPorPais(int idPais)
        {
            try
            {
                var estados =
                    _estadoApp.ConsultarPorIdPais(idPais)
                        .ToList()
                        .OrderBy(o => o.Nome)
                        .Select(o => new {o.IdEstado, Descricao = o.Nome});

                return new JsonResult().Responde(estados);
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
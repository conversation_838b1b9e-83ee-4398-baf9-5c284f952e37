﻿using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class LayoutCartao
    {
        /// <summary>
        /// Id
        /// </summary>
        public int IdLayout { get; set; }

        /// <summary>
        /// Nome do Layout
        /// </summary>
        public string Nome { get; set; }

        /// <summary>
        /// Itens do LayOutCartao
        /// </summary>
        public virtual IList<LayoutCartaoItem> Itens { get; set; }
    }
}
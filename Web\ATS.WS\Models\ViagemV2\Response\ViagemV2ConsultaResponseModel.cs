using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Models.Ciot;

namespace ATS.WS.Models.ViagemV2.Response
{
    public class ViagemV2ConsultaResponseModel
    {
        public int ViagemId { get; set; }

        public string ClienteOrigem { get; set; }
        
        public string ClienteOrigemDocumento { get; set; }

        public string ClienteDestino { get; set; }
        public string ClienteDestinoDocumento { get; set; }

        public string Filial { get; set; }
        public string FilialDocumento { get; set; }
        
        public string DataColeta { get; set; }
        public string DataPrevisaoEntrega { get; set; }

        public string Motorista { get; set; }
        public string MotoristaDocumento { get; set; }
        
        public string Proprietario { get; set; }
        public string ProprietarioDocumento { get; set; }

        public EStatusViagem StatusViagem { get; set; }
        public string StatusViagemDescricao { get; set; }

        public decimal? PesoSaida { get; set; }

        public decimal? PesoChegada { get; set; }

        public string EnderecoColeta { get; set; }
        public string EnderecoEntrega { get; set; }

        public string Produto { get; set; }
        public int? NaturezaCarga { get; set; }

        public decimal? Irrpf { get; set; }
        public decimal? Inss { get; set; }
        public decimal? SestSenat { get; set; }

        public List<ViagemV2EventosConsultaResponseModel> Eventos { get; set; }
        public DeclararCiotResult Ciot { get; set; }
        public SolicitarCompraPedagioResponseDTO Pedagio { get; set; }
        public List<ViagemV2EstabelecimentosAutorizadosResponseModel> EstabelecimentosAutorizados { get; set; }
    }

    public class ViagemV2EventosConsultaResponseModel
    {
        public int ViagemEventoId { get; set; }
        public string NumeroControle { get; set; }
        public string Token { get; set; }
        public ETipoEventoViagem TipoEventoViagem { get; set; }
        public string TipoEventoViagemDescricao { get; set; }
        public decimal? ValorBruto { get; set; }
    }


    public class ViagemV2CiotConsultaResponseModel
    {
        public int Resultado { get; set; }
        public bool Declarado { get; set; }
        public string Mensagem { get; set; }
    }

    public class ViagemV2PedagioConsultaResponseModel
    {
        public string Status { get; set; }
        public string Mensagem { get; set; }
        public decimal? Valor { get; set; }
    }
    
    public class ViagemV2EstabelecimentosAutorizadosResponseModel
    {
        public string Cnpj { get; set; }
        public string Nome { get; set; }
        public ETipoEvento TipoEvento { get; set; }
    }
}
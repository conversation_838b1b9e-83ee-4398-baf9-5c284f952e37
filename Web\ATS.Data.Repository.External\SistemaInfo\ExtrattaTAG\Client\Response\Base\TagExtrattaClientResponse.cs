namespace ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Client.Response.Base
{
    public class TagExtrattaClientResponse<T>
    {
        public T Result { get; private set; }
        public bool Success { get; private set; }
        public int? StatusCode { get; private set; }
        public string Messages { get; private set; }

        private TagExtrattaClientResponse(bool sucess)
        {
            Success = sucess;
        }

        private TagExtrattaClientResponse(bool sucess, string messages)
        {
            Success = sucess;
            Messages = messages;
        }

        public TagExtrattaClientResponse(bool sucess, string messages, int statusCode)
        {
            Success = sucess;
            Messages = messages;
            StatusCode = statusCode;
        }
        
        private TagExtrattaClientResponse(bool sucess,T result)
        {
            Success = sucess;
            Result = result;
        }
        
        private TagExtrattaClientResponse(bool sucess, string messages,T result)
        {
            Success = sucess;
            Messages = messages;
            Result = result;
        }

        public static TagExtrattaClientResponse<T> Valid()
        {
            return new TagExtrattaClientResponse<T>(true);
        }

        public static TagExtrattaClientResponse<T> Valid(string message)
        {
            return new TagExtrattaClientResponse<T>(true, message);
        }
        
        public static TagExtrattaClientResponse<T> Valid(string message,T result)
        {
            return new TagExtrattaClientResponse<T>(true, message,result);
        }
        
        public static TagExtrattaClientResponse<T> Valid(T result)
        {

            return new TagExtrattaClientResponse<T>(true,result);
        }

        public static TagExtrattaClientResponse<T> Error(string message)
        {
            return new TagExtrattaClientResponse<T>(false, message);
        }
        
        public static TagExtrattaClientResponse<T> Error(string messages,int statusCode)
        {
            return new TagExtrattaClientResponse<T>(false, messages,statusCode);
        }
        

        public override string ToString()
        {
            return string.Join(", ", Messages);
        }
    }
}
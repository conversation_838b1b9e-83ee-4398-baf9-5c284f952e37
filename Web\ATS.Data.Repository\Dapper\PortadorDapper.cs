using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using Dapper;
using NLog;

namespace ATS.Data.Repository.Dapper
{
    public class PortadorDapper : DapperFactory<PortadorDto>, IPortadorDapper
    {
        public PortadorDto ConsultarPortadoresPaginado(int? idEmpresa, string documento, string nome, int itensPorPagina, int pagina)
        {
            try
            {
                var clauseWhereProprietario = idEmpresa.HasValue ? @" WHERE idempresa = @IdEmpresa and ativo = 1" : string.Empty;
                var clauseWhereMotorista = idEmpresa.HasValue ? @" WHERE idempresa = @IdEmpresa and ativo = 1" : string.Empty;
                var clauseWhereUsuario = idEmpresa.HasValue ?  @" AND idempresa = @IdEmpresa and ativo = 1" : string.Empty;

                if (!string.IsNullOrEmpty(documento))
                {
	                clauseWhereProprietario += string.IsNullOrEmpty(clauseWhereProprietario) ? @" WHERE cnpjcpf = @Documento" : @" AND cnpjcpf = @Documento";
	                clauseWhereMotorista += string.IsNullOrEmpty(clauseWhereMotorista) ? @" WHERE cpf = @Documento" : @" AND cpf = @Documento";
	                clauseWhereUsuario += @" AND cpfcnpj = @Documento";
                }

                if (!string.IsNullOrEmpty(nome))
                {
	                clauseWhereProprietario += string.IsNullOrEmpty(clauseWhereProprietario) ? @" WHERE nomefantasia like @Nome" : @" AND nomefantasia like @Nome";
	                clauseWhereMotorista += string.IsNullOrEmpty(clauseWhereProprietario) ? @" WHERE nome like @Nome" : @" AND nome like @Nome";
	                clauseWhereUsuario += @" AND nome like @Nome";
                }
                
                var query =
                    $@"DECLARE @ItensPorPagina INT = @ItensPorPaginaParam,
					 @Pagina INT = @PaginaParam,
					 @TotalRows INT

					 SELECT x.documento, 
							coalesce((SELECT TOP 1 p.nomefantasia FROM proprietario p WHERE p.cnpjcpf = x.documento), 
									 (SELECT TOP 1 m.nome FROM motorista m WHERE m.cpf = x.documento), 
									 (SELECT TOP 1 u.nome FROM usuario u WHERE u.cpfcnpj = x.documento)) AS nome

					 FROM (
						 SELECT cnpjcpf AS documento FROM PROPRIETARIO {clauseWhereProprietario}
						
						 UNION
						
						 SELECT cpf AS documento FROM MOTORISTA {clauseWhereMotorista}
						
						 UNION
						
						 SELECT cpfcnpj AS documento FROM USUARIO  where perfil  in (3, 5) {clauseWhereUsuario}
						 ) x
					 WHERE x.documento IS NOT NULL
					 ORDER BY nome 
					 OFFSET(@Pagina - 1) * @ItensPorPagina ROWS
					 FETCH NEXT @ItensPorPagina ROWS ONLY ";

                var queryTotal =
	                $@"SELECT count(*)
		                FROM (
			                SELECT cnpjcpf AS documento FROM PROPRIETARIO WITH (NOLOCK) {clauseWhereProprietario}

		                UNION

			                SELECT cpf AS documento FROM MOTORISTA WITH (NOLOCK) {clauseWhereMotorista}

		                UNION

			                SELECT cpfcnpj AS documento FROM USUARIO WITH (NOLOCK) where perfil in (3, 5) {clauseWhereUsuario}
			                ) x
			                WHERE x.documento IS NOT NULL";
				
                var parameters = new
                {
	                ItensPorPaginaParam = itensPorPagina, PaginaParam = pagina, IdEmpresa = idEmpresa,
	                Documento = documento, Nome = "%" + nome + "%"
                };
                
                using (var connection = new DapperContext().GetConnection)
                {
	                var result = connection.Query<PortadorItensDto>(query, parameters).ToList();
	                var resultCount = connection.Query<int>(queryTotal, parameters).FirstOrDefault();
	                return new PortadorDto
	                {
		                Itens = result,
		                TotalItens = resultCount
	                };
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return new PortadorDto();
            }
        }
        
        public MotoristaCargaAvulsaModel ConsultarPortadorCadastrado(string documento)
        {
	        var query = $@"SELECT x.Documento as Documento
							FROM (
							       SELECT cpf as Documento
							       FROM MOTORISTA

							       UNION

							       SELECT cnpjcpf as Documento
							       FROM PROPRIETARIO

							       UNION

							       SELECT cpfcnpj as Documento
							       FROM USUARIO
							     ) x
							where x.Documento = @Documento";

	        using (var connection = new DapperContext().GetConnection)
	        {
		        var parameters = new {Documento = documento};
		        var result = connection.Query<MotoristaCargaAvulsaModel>(query, parameters).FirstOrDefault();
		        
		        return result;
	        }
        }
    }
}
﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class ModuloMenuService : ServiceBase, IModuloMenuService
    {
        private readonly IModuloMenuRepository _moduloMenuRepository;

        public ModuloMenuService(IModuloMenuRepository moduloMenuRepository)
        {
            _moduloMenuRepository = moduloMenuRepository;
        }

        public IQueryable<ModuloMenu> GetPorIdMenu(int idMenu, List<int> modulosPermitidos)
        {
            var qry = _moduloMenuRepository
                    .Find(x => x.IdMenu == idMenu)
                    .Include(x => x.Modulo)
                    .Include(x => x.Menu);

            if (modulosPermitidos != null && modulosPermitidos.Any())
            {
                qry = qry.Where(x => modulosPermitidos.Contains(x.IdModulo));
            }

            return qry;
        }

        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="moduloMenu">Informações sobre a mensagem</param>
        /// <returns></returns>
        public ValidationResult Add(ModuloMenu moduloMenu)
        {
            try
            {
                _moduloMenuRepository.Add(moduloMenu);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }


        public ValidationResult DeleteRange(int idMenu)
        {
            try
            {
                var modulosMenu = _moduloMenuRepository.Find(x => x.IdMenu == idMenu).ToList();
                foreach (var moduloMenu in modulosMenu)
                {
                    _moduloMenuRepository.Delete(moduloMenu);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}

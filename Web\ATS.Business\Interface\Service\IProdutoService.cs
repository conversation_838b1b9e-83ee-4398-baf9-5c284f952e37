﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IProdutoService : IService<Produto>
    {
        ValidationResult Add(Produto produto);
        ValidationResult Inativar(int idProduto);
        List<Produto> ConsultarPorEmpresa(int emp);
        ValidationResult Reativar(int idProduto);
        object ConsultaGrid(int? idEmpresa, int? idProduto, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Produto Get(int idProduto);
        ValidationResult Update(Produto produto);
        Produto GetById(int idProduto);
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Net;
using ATS.Domain.Enum;

namespace ATS.Domain.Service
{
    public class PINService : ServiceBase, IPINService
    {

        private readonly IPINRepository _repository;
        private readonly ISMService _smService;

        public PINService(IPINRepository repository, ISMService smService)
        {
            _repository = repository;
            _smService = smService;
        }

        public ValidationResult Add(PIN entity)
        {
            try
            {
                var validation = new ValidationResult();

                _repository.Add(entity);

                return validation;

            } catch ( Exception e)
            {
                throw e;
            }
        }

        public PIN Get(int id)
        {
            return _repository.Get(id);
        }

        public PIN Get( string IdSMS)
        {
            return _repository.FirstOrDefault( x => x.IdSMS == IdSMS);
        }

        public List<PIN> Get(string celular, bool IsValid = false)
        {
            var pins =  _repository.Find(x => x.Celular == celular);

            if (IsValid)
                pins = pins.Where(x => x.DataValidade >= DateTime.Now && !x.Validado && !x.Inativo);

            return pins.ToList();

        }

        public ValidationResult ValidatePIN( string PIN, string celular )
        {
            var validation = new ValidationResult();
            var pins = Get(celular, true);

            if (pins == null || pins.Count() == 0)
                return validation.Add("Nenhum código de segurança válido encontrado!");

            var pin = pins.FirstOrDefault( x => x.Codigo == PIN);

            if (pin == null)
                return validation.Add("Nenhum código de segurança válido encontrado!");

            if (!pin.Enviado)
                validation.Add("Código de segurança ainda não foi enviado!");

            if (pin.DataValidade <= DateTime.Now)
                validation.Add("Código de segurança expirou!");

            if (pin.Validado)
                validation.Add("Código de segurança já foi validado!");

            pin.DataValidacao = DateTime.Now;
            pin.Validado = true;

            _repository.Update(pin);

            return validation;
        }

        public ValidationResult SendPIN(string celular, string codigo)
        {

            //var codigo = new Random().Next().ToString().Substring(0, 6);

            var bodySms = $"TMov\nUtilize este código de segurança:{codigo} para continuar o processo.";
            var smsResponse = _smService.EnviarSMS(celular, bodySms, EProcessoEnvio.SolicitacaoCodigoSeguranca);

            var PIN = new PIN()
            {
                Celular = celular,
                DataValidade = DateTime.Now.AddHours(2),
                DataCriacao = DateTime.Now,
                Codigo = codigo,
                IdSMS = smsResponse.id,
                Enviado = true
            };

            #region Verifica se celular informado é inválido
            var pinInvalido = Get(celular, true)?.FirstOrDefault(x => x.DataValidade <= DateTime.Now && !x.Enviado &&
                (x.StatusOperadora != ESMSSendStatus.INVALID_DESTINATION_NUMBER));

            if (pinInvalido != null)
            {
                 pinInvalido.Inativo = true;
                _repository.Update(pinInvalido);
                throw new Exception("Número de celular informádo é inválido! Entre em contato com a operadora!");
            }
            #endregion

            //Inativa pins que ainda não foram validados para permitir apenas o mais recente
            var pins = Get(celular, false)?.Where(x => !x.Inativo && !x.Validado);

            _repository.Add(PIN);

            if (pins != null && pins.Count() > 0)
            {
                foreach (var pin in pins)
                {
                    pin.Inativo = true;
                    _repository.Update(PIN);
                }
            }

            return new ValidationResult();
        }

        public ValidationResult SetEnviado(string IdSMS, bool status, ESMSDeliveredStatus statusCode, DateTime? DataEnvio)
        {
            var PIN = Get(IdSMS);

            if (PIN == null)
                return new ValidationResult().Add("Nenhum código de segurança encontrado!");

            PIN.StatusEnvio = statusCode;
            PIN.Enviado = status;
            PIN.DataEnvio = DataEnvio;

            _repository.Update(PIN);

            return new ValidationResult();
        }

        public ValidationResult SetEnviadoOperadora(string IdSMS, bool status, ESMSSendStatus statusCode, DateTime? DataEnvio)
        {
            var PIN = Get(IdSMS);

            if (PIN == null)
                return new ValidationResult().Add("Nenhum código de segurança encontrado!");

            PIN.StatusOperadora = statusCode;
            PIN.EnviadoOperadora = status;
            PIN.DataEnvioOperadora = DataEnvio;

            _repository.Update(PIN);

            return new ValidationResult();
        }

        public SMSResponse EnviarSMS(string celular, string message)
        {
            try
            {
                var webAddr = "https://api-messaging.movile.com/v1/send-sms";

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(webAddr);
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add("authenticationtoken:g2nPGSyVSgxpnqFxPKKc-3Rjxj1VKGExnAKGZyRS");
                httpWebRequest.Headers.Add("username:tmov-sms");
                httpWebRequest.Method = "POST";

                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    var lSMSBody = new
                    {
                        destination = $"55{celular}",
                        messageText = message
                    };
                    var json = JsonConvert.SerializeObject(lSMSBody);
                    streamWriter.Write(json);
                    streamWriter.Flush();
                }

                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    string content =  streamReader.ReadToEnd();
                    var response = JsonConvert.DeserializeObject<SMSResponse>(content);

                    return response;
                }
            }
            catch (Exception)
            {
                throw new Exception("Erro ao enviar SMS, solicite novamente.");
            }
        }

        public ValidationResult Update(PIN entity)
        {
            throw new NotImplementedException();
        }
    }
}
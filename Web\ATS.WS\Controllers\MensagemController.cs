﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Mensagem;
using ATS.WS.Services;
using ATS.Domain.Entities;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class MensagemController : BaseController
    {
        private readonly SrvMensagem _srvMensagem;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public MensagemController(BaseControllerArgs baseArgs, SrvMensagem srvMensagem, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _srvMensagem = srvMensagem;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarMensagens(MensagemConsultarRequestModel request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvMensagem
                                    .GetMensagensPorCpfCnpjUsuario(request.CPFCNPJUsuario, request.DataBase));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string AtualizaMensagem(AtualizarMensagemRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvMensagem.AtualizarMensagens(request));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public System.Web.Mvc.JsonResult SendBroadcast()
        {
            //porque nao valida token aqui ???????????
            var srvMsg = _srvMensagem;

            var ret = srvMsg.SendBroadcast(); 

            if (!ret)
                return Responde(new Retorno<Mensagem>(true, "Não foi possível enviar!", null));

            return Responde(new Retorno<Mensagem>(true, "Push enviado com sucesso!", null));
        }
    }
}
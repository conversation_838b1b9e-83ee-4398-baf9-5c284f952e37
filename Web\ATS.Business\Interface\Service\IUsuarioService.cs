﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Models.Usuario;
using ATS.Domain.DTO;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioService : IService<Usuario>
    {
        Usuario GetAllChilds(int id);
        string GetNome(int id);
        ValidationResult Add(Usuario usuario, int? idUsuarioLogon, string cnpjEmpresa);
        ValidationResult Update(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa = "");
        Usuario Get(int id, bool asNoTracking);
        Usuario GetUsuarioFromFacebook(long idFacebook);
        UsuarioTokenDTO ValidarUsuario(string login, string senha, long? idFacebook);
        UsuarioTokenDTO ValidarCodigoAcesso(string codigo, string session_state);
        ValidationResult SalvarLocalizacao(LocalizacaoUsuarioPortal localizacao);
        bool ExisteLocalizacaoPorUsuarioIp(int idUsuario, string ip);
        Usuario ValidarUsuarioPorCNPJCPF(string nCNPJCPF, string senha);
        ValidationResult UpdateSenha(int id, string senha, string novasenha, int idUsuarioLogon, bool verificarSenhaAtual);
        ValidationResult UpdateSenha(int id, string novasenha);
        IQueryable<Usuario> Consultar(string nome, int? idEmpresa, int idUsuarioLogOn, bool listarTerceiros);
        IQueryable<Usuario> Consultar(int idUsuario);
        byte[] GetFoto(int id);
        ValidationResult Inativar(int idUsuario, int idUsuarioRequisicao);
        ValidationResult Reativar(int idUsuario, int idUsuarioRequisicao);
        EStatusUsuario GetStatus(string nCNPJCPF);
        Usuario GetUsuarioEmpresa(int idEmpresa);
        IQueryable<Usuario> GetUsuariosPorEmpresa(int idEmpresa);
        ETipoContrato GetTipoContrato(int idUsuario, int? idEmpresa);
        string GetSenha(int id);
        EStatusUsuario GetStatus(string login, string senha);
        bool HasPermissaoAcessoMenu(int idUsuario, int idMenu);
        ValidationResult AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso);
        List<Usuario> GetUsuarioByIdPonto(int Idponto);
        bool ValidaSessaoUsuario(string key);
        Usuario GetUsuarioByKey(string key);
        List<UsuarioEstabelecimento> GetEstabelecimentos(int IdUsuario);
        Usuario ValidarUsuarioPorUsuario(string Usuario, string senha);
        ValidationResult UpdateUsuarioCheckList(Usuario usuario);
        UsuarioDocumento GetDocumentoCNHPorUsuarioId(int idUsuario);
        int? GetIdByTokenFirebase(string TokenFirebase);
        IQueryable<Usuario> GetQueryPorCNPJCPF(string nCNPJCPF);
        Dictionary<int, int> GetFirstIdsEstados(List<int> idsUsuarios);
        IQueryable<Usuario> GetUsuariosPorPerfisMensagens(int? idEmpresa, int idUsuario);
        IQueryable<Usuario> GetUsuariosChatByEmpresa(int idEmpresa);
        List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc);
        Usuario GetClientesByUsuario(int? idUsuario);
        List<UsuarioPreferencias> GetUsuarioPreferencias(int idUsuario, string campo = null);
        ValidationResult SaveUsuarioPreferencias(List<UsuarioPreferencias> usuarioPreferencias);
        List<UsuarioPreferencias> GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix);
        List<dynamic> ConsultarUsuarios(int idEmpresa, bool vistoriador);
        Usuario ValidarUsuarioPorToken(string TokenFirebase);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, IUserIdentity userIdentity);
        ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosLiderados(int idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosDisponiveisParaSeremLiderados(int idUsuario, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult IncluirUsuarioLiderado(int idUsuario, int? idEmpresa, int idUsuarioParaIncluir, int idUsuarioCadastro);
        ValidationResult RemoverUsuarioLiderado(int idUsuario, int idUsuarioParaRemover);
        IEnumerable<UsuarioEstabelecimento> GetUsuariosEstabelecimento(int idUsuario);
        //Usuario ResetarSenhaUsuario(string cpf, string email);
        bool ResetarSenha(int idUsuario, int idUsuarioLogon, string senha);
        bool Resetar2FA(int idUsuario);
        void EnviarEmailRecuperacaoSenha(Usuario usuario, Usuario usuarioRequisicao, string novaSenha, string dominio = null, string templateEmail = "recuperacao");
        IQueryable<object> GetFiliaisAutorizadas(int idUsuario);
        object ConsultaGrid(int? idEmpresa, int Take, int Page, OrderFilters orderFilters,
            List<QueryFilters> Filters, EPerfil prfUsuLogado, int? idUsuarioLogOn, bool listarTerceiros, bool usuariosAtivos);
        DateTime? GetUltimoAcessoUsuarioAplivativo(int IdUsuario);
        DateTime? GetUltimoAcessoUsuarioAplivativoByCpf(string CPFCNPJ);
        byte[] GerarRelatorioGridUsuarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            EPerfil perfilUsuarioLogado, int? idUsuario, bool listarTerceiros, string tipoArquivo, string logo);
        KeyValuePair<ValidationResult, string> GerarKeyCodeTransaction(int idUsuario, string cpf);
        List<Usuario> GetByPerfil(EPerfil perfil, int? idFilial, int? idEmpresa);
        IQueryable<Filial> GetFiliaisAutorizadasCompleto(int idUsuario);
        IQueryable<GridContatosModel> GetEmailsUsuarioByListaId(int[] listaIds);
        bool HasVeiculoCadastrado(string placa);
        int? GetIdEstabelecimentoBase(int idUsuario);
        IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario);
        IQueryable<int> GetIdsFiliaisAutorizadasCompleto(int idUsuario);
        Usuario GetComEstabelecimentos(int id);
        bool HasEstabelecimento(int idUsuario, int? idEstabelecimentoBase);
        Empresa GetEmpresaPorCPFCNPJUsuario(string cpfCnpjUsuario);
        object ConsultaGridPorEmpresa(int? idEmpresa, int? idFilial, int? idOperacao, int Take, int Page,
            OrderFilters orderFilters, List<QueryFilters> Filters, bool marcarTodos, int apertou, List<int> grupoUsuarios);
        ValidationResult AtualizarContatado(string aCPF, int aStatus);
        IQueryable<Usuario> GetQueryByLogin(string login);
        UsuarioPermissaoGestor GetConfiguracaoPermissaoGestor(int idusuario, EBloqueioGestorTipo tipo);
        Usuario GetUsuarioGestaoEntregas(string cpfCnpj);
        IQueryable<Usuario> Find(Expression<Func<Usuario, bool>> predicate);
        UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario);
        UsuarioFotoDto GetFotoUsuario(string cpfcnpj);
        ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? sistemaOperacional);
        IQueryable<Usuario> GetPorCnpjcpfEmpresaQueryable(string nCNPJCPF, int idEmpresa);
        string BuscaUsuarioMasterEstabelecimento(int idEstabelecimento);
        string GetNomeById(int idUsuario);
        IQueryable<Usuario> GetPorCnpjcpfQueryable(string nCNPJCPF);
        Usuario GetByCpf(string cpf, bool getDesabilitado = false);
        Usuario GetByLogin(string login);
        IQueryable<Usuario> GetByCpfQuery(string cpf, bool getDesabilitado = false);
        bool UserGroupIsActive(int idUsuario);
        String GerarSenhaAleatoria(EPerfil perfil, int length = 10);
        List<Usuario> GetTodos();
        bool IncrementarErroSenhaLoginMobile(int usuarioId);
        void ResetarErroSenhaLoginMobile(int usuarioId);
    }
}
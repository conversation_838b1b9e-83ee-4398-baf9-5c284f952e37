using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Request.Ciot
{
    public class EncerrarCiotRequest : RequestBase
    {
        public string Ciot { get; set; }

        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);
            
            if (string.IsNullOrWhiteSpace(Ciot))
                validacao.Add("É obrigatório o envio do campo Ciot");
            else
            {
                Ciot = Ciot.OnlyNumbers();
                
                if (Ciot.Length != 12)
                    validacao.Add("O campo Ciot está inválido");
            }

            return validacao;
        }
    }
}
﻿using ATS.Domain.Validation;

namespace ATS.WS.Models.Common.Request
{
    public class EstornarPedagioValorTagWebhookRequestModel
    {
        public string Token { get; set; }
        public long? EventoSaldoTagId { get; set; }
        
      public BusinessResult ValidRequest()
      {
          if(!EventoSaldoTagId.HasValue)
              return BusinessResult.Error("EventoSaldoTagId é obrigatório!");
          
          return BusinessResult.Valid();
      }
    }
}
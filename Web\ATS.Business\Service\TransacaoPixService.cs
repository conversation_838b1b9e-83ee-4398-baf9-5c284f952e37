using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.TransferenciaPix;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.Extratta.Models;
using ATS.Domain.DTO.Pix;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;

namespace ATS.Domain.Service
{
    public class TransacaoPixService : ServiceBase, ITransacaoPixService
    {
        private readonly IExtrattaBizApiClient _bizApiClient;
        private readonly IUserIdentity _userIdentity;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly ITransacaoPixRepository _transacaoPixRepository;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IBloqueioGestorValorService _bloqueioGestorValorService;
        private readonly IEmailService _emailService;
        private readonly IUsuarioService _usuarioService;
        private readonly IPushService _pushService;
        private readonly IEmpresaService _empresaService;
        private readonly IParametrosRepository _parametrosRepository;
        private readonly IViagemRepository _viagemRepository;
        private readonly CartoesServiceArgs _cartoesServiceArgs;

        public TransacaoPixService(IExtrattaBizApiClient bizApiClient,
            IUserIdentity userIdentity,
            IEmpresaRepository empresaRepository,
            IProprietarioRepository proprietarioRepository,
            IParametrosUsuarioService parametrosUsuarioService,
            IParametrosEmpresaService parametrosEmpresaService,
            IParametrosGenericoService parametrosGenericoService,
            ITransacaoPixRepository transacaoPixRepository,
            IParametrosProprietarioService parametrosProprietarioService,
            IViagemEventoRepository viagemEventoRepository, 
            IBloqueioGestorValorService bloqueioGestorValorService, 
            IEmailService emailService, 
            IUsuarioService usuarioService, 
            IPushService pushService, 
            IEmpresaService empresaService,
            IParametrosRepository parametrosRepository,
            IViagemRepository viagemRepository, 
            CartoesServiceArgs cartoesServiceArgs)
        {
            _bizApiClient = bizApiClient;
            _userIdentity = userIdentity;
            _empresaRepository = empresaRepository;
            _proprietarioRepository = proprietarioRepository;
            _parametrosUsuarioService = parametrosUsuarioService;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosGenericoService = parametrosGenericoService;
            _transacaoPixRepository = transacaoPixRepository;
            _parametrosProprietarioService = parametrosProprietarioService;
            _viagemEventoRepository = viagemEventoRepository;
            _bloqueioGestorValorService = bloqueioGestorValorService;
            _emailService = emailService;
            _usuarioService = usuarioService;
            _pushService = pushService;
            _empresaService = empresaService;
            _parametrosRepository = parametrosRepository;
            _viagemRepository = viagemRepository;
            _cartoesServiceArgs = cartoesServiceArgs;
        }

        public BusinessResult<TransferenciaPixModelResponse> EfetuarTransferenciaPix(int idEmpresaOrigem, string cpfCnpjProprietarioDestino, decimal valor,
            ViagemEvento evento, string mensagem, string documentoUsuarioAudit, bool integracao = false)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresaOrigem))
                    return BusinessResult<TransferenciaPixModelResponse>.Error($"Empresa sem permissão para efetuar pagamentos Pix.");

                if (!_parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(cpfCnpjProprietarioDestino, idEmpresaOrigem))
                    return BusinessResult<TransferenciaPixModelResponse>.Error($"Proprietário sem permissão para receber pagamentos Pix pela empresa.");

                #endregion
                
                #region Validacoes do evento e transacao

                //Verifica se o evento tá aberto ou baixado
                if (evento.Status != EStatusViagemEvento.Aberto && evento.Status != EStatusViagemEvento.Baixado)
                    return BusinessResult<TransferenciaPixModelResponse>.Error($"Status do evento está diferente de aberto ou baixado para pagamento Pix.");
                
                //Verifica se ja tem uma transacao de sucesso (Confirmado)
                var transacao = _transacaoPixRepository.FirstOrDefault(c =>
                    c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Confirmado);
                if (transacao != null)
                    return BusinessResult<TransferenciaPixModelResponse>.Valid(
                        $"Transerência Pix já efetuada em {(transacao.DataConfirmacao ?? transacao.DataCadastro).ToString("dd/MM/yyyy HH:mm")}", null);
                
                //Verifica se a transacao caiu pra aprovacao do gestor de alçadas
                transacao = _transacaoPixRepository.FirstOrDefault(c => 
                    c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Pendente);
                if (transacao != null)
                    return BusinessResult<TransferenciaPixModelResponse>.Error("Transerência ainda pendente de aprovação do gestor.");

                //Verifica se ja tem uma transacao que acabou de ser criada (Aberto), deve ser a ultima checagem para
                //que caso o processo tenha sido abortado no meio, podemos pegar essa transacao e continuar a partir dela
                transacao = _transacaoPixRepository.FirstOrDefault(c =>
                    c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Aberto);
                if (transacao != null && transacao.DataCadastro > DateTime.Now.AddMinutes(-2))
                    return BusinessResult<TransferenciaPixModelResponse>.Valid(
                        $"Transerência Pix em processo de pagamento, aguarde alguns minutos e tente integra-la novamente.", null);

                #endregion

                if (transacao == null)
                {
                    transacao = new TransacaoPix()
                    {
                        IdTransacaoPix = 0,
                        IdEmpresa = idEmpresaOrigem,
                        Valor = valor,
                        DataCadastro = DateTime.Now,
                        OrigemPagamento = integracao ? "INTEGRACAO" : "PORTAL",
                        DocumentoDestino = cpfCnpjProprietarioDestino,
                        IdViagemEvento = evento.IdViagemEvento,
                        IdTransacaoPixStatus = ETransacaoPixStatus.Aberto,
                        DocumentoUsuarioAuditCriacao = string.IsNullOrWhiteSpace(documentoUsuarioAudit) || documentoUsuarioAudit == CartoesService.AuditDocIntegracao ? _userIdentity.CpfCnpj : documentoUsuarioAudit,
                    };
                    
                    if (_userIdentity.IdUsuario != 0) transacao.IdUsuarioCadastro = _userIdentity.IdUsuario;
                }
                else
                {
                    transacao.DocumentoUsuarioAuditAtualizacao = string.IsNullOrWhiteSpace(documentoUsuarioAudit) || documentoUsuarioAudit == CartoesService.AuditDocIntegracao ? _userIdentity.CpfCnpj : documentoUsuarioAudit;
                    if (_userIdentity.IdUsuario != 0) transacao.IdUsuarioAtualizacao = _userIdentity.IdUsuario;
                }
                
                var documentoEmpresa = _empresaRepository.GetCnpj(idEmpresaOrigem);
                
                if (transacao.IdTransacaoPix == 0) _transacaoPixRepository.Add(transacao);
                else _transacaoPixRepository.Update(transacao);
                
                #region Validacoes de limite
                
                decimal? limiteUnitario  = _userIdentity.IdUsuario == 0 ? null : _parametrosUsuarioService.GetLimiteUnitarioPagamentoPixUsuario(_userIdentity.IdUsuario);
                if (limiteUnitario == null || limiteUnitario == 0) limiteUnitario = _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorLimitePagamentoPixUnitario, idEmpresaOrigem, integracao ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);
                if (limiteUnitario == null || limiteUnitario == 0) limiteUnitario = _parametrosGenericoService.GetParametro<decimal?>(GLOBAL.LimiteUnitarioPagamentoPix, 0);
                if (limiteUnitario == null || limiteUnitario == 0) limiteUnitario = 10000;
                
                if (valor > limiteUnitario)
                {
                    var motivo = $"Valor excede o limite unitário de {limiteUnitario.Value.ToString("C", new CultureInfo("pt-br"))}. Parcela foi bloqueada e enviada para aprovação do gestor.";
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Pendente;
                    transacao.MensagemRetorno = motivo;
                    _transacaoPixRepository.SaveChanges();
                    evento.Status = EStatusViagemEvento.Bloqueado;
                    evento.MotivoBloqueio = motivo;
                    _viagemEventoRepository.Update(evento);
                    _ = EnviarEmaiAvisoGestorAlcadas(idEmpresaOrigem, cpfCnpjProprietarioDestino, valor, evento, limiteUnitario, null, "Unitário");
                    return BusinessResult<TransferenciaPixModelResponse>.Error(motivo);
                }

                decimal? limiteDiario = _userIdentity.IdUsuario == 0 ? null : _parametrosUsuarioService.GetLimiteDiarioPagamentoPixUsuario(_userIdentity.IdUsuario);
                if (limiteDiario == null || limiteDiario == 0) limiteDiario = _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorLimitePagamentoPixDiario, idEmpresaOrigem, integracao ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);
                if (limiteDiario == null || limiteDiario == 0) limiteDiario = _parametrosGenericoService.GetParametro<decimal?>(GLOBAL.LimiteDiarioPagamentoPix, 0);
                if (limiteDiario == null || limiteDiario == 0) limiteDiario = 50000;

                var hoje = DateTime.Today;
                var amanha = DateTime.Today.AddDays(1);
                var transacoesPixDia = _transacaoPixRepository
                    .Where(c => 
                        c.IdEmpresa == idEmpresaOrigem && c.DataCadastro >= hoje && 
                        c.DataCadastro < amanha && c.IdTransacaoPixStatus != ETransacaoPixStatus.Erro)
                    .ToList();
                
                var somaValorTransacoesPixDia = transacoesPixDia.Count == 0 ? 0 : transacoesPixDia.Sum(c => c.Valor);
                    
                if (somaValorTransacoesPixDia + valor > limiteDiario)
                {
                    var motivo = $"Valor excede o limite diário de {limiteDiario.Value.ToString("C", new CultureInfo("pt-br"))}. Parcela foi bloqueada e enviada para aprovação do gestor.";
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Pendente;
                    transacao.MensagemRetorno = motivo;
                    _transacaoPixRepository.SaveChanges();
                    evento.Status = EStatusViagemEvento.Bloqueado;
                    evento.MotivoBloqueio = motivo;
                    _viagemEventoRepository.Update(evento);
                    _ = EnviarEmaiAvisoGestorAlcadas(idEmpresaOrigem, cpfCnpjProprietarioDestino, valor, evento, null, limiteDiario, "Diário");
                    return BusinessResult<TransferenciaPixModelResponse>.Error(motivo);
                }

                #endregion


                IntegracaoResult<TransferenciaPixModelResponse> response;

                try
                {
                    response = _bizApiClient.PostTransferenciaPix(documentoEmpresa, cpfCnpjProprietarioDestino, 
                        valor, transacao.IdTransacaoPix, mensagem);
                }
                catch (Exception e)
                {
                    var msg = "Erro de comunicação interna durante o processo.";
                    _logger.Error(e, msg);
                    transacao.DataAtualizacao = DateTime.Now;
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Erro;
                    transacao.MensagemRetorno = msg;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult<TransferenciaPixModelResponse>.Error(msg);
                }

                transacao.IdTransacaoMsCartao = response.Value?.TransacaoId;
                transacao.DataAtualizacao = DateTime.Now;
                
                if (response.Success && response.Value != null && response.Value.StatusTransacaoPix == ETransacaoPixStatus.NaoProcessado)
                {
                    var msg = "Transferência não foi processada pelo Banco Central. Integre a parcela novamente.";
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.NaoProcessado;
                    transacao.MensagemRetorno = msg;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult<TransferenciaPixModelResponse>.Error(msg);
                }
                
                if (!response.Success || response.Value == null)
                {
                    var msg = response.Messages.FirstOrDefault();
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Erro;
                    transacao.MensagemRetorno = msg;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult<TransferenciaPixModelResponse>.Error(msg);
                }

                var msgSucesso = "Pix efetuado com sucesso.";
                transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Confirmado;
                transacao.DataConfirmacao = response.Value?.DataTransacao;
                transacao.MensagemRetorno = msgSucesso;
                _transacaoPixRepository.SaveChanges();

                var email = EnviarEmailComprovanteProprietario(idEmpresaOrigem, cpfCnpjProprietarioDestino, response.Value!.Comprovante, ETipoTransferenciaPix.Pagamento);
                //essa notif aparece pelo app da extratta, mas o proprio banco do portador ja ira notifica-lo entao
                //é desnecessario ate pq o dinheiro nao cai na conta do app da extratta 
                //var push = EnviarPushComprovanteProprietario(idEmpresaOrigem, cpfCnpjProprietarioDestino, valor);
                
                return BusinessResult<TransferenciaPixModelResponse>.Valid(msgSucesso, response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<TransferenciaPixModelResponse>.Error(e.Message);
            }
        }

        private async Task EnviarEmailComprovanteProprietario(int idEmpresaOrigem, string documentoProprietario, TransferenciaPixComprovanteResponse transferencia, ETipoTransferenciaPix tipo)
        {
            try
            {
                if (idEmpresaOrigem == 0 || string.IsNullOrWhiteSpace(documentoProprietario) || string.IsNullOrWhiteSpace(transferencia?.EndToEndId)) return;

                var prop = _proprietarioRepository.Find(c => c.CNPJCPF == documentoProprietario)
                    .Include(c => c.Contatos).FirstOrDefault();
                var contatos = prop?.Contatos?.Select(c => c.Email).ToList();

                if (contatos == null || !contatos.Any())
                {
                    var usuario = _usuarioService.Find(c => c.CPFCNPJ == documentoProprietario && c.Ativo)
                        .Include(c => c.Contatos).FirstOrDefault();
                    contatos = usuario?.Contatos?.Select(c => c.Email).ToList();
                }

                if (contatos == null || !contatos.Any()) 
                    return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Comprovante de Transferência Pix",
                    Destinatarios = contatos,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\comprovante-transferencia-pix.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{ENDTOEND}", transferencia.EndToEndId);
                    html = html.Replace("{VALOR}", $"R$ {(transferencia.Value ?? 0).ToString($"C2")}");
                    html = html.Replace("{DATAHORA}", (transferencia.TransactionDate ?? DateTime.Now).ToString("dd/MM/yyyy HH:mm:ss"));

                    html = html.Replace("{ORIGEM_NOME}", transferencia.Origin.Name);
                    html = html.Replace("{ORIGEM_DOCUMENTO}", transferencia.Origin.Identification);
                    html = html.Replace("{ORIGEM_BANCO}", transferencia.Origin.IspbName);
                    html = html.Replace("{ORIGEM_AGENCIA}", transferencia.Origin.BankAgency);
                    html = html.Replace("{ORIGEM_CONTA}", transferencia.Origin.BankAccount);

                    html = html.Replace("{DESTINO_NOME}", transferencia.Destiny.Name);
                    html = html.Replace("{DESTINO_DOCUMENTO}", transferencia.Destiny.Identification);
                    html = html.Replace("{DESTINO_BANCO}", transferencia.Destiny.IspbName);
                    html = html.Replace("{DESTINO_AGENCIA}", transferencia.Destiny.BankAgency);
                    html = html.Replace("{DESTINO_CONTA}", transferencia.Destiny.BankAccount);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO COMPROVANTE PIX");
            }
        }

        private async Task EnviarPushComprovanteProprietario(int idEmpresaOrigem, string documentoProprietario, decimal valor)
        {
            try
            {
                if (idEmpresaOrigem == 0 || string.IsNullOrWhiteSpace(documentoProprietario)) return;

                var nomeEmpresa = await _empresaRepository
                    .Find(c => c.IdEmpresa == idEmpresaOrigem).Select(c => c.RazaoSocial)
                    .FirstOrDefaultAsync();

                var msg = $"Pix recebido no valor R$ {valor.ToString("C2")}";
                if(!string.IsNullOrWhiteSpace(nomeEmpresa)) msg += $" de {nomeEmpresa}";

                _pushService.EnviarPorDocumento(documentoProprietario, "Transferência Pix", msg);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO PUSH PIX");
            }
        }
        
        private async Task EnviarEmaiAvisoGestorAlcadas(int idEmpresaOrigem, string documentoProprietario, decimal valor, ViagemEvento evento, decimal? limiteUnitario, decimal? limiteDiario, string limiteUltrapassado)
        {
            try
            {
                if (idEmpresaOrigem == 0 || string.IsNullOrWhiteSpace(documentoProprietario) || evento == null) return;

                var idsUsuariosGestoresPix = _parametrosRepository
                    .Where(c => c.Chave == USUARIO_ID.GestorAlcadasPix.ToString() && c.ValorBoolean == true && c.IdRegistro.HasValue)
                    .Select(c => c.IdRegistro.Value).ToList();
                
                var emailsGestores = _usuarioService.Find(c => idsUsuariosGestoresPix.Contains(c.IdUsuario) && c.Ativo && c.Contatos.Any())
                    .Select(x => x.Contatos.FirstOrDefault())
                    .Select(x => x.Email)
                    .ToList();

                var infosEmpresa = _empresaRepository.Where(c => c.IdEmpresa == idEmpresaOrigem).Select(c => new
                {
                    c.CNPJ,
                    c.NomeFantasia
                }).FirstOrDefault();

                if (!emailsGestores.Any()) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Transferência Pix pendente de aprovação",
                    Destinatarios = emailsGestores,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\aprovacao-gestor-transferencia-pix.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", infosEmpresa?.NomeFantasia);
                    html = html.Replace("{EMPRESA_CNPJ}", infosEmpresa?.CNPJ.FormatarCpfCnpjSafe());
                    html = html.Replace("{ID_VIAGEM}", evento.IdViagem.ToString());
                    html = html.Replace("{ID_PARCELA}", evento.IdViagemEvento.ToString());
                    html = html.Replace("{DOCUMENTO_CLIENTE}", evento.Viagem?.DocumentoCliente ?? "--");
                    html = html.Replace("{DATA_BAIXA}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
                    html = html.Replace("{CPFCNPJ_PROPRIETARIO}", documentoProprietario.FormatarCpfCnpjSafe());
                    html = html.Replace("{VALOR_PARCELA}", valor.ToString("C", new CultureInfo("pt-BR")));
                    html = html.Replace("{LIMITE}", (limiteUnitario ?? limiteDiario)?.ToString("C", new CultureInfo("pt-BR")));
                    html = html.Replace("{LIMITE_ULTRAPASSADO}", limiteUltrapassado);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO AVISO GESTOR ALÇADAS PIX");
            }
        }
        
        public bool VerificarProprietario(int idempresa, int idProprietario)
        {
            var empresaPix = _parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idempresa);

            if (!empresaPix) return false;
            
            var usuarioPix = _parametrosUsuarioService.GetPermiteRealizarPagamentoPix(_userIdentity.IdUsuario);

            if (!usuarioPix) return false;

            var proprietarioDocumento = _proprietarioRepository.All()
                .Where(c => c.IdProprietario == idProprietario)
                .Select(p => p.CNPJCPF)
                .FirstOrDefault();

            var proprietarioPix = _parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(proprietarioDocumento, idempresa);

            return proprietarioPix;
        }

        public BusinessResult<GerarQrCodeEstaticoPixModelResponse> GerarQrCode(int idEmpresa)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Error($"Empresa sem permissão para efetuar pagamentos Pix.");

                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);
                
                IntegracaoResult<GerarQrCodeEstaticoPixModelResponse> response;
                try
                { 
                    response = _bizApiClient.GetGerarQrCodeEstaticoPix(empresaCnpj);
                }
                catch (Exception)
                {
                    return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Error("Erro ao gerar QR Code Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Error("Erro ao gerar QR Code Pix: " + response.Messages.FirstOrDefault());
                }


                if (string.IsNullOrWhiteSpace(response.Value?.Base64))
                {
                    return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Error("QR Code retornado vazio.");
                }

                return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<GerarQrCodeEstaticoPixModelResponse>.Error(e.Message);
            }
        }

        public BusinessResult<TransferenciaPixGridResponse> ConsultarGrid(int idEmpresa, int page, int take, 
            DateTime dataInicial, DateTime dataFinal, List<QueryFilters> filters)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<TransferenciaPixGridResponse>.Error(
                        $"Empresa sem permissão para efetuar pagamentos Pix.");

                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<TransferenciaPixGridTimelineResponse> response;
                try
                {
                    response = _bizApiClient.GetAccountTimelinePix(empresaCnpj, page, take, dataInicial, dataFinal);
                }
                catch (Exception)
                {
                    return BusinessResult<TransferenciaPixGridResponse>.Error("Erro ao listar as transferências Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<TransferenciaPixGridResponse>.Error("Erro ao listar as transferências Pix: " +
                                                                              response.Messages.FirstOrDefault());
                }

                if (response.Value?.Items == null || response.Value.Items.Count == 0 || response.Value.TotalItems == 0)
                {
                    return BusinessResult<TransferenciaPixGridResponse>.Error("Nenhuma transferência Pix encontrada.");
                }

                var resp = Mapper.Map<TransferenciaPixGridResponse>(response.Value);

                var idsTransacoesPix = resp.items
                    .Where(c => c.IdTransacaoPixPortal.HasValue)
                    .Select(c => c.IdTransacaoPixPortal.Value).ToList();

                // pega as transacoes que deram certo pelo idtransacaopix e as que deram erro no periodo
                var transacoesPix = _transacaoPixRepository
                    .Where(c => idsTransacoesPix.Contains(c.IdTransacaoPix) || ((c.IdTransacaoPixStatus == ETransacaoPixStatus.Erro || c.IdTransacaoPixStatus == ETransacaoPixStatus.NaoProcessado) && c.DataCadastro >= dataInicial && c.DataCadastro <= dataFinal && c.IdEmpresa == idEmpresa))
                    .Select(c => new
                    {
                        IdTransacaoPix = c.IdTransacaoPix,
                        IdViagemEvento = c.IdViagemEvento,
                        IdViagem = c.ViagemEvento.IdViagem,
                        Ciot = c.ViagemEvento.Viagem.DeclaracaoCiot != null ? c.ViagemEvento.Viagem.DeclaracaoCiot.Ciot : null,
                        DocumentoCliente = c.ViagemEvento.Viagem.DocumentoCliente,
                        TipoEvento = c.ViagemEvento.TipoEventoViagem,
                        DocumentoDestino = c.ViagemEvento.Viagem.CPFCNPJProprietario,
                        NomeDestino = c.ViagemEvento.Viagem.NomeProprietario,
                        DocumentoOrigem = c.ViagemEvento.Viagem.Empresa.CNPJ,
                        NomeOrigem = c.ViagemEvento.Viagem.Empresa.RazaoSocial,
                        Valor = c.Valor,
                        MensagemRetorno = c.MensagemRetorno,
                        Data = c.DataCadastro,
                        Status = c.IdTransacaoPixStatus,
                    })
                    .ToList();

                // preenchimento do resto das propriedades das transacoes q deram certo
                foreach (var transf in resp.items)
                {
                    if (!transf.IdTransacaoPixPortal.HasValue) continue;
                    var transacaoPix = transacoesPix
                        .FirstOrDefault(c => c.IdTransacaoPix == transf.IdTransacaoPixPortal);
                    if (transacaoPix == null) continue;
                    transf.IdViagemEvento = transacaoPix.IdViagemEvento;
                    transf.IdViagem = transacaoPix.IdViagem;
                    transf.Ciot = transacaoPix.Ciot;
                    transf.DocumentoCliente = transacaoPix.DocumentoCliente;
                    transf.TipoEvento = transacaoPix.TipoEvento;
                    transf.MensagemRetorno = transacaoPix.MensagemRetorno;
                    transf.Status = transacaoPix.Status;
                    transf.StatusDescricao = transacaoPix.Status.GetDescription();
                    transf.DataTransferenciaDateTime = transacaoPix.Data;
                    if (transf.TipoEvento != null) transf.TipoEventoDescricao = transacaoPix.TipoEvento.GetDescription();
                }
                
                //transacoes que deram erro no periodo
                var transacoesErro = transacoesPix
                    .Where(c => c.Status == ETransacaoPixStatus.Erro).ToList();
                
                //preenchimento das transacoes q deram erro
                foreach (var transacaoErro in transacoesErro)
                {
                    var transf = new TransferenciaPixGridResponseItem
                    {
                        DocumentoDestino = transacaoErro.DocumentoDestino,
                        NomeDestino = transacaoErro.NomeDestino,
                        DocumentoOrigem = transacaoErro.DocumentoOrigem,
                        NomeOrigem = transacaoErro.NomeOrigem,
                        TipoEvento = transacaoErro.TipoEvento,
                        TipoEventoDescricao = transacaoErro.TipoEvento.GetDescription(),
                        IdViagemEvento = transacaoErro.IdViagemEvento,
                        IdViagem = transacaoErro.IdViagem,
                        DocumentoCliente = transacaoErro.DocumentoCliente,
                        Valor = transacaoErro.Valor.ToString("C2", new CultureInfo("pt-br")),
                        MensagemRetorno = transacaoErro.MensagemRetorno,
                        DataTransferencia = transacaoErro.Data.ToString("dd-MM-yyyy HH:mm:ss"),
                        DataTransferenciaDateTime = transacaoErro.Data,
                        TipoEnum = ETipoTransferenciaPix.Pagamento,
                        Tipo = ETipoTransferenciaPix.Pagamento.GetDescription(),
                        Ciot = transacaoErro.Ciot,
                        IdTransacaoPixPortal = transacaoErro.IdTransacaoPix,
                        Codigo = "Pagamento não realizado",
                        Status = transacaoErro.Status,
                        StatusDescricao = transacaoErro.Status.GetDescription(),
                    };
                    
                    resp.items.Add(transf);
                }

                resp.items = resp.items.AsQueryable().AplicarFiltrosDinamicos(filters)
                    .OrderByDescending(c => c.DataTransferenciaDateTime).ToList();

                return BusinessResult<TransferenciaPixGridResponse>.Valid(resp);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<TransferenciaPixGridResponse>.Error(e.Message);
            }
        }

        public byte[] GerarRelatorioGrid(int idEmpresa, object dados, string tipoArquivo)
        {
            var logoEmpresa = _empresaService.All().Where(c => c.IdEmpresa == idEmpresa)
                .Select(c => c.Logo).FirstOrDefault();
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);
            
            return new RelatorioTransferenciaPix().GetReport(dados, tipoArquivo, logo);
        }

        public BusinessResult<ContaPixResponse> ConsultarContaPix(int idEmpresa)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<ContaPixResponse>.Error(
                        $"Empresa sem permissão para utilizar os serviços Pix.");

                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<ContaPixResponse> response;
                try
                {
                    response = _bizApiClient.GetContaPix(empresaCnpj);
                }
                catch (Exception)
                {
                    return BusinessResult<ContaPixResponse>.Error("Erro ao consultar a conta Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<ContaPixResponse>.Error("Erro ao consultar a conta Pix: " +
                        response.Messages.FirstOrDefault());
                }

                if (string.IsNullOrWhiteSpace(response.Value?.AccountId))
                {
                    return BusinessResult<ContaPixResponse>.Error("Nenhuma transferência Pix encontrada.");
                }

                return BusinessResult<ContaPixResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<ContaPixResponse>.Error(e.Message);
            }
        }

        public BusinessResult<TransferenciaPixComprovanteResponse> ConsultarTransferencia(int idEmpresa, string endToEndId, ETipoTransferenciaPix tipo)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error(
                        $"Empresa sem permissão para efetuar pagamentos Pix.");

                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<TransferenciaPixComprovanteResponse> response;
                try
                {
                    response = _bizApiClient.GetComprovanteTransferenciaPix(empresaCnpj, endToEndId, tipo);
                }
                catch (Exception)
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Erro ao consultar a transferência Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Erro ao consultar a transferência Pix: " +
                                                                              response.Messages.FirstOrDefault());
                }

                if (string.IsNullOrWhiteSpace(response.Value?.EndToEndId))
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Transferência Pix não encontrada.");
                }

                var transferencia = response.Value;

                if (transferencia.IdTransacaoPixPortal.HasValue)
                {
                    var transacaoPix = _transacaoPixRepository
                        .Where(c => c.IdTransacaoPix == transferencia.IdTransacaoPixPortal.Value)
                        .Select(c => new
                        {
                            IdViagemEvento = c.IdViagemEvento,
                            IdViagem = c.ViagemEvento.IdViagem,
                            DocumentoCliente = c.ViagemEvento.Viagem.DocumentoCliente,
                            TipoPagamento = c.ViagemEvento.TipoEventoViagem,
                            Ciot = c.ViagemEvento.Viagem.DeclaracaoCiot != null ? c.ViagemEvento.Viagem.DeclaracaoCiot.Ciot : null,
                        }).FirstOrDefault();
                    
                    if (transacaoPix != null)
                    {
                        transferencia.IdViagemEvento = transacaoPix.IdViagemEvento;
                        transferencia.IdViagem = transacaoPix.IdViagem;
                        transferencia.DocumentoCliente = transacaoPix.DocumentoCliente;
                        transferencia.TipoPagamento = transacaoPix.TipoPagamento.GetDescription();
                        transferencia.Ciot = transacaoPix.Ciot;
                    }
                }

                return BusinessResult<TransferenciaPixComprovanteResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<TransferenciaPixComprovanteResponse>.Error(e.Message);
            }
        }

        public BusinessResult<TransferenciaPixComprovanteResponse> ConsultarTransferenciaPorEvento(int idEmpresa, int idViagemEvento)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");

                #endregion

                var transacaoPix = _transacaoPixRepository
                    .Where(c => c.IdViagemEvento == idViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Confirmado && c.IdTransacaoMsCartao.HasValue)
                    .Select(c => new
                    {
                        IdViagemEvento = c.IdViagemEvento,
                        IdViagem = c.ViagemEvento.IdViagem,
                        DocumentoCliente = c.ViagemEvento.Viagem.DocumentoCliente,
                        TipoPagamento = c.ViagemEvento.TipoEventoViagem,
                        Ciot = c.ViagemEvento.Viagem.DeclaracaoCiot != null ? c.ViagemEvento.Viagem.DeclaracaoCiot.Ciot : null,
                        IdTransacaoMsCartao = c.IdTransacaoMsCartao,
                    }).FirstOrDefault();

                if(transacaoPix?.IdTransacaoMsCartao == null || transacaoPix.IdTransacaoMsCartao == 0)
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error($"Transferência não encontrada.");
                    

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<TransferenciaPixComprovanteResponse> response;
                try
                {
                    response = _bizApiClient.GetComprovanteTransferenciaPixPorTransacao(empresaCnpj, transacaoPix.IdTransacaoMsCartao.Value);
                }
                catch (Exception)
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Erro ao consultar a transferência Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Erro ao consultar a transferência Pix: " +
                                                                              response.Messages.FirstOrDefault());
                }

                if (string.IsNullOrWhiteSpace(response.Value?.EndToEndId))
                {
                    return BusinessResult<TransferenciaPixComprovanteResponse>.Error("Transferência Pix não encontrada.");
                }
                
                if (response.Value != null)
                {
                    response.Value.IdViagemEvento = transacaoPix.IdViagemEvento;
                    response.Value.IdViagem = transacaoPix.IdViagem;
                    response.Value.DocumentoCliente = transacaoPix.DocumentoCliente;
                    response.Value.TipoPagamento = transacaoPix.TipoPagamento.GetDescription();
                    response.Value.Ciot = transacaoPix.Ciot;
                }

                return BusinessResult<TransferenciaPixComprovanteResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<TransferenciaPixComprovanteResponse>.Error(e.Message);
            }
        }

        public BusinessResult CadastrarChave(int idEmpresa, string chave, ETipoChavePix tipo)
        {
            try
            {
                if (!_parametrosUsuarioService.GetPermitirCadastroChavePix(_userIdentity.IdUsuario))
                    return BusinessResult.Error($"Usuário sem permissão para cadastros de chaves Pix.");

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");
                
                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                var response = _bizApiClient.CadastrarChavePix(empresaCnpj, chave, tipo);
                if (!response.Success)
                {
                    return BusinessResult.Error("Chave já cadastrada ou inválida.");
                }
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult.Error("Ocorreu um erro interno ao cadastrar a chave Pix.");
            }
        }

        public BusinessResult DeletarChave(int idEmpresa, string chave)
        {
            try
            {
                if (!_parametrosUsuarioService.GetPermitirCadastroChavePix(_userIdentity.IdUsuario))
                    return BusinessResult.Error($"Usuário sem permissão para deleções de chaves Pix.");

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                var response = _bizApiClient.DeletarChavePix(empresaCnpj, chave);
                if (!response.Success)
                {
                    return BusinessResult.Error("Chave Pix não encontrada.");
                }
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult.Error("Ocorreu um erro interno ao deletar a chave Pix.");
            }
        }

        public BusinessResult<ChavesPixGridResponse> ConsultarChaves(int idEmpresa)
        {
            try
            {
                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<ChavesPixGridResponse>.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                var response = _bizApiClient.ConsultarChavesPix(empresaCnpj);
                if (!response.Success || response.Value?.Chaves == null || response.Value.Chaves.Count == 0)
                {
                    return BusinessResult<ChavesPixGridResponse>.Error("Chaves Pix não encontradas.");
                }

                var retorno = new ChavesPixGridResponse()
                {
                    items = response.Value.Chaves,
                    totalItems = response.Value.Chaves.Count
                };
                
                return BusinessResult<ChavesPixGridResponse>.Valid(retorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<ChavesPixGridResponse>.Error("Ocorreu um erro interno ao consultar as chaves Pix.");
            }
        }

        public BusinessResult<LimitesPixAlterarModelResponse> AlterarLimite(int idEmpresa, AlterarLimitesPixRequest request)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<LimitesPixAlterarModelResponse>.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");

                var permiteSolicitarLimites = _parametrosUsuarioService.GetPermiteSolicitarAlteracoesLimitePix(_userIdentity.IdUsuario);
                if (permiteSolicitarLimites != true)
                    return BusinessResult<LimitesPixAlterarModelResponse>.Error($"Usuário sem permissão para solicitar alterações de limites Pix.");
                
                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<LimitesPixAlterarModelResponse> response;
                try
                {
                    var req = new LimitesPixAlterarModelRequest
                    {
                        Valor = request.Valor,
                        Tipo = request.Tipo,
                    };
                    response = _bizApiClient.AlterarLimite(empresaCnpj, req);
                }
                catch (Exception)
                {
                    return BusinessResult<LimitesPixAlterarModelResponse>.Error("Erro ao alterar o limite Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<LimitesPixAlterarModelResponse>.Error(response.Messages.FirstOrDefault());
                }

                return BusinessResult<LimitesPixAlterarModelResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<LimitesPixAlterarModelResponse>.Error(e.Message);
            }
        }

        public BusinessResult<LimitesPixConsultarModelResponse> ConsultarLimites(int idEmpresa)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(idEmpresa))
                    return BusinessResult<LimitesPixConsultarModelResponse>.Error($"Empresa sem permissão para efetuar pagamentos por Pix.");
                
                #endregion

                var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);

                IntegracaoResult<LimitesPixConsultarModelResponse> response;
                try
                {
                    response = _bizApiClient.ConsultarLimites(empresaCnpj);
                }
                catch (Exception)
                {
                    return BusinessResult<LimitesPixConsultarModelResponse>.Error("Erro ao consultar os limites Pix.");
                }

                if (!response.Success)
                {
                    return BusinessResult<LimitesPixConsultarModelResponse>.Error("Erro ao consultar os limites Pix: " +
                        response.Messages.FirstOrDefault());
                }

                return BusinessResult<LimitesPixConsultarModelResponse>.Valid(response.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<LimitesPixConsultarModelResponse>.Error(e.Message);
            }
        }

        #region Gestão de Alçadas

        public BusinessResult EfetuarTransferenciaPixGestaoAlcadas(int idViagemEvento)
        {
            try
            {
                #region Validacoes
                
                if (!_userIdentity.IdEmpresa.HasValue)
                    return BusinessResult.Error($"Dados inválidos.");
                
                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(_userIdentity.IdEmpresa.Value))
                    return BusinessResult.Error($"Empresa sem permissão para efetuar pagamentos Pix.");
                
                if (!_parametrosUsuarioService.GetGestorAlcadasPix(_userIdentity.IdUsuario))
                    return BusinessResult.Error($"Usuário sem permissões de gestão de pagamentos Pix.");

                var evento = _viagemEventoRepository
                    .Where(c => c.IdViagemEvento == idViagemEvento && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .FirstOrDefault();
                
                if (evento == null)
                    return BusinessResult.Error($"Parcela não encontrada.");

                var viagem = _viagemRepository.Where(c => c.IdViagem == evento.IdViagem).Include(c => c.DeclaracaoCiot)
                    .FirstOrDefault();
                
                if (viagem == null)
                    return BusinessResult.Error($"Viagem da parcela não encontrada.");
                
                if (viagem.StatusViagem != EStatusViagem.Aberto)
                    return BusinessResult.Error($"Viagem da parcela não está aberta.");
                
                if (evento.HabilitarPagamentoPix != true)
                    return BusinessResult.Error($"Parcela não é um pagamento Pix.");

                if (evento.Status != EStatusViagemEvento.Bloqueado)
                    return BusinessResult.Error($"Parcela com status inválido para aprovação do gestor.");

                if (!_parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(viagem.CPFCNPJProprietario, viagem.IdEmpresa))
                    return BusinessResult.Error($"Proprietário sem permissão para receber pagamentos Pix pela empresa.");
                
                //Verifica se ja tem uma transacao de sucesso (Confirmado) (não é pra cair aqui pq a consulta pra tela nao traz parcelas já baixadas/com transacao confirmada)
                var transacao = _transacaoPixRepository.FirstOrDefault(c => c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Confirmado);
                if (transacao != null)
                    return BusinessResult.Valid($"Transerência Pix já efetuada em {(transacao.DataConfirmacao ?? transacao.DataCadastro).ToString("dd/MM/yyyy HH:mm")}");
                
                //Verifica se tem uma transacao aberta (acabou de ser criada)
                transacao = _transacaoPixRepository.FirstOrDefault(c => c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Aberto);
                if (transacao != null && transacao.DataCadastro > DateTime.Now.AddMinutes(-2))
                    return BusinessResult.Error($"Transerência Pix em processo de pagamento, aguarde alguns minutos e tente aprová-la novamente.");
                
                #endregion
                
                if (transacao == null)
                {
                    transacao = new TransacaoPix()
                    {
                        IdTransacaoPix = 0,
                        IdEmpresa = evento.IdEmpresa,
                        Valor = evento.ValorPagamento,
                        DataCadastro = DateTime.Now,
                        OrigemPagamento = "APROVAÇÃO GESTOR DE ALÇADAS",
                        DocumentoDestino = viagem.CPFCNPJProprietario,
                        IdViagemEvento = evento.IdViagemEvento,
                        IdTransacaoPixStatus = ETransacaoPixStatus.Aberto,
                        DocumentoUsuarioAuditCriacao = _userIdentity.CpfCnpj
                    };

                    if (_userIdentity.IdUsuario != 0) transacao.IdUsuarioCadastro = _userIdentity.IdUsuario;
                }
                else
                {
                    transacao.DocumentoUsuarioAuditAtualizacao = _userIdentity.CpfCnpj;
                    if (_userIdentity.IdUsuario != 0) transacao.IdUsuarioAtualizacao = _userIdentity.IdUsuario;
                }

                var documentoEmpresa = _empresaRepository.GetCnpj(evento.IdEmpresa);

                if (transacao.IdTransacaoPix == 0) _transacaoPixRepository.Add(transacao);
                else _transacaoPixRepository.Update(transacao);

                IntegracaoResult<TransferenciaPixModelResponse> response;

                try
                {
                    var mensagem = $"Tipo: {evento.TipoEventoViagem.GetDescription()}, " +
                                   $"IdViagem: {evento.IdViagem}, " +
                                   $"IdParcela: {evento.IdViagemEvento}, " +
                                   $"CIOT: {viagem.DeclaracaoCiot?.Ciot ?? "--"}, " +
                                   $"Documento: {viagem.DocumentoCliente}";
                    response = _bizApiClient.PostTransferenciaPix(documentoEmpresa, viagem.CPFCNPJProprietario, 
                        evento.ValorPagamento, transacao.IdTransacaoPix, mensagem);
                }
                catch (Exception)
                {
                    transacao.DataAtualizacao = DateTime.Now;
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Erro;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult.Error("Erro de comunicação interna durante o processo.");
                }

                transacao.IdTransacaoMsCartao = response.Value?.TransacaoId;
                transacao.DataAtualizacao = DateTime.Now;
                
                if (response.Success && response.Value != null && response.Value.StatusTransacaoPix == ETransacaoPixStatus.NaoProcessado)
                {
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.NaoProcessado;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult.Error("Transferência não foi processada pelo Banco Central. Integre a parcela novamente.");
                }
                
                if (!response.Success || response.Value == null)
                {
                    transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Erro;
                    _transacaoPixRepository.SaveChanges();
                    return BusinessResult.Error(response.Messages.FirstOrDefault());
                }
                
                transacao.IdTransacaoPixStatus = ETransacaoPixStatus.Confirmado;
                transacao.DataConfirmacao = response.Value?.DataTransacao;
                _transacaoPixRepository.SaveChanges();
                evento.Status = EStatusViagemEvento.Baixado;
                evento.DataHoraPagamento = transacao.DataConfirmacao ?? DateTime.Now;
                evento.MotivoBloqueio = null;
                _viagemEventoRepository.SaveChanges();

                var email = EnviarEmailComprovanteProprietario(evento.IdEmpresa, viagem.CPFCNPJProprietario, response.Value!.Comprovante, ETipoTransferenciaPix.Pagamento);
                
                return BusinessResult.Valid("Transerência Pix efetuada com sucesso.");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CancelarTransferenciaPixGestaoAlcadas(int idViagemEvento)
        {
            try
            {
                #region Validacoes
                
                if (!_userIdentity.IdEmpresa.HasValue)
                    return BusinessResult.Error($"Dados inválidos.");

                if (!_parametrosUsuarioService.GetGestorAlcadasPix(_userIdentity.IdUsuario))
                    return BusinessResult.Error($"Usuário sem permissões de gestão de pagamentos Pix.");

                if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(_userIdentity.IdEmpresa.Value))
                    return BusinessResult.Error($"Empresa sem permissão para efetuar pagamentos Pix.");

                var evento = _viagemEventoRepository
                    .Where(c => c.IdViagemEvento == idViagemEvento && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Include(c => c.Viagem)
                    .FirstOrDefault();
                
                if (evento == null)
                    return BusinessResult.Error($"Parcela não encontrada.");
                
                if (evento.HabilitarPagamentoPix != true)
                    return BusinessResult.Error($"Parcela não é um pagamento Pix.");

                if (evento.Status != EStatusViagemEvento.Bloqueado)
                    return BusinessResult.Error($"Parcela com status inválido para aprovação do gestor.");
                
                if (!_parametrosUsuarioService.GetGestorAlcadasPix(_userIdentity.IdUsuario))
                    return BusinessResult.Error($"Usuário sem permissões de gestão de pagamentos Pix.");
                
                //Verifica se ja tem uma transacao de sucesso (Confirmado) (não é pra cair aqui pq a consulta pra tela nao traz parcelas já baixadas/com transacao confirmada)
                var transacao = _transacaoPixRepository.FirstOrDefault(c => c.IdViagemEvento == evento.IdViagemEvento && c.IdTransacaoPixStatus == ETransacaoPixStatus.Confirmado);
                if (transacao != null)
                    return BusinessResult.Valid($"Transerência Pix já efetuada em {(transacao.DataConfirmacao ?? transacao.DataCadastro).ToString("dd/MM/yyyy HH:mm")}");
                
                #endregion

                evento.Status = EStatusViagemEvento.Cancelado;
                evento.DataHoraPagamento = null;
                _viagemEventoRepository.SaveChanges();
                
                return BusinessResult.Valid("Parcela cancelada com sucesso.");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult ValidarSenhaCartaoPixGestaoAlcadas(int idViagemEvento, string senha)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return BusinessResult.Error("Dados inválidos.");
                var idEmpresa = _userIdentity.IdEmpresa.Value;
                var cnpjEmpresa = _viagemEventoRepository
                    .Where(c => c.IdViagemEvento == idViagemEvento && c.IdEmpresa == idEmpresa)
                    .Select(c => c.Viagem.Empresa.CNPJ).FirstOrDefault();
                if (cnpjEmpresa == null) 
                    return BusinessResult.Error("Parcela não encontrada.");
                var tokenEmpresa = _empresaRepository.GetTokenMicroServices(idEmpresa);
                var cartoesService = new CartoesService(_cartoesServiceArgs, idEmpresa, tokenEmpresa, "***********", null);
                var produto = cartoesService.GetIdProdutoCartaoFretePadrao();
                var cartoes = cartoesService.GetCartoesVinculados(cnpjEmpresa, new List<int> { produto }, false);
                if (cartoes?.Cartoes == null || cartoes.Cartoes.Count == 0) 
                    return BusinessResult.Error($"Nenhum cartão foi encontrado para o favorecido de documento {cnpjEmpresa}");
                var cartao = cartoes.Cartoes.Last();
                var senhaValidada = cartoesService.ValidarSenhaCartao(cartao, senha);
                if (!senhaValidada.Sucesso)
                    return BusinessResult.Error($"Senha inválida.");
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult<TransferenciaPixGridGestaoAlcadasResponse> ConsultarGridGestaoAlcadas(int page, int take, 
            DateTime dataInicial, DateTime dataFinal, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                #region Validacoes de permissao

                if (!_parametrosUsuarioService.GetGestorAlcadasPix(_userIdentity.IdUsuario))
                    return BusinessResult<TransferenciaPixGridGestaoAlcadasResponse>.Error(
                        $"Usuário sem permissões de gestão de pagamentos Pix.");

                #endregion

                var itensQuery = _viagemEventoRepository
                    .Where(c => 
                        c.IdEmpresa == _userIdentity.IdEmpresa.Value && 
                        c.Status == EStatusViagemEvento.Bloqueado &&
                        c.HabilitarPagamentoPix == true && 
                        c.Viagem.StatusViagem == EStatusViagem.Aberto &&
                        c.TransacoesPix.Any(t => t.IdTransacaoPixStatus == ETransacaoPixStatus.Pendente) &&
                        c.TransacoesPix.All(t => t.IdTransacaoPixStatus != ETransacaoPixStatus.Confirmado));

                itensQuery = itensQuery.Where(c =>
                    c.TransacoesPix.Where(t => t.IdTransacaoPixStatus == ETransacaoPixStatus.Pendente)
                        .Select(t => t.DataCadastro).FirstOrDefault() >= dataInicial &&
                    c.TransacoesPix.Where(t => t.IdTransacaoPixStatus == ETransacaoPixStatus.Pendente)
                        .Select(t => t.DataCadastro).FirstOrDefault() < dataFinal);
                
                itensQuery = string.IsNullOrWhiteSpace(order?.Campo)
                    ? itensQuery.OrderBy(x => x.IdViagemEvento)
                    : itensQuery.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

                var count = itensQuery.Count();
                
                var itens = itensQuery.AplicarFiltrosDinamicos(filters)
                    .Skip((page - 1) * take).Take(take)
                    .Select(c => new TransferenciaPixGridGestaoAlcadasResponseItem
                    {
                        IdViagem = c.IdViagem,
                        IdViagemEvento = c.IdViagemEvento,
                        IdEmpresa = c.IdEmpresa,
                        DocumentoCliente = c.Viagem.DocumentoCliente,
                        NomeEmpresa = c.Viagem.Empresa.RazaoSocial,
                        CnpjEmpresa = c.Viagem.Empresa.CNPJ,
                        NomeProprietario = c.Viagem.Proprietario.RazaoSocial,
                        DocumentoProprietario = c.Viagem.CPFCNPJProprietario,
                        ValorDecimal = c.ValorPagamento,
                        TipoEventoEnum = c.TipoEventoViagem,
                        DataCadastro = c.TransacoesPix.Where(t => t.IdTransacaoPixStatus == ETransacaoPixStatus.Pendente).Select(t => t.DataCadastro).FirstOrDefault(),
                        Ciot = c.Viagem.DeclaracaoCiot == null ? null : c.Viagem.DeclaracaoCiot.Ciot
                    })
                    .ToList();

                var resp = new TransferenciaPixGridGestaoAlcadasResponse
                {
                    items = itens,
                    totalItems = count
                };
                
                return BusinessResult<TransferenciaPixGridGestaoAlcadasResponse>.Valid(resp);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return BusinessResult<TransferenciaPixGridGestaoAlcadasResponse>.Error(e.Message);
            }
        }

        #endregion
    }
}
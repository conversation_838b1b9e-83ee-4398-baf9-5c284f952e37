﻿/*using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Application.WS;
using ATS.WS.Controllers.Base;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class PushMobileController : BaseController
    {
        private readonly MobilePush _mobilePush;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public PushMobileController(BaseControllerArgs baseArgs, MobilePush mobilePush, IUsuarioApp usuarioApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _mobilePush = mobilePush;
            _usuarioApp = usuarioApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string EnviarPushMobile(string token, string cnpjAplicacao, string tituloMensagem, string mensagem, string cpfMotorista)
        {
            try
            {
                if (ValidarToken(token) || _autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                {

                    var idsPush = _usuarioApp.GetPorCNPJCPF(cpfMotorista).IdPush;
                    if (!string.IsNullOrEmpty(idsPush))
                        _mobilePush.EnviarMensagem(mensagem, tituloMensagem, ETipoMensagemPush.MessageGeneric, cpfMotorista);

                    return new JsonResult().Responde(new { Sucesso = true, Mensagem = "Push enviado com sucesso. " });
                }
                return new JsonResult().TokenInvalido();
            }
            catch (Exception e)
            {
                return new JsonResult().Responde(new { Sucesso = false, Mensagem = e.Message });
            }

        }
    }
}*/
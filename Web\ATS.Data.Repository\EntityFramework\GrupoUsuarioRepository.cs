﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class GrupoUsuarioRepository : Repository<GrupoUsuario>, IGrupoUsuarioRepository
    {
        public GrupoUsuarioRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Consultar os grupos de usuário através dos filtros especificados
        /// </summary>
        /// <param name="nome">Nome do grupo</param>
        /// <param name="idEmpresa">ID do empresa</param>
        /// /// <param name="idEstabelecimentoBase">ID do estabelecimento base</param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase)
        {
            if(idEstabelecimentoBase.HasValue)
                return (from Grupo in All().Include(x => x.Empresa).Include(x => x.EstabelecimentoBase)
                    where ((nome == null || Grupo.Descricao.Contains(nome)) && (Grupo.IdEstabelecimentoBase == idEstabelecimentoBase))
                    select Grupo);
            else
                return (from Grupo in All().Include(x => x.Empresa).Include(x => x.EstabelecimentoBase)
                       where ((nome == null || Grupo.Descricao.Contains(nome)) && (idEmpresa == null || Grupo.IdEmpresa == idEmpresa.Value))
                      select Grupo);
        }

        /// <summary>
        /// Retorna a lista de grupos de usuário do Empresa
        /// </summary>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="idEstabelecimentoBase"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase)
        {
            var gruposUsuarios =  (from grp in All().Include(x => x.Empresa)
                 where grp.Ativo
                select grp);
            if (idEmpresa.HasValue)
                gruposUsuarios = gruposUsuarios.Where(x => x.IdEmpresa == idEmpresa);

            if (idEstabelecimentoBase.HasValue)
                gruposUsuarios = gruposUsuarios.Where(o =>
                    o.IdEstabelecimentoBase.HasValue && o.IdEstabelecimentoBase == idEstabelecimentoBase);

            return gruposUsuarios;
        }

        /// <summary>
        /// Retorna o grupo de usuáio com todos os objetos que faz referência
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GrupoUsuario GetChilds(int id)
        {
            return (from grpusuario in All()
                         .Include(g => g.Menus)
                         .Include(g => g.Empresa)
                   where grpusuario.IdGrupoUsuario == id
                  select grpusuario)?.FirstOrDefault();
        }

        /// <summary>
        /// Retorna o objeto de Grupo de Usuário
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public override GrupoUsuario Get(int id)
        {
            return (from grpusuario in All()
                   where grpusuario.IdGrupoUsuario == id
                  select grpusuario)?.FirstOrDefault();
        }

        /// <summary>
        /// Retorna o grupo de usuáio por transportador com todos os objetos que faz referência
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public GrupoUsuario GetChildsPorEmpresa(int idEmpresa)
        {
            return (from grpusuario in All()
                         .Include(g => g.Menus)
                         .Include(g => g.Empresa)
                    where grpusuario.IdEmpresa == idEmpresa && grpusuario.Ativo
                    select grpusuario)?.FirstOrDefault();
        }
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemCargaMap : EntityTypeConfiguration<ViagemCarga>
    {
        public ViagemCargaMap()
        {
            ToTable("VIAGEM_CARGA");

            HasKey(t => new { t.IdViagem, IdEmpresa = t.IdEmpresa, t.IdCarga });

            Property(t => t.IdViagem)
                 .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdCarga)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdViagem)
                .IsRequired();

            Property(t => t.IdCarga)
                .IsRequired();

            HasRequired(a => a.Viagem)
                .WithMany(b => b.ViagemCargas)
                .HasForeignKey(c => new { c.IdViagem, IdEmpresa = c.IdEmpresa });
        }
    }
}
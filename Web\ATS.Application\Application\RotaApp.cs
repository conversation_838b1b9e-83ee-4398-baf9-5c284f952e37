﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using System;
using System.Collections.Generic;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class RotaApp : AppBase, IRotaApp
    {
        private readonly IRotaService _rotaService;

        public RotaApp(IRotaService rotaService)
        {
            _rotaService = rotaService;
        }

        public Rota Get(int id)
        {
            return _rotaService.Get(id);
        }

        public List<Rota> ConsultarRotas(int idEmpresa, DateTime? dataBase)
        {
            return _rotaService.ConsultarRotas(idEmpresa, dataBase);
        }

        public void Reativar(int idRota)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    _rotaService.Reativar(idRota);
                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public void Inativar(int idRota)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    _rotaService.Inativar(idRota);
                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public object ConsultaGrid(int? idEmpresa, bool? listarInativos, string descricao, int take, int page, 
            OrderFilters order, List<QueryFilters> filters)
        {
            return _rotaService.ConsultarGrid(idEmpresa, listarInativos, descricao, take, page, order, filters);
        }

        public void Excluir(int idRota)
        {            
            _rotaService.Excluir(idRota);
        }
    }
}

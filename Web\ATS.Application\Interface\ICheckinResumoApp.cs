using System;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Models;

namespace ATS.Application.Interface
{
    public interface ICheckinResumoApp : IAppBase<CheckinResumo>
    {
        CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int? empresaId, int? itensPorPagina, int? pagina, DateTime? dataInicio, DateTime? dataFim);
        void SalvarOuEditarResumo(CheckIn checkin);
    }
}
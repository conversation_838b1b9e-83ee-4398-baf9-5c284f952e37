﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Modulo;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class ModuloAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IModuloApp _moduloApp;
        private readonly IMenuApp _menuApp;

        public ModuloAtsController(IUserIdentity userIdentity, IModuloApp moduloApp, IMenuApp menuApp)
        {
            _userIdentity = userIdentity;
            _moduloApp = moduloApp;
            _menuApp = menuApp;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var modulos = _moduloApp.ConsultarGrid(take, page, order, filters);

                return ResponderSucesso(modulos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idModulo)
        {
            try
            {
                var validationResult = _moduloApp.Inativar(idModulo);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Módulo inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idModulo)
        {
            try
            {
                var validationResult = _moduloApp.Reativar(idModulo);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Módulo reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(CadastroModuloRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                var validationResult = new ValidationResult();
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Apenas administradores do sistema podem realizar o cadastro de módulo");
                if (string.IsNullOrWhiteSpace(@params.Descricao))
                    validationResult.Add("Descrição é obrigatória para o cadastro de módulo.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                var modulo = new Modulo
                {
                    Ativo = true,
                    ClassIcon = @params.ClassIcon,
                    Descricao = @params.Descricao,
                    Sequencia = @params.Sequencia
                };

                validationResult = _moduloApp.Add(modulo);

                return validationResult.IsValid ? ResponderPadrao(true, "Módulo incluído com sucesso.")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(CadastroModuloRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                var validationResult = new ValidationResult();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                var modulo = _moduloApp.Get(@params.IdModulo);
                if (modulo == null)
                    validationResult.Add("Não foi possível localizar o módulo.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                ReflectionHelper.CopyProperties(@params, modulo);
                validationResult = _moduloApp.Update(modulo);

                return validationResult.IsValid
                    ? ResponderPadrao(true, "Módulo alterado com sucesso.")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idModulo)
        {
            try
            {
                var modulo = _moduloApp.Get(idModulo);
                var retorno = new
                {
                    modulo.IdModulo,
                    modulo.Descricao,
                    modulo.ClassIcon,
                    modulo.Ativo,
                    modulo.Sequencia
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorEmpresa(int idEmpresa, int? idGrupoUsuario)
        {
            try
            {
                if (_userIdentity.Perfil == 6)
                {
                    var idModulosPermitidosList = _menuApp.GetMenusPermitidos(_userIdentity.IdUsuario).menus.Select(x => x.IdModulo);
                    var modulosEstabelecimento = _moduloApp.All().Where(x => idModulosPermitidosList.Contains(x.IdModulo)).ToList();

                    var retorno = modulosEstabelecimento.ToList().Select(o => new
                    {
                        o.IdModulo,
                        o.Descricao,
                        o.ClassIcon,
                        SubMenus = o.Menus.Select(m => new
                        {
                            m.Descricao
                        })
                    });

                    return ResponderSucesso(retorno);
                }
            
                var modulos = _moduloApp.GetModulosPorEmpresa(idEmpresa).ToList()
                    .Select(x => new
                    {
                        x.IdModulo,
                        x.Descricao,
                        x.ClassIcon,
                        SubMenus = x.Menus.Select(p => new
                        {
                            p.Descricao
                        })
                    });

                return ResponderSucesso(modulos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarModulosUsuario()
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    ResponderSucesso(null);
                var modulos = _moduloApp.GetModulosPorUsuario(_userIdentity.IdUsuario);
                return ResponderSucesso(modulos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarModulosCadastroMenu()
        {
            try
            {
                return ResponderSucesso(_moduloApp.GetModulosCadastroMenu());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
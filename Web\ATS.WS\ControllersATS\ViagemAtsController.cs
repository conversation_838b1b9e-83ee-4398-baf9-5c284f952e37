using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Webservice.Request.Pedagio;
using ATS.WS.Services;
using ATS.WS.Services.ViagemServices;
using AutoMapper.QueryableExtensions;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ViagemDocumentoFiscalModel = ATS.WS.Models.Common.Request.ViagemDocumentoFiscalModel;

namespace ATS.WS.ControllersATS
{
    public class ViagemAtsController : BaseAtsController<IViagemApp>
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IClienteApp _clienteApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly SrvViagem _srvViagem;
        private readonly BaixaEventoViagem _baixaEventoViagem;
        private readonly CancelamentoEventoViagem _cancelamentoEventoViagem;
        private readonly BloqueioEventoViagem _bloqueioEventoViagem;
        private readonly DesbloqueioEventoViagem _desbloqueioEventoViagem;
        private readonly StatusEventoViagem _statusEventoViagem;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IViagemApp _viagemApp;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IUserIdentity _userIdentity;


        public ViagemAtsController(IViagemApp app, IUserIdentity userIdentity, IProprietarioApp proprietarioApp, IEmpresaApp empresaApp, IClienteApp clienteApp, IFilialApp filialApp,
            IVeiculoApp veiculoApp, IMotoristaApp motoristaApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App,
            SrvViagem srvViagem, BaixaEventoViagem baixaEventoViagem, CancelamentoEventoViagem cancelamentoEventoViagem,
            BloqueioEventoViagem bloqueioEventoViagem, DesbloqueioEventoViagem desbloqueioEventoViagem, IVersaoAnttLazyLoadService versaoAntt, StatusEventoViagem statusEventoViagem,
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IViagemApp viagemApp, IParametrosUsuarioService parametrosUsuarioService) : base(app, userIdentity)
        {
            _userIdentity = userIdentity;
            _proprietarioApp = proprietarioApp;
            _empresaApp = empresaApp;
            _clienteApp = clienteApp;
            _filialApp = filialApp;
            _veiculoApp = veiculoApp;
            _motoristaApp = motoristaApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _srvViagem = srvViagem;
            _baixaEventoViagem = baixaEventoViagem;
            _cancelamentoEventoViagem = cancelamentoEventoViagem;
            _bloqueioEventoViagem = bloqueioEventoViagem;
            _desbloqueioEventoViagem = desbloqueioEventoViagem;
            _versaoAntt = versaoAntt;
            _statusEventoViagem = statusEventoViagem;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _viagemApp = viagemApp;
            _parametrosUsuarioService = parametrosUsuarioService;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarViagens(ConsultaViagemRequestDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (SessionUser.Perfil == (int) EPerfil.Empresa)
                    request.IdEmpresa = SessionUser.IdEmpresa;

                var result = App.ConsultarViagens(request, take, page, order, filters);

                var resultado = ResponderSucesso(result);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioConsultaViagem(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioConsultaViagemRequestDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = SessionUser.IdEmpresa;

            var retorno = App.RelatorioConsultaViagens(filtrosGridModel, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters,
                filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(retorno, mimeType, $"Relatório de consulta de viagem.{filtrosGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhesCiotViagem(int idViagem)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idViagem))
                    return ResponderErro("Registro não encontrado.");

                var viagemApp = App;

                var result = viagemApp.ConsultarDetalhesCiotViagem(idViagem);

                var resultado = ResponderSucesso(result);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarSituacaoPortador(string cnpjCpf, string rntrc)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                var viagemApp = App;

                var proprietarioExistente = _proprietarioApp.ConsultarPorCnpjCpf(cnpjCpf, SessionUser.IdEmpresa.Value);

                if (proprietarioExistente == null)
                    return ResponderErro("Não foi encontrado proprietário com os dados informados!");

                var result = viagemApp.ConsultarSituacaoTransportador(cnpjCpf, rntrc, SessionUser.IdEmpresa.Value);

                var resultado = ResponderSucesso(result);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCustoPedagioRota(ViagemCalcularValorPedagioRequest request)
        {
            try
            {
                var consulta = App.CalcularValorPedagio(request);

                if (consulta.Sucesso)
                    return ResponderSucesso(consulta);

                return ResponderErro(consulta.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult Salvar(ViagemIntegrarRequest request)
        {
            try
            {
                var validacao = request.Validar();

                if (!validacao.Key) return ResponderErro(validacao.Value);

                if (request.Parcelas != null &&
                    request.Parcelas.Any(c => c.FormaPagamento == EViagemEventoFormaPagamento.Pix))
                {
                    if(!_parametrosUsuarioService.GetPermiteRealizarPagamentoPix(_userIdentity.IdUsuario))
                        return ResponderErro("Usuário sem permissão para realizar pagamentos Pix.");
                }
 
                var requestViagem = DeParaViagem(request);

                var retorno = requestViagem.IdViagem.HasValue ? _srvViagem.Alterar(requestViagem) : _srvViagem.Integrar(requestViagem);

                var resultado = new ViagemIntegrarResponse();
                resultado.Status = retorno.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                resultado.Mensagem = retorno.Sucesso ? "Operação realizada com sucesso." : retorno.Mensagem?.Replace("<br/>", "");

                resultado.CIOT = new ViagemIntegrarItemResponse();
                resultado.CIOT.Status = retorno.Objeto?.CIOT.Resultado == EResultadoDeclaracaoCiot.NaoHabilitado ? StatusRetorno.NaoRealizado :
                    retorno.Objeto?.CIOT.Resultado == EResultadoDeclaracaoCiot.Erro ? StatusRetorno.Erro : StatusRetorno.Sucesso;

                resultado.CIOT.Mensagem = retorno.Objeto?.CIOT.Mensagem?.Replace("<br/>", "") ?? string.Empty;
                switch (retorno.Objeto?.CIOT.Resultado)
                {
                    case EResultadoDeclaracaoCiot.Sucesso:
                        resultado.CIOT.Mensagem = retorno.Objeto.CIOT?.Mensagem?.Replace("<br/>", "") ?? "Operação realizada com sucesso.";

                        if (retorno.Objeto.CIOT?.Dados?.AvisoTransportador != null)
                            resultado.CIOT.Mensagem += string.Format("{0}{1}", resultado.CIOT.Mensagem.EndsWith(".") ? " " : ". ",
                                retorno.Objeto.CIOT.Dados.AvisoTransportador);

                        if (!resultado.CIOT.Mensagem.EndsWith("."))
                            resultado.CIOT.Mensagem += ".";

                        break;
                    case EResultadoDeclaracaoCiot.Erro:
                        resultado.CIOT.Mensagem = retorno.Objeto.CIOT?.Mensagem?.Replace("<br/>", "") ?? "Erro ao realizar solicitação de CIOT.";
                        break;
                    case EResultadoDeclaracaoCiot.NaoObrigatorio:
                    case EResultadoDeclaracaoCiot.NaoHabilitado:
                        resultado.CIOT.Mensagem = retorno.Objeto.CIOT?.Mensagem?.Replace("<br/>", "") ?? "Não realizado.";
                        break;
                }

                resultado.Pedagio = new ViagemIntegrarItemResponse();
                resultado.Pedagio.Status = retorno.Objeto?.Pedagio?.Status == EResultadoCompraPedagio.NaoRealizado ? StatusRetorno.NaoRealizado
                    : retorno.Objeto?.Pedagio?.Status == EResultadoCompraPedagio.Erro ? StatusRetorno.Erro : StatusRetorno.Sucesso;
                resultado.Pedagio.Mensagem = !string.IsNullOrWhiteSpace(retorno.Objeto?.Pedagio?.Mensagem) 
                    ? retorno.Objeto.Pedagio.Mensagem.Replace("<br/>", "") 
                    : resultado.Pedagio.Status.GetDescription();

                resultado.Cartao = new ViagemIntegrarItemResponse();
                resultado.Cartao.Status = StatusRetorno.NaoRealizado;
                resultado.Cartao.Mensagem = "Operação não realizada.";

                if (retorno.Objeto?.Eventos.Any(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado) ?? false)
                {
                    resultado.Cartao.Status =
                        retorno.Objeto?.Eventos?.FirstOrDefault(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado)?.OperacaoCartao.Status == ERetornoOperacaoCartao.Erro
                            ? StatusRetorno.Erro
                            : StatusRetorno.Sucesso;
                    resultado.Cartao.Mensagem = (retorno.Objeto?.Eventos).FirstOrDefault(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado)?.OperacaoCartao.Mensagem ??
                                                "Operação não realizada.";

                    switch (resultado.Cartao.Status)
                    {
                        case StatusRetorno.NaoRealizado:
                            resultado.Cartao.Mensagem = (retorno.Objeto?.Eventos).FirstOrDefault(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado)?.OperacaoCartao.Mensagem
                                ?.Replace("<br/>", "") ?? "Não realizado";
                            break;
                        case StatusRetorno.Sucesso:
                            resultado.Cartao.Mensagem =
                                (retorno.Objeto?.Eventos).FirstOrDefault(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado)?.OperacaoCartao.Mensagem?.Replace("<br/>", "") ??
                                "Operação realizada com sucesso.";
                            break;
                        case StatusRetorno.Erro:
                            resultado.Cartao.Mensagem =
                                (retorno.Objeto?.Eventos).FirstOrDefault(c => c.OperacaoCartao.Status != ERetornoOperacaoCartao.NaoHabilitado)?.OperacaoCartao.Mensagem?.Replace("<br/>", "") ??
                                "Erro ao realizar a carga do cartão.";
                            break;
                    }
                }

                if (retorno.Objeto?.BloqueioGestorNew.Objeto.Status == EBloqueioGestorStatus.Pendente)
                {
                    if(retorno.Objeto?.BloqueioGestorNew.Objeto.MensagemFrete != null)
                        resultado.Cartao.Mensagem = $"{resultado.Cartao.Mensagem} <br/> {retorno.Objeto?.BloqueioGestorNew.Objeto.MensagemFrete}";
                    
                    if(retorno.Objeto?.BloqueioGestorNew.Objeto.MensagemFrete != null)
                        resultado.Pedagio.Mensagem = $"{resultado.Pedagio.Mensagem} <br/> {retorno.Objeto?.BloqueioGestorNew.Objeto.MensagemPedagio}";
                }

                if (resultado.CIOT.Status == StatusRetorno.Erro)
                {
                    resultado.Pedagio.Status = StatusRetorno.NaoRealizado;
                    resultado.Cartao.Status = StatusRetorno.NaoRealizado;
                }

                return !retorno.Sucesso ? ResponderErro(retorno.Mensagem) : ResponderSucesso("Operação realizada com sucesso.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhesViagem(int idviagem)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idviagem))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? App.ConsultarViagemV2(idviagem, SessionUser.IdEmpresa, SessionUser.IdUsuario, true)
                    : App.ConsultarViagemV3(idviagem, true);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult BaixarEvento(ViagemBaixarEventoRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var viagemExistente = App.GetEvento(request.IdViagem, request.IdViagemEvento);

                // Vallidação devido a se algum problema de cache enviar um IdViagem / IdViagemEvento que não batem, retorna a mensagem amigável para o usuário.
                if (!viagemExistente.Any())
                    return ResponderErro("Registro não encontrado.");
                
                if (viagemExistente.Any(c => c.HabilitarPagamentoPix == true) && !_parametrosUsuarioService.GetPermiteRealizarPagamentoPix(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão para realizar pagamentos Pix.");

                var baixaEventoViagem = DeParaBaixaEventoViagem(request);

                var baixaResponse =
                    _baixaEventoViagem.BaixarEvento(baixaEventoViagem);

                var retorno = new ViagemBaixarEventoResponse();
                retorno.Status = baixaResponse.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                retorno.Mensagem = !string.IsNullOrWhiteSpace(baixaResponse.Mensagem) ? baixaResponse.Mensagem : "Operação realizada com sucesso";

                var evento = viagemExistente.Select(c => new
                {
                    c.Status,
                    c.DataHoraPagamento,
                    c.ValorPagamento,
                    c.HabilitarPagamentoCartao
                }).FirstOrDefault();

                retorno.StatusEvento = evento?.Status ?? EStatusViagemEvento.Aberto;
                retorno.ValorPagamento = evento?.ValorPagamento ?? 0;
                retorno.DataPagamento = evento?.DataHoraPagamento;
                retorno.IdViagemEvento = request.IdViagemEvento;
                retorno.FormaPagamento = request.FormaPagamento;

                return retorno.Status != StatusRetorno.Erro
                    ? ResponderSucesso(retorno.Mensagem, retorno)
                    : ResponderErro(retorno.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarEvento(ViagemCancelamentoEventoRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var viagemExistente = App.GetEvento(request.IdViagem, request.IdViagemEvento);

                // Vallidação devido a se algum problema de cache enviar um IdViagem / IdViagemEvento que não batem, retorna a mensagem amigável para o usuário.
                if (!viagemExistente.Any())
                    return ResponderErro("Registro não encontrado.");

                var cancelaEventoViagem = DeParaCancelaEventoViagem(request);

                var baixaResponse =
                    _cancelamentoEventoViagem.CancelarEvento(cancelaEventoViagem);

                var retorno = new ViagemCancelamentoEventoResponse();
                retorno.Status = baixaResponse.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                retorno.Mensagem = !string.IsNullOrWhiteSpace(baixaResponse.Mensagem) ? baixaResponse.Mensagem : $"Operação realizada com sucesso. {baixaResponse.Avisos}";

                var evento = viagemExistente.Select(c => new
                {
                    c.Status,
                    c.DataHoraPagamento,
                    c.ValorPagamento,
                    c.HabilitarPagamentoCartao
                }).FirstOrDefault();

                retorno.StatusEvento = evento?.Status ?? EStatusViagemEvento.Aberto;
                retorno.IdViagemEvento = request.IdViagemEvento;

                return ResponderSucesso(retorno.Mensagem, retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult BloquearEvento(ViagemBloquearEventoRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var viagemExistente = App.GetEvento(request.IdViagem, request.IdViagemEvento);

                // Vallidação devido a se algum problema de cache enviar um IdViagem / IdViagemEvento que não batem, retorna a mensagem amigável para o usuário.
                if (!viagemExistente.Any())
                    return ResponderErro("Registro não encontrado.");

                var bloqueioEventoViagem = DeParaBloqueioEventoViagem(request);

                var baixaResponse =
                    _bloqueioEventoViagem
                        .BloquearEvento(bloqueioEventoViagem); //new SrvViagem(App, _clienteApp, _parametrosApp, _ciotV2App, _ciotV3App, _proprietarioApp).BloquearEvento(bloqueioEventoViagem);

                var retorno = new ViagemCancelamentoEventoResponse();
                retorno.Status = baixaResponse.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                retorno.Mensagem = !string.IsNullOrWhiteSpace(baixaResponse.Mensagem) ? baixaResponse.Mensagem : "Operação realizada com sucesso";

                var evento = viagemExistente.Select(c => new
                {
                    c.Status,
                    c.DataHoraPagamento,
                    c.ValorPagamento,
                    c.HabilitarPagamentoCartao
                }).FirstOrDefault();

                retorno.StatusEvento = evento?.Status ?? EStatusViagemEvento.Aberto;
                retorno.IdViagemEvento = request.IdViagemEvento;

                return ResponderSucesso(retorno.Mensagem, retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesbloquearEvento(ViagemDesbloquearEventoRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var viagemExistente = App.GetEvento(request.IdViagem, request.IdViagemEvento);

                // Vallidação devido a se algum problema de cache enviar um IdViagem / IdViagemEvento que não batem, retorna a mensagem amigável para o usuário.
                if (!viagemExistente.Any())
                    return ResponderErro("Registro não encontrado.");

                var desbloqueioEventoViagem = DeParaDesbloqueioEventoViagem(request);

                var baixaResponse =
                    _desbloqueioEventoViagem.DesbloquearEvento(desbloqueioEventoViagem);

                var retorno = new ViagemCancelamentoEventoResponse();
                retorno.Status = baixaResponse.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                retorno.Mensagem = !string.IsNullOrWhiteSpace(baixaResponse.Mensagem) ? baixaResponse.Mensagem : "Operação realizada com sucesso";

                var evento = viagemExistente.Select(c => new
                {
                    c.Status,
                    c.DataHoraPagamento,
                    c.ValorPagamento,
                    c.HabilitarPagamentoCartao
                }).FirstOrDefault();

                retorno.StatusEvento = evento?.Status ?? EStatusViagemEvento.Aberto;
                retorno.IdViagemEvento = request.IdViagemEvento;

                return ResponderSucesso(retorno.Mensagem, retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarViagem(ViagemCancelarRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
                {
                    c.CNPJ,
                    Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
                }).FirstOrDefault();

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var retorno = _statusEventoViagem.AlterarStatus(request.IdViagem, EStatusViagem.Cancelada, empresa.CNPJ,
                        empresa.Token, SessionUser.CpfCnpj, SessionUser.Nome, null, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, request.DataAtualizacao);

                if (!retorno.Sucesso)
                    return ResponderErro(retorno.Mensagem);

                var response = new ViagemCancelarResponse();
                response.Status = retorno.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                response.Mensagem = retorno.Mensagem;

                return ResponderSucesso("Operação realizada com sucesso!", response);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult BaixarViagem(ViagemBaixarRequest request)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
                {
                    c.CNPJ,
                    Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
                }).FirstOrDefault();

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdViagem))
                    return ResponderErro("Registro não encontrado.");

                var retorno =
                    _statusEventoViagem.AlterarStatus(request.IdViagem, EStatusViagem.Baixada, empresa.CNPJ,
                        empresa.Token, SessionUser.CpfCnpj, SessionUser.Nome, null, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, request.DataAtualizacao);

                if (!retorno.Sucesso)
                    return ResponderErro(retorno.Mensagem);

                var response = new ViagemBaixarResponse();
                response.Status = retorno.Sucesso ? StatusRetorno.Sucesso : StatusRetorno.Erro;
                response.Mensagem = retorno.Mensagem;

                return ResponderSucesso("Operação realizada com sucesso!", response);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ImprimirCiot(int idviagem)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                    throw new Exception("Nenhuma empresa vinculada a este usuário.");

                if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, idviagem))
                    throw new Exception("Registro não encontrado.");

                var dadosCiot = App.GetDadosCiot(idviagem);

                if (dadosCiot.CiotStatus == EResultadoDeclaracaoCiot.NaoHabilitado)
                    return ResponderErro("CIOT não foi habilitado para esta viagem.");

                if (dadosCiot.CiotStatus == EResultadoDeclaracaoCiot.Erro)
                    return ResponderErro(dadosCiot.CiotMensagem);

                var model = new ImprimirComprovanteContratoAgregadoModel
                {
                    Ciot = dadosCiot.Numero,
                    Senha = dadosCiot.Senha
                };

                var link = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.LinkComprovanteContrato(model)
                    : _ciotV3App.LinkComprovanteContrato(model);

                return ResponderSucesso(link);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Validate2FA]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult DesvincularCiot(int idViagem, int idUsuario)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, idViagem))
                    return ResponderErro("Registro não encontrado.");

                var retorno = App.DesvincularCiot(idViagem, idUsuario);

                if (!retorno.IsValid)
                    return ResponderErro(retorno.Errors.FirstOrDefault()?.Message);

                return ResponderSucesso("CIOT desvinculado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult ImprimirValePedagio(string json)
        {
            var request = JsonConvert.DeserializeObject<ImpressaoComprovantePedagioRequest>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, request.IdViagem))
                throw new Exception("Registro não encontrado.");

            ViagemDadosValePedagio dadosAtsValePedagio;
            var dadosComprovante = App.GetDadosValePedagio(request.IdViagem, out dadosAtsValePedagio);

            if (dadosAtsValePedagio == null)
                throw new Exception("Registro não encontrado");

            if (dadosComprovante?.Status == ComprovanteValePedagioResponseStatus.Falha)
                throw new Exception(dadosComprovante.Mensagem);

            var impressaoComprovante = !string.IsNullOrWhiteSpace(dadosAtsValePedagio.ProtocoloEnvioValePedagio)
                ? App.GetDadosValePedagioReport(dadosComprovante, dadosAtsValePedagio.Fornecedor)
                : App.ConsultarReciboMoedeiro(request.IdViagem, dadosAtsValePedagio.IdEmpresa);

            return File(impressaoComprovante, ConstantesUtils.PdfMimeType, $"Comprovante de compra de pedágio.pdf");
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult ReciboVPO(int id)
        {
            ViagemDadosValePedagio dadosAtsValePedagio;

            if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, id))
                throw new Exception("Registro não encontrado.");

            var dadosComprovante = App.GetDadosValePedagio(id, out dadosAtsValePedagio);

            if (dadosAtsValePedagio == null)
                throw new Exception("Dados nao encontrado");

            if (dadosComprovante?.Status == ComprovanteValePedagioResponseStatus.Falha)
                throw new Exception("Recibo VPO com status erro");

            var impressaoComprovante = !string.IsNullOrWhiteSpace(dadosAtsValePedagio.ProtocoloEnvioValePedagio)
                ? App.GetDadosValePedagioReport(dadosComprovante, dadosAtsValePedagio.Fornecedor)
                : App.ConsultarReciboMoedeiro(id, dadosAtsValePedagio.IdEmpresa);

            return File(impressaoComprovante, ConstantesUtils.PdfMimeType, $"Comprovante de compra de pedágio.pdf");
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTiposCarga(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Não foi possível validar a empresa na autenticação do usuário.");

                var resposta = App.ConsultarTiposCarga(SessionUser.IdEmpresa.Value, take, page, order, filters);

                if (resposta.Sucesso == true)
                {
                    var retorno = new
                    {
                        totalItems = resposta.Tipos.Count(),
                        items = resposta.Tipos.Skip((page - 1) * take).Take(take).ToList()
                    };

                    return ResponderSucesso(retorno);
                }

                return ResponderErro(resposta.ExceptionMessage ?? resposta.Excecao?.Mensagem ?? "Serviço de CIOT indisponível");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarUltimaViagemEmpresaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var empresaId = filters.FirstOrDefault(x => x.Campo == "empresaId")?.Valor.ToInt();

                if (SessionUser.Perfil == (int) EPerfil.Empresa)
                    empresaId = SessionUser.IdEmpresa;

                if (!empresaId.HasValue)
                    return ResponderErro("Empresa não informada");

                var retornoConsulta = App.ConsultarUltimaViagemEmpresa(empresaId.Value, take, page, order, filters);

                return ResponderSucesso(retornoConsulta);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult VincularViagemAoContrato(int idContratoCiotAgregado, int idUltimaViagem)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idUltimaViagem))
                    return ResponderErro("Registro não encontrado.");

                var retorno = App.VincularViagemAoContrato(idContratoCiotAgregado, idUltimaViagem);

                return retorno ? ResponderSucesso($"Viagem vinculada com sucesso ao contrato {idContratoCiotAgregado}") : ResponderErro("Erro interno ao vincular viagem");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        #region Métodos privados

        private ViagemIntegrarRequestModel DeParaViagem(ViagemIntegrarRequest request)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            var requestViagem = new ViagemIntegrarRequestModel();
            requestViagem.DataAtualizacao = request.DataAtualizacao;
            requestViagem.CNPJAplicacao = StringExtension.OnlyNumbers(empresa.CNPJ);
            requestViagem.CNPJEmpresa = requestViagem.CNPJAplicacao;
            requestViagem.Token = empresa.Token;
            requestViagem.IdViagem = request.IdViagem;
            requestViagem.NumeroDocumento = request.DocumentoCliente;
            requestViagem.DocumentoCliente = request.DocumentoCliente.ValueLimited(100);
            requestViagem.NaturezaCarga = request.NaturezaCarga;
            requestViagem.IRRPF = request.IRRPF ?? 0;
            requestViagem.SESTSENAT = request.SESTSENAT ?? 0;
            requestViagem.INSS = request.INSS ?? 0;

            if (_versaoAntt.Value == EVersaoAntt.Versao3)
            {
                requestViagem.CepOrigem = request.CepOrigem;
                requestViagem.CepDestino = request.CepDestino;
                requestViagem.DistanciaViagem = request.DistanciaViagem;
                requestViagem.CodigoTipoCarga = request.CodigoTipoCarga;

                if (request.DadosPagamento != null)
                    requestViagem.DadosPagamento = new ViagemDadosPagamentoIntegrarModel()
                    {
                        FormaPagamento = request.DadosPagamento.FormaPagamento,
                        CodigoBacen = request.DadosPagamento.CodigoBacen,
                        Agencia = request.DadosPagamento.Agencia,
                        Conta = request.DadosPagamento.Conta
                    };

                if (request.DadosAntt != null)
                    requestViagem.DadosAntt = new ViagemDadosAnttIntegrarModel()
                    {
                        AltoDesempenho = request.DadosAntt.AltoDesempenho,
                        DestinacaoComercial = request.DadosAntt.DestinacaoComercial,
                        FreteRetorno = request.DadosAntt.FreteRetorno,
                        CepRetorno = request.DadosAntt.CepRetorno,
                        DistanciaRetorno = request.DadosAntt.DistanciaRetorno
                    };
            }

            if (request.IdFilial.HasValue)
            {
                var filialCnpj = _filialApp.QueryById(request.IdFilial.Value).Select(c => c.CNPJ).FirstOrDefault();
                requestViagem.CNPJFilial = filialCnpj;
            }

            var numeroEixos = 0;

            var veiculoPlaca = _veiculoApp.QueryById(request.Veiculo.IdVeiculo).Select(c => new
            {
                c.Placa,
                c.QuantidadeEixos
            }).FirstOrDefault();
            requestViagem.Placa = veiculoPlaca?.Placa;

            numeroEixos += veiculoPlaca?.QuantidadeEixos ?? 0;

            if (request.Carretas != null)
            {
                requestViagem.Carretas = new List<string>();
                foreach (var carreta in request.Carretas)
                {
                    var carretaPlaca = _veiculoApp.QueryById(carreta.IdVeiculo).Select(c => new
                    {
                        c.Placa,
                        c.QuantidadeEixos
                    }).FirstOrDefault();

                    if (carretaPlaca != null)
                    {
                        requestViagem.Carretas.Add(carretaPlaca.Placa);
                        numeroEixos += carretaPlaca.QuantidadeEixos;
                    }
                }
            }

            requestViagem.StatusViagem = request.Status;

            var motorista = _motoristaApp.QueryById(request.IdMotorista).Select(c => new
            {
                c.CPF,
                c.Nome
            }).FirstOrDefault();

            requestViagem.CPFMotorista = motorista?.CPF;

            var proprietario = _proprietarioApp.All().Where(c => c.IdProprietario == request.IdProprietario)
                .ProjectTo<ProprietarioViagemDto>()
                .FirstOrDefault();

            requestViagem.CPFCNPJProprietario = proprietario?.CNPJCPF;
            requestViagem.NomeProprietario = proprietario?.NomeFantasia;
            requestViagem.RNTRC = proprietario?.RNTRC.ToIntNullable();
            requestViagem.DataColeta = request.DataInicioFrete;
            requestViagem.DataPrevisaoEntrega = request.DataFimFrete;
            requestViagem.DataEmissao = request.DataEmissao;
            requestViagem.HabilitarDeclaracaoCiot = request.HabilitarDeclaracaoCiot;
            requestViagem.ForcarCiotNaoEquiparado = request.ForcarDeclaracaoCiot;

            requestViagem.DocumentosFiscais = new List<ViagemDocumentoFiscalModel>();

            if (request.Documentos != null && request.Documentos.Any())
            {
                requestViagem.PesoSaida = request.Documentos.Sum(c => c.PesoSaida);
                requestViagem.ValorMercadoria = request.Documentos.Sum(c => c.Valor);
                requestViagem.Quantidade = request.Documentos.Count;

                var idClienteOrigem = request.Documentos.Select(c => c.IdClienteOrigem).FirstOrDefault();
                var idClienteDestino = request.Documentos.Select(c => c.IdClienteDestino).LastOrDefault();

                var clienteOrigem = _clienteApp.All().Where(c => c.IdCliente == idClienteOrigem).Select(c => new
                {
                    c.CNPJCPF,
                    c.NomeFantasia
                }).FirstOrDefault();

                var clienteDestino = _clienteApp.All().Where(c => c.IdCliente == idClienteDestino).Select(c => new
                {
                    c.CNPJCPF,
                    c.NomeFantasia
                }).FirstOrDefault();

                requestViagem.CPFCNPJClienteOrigem = StringExtension.OnlyNumbers(clienteOrigem?.CNPJCPF);
                requestViagem.CPFCNPJClienteDestino = StringExtension.OnlyNumbers(clienteDestino?.CNPJCPF);

                foreach (var documentoRequest in request.Documentos)
                {
                    var documento = new ViagemDocumentoFiscalModel();
                    documento.IdViagemDocumentoFiscal = documentoRequest.Id;
                    documento.NumeroDocumento = documentoRequest.NumeroDocumento.ToDecimalSafe() ?? 0;
                    documento.Serie = documentoRequest.Serie;
                    documento.Chave = documentoRequest.Chave;
                    documento.PesoSaida = documentoRequest.PesoSaida ?? 0;
                    documento.Valor = documentoRequest.Valor ?? 0;
                    documento.TipoDocumento = documentoRequest.TipoDocumento;
                    documento.IdClienteOrigem = documentoRequest.IdClienteOrigem;
                    documento.IdClienteDestino = documentoRequest.IdClienteDestino;

                    requestViagem.DocumentosFiscais.Add(documento);
                }
            }

            requestViagem.ViagemRegra = new List<ViagemRegraIntegrarModel>();

            // Criado apenas para a necessidade do ATS de requerer ao menos 1 Viagem regra para adicionar os eventos
            requestViagem.ViagemRegra.Add(new ViagemRegraIntegrarModel
            {
                TaxaAntecipacao = 0,
                ToleranciaPeso = 0,
                TarifaTonelada = 0
            });

            requestViagem.ViagemEventos = new List<ViagemEventoIntegrarModel>();

            if (request.Parcelas != null)
                foreach (var parcela in request.Parcelas)
                {
                    var parcelaRequest = new ViagemEventoIntegrarModel();

                    parcelaRequest.IdViagemEvento = parcela.IdViagemEvento;
                    parcelaRequest.TipoEvento = parcela.TipoEvento;
                    parcelaRequest.ValorPagamento = parcela.Valor;
                    parcelaRequest.HabilitarPagamentoCartao = parcela.FormaPagamento == EViagemEventoFormaPagamento.Cartao;
                    parcelaRequest.HabilitarPagamentoPix = parcela.FormaPagamento == EViagemEventoFormaPagamento.Pix;
                    parcelaRequest.Status = parcela.Status;
                    parcelaRequest.Instrucao = parcela.Instrucao;
                    parcelaRequest.DataAgendamentoPagamento = parcela.DataAgendamentoPagamento;

                    parcelaRequest.ViagemDocumentos = new List<ViagemDocumentoIntegrarModel>();

                    if (parcela.Documentos != null)
                        foreach (var documento in parcela.Documentos)
                        {
                            var documentoRequest = new ViagemDocumentoIntegrarModel();
                            documentoRequest.IdEvento = parcela.IdViagemEvento ?? 0;
                            documentoRequest.IdViagemDocumento = documento.Id;
                            documentoRequest.TipoDocumento = documento.TipoDocumento;
                            documentoRequest.NumeroDocumento = documento.Numero.ToIntNullable() ?? 0;
                            documentoRequest.TipoEvento = parcelaRequest.TipoEvento;
                            documentoRequest.Descricao = documento.Descricao;
                            documentoRequest.ObrigaAnexo = documento.AnexoObrigatorio;

                            parcelaRequest.ViagemDocumentos.Add(documentoRequest);
                        }

                    parcelaRequest.ViagemOutrosDescontos = new List<ViagemValorAdicionalIntegrarModel>();
                    parcelaRequest.ViagemOutrosAcrescimos = new List<ViagemValorAdicionalIntegrarModel>();
                    if (parcela.AcrescimosDescontos != null)
                    {
                        foreach (var desconto in parcela.AcrescimosDescontos.Where(c => c.Tipo == ETipoValorAdicional.Desconto))
                        {
                            var descontoRequest = new ViagemValorAdicionalIntegrarModel();
                            descontoRequest.IdViagemValorAdicional = desconto.Id;
                            descontoRequest.Valor = desconto.Valor ?? 0;
                            descontoRequest.Descricao = desconto.Descricao;
                            descontoRequest.NumeroDocumento = desconto.NumeroDocumento.ToIntNullable() ?? 0;

                            parcelaRequest.ViagemOutrosDescontos.Add(descontoRequest);
                        }

                        foreach (var acrescimo in parcela.AcrescimosDescontos.Where(c => c.Tipo == ETipoValorAdicional.Acrescimo))
                        {
                            var acrescimoRequest = new ViagemValorAdicionalIntegrarModel();
                            acrescimoRequest.IdViagemValorAdicional = acrescimo.Id;
                            acrescimoRequest.Valor = acrescimo.Valor ?? 0;
                            acrescimoRequest.Descricao = acrescimo.Descricao;
                            acrescimoRequest.NumeroDocumento = acrescimo.NumeroDocumento.ToIntNullable() ?? 0;

                            parcelaRequest.ViagemOutrosAcrescimos.Add(acrescimoRequest);
                        }
                    }

                    requestViagem.ViagemEventos.Add(parcelaRequest);
                }

            if (request.Pedagio != null && request.Pedagio.Fornecedor != FornecedorEnum.Desabilitado)
            {
                requestViagem.Pedagio = new PedagioModel();
                requestViagem.Pedagio.Fornecedor = request.Pedagio.Fornecedor;
                requestViagem.Pedagio.TipoVeiculo = request.Pedagio.TipoVeiculo;
                requestViagem.Pedagio.QtdEixos = numeroEixos;
                requestViagem.Pedagio.ValorPedagio = request.Pedagio.ValorPedagio;
                requestViagem.ValorPedagio = request.Pedagio.ValorPedagio;

                if (FornecedorEnumExtensions.RoteirizacoesObrigatorias.Contains(request.Pedagio.Fornecedor) ||
                    request.Pedagio.Roteirizar && request.Pedagio.Fornecedor == FornecedorEnum.Moedeiro && request.IdViagem == null)
                {
                    var pedagioConsulta = request.Pedagio.IdentificadorHistorico ?? GetConsultaHistoricoPedagio(numeroEixos, request.Pedagio);
                    requestViagem.Pedagio.IdentificadorHistorico = pedagioConsulta;
                }

                requestViagem.Pedagio.Localizacoes = new List<LocalizacaoDTO>();

                if (request.Pedagio.Localizacoes != null)
                    foreach (var localizacao in request.Pedagio.Localizacoes)
                        requestViagem.Pedagio.Localizacoes.Add(new LocalizacaoDTO
                        {
                            Latitude = localizacao.Latitude,
                            Longitude = localizacao.Longitude
                        });
            }

            if (request.DadosBancario != null)
            {
                requestViagem.DadosBancarioPagamentoSemCartao = new ViagemIntegrarDadosBancarioSemCartao()
                {
                    Agencia = request.DadosBancario.Agencia,
                    IdBanco = request.DadosBancario.IdBanco,
                    ContaCorrente = request.DadosBancario.ContaCorrente,
                    TipoConta = request.DadosBancario.TipoConta,
                    FormaPagamentoSemCartao = request.DadosBancario.FormaPagamento,
                    DescricaoBanco = request.DadosBancario.DescricaoBanco
                };
            }

            return requestViagem;
        }

        private CancelarEventoRequestModel DeParaCancelaEventoViagem(ViagemCancelamentoEventoRequest request)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            var requestModel = new CancelarEventoRequestModel();
            requestModel.DataAtualizacao = request.DataAtualizacao;
            requestModel.CNPJAplicacao = StringExtension.OnlyNumbers(empresa.CNPJ);
            requestModel.CNPJEmpresa = requestModel.CNPJAplicacao;
            requestModel.Token = empresa.Token;

            requestModel.IdViagem = request.IdViagem;
            requestModel.IdViagemEvento = request.IdViagemEvento;

            requestModel.DocumentoUsuarioAudit = SessionUser.CpfCnpj;
            requestModel.NomeUsuarioAudit = SessionUser.Nome;

            return requestModel;
        }

        private BloquearEventoRequestModel DeParaBloqueioEventoViagem(ViagemBloquearEventoRequest request)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            var requestModel = new BloquearEventoRequestModel();
            requestModel.DataAtualizacao = request.DataAtualizacao;
            requestModel.CNPJAplicacao = StringExtension.OnlyNumbers(empresa.CNPJ);
            requestModel.CNPJEmpresa = requestModel.CNPJAplicacao;
            requestModel.Token = empresa.Token;

            requestModel.IdViagem = request.IdViagem;
            requestModel.IdViagemEvento = request.IdViagemEvento;

            requestModel.DocumentoUsuarioAudit = SessionUser.CpfCnpj;
            requestModel.NomeUsuarioAudit = SessionUser.Nome;

            return requestModel;
        }

        private DesbloquearEventoRequestModel DeParaDesbloqueioEventoViagem(ViagemDesbloquearEventoRequest request)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            var requestModel = new DesbloquearEventoRequestModel();
            requestModel.DataAtualizacao = request.DataAtualizacao;
            requestModel.CNPJAplicacao = StringExtension.OnlyNumbers(empresa.CNPJ);
            requestModel.CNPJEmpresa = requestModel.CNPJAplicacao;
            requestModel.Token = empresa.Token;

            requestModel.IdViagem = request.IdViagem;
            requestModel.IdViagemEvento = request.IdViagemEvento;

            requestModel.DocumentoUsuarioAudit = SessionUser.CpfCnpj;
            requestModel.NomeUsuarioAudit = SessionUser.Nome;

            return requestModel;
        }

        private BaixarEventoRequestModel DeParaBaixaEventoViagem(ViagemBaixarEventoRequest request)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            var requestModel = new BaixarEventoRequestModel();
            requestModel.DataAtualizacao = request.DataAtualizacao;
            requestModel.CNPJAplicacao = StringExtension.OnlyNumbers(empresa.CNPJ);
            requestModel.CNPJEmpresa = requestModel.CNPJAplicacao;
            requestModel.Token = empresa.Token;

            requestModel.IdViagem = request.IdViagem;
            requestModel.IdViagemEvento = request.IdViagemEvento;

            requestModel.DocumentoUsuarioAudit = SessionUser.CpfCnpj;
            requestModel.NomeUsuarioAudit = SessionUser.Nome;

            requestModel.HabilitarPagamentoCartao = request.FormaPagamento == EViagemEventoFormaPagamento.Cartao;
            requestModel.HabilitarPagamentoPix = request.FormaPagamento == EViagemEventoFormaPagamento.Pix;

            var erroCamposChamada = requestModel.Valida();
            if (!string.IsNullOrWhiteSpace(erroCamposChamada)) throw new Exception(erroCamposChamada);

            return requestModel;
        }

        private Guid GetConsultaHistoricoPedagio(int quantidadeEixos, ViagemIntegrarPedagioRequest pedagioRequest)
        {
            var request = new ConsultaRotaRequest();
            request.QtdEixos = quantidadeEixos;

            switch (pedagioRequest.TipoVeiculo)
            {
                case ETipoVeiculoPedagioEnum.Carro:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Carro;
                    break;
                case ETipoVeiculoPedagioEnum.Motocicleta:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Motocicleta;
                    break;
                case ETipoVeiculoPedagioEnum.Onibus:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Onibus;
                    break;
                case ETipoVeiculoPedagioEnum.Caminhao:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Caminhao;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(ViagemIntegrarPedagioRequest), pedagioRequest.TipoVeiculo, null);
            }

            request.Localizacoes = new List<LocationDTO>();

            foreach (var viagemIntegrarPedagioLocalizacaoRequest in pedagioRequest.Localizacoes)
                request.Localizacoes.Add(new LocationDTO
                {
                    Latitude = viagemIntegrarPedagioLocalizacaoRequest.Latitude,
                    Longitude = viagemIntegrarPedagioLocalizacaoRequest.Longitude
                });

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, SessionUser.IdEmpresa, SessionUser.IdUsuario, true);

            var empresa = _empresaApp.Get(SessionUser.IdEmpresa ?? 0);

            request.DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas;
            
            var response = cartoesApp.ConsultarCustoRota(request);

            if (response.Status == ConsultaRotaResponseDtoStatus.Sucesso)
                return response.IdentificadorHistorico ?? Guid.Empty;

            return Guid.Empty;
        }

        #endregion

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarVpo(int idViagem,bool cancelarViagem)
        {
            try
            {
                if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, idViagem))
                    return ResponderErro("Registro não encontrado.");

                var retorno =
                    _statusEventoViagem.CancelarVpo(idViagem, SessionUser.CpfCnpj, SessionUser.Nome);

                if (retorno.Sucesso)
                {
                    if (cancelarViagem)
                    {
                        var empresa = _empresaApp.All().Where(c => c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new
                        {
                            c.CNPJ,
                            Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
                        }).FirstOrDefault();

                        if (empresa == null)
                            return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                        var retornoCancelarViagem =
                            _statusEventoViagem.AlterarStatus(idViagem, EStatusViagem.Cancelada, empresa.CNPJ,
                                empresa.Token, SessionUser.CpfCnpj, SessionUser.Nome);

                        if (retornoCancelarViagem.Sucesso)
                            return ResponderSucesso(retorno.Mensagem);

                        return ResponderErro($"VPO cancelado com sucesso, porém o cancelamento da viagem falhou:{retornoCancelarViagem.Mensagem}");
                    }
                    
                    return ResponderSucesso(retorno.Mensagem);
                }
                
                return ResponderErro(retorno.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult ReciboPEF(string json)
        {
            var request = JsonConvert.DeserializeObject<ImpressaoComprovantePedagioRequest>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (!SessionUser.IdEmpresa.HasValue && SessionUser.Perfil != EPerfil.Administrador.GetHashCode())
                throw new Exception("Nenhuma empresa vinculada a este usuário.");

            if (SessionUser.Perfil != EPerfil.Administrador.GetHashCode() && !_viagemApp.PertenceAEmpresa(SessionUser.IdEmpresa, request.IdViagem))
                throw new Exception("Registro não encontrado.");
            
            var dadosRecibo = App.GetDadosReciboPef(request.IdViagem, request.ListarParcelasCanceladas);

            if (dadosRecibo == null)
                throw new Exception("Não foram encontrados dados do recibo de pagamento na base do ATS");

            var impressaoComprovante = App.GetDadosReciboPefReport(dadosRecibo);

            return File(impressaoComprovante, ConstantesUtils.PdfMimeType, "Recibo de pagamento.pdf");
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridPedagioAvulso(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioConsultaViagemRequestDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = SessionUser.IdEmpresa;

            var retorno = App.RelatorioConsultaPedagioVPO(filtrosGridModel, filtrosGridModel?.Take ?? 1, filtrosGridModel?.Page ?? 1, 
                filtrosGridModel?.Order, filtrosGridModel?.Filters, filtrosGridModel?.Extensao);

            string mimeType;

            switch (filtrosGridModel?.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
                default:
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
            }

            return File(retorno, mimeType, $"Relatório pedágio avulso.{filtrosGridModel?.Extensao}");
        }
        
        [HttpPost]
        [EnableLogRequest]
        [EnableLogAudit]
        [Validate2FA]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult SalvarPedagioAvulso(PedagioAvulsoRequest request)
        {
            try
            {
                if (request.IdEmpresa != SessionUser.IdEmpresa)
                    return ResponderErro("Não é permitido salvar registros para outra empresa.");

                //Auditoria Session User
                request.UsuarioAudit = new UsuarioAudit()
                {
                     DocumentoUsuarioAudit = SessionUser.CpfCnpj, 
                     NomeUsuarioAudit = SessionUser.Nome,
                     IdUsuario = SessionUser.IdUsuario
                };
                    
                var retorno = _srvViagem.IntegrarPedagioAvulso(request);
                
                if(!retorno.Sucesso)
                    return ResponderErro(retorno.Mensagem);

                return ResponderSucesso(retorno.Mensagem,retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult VerificarPermissaoPix(int? idProprietario = null)
        {
            try
            {
                if (SessionUser.IdEmpresa == null || SessionUser.IdEmpresa == 0 || idProprietario == null)
                    return ResponderErro("Não foi possível validar a permissão.");

                var valido = _viagemApp.VerificarPermissaoPix(SessionUser.IdEmpresa.Value, idProprietario.Value);

                return valido ? ResponderSucesso(string.Empty) : ResponderErro(string.Empty);
            }
            catch (Exception)
            {
                return ResponderErro("Erro ao validar a permissão.");
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using ATS.Domain.DTO;

namespace ATS.Domain.Interface.Dapper
{
    public interface IViagemEventoDapper : IRepositoryDapper<ViagemEvento>
    {
        List<ViagemEvento> GetEventosAbertosParaEmpresa(int IdEmpresa);
        void BloquearPagamento(int idViagemEvento, string motivo);
        List<int> PeriodoBloqueioEventosAberto(int IdEmpresa);
        void VincularProtocoloAosEventos(List<int> viagensEventosVincularProtocolo, int idProtocolo);
        List<EventoCiotAgregadoDto> ConsultarEventosCiotAgregado(int idContratoCiotAgregado);
    }
}
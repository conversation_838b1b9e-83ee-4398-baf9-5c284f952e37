﻿using ATS.Data.Context.Mapping.Common;
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioContatoMap : ContatoBaseMap<UsuarioContato>
    {
        public UsuarioContatoMap()
        {
            ToTable("USUARIO_CONTATO");

            HasKey(t => new { t.IdUsuario, t.IdContato });

            Property(t => t.IdUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdContato)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(t => t.Usuario)
                .WithMany(t => t.Contatos)
                .HasForeignKey(d => d.IdUsuario);
        }
    }
}
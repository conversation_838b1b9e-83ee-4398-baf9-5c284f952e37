﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IAuthSessionRepository : IRepository<AuthSession>
    {
        AuthSession GetByToken(string Token);
        AuthSession Gerar(int IdUsuario);
        int GetIdUsuario(string Token);
        void AtualizaDataUltimaRequisicao(string Token);
        void InvalidarSessoesAtivasParaUsuario(int idUsuario);
        void InvalidarSessao(string sessionKey);
    }
}

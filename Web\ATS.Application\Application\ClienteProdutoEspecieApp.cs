﻿using System.Linq;
using System.Data.Entity;
using System.Collections.Generic;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class ClienteProdutoEspecieApp : AppBase, IClienteProdutoEspecieApp
    {
        private readonly IClienteProdutoEspecieRepository _clienteProdutoEspecieRepository;
        private readonly IClienteProdutoEspecieService _clienteProdutoEspecieService;

        public ClienteProdutoEspecieApp(IClienteProdutoEspecieRepository clienteProdutoEspecieRepository, IClienteProdutoEspecieService clienteProdutoEspecieService)
        {
            _clienteProdutoEspecieRepository = clienteProdutoEspecieRepository;
            _clienteProdutoEspecieService = clienteProdutoEspecieService;
        }

        public List<ClienteProdutoEspecie> GetTodosPorCliente(int idCliente) =>
            _clienteProdutoEspecieRepository.Where(x => x.IdCliente == idCliente)
                                                            .Include(x => x.Produto)
                                                            .Include(x => x.Especie)
                                                            .ToList();

        public object GetTodosProdutosPorClienteForUiGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters) =>
            _clienteProdutoEspecieService.GetTodosProdutosPorClienteForUiGrid(take, page, orderFilters, filters);

        public object GetTodasEspeciesPorClienteForUiGrid(int Take, int Page, OrderFilters Order, List<QueryFilters> Filters) =>
            _clienteProdutoEspecieService.GetTodasEspeciesPorClienteForUiGrid(Take, Page, Order, Filters);

    }
}

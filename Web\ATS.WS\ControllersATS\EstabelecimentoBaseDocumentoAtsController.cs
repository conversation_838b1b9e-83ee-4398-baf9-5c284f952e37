﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Response.Documento;

namespace ATS.WS.ControllersATS
{
    public class EstabelecimentoBaseDocumentoAtsController : DefaultController
    {
        private readonly IEstabelecimentoBaseDocumentoApp _estabelecimentoBaseDocumentoApp;

        public EstabelecimentoBaseDocumentoAtsController(IEstabelecimentoBaseDocumentoApp estabelecimentoBaseDocumentoApp)
        {
            _estabelecimentoBaseDocumentoApp = estabelecimentoBaseDocumentoApp;
        }
        
        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaDocumentos(List<int> idsEmpresa, List<int> idsDocumentoIgnorar)
        {
            try
            {
                var documentoCredenciamentoResponseList = _estabelecimentoBaseDocumentoApp.GetAllDocumentoCredenciamento(idsEmpresa, idsDocumentoIgnorar)
                    .Select(x => new ConsultarDocumentoCredenciamentoResponse{
                        IdDocumento = x.IdDocumento,
                        Descricao = x.Descricao,
                        Obrigatorio = x.ObrigaDocOriginal,
                        PermiteEditarData = x.PossuirValidade,
                        DiasValidade = x.DiasValidade
                    }).ToList();

                foreach (var doc in documentoCredenciamentoResponseList)
                    if(!doc.PermiteEditarData)
                        doc.DataValidade = DateTime.Now.AddDays(Convert.ToDouble(doc.DiasValidade)).ToString("dd/MM/yyyy");
                
                return ResponderSucesso(documentoCredenciamentoResponseList);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            } 
        }
        
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Download(string token)
        {
            try
            {
                object documento = _estabelecimentoBaseDocumentoApp.Download(token);

                return ResponderSucesso(documento);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Upload(string dataBase64, string fileName)
        {
            try
            {
                var token = _estabelecimentoBaseDocumentoApp.Upload(dataBase64, fileName);

                return ResponderSucesso(new
                {
                    Token = token
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
    }
}
﻿using System.Configuration;
using ATS.CrossCutting.IoC.Utils;
using RestSharp.Extensions;
using Sistema.Framework.Util.Extension;

namespace ATS.Data.Repository.External.SistemaInfo
{

    public static class SistemaInfoConsts
    {
        public static readonly string TokenAdministradora = ConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];

        public static readonly int? ProdutoFretePadraoAdministradora =
            ConvertUtils.ToIntNullable(ConfigurationManager.AppSettings["MS_PRODUTO_FRETE_PADRAO_ADMINISTRADORA"]);

        private static readonly string BaseUrl = ConfigurationManager.AppSettings["MS_URL"].SetEndChars("/");
        private static readonly string CartaoBaseUrl = ConfigurationManager.AppSettings["MS_URL.Cartao"].DefaultIfNullOrWhiteSpace(BaseUrl).SetEndChars("/");


        public static readonly string UrlCiotConsultaTransportador = ConfigurationManager.AppSettings["MS_URL_CIOT_CONSULTA_TRANSPORTADOR"];
        public static readonly string AuthApiUrl = BaseUrl + "Auth/Api";
        public static readonly string CartaoApiUrl = CartaoBaseUrl + "Cartoes/Api";
        public static readonly string CartaoWebUrl = CartaoBaseUrl + "Cartoes/Web";

        //TODO: remover esta tag quando a versão da antt de homologação for igual a de produção
        private static readonly string BaseCiotUrl = ConfigurationManager.AppSettings["MS_URL_CIOT"];
        //Tag criada para teste de consulta de sequencia VPO
        private static readonly string BasePedagioUrl = ConfigurationManager.AppSettings["MS_URL_PEDAGIO"];

        public static readonly string CiotApiUrl = BaseCiotUrl != null
            ? BaseCiotUrl.SetEndChars("/") + "Ciot/Api"
            : BaseUrl + "Ciot/Api";
        //public static readonly string CiotApiUrl = "http://localhost:50500/Ciot/Api/";

        public static readonly string InfraApiUrl = BaseUrl + "Infra/Api";

        public static readonly string PedagioApiUrl = BaseUrl + "Pedagios/Api";
        // public static readonly string PedagioApiUrl = "http://localhost:50600/Pedagios/Api/";
        public static readonly string PedagioWebUrl = BaseUrl + "Pedagios/Web";

        public static readonly string CadastroApiUrl = BaseUrl + "Cadastros/Api";

        public const string TipoPessoaFisica = "F";
        public const string TipoPessoaJuridica = "J";

        public const string Masculino = "M";
        public const string Feminino = "F";

        #region Identificadores do log gerado para a requisição atual

        /// <summary>
        /// Chave de identificação do id do log gerado para a requisição atual (HttpContext.Current.Items["key"])
        /// </summary>
        public const string CurrentLogIdSessionKey = "sie-bus-log-current-id";

        /// <summary>
        /// Chave de identificação do nível atual do log gerado para a requisição atual (HttpContext.Current.Items["key"])
        /// </summary>
        public const string CurrentLogNivelSessionKey = "sie-bus-log-current-nivel";

        #endregion

        #region Transmitir log atual para outro serviço da Sistema Info vincular seu processo a este

        public const string RestParentLogIdHeader = "bus-log-parent-id";
        public const string RestRootLogIdHeader = "bus-log-root-id";
        public const string RestCurrentLogNivelHeader = "bus-log-current-nivel";

        #endregion

        public static readonly bool AutoApplyMigrations = ConfigurationManager.AppSettings["Migrations:AutoApply"].ToBoolSafe(false);
    }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IConjuntoEmpresaService
    {
        ConjuntoEmpresa Get(int id);
        ValidationResult Add(ConjuntoEmpresa entity);
        ValidationResult Update(ConjuntoEmpresa entity);        
    }
}
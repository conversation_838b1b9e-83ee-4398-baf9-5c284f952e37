﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using TagExtrattaClient;
using EStatusTag = ATS.Domain.Enum.EStatusTag;
using FornecedorEnum = ATS.Domain.Enum.FornecedorEnum;


namespace ATS.Domain.Interface.Service
{
    public interface ITagExtrattaService : IService<DespesasViagem>
    {
        byte[] GerarRelatorioConsultaSituacaoTags(string logo, string extensao, List<RelatorioConsultaSituacaoTagsDataType> relatorio);
        BusinessResult<RemessasGetModelResponse> ConsultaRemessas(int? take, int? page, List<QueryFilters> filters, OrderFilters order);
        BusinessResult<RemessaGetModelResponse> GetRemessa(int id);
        BusinessResult<List<TagGetModelResponse>> GetLote(long min,long max,EStatusTag? statusTag);
        BusinessResult EnviarRemessa(RemessaCadastrarModelRequest request);
        BusinessResult<TagGetSerialModelResponse> GetTagSerial(long serial,EStatusTag? statusTag);
        BusinessResult ReceberRemessa(int idRemessa);
        BusinessResult<TagsGetModelResponse> GetTags(int? take, int? page, List<QueryFilters> filters, OrderFilters order,List<long> tagsSelecionadas = null, DateTime? dataInicio = null, DateTime? dataFim  = null);
        BusinessResult<VeiculoGetModelResponse> GetVeiculo(string placa);
        BusinessResult<ModelosVeiculoMoveMaisGetModelResponse> GetModelosMoveMais(GetModelosVeiculoMoveMaisRequest request);    
        BusinessResult<string> Vincular(TagVincularModelRequest request, long serial, string placa);
        BusinessResult<string> Bloquear(long serial);
        BusinessResult<string> Desbloquear(long serial);
        BusinessResult<string> Desvincular(long serial);
        BusinessResult<BloqueioGetModelResponse> GetBloqueios(int usuarioId);
        BusinessResult<string> CadastrarBloqueios(BloqueioCadastrarModelRequest request);
        BusinessResult<ConsultarGridValePedagioHubModelResponse> GridValePedagioHub(int? take, int? page, List<QueryFilters> filters, OrderFilters order,DateTime? dataInicio,DateTime? dataFim);
        byte[] GerarRelatorioPracasPedagioMoveMais(PracasPedagioResponseDTO data, string extensao, int? idEmpresa);
        byte[] GerarRelatorioGridValePedagioHub(List<GridValePedagioHubItemResponse> items, string extensao, int? idEmpresa);
        BusinessResult<string> CadastrarModelosMoveMais();
        BusinessResult<string> CadastrarEstoqueTags();
        BusinessResult<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest request);
        BusinessResult<PagamentosGetModelResponse> GetPagamentos(int? take, int? page, List<QueryFilters> filters, OrderFilters order, DateTime? dataInicio, DateTime? dataFim, int? idEmpresa,FornecedorEnum fornecedor);
        byte[] GerarRelatorioGridPagamentos(List<PagamentosItemTagResponse> items, string extensao);
        BusinessResult<PagamentosItemGetModelResponse> GetPagamento(long id);
        BusinessResult<string> PagamentoManualEventoTag(PagamentoManualRequest request);
        BusinessResult<string> EstornoManualEventoTag(PagamentoManualRequest request);
        BusinessResult<PassagensPracaGetModelResponse> GetPassagensPraca(int? take, int? page, List<QueryFilters> filters, OrderFilters order, DateTime? dataInicio, DateTime? dataFim,int? empresaId);
        byte[] GerarRelatorioGridPassagemWebhook(List<GridPassagemWebhookItemResponse> items, string extensao);
        BusinessResult<string> DesvincularEmpresa(long serial);
        BusinessResult<FaturamentoGetModelResponse> ConsultaFaturamento(int? take, int? page, List<QueryFilters> filters, OrderFilters order, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor);
        BusinessResult<FaturamentoTotalizadorGetModelResponse> ConsultaTotalizadorFaturamento(FaturamentoTotalizadorGetRequest request);
        byte[] GerarRelatorioGridFaturamento(List<FaturamentoTagItemResponse> items,FaturamentoTotalizadorResponse total,string extensao);
        BusinessResult<FaturaGetModelResponse> ConsultaFatura(DateTime dataInicio, DateTime dataFim, int empresaId, FornecedorTagEnum fornecedorTag);
        byte[] GerarRelatorioFatura(FaturaTagGetResponse request, FornecedorTagEnum fornecedorTag);
        BusinessResult<PlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorEnum fornecedor);
        BusinessResult<PassagensPedagioCompraHubGetModelResponse> GetPassagensPedagioCompraHub(int compraId, FornecedorTagEnum fornecedor);
        BusinessResult<SaldoValePedagioVeiculoTagResponse> ConsultarSaldoValePedagioVeiculo(string placa);
        BusinessResult<PassagensPracaVeiculoGetModelResponse> GetPassagensVeiculoPraca(DateTime? dataInicio,DateTime? dataFim, string placa);
        BusinessResult<string> ContestarPassagem(ContestacaoPassagemPedagioModelRequest request);
    }
} 
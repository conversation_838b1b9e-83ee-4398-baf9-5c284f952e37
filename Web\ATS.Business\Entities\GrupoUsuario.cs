using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class GrupoUsuario
    {
        /// <summary>
        /// Código do Grupo de Usuário
        /// </summary>
        public int IdGrupoUsuario { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Descrição
        /// </summary>
        public string Descricao { get; set; }

        /// <summary>
        /// Informa se esta ativado
        /// </summary>
        public bool Ativo { get; set; } = true;
        
        public int? IdEstabelecimentoBase { get; set; }

        #region Referências

        /// <summary>
        /// Empresa vinculados ao grupo de usuário
        /// </summary>
        public virtual Empresa Empresa { get; set; }
        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }

        #endregion

        #region Navegação Inversa

        /// <summary>
        /// Usuários vinculados ao grupo de usuário
        /// </summary>
        public virtual ICollection<Usuario> Usuarios { get; set; }
        public virtual ICollection<NotificacaoPush> NotificacoesPush { get; set; }
        public virtual ICollection<NotificacaoPushGrupoUsuario> NotificacaoPushGrupoUsuario { get; set; }
        public virtual ICollection<Layout> LayoutMotorista { get; set; }
        public virtual ICollection<Layout> LayoutProprietario { get; set; }
        
        #endregion

        #region Tabelas Filhas

        /// <summary>
        /// Menus do grupo de usuário
        /// </summary>
        public virtual ICollection<GrupoUsuarioMenu> Menus { get; set; }

        #endregion
    }
}
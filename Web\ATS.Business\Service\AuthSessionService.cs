﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Linq;
using System;
using System.Threading.Tasks;

namespace ATS.Domain.Service
{
    public class AuthSessionService : ServiceBase, IAuthSessionService
    {
        private readonly IAuthSessionRepository _authSessionRepository;

        public AuthSessionService(IAuthSessionRepository authSessionRepository)
        {
            _authSessionRepository = authSessionRepository;
        }

        public AuthSession GetByToken(string Token)
        {
            return _authSessionRepository.GetByToken(Token);
        }

        public int GetIdUsuario(string Token)
        {
            return _authSessionRepository.GetIdUsuario(Token);
        }

        public AuthSession Gerar(int idUsuario)
        {
            return _authSessionRepository.Gerar(idUsuario);
        }

        public void AtualizaDataUltimaRequisicao(string token)
        {
            _authSessionRepository.AtualizaDataUltimaRequisicao(token);
        }

        public void InvalidarSessao(string sessionKey)
        {
            _authSessionRepository.InvalidarSessao(sessionKey);
        }

        public void InvalidarSessoesAtivasParaUsuario(int idUsuario)
        {
            _authSessionRepository.InvalidarSessoesAtivasParaUsuario(idUsuario);
        }

        public int GetTotalRequisicoes()
        {
            //var totalReq = _authSessionRepository
            //    .AsNoTracking()
            //    .Select(x => x.TotalReq);

            //if (totalReq == null || !totalReq.Any())
            //    return 0;
            //return totalReq.Sum();
            return 0;
        }
    }
}

using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemPendenteGestorMap : EntityTypeConfiguration<ViagemPendenteGestor>
    {

        public ViagemPendenteGestorMap()
        {
            ToTable("VIAGEM_PENDENTE_GESTOR");

            HasKey(t => new { t.IdViagem, t.IdEmpresa, t.IdBloqueioGestorTipo });
            //HasKey(t => new { t.IdBloqueioGestorTipo });

            Property(u => u.DataCadastro).IsRequired();

            Property(u => u.IdFilial).IsOptional();

            Property(u => u.DataStatus).IsOptional();

            Property(u => u.Ocorrencia).HasMaxLength(300).IsOptional();

            Property(u => u.Motivo).IsOptional();

            Property(u => u.Status).IsRequired();

            Property(u => u.IdUsuarioDesbloqueio).IsOptional();

            HasOptional(u => u.UsuarioDesbloqueio)
                .WithMany()
                .HasForeignKey(u => u.IdUsuarioDesbloqueio);

            HasRequired(u => u.BloqueioGestorTipo)
                .WithMany()
                .HasForeignKey(u => u.IdBloqueioGestorTipo);

            HasRequired(u => u.Viagem)
                .WithMany()
                .HasForeignKey(u => new { u.IdViagem, u.IdEmpresa });
        }
    }
}
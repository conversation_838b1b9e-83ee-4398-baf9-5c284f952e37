using System.Collections.Generic;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2CadastrosPreViagemModel
    {
        /// <summary>
        /// Dados para integração do cliente de origem
        /// </summary>
        public ClienteRequestModel ClienteOrigem { get; set; }

        /// <summary>
        /// Dados para integração do cliente de destino
        /// </summary>
        public ClienteRequestModel ClienteDestino { get; set; }

        /// <summary>
        /// Dados para a integração de proprietário
        /// </summary>
        public ProprietarioIntegrarRequestModel Proprietario { get; set; }

        /// <summary>
        /// Dados para integração de motorista
        /// </summary>
        public MotoristaIntegrarRequestModel Motorista { get; set; }

        /// <summary>
        /// Dados para integração de veículo
        /// </summary>
        public VeiculoIntegrarRequestModel Veiculo { get; set; }
        
        /// <summary>
        /// Carretas que referenciam a viagem
        /// </summary>
        public List<ViagemCarretasV2IntegrarModel> Carretas { get; set; }
    }
}
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioPermissoesConcedidasMobileMap : EntityTypeConfiguration<UsuarioPermissoesConcedidasMobile>
    {
        public UsuarioPermissoesConcedidasMobileMap()
        {
            ToTable("USUARIO_PERMISSOES_CONCEDIDAS_MOBILE");

            HasKey(o => o.IdUsuarioPermissaoConcediadMobile);

            HasRequired(o => o.Usuario)
                .WithMany(o => o.PermissoesConcedidasMobile)
                .HasForeignKey(o => o.IdUsuario);

            // Calendário
            Property(o => o.PermiteLerCalendario).IsRequired();
            Property(o => o.PermiteEscreverCalendario).IsRequired();
            
            // Câmera
            Property(o => o.PermiteUsarCamera).IsRequired();
            
            // Contatos
            Property(o => o.PermiteLerContatos).IsRequired();
            Property(o => o.PermiteEscreverContatos).IsRequired();
            Property(o => o.PermiteBuscarContatos).IsRequired();
            
            // Localização
            Property(o => o.PermiteAcessoLocalizacao).IsRequired();
            
            // Microfone
            Property(o => o.PermiteGravarAudio).IsRequired();
            
            // Telefone
            Property(o => o.PermiteLerEstadoTelefone).IsRequired();
            Property(o => o.PermiteRealizarChamada).IsRequired();
            Property(o => o.PermiteLerLogChamada).IsRequired();
            Property(o => o.PermiteEscreverLogChamada).IsRequired();
            Property(o => o.PermiteAdicionarCorreioVoz).IsRequired();
            Property(o => o.PermiteUsarSip).IsRequired();
            Property(o => o.PermiteProcessarChamadaSaida).IsRequired();
            
            // Sensores
            Property(o => o.PermiteUsarSensoresComporais).IsRequired();
            
            // SMS
            Property(o => o.PermiteEnviarSms).IsRequired();
            Property(o => o.PermiteReceberSms).IsRequired();
            Property(o => o.PermiteLerSms).IsRequired();
            Property(o => o.PermiteReceberWapPush).IsRequired();
            Property(o => o.PermiteReceberMms).IsRequired();
            
            // Storage
            Property(o => o.PermiteLerArmazenamentoExterno).IsRequired();
            Property(o => o.PermiteEscreverArmazenamentoExterno).IsRequired();

            Property(o => o.DataAtualizacao).IsRequired();
        }
    }
}
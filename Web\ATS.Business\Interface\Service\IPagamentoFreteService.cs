﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Models.PagamentoFrete;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.Interface.Service
{
    public interface IPagamentoFreteService
    {
        decimal GetNumeroDeSacas(decimal? pesoChegada, decimal? pesoSaida, decimal? quantidade);
        PagamentoFreteModel ConsultarPorToken(string token, string cpfCnpjUsuario, string nomeUsuario, List<int> idsEstabelecimentosBaseUsuario = null, int? idEstabelecimento = null);
        CheckTokenModel CheckToken(string token);
        void AtualizarPagamentoSemChave(bool liberarPagamento, string token, int? estabId, string observacao, int idUsuarioLibSemChave);

        /// <summary>
        /// Realiza a consulta de um evento da viagem por seu token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        ViagemEvento ConsultarViagemEvento(string token);

        bool hasCredentials(PagamentoFreteModel pagamentoFreteModel, List<int> idsEstabelecimentosUsuario, Usuario usuarioLogado);

        ValidationResult EfetuarBaixaEvento(PagamentoFreteModel pagamentoFreteModel,
            List<int> idsEstabelecimentosUsuario, bool integracaoAts, Usuario usuarioLogado, Viagem viagem);

        ValidationResult ConsultaCartoesPagamentoEvento(ICartoesService cartoesService, Usuario usuarioLogado, string cpfCnpjProprietario, string cpfMotorista,
            out CartaoVinculadoPessoaListResponse cartaoProprietario, out CartaoVinculadoPessoaListResponse cartaoMotorista);

        BaixarEventoSaldoResuLt PagamentoCartao(ICartoesService cartoesService, CartaoVinculadoPessoaListResponse cartaoProprietario, CartaoVinculadoPessoaListResponse cartaoMotorista, 
            Viagem viagem, ViagemEvento viagemEvento);

        ValidationResult CreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, IUserIdentity usuarioLogado, decimal? valor);
        ValidationResult NaoCreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, int? idMotivo, string descricao = null, int? idUsuario = null );
        ValidationResult ValidarCredenciamento(Usuario usuarioLogado, int? idEstabelecimento, int administradoraPlataforma);
        ValidationResult AlterarSaldo(decimal saldo, decimal pesoChegada, decimal quebraMercadoria, decimal difFreteMotorista, string token);
        byte[] GerarReciboViagem(string token, int? idViagemEvento);
        byte[] GerarReciboProtocolo(string token, int? idViagemEvento, string usuario);
        byte[] ImageToByteArray(Image imageIn);
        PagamentoFreteEventoModel CalcularValoresViagem(string token, bool habilitaPagamentoCartao, decimal? pesoChegada, int? numeroSacas, EUnidadeMedida unidadeInformada, bool? quebraAbonada = null);
        PagamentoFreteEventoModel CalcularValoresProtocolo(string token, decimal? pesoChegada, int? numeroSacas);
        PagamentoFreteEventoModel ConsultarEvento(string token);
        List<PagamentoFreteValorAdicionalModel> ConsultarOutrosDescontos(string token);
        List<PagamentoFreteValorAdicionalModel> ConsultarOutrosAcrescimos(string token);
        List<PagamentoFreteAnexoModel> ConsultarAnexos(string token, bool? estabelecimentoObrigaDocumentosPagamento = null, int? idUsuarioLogado = null);
        void VincularAnexoMidiaAoViagemDocumento(string tokenMidia, int idViagemDocumento);
        bool RemoverAnexo(string token_);
        void RemoverAnexo(int idViagemDocumento);
        bool EventoDocumentosObrigatoriosAnexados(ViagemEvento vgEvent, Viagem vg, List<Estabelecimento> estabelecimentos, int? idUsuarioLogado= null);
        List<Protocolo> ConsultarProtocolosProcessados(int idEmpresa, int? idProtocolo, bool includeEstabelecimentoBase = false);
        List<Protocolo> ConsultarProtocolosPorEstabelecimentos(List<int> idsEstabelecimentoBase, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, int? idProtocolo);
        ViagemEvento AbonarViagemEvento(ViagemEvento viagemEvento_, string token_);
        ViagemSolicitacaoAbono SolicitarAbono(int IdViagemEvento, int? IdMotivo, string Detalhamento, int IdUsuario);
        void CancelarSolicitacao(string token);
        object ConsultarTotalPagamentosCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int page_, int take_, int idempresa);
        byte[] GerarRelatorioGrid(DateTime dataInicial, DateTime dataFinal, string uf, double? a, double? b, double? c, string tipoArquivo, string logo, int idempresa);
        object ConsultarGraficosSumPorEstabelecimento(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int idempresa);
        object ConsultarPagamentosEstabelecimentoGrid(DateTime dataInicio_, DateTime dataFim_, int idEstabelecimentoBase, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] ConsultarPagamentosEstabelecimentoReport(DateTime? dataInicio, DateTime? dataFim, int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
        IQueryable<ViagemEvento> ConsultarPagamentosEstabelecimentoGridAndReport(DateTime? dataInicio_, DateTime? dataFim_, int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters);

        object ConsultarGridDetalhesAberto(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);

        byte[] GerarRelatorioDetalhesProvisaoAberto(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa);

        object ConsultarGridDetalhesBaixado(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);

        byte[] GerarRelatorioDetalhesProvisaoBaixado(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa);

        IQueryable<ViagemEvento> ConsultarGridDetalhesAbertoToGridAndReport(DateTime dataInicio_,
            DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase,
            ETipoEventoViagem? TipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);

        IQueryable<ViagemEvento> ConsultarGridDetalhesBaixadoToGridAndReport(DateTime dataInicio_,
            DateTime dataFim_, bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase,
            ETipoEventoViagem? TipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa);

        object GetTotaisPagamentosPorTipo(DateTime dataInicio, DateTime dataFinal, int? UF_,
            EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimento, int? idEmpresa);

        /// <summary>
        /// Verificar se o evento indicado deve possuir o valor do pedágio somando para apresentar ao operador do posto.
        /// Somente adiantamento, no modelo de pgto Carta Frete e sem a informação de pedágio baixado deve ser incluso.
        /// </summary>
        /// <param name="eventoAdiantamento"></param>
        /// <param name="viagem"></param>
        /// <returns></returns>
        bool DeveIncluirPedagioJuntoComPagamentoDoEvento(ViagemEvento eventoAdiantamento, Viagem viagem);

        List<ViagemEventoByCiotModel> GetByCiot(string numeroCiot);
        ValidationResult ValidarChavePagamento(int empresaId, string documento, string chave, string tokenPagamento);
        ValidationResult SetMotivoRejeicaoAbono(int IdViagemEvento, int? IdMotivo, string detalhamentoMotivo, int? idUsuario);
        ValidationResult NotificarRejeicaoAbono(int idViagemEvento, int idProtocolo);
        object ConsultarMotivoAbono(int IdViagemEvento);
        List<BancosFebrabanModel> GetBancosFebraban();
        ValidationResult DeletarEventoAbono(int idViagem);
        byte[] GerarRelatorioFaturamento(List<FaturamentoGridDto> data,string extensao,int? empresaFiltro);
        public IList<FaturamentoGridDto> RelatorioFaturamento(DateTime dataInicial, DateTime datafinal, int? empresaFiltro);
    }
}
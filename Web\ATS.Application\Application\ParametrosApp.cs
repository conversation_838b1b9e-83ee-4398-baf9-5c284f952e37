using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Validation;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.AtendimentoPortador;

namespace ATS.Application.Application
{
    public class ParametrosApp : BaseApp<IParametrosService>, IParametrosApp
    {
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradoraPlataformaService;
        private readonly IParametrosEstabelecimentoService _parametrosEstabelecimentoService;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;

        public ParametrosApp(IParametrosService service, IParametrosEmpresaService parametrosEmpresaService, IParametrosUsuarioService parametrosUsuarioService,
            IParametrosAdministradoraPlataformaService parametrosAdministradoraPlataformaService, IParametrosEstabelecimentoService parametrosEstabelecimentoService, IParametrosGenericoService parametrosGenericoService, IParametrosProprietarioService parametrosProprietarioService) : base(service)
        {
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosUsuarioService = parametrosUsuarioService;
            _parametrosAdministradoraPlataformaService = parametrosAdministradoraPlataformaService;
            _parametrosEstabelecimentoService = parametrosEstabelecimentoService;
            _parametrosGenericoService = parametrosGenericoService;
            _parametrosProprietarioService = parametrosProprietarioService;
        }

        public ValidationResult SetPercentualTransferenciaFreteGenerico(PercentualTransferenciaFreteViagemParametro percentuais)
        {
            return Service.SetPercentualTransferenciaFreteGenerico(percentuais);
        }

        public ValidationResult SetIdTipoEstabelecimentoPadraoJSL(int idTipoEstabelecimento)
        {
            return Service.SetIdTipoEstabelecimentoPadraoJSL(idTipoEstabelecimento);
        }

        public ValidationResult SetPercentualTransferenciaFreteProprietario(PercentualTransferenciaFreteViagemParametro percentuais, string documento)
        {
            return Service.SetPercentualTransferenciaFreteProprietario(percentuais, documento);
        }

        public ValidationResult SetAutorizaEstabelecimentosRedeJSL(bool valor, int idEmpresa)
        {
            return Service.SetAutorizaEstabelecimentosRedeJSL(valor, idEmpresa);
        }

        public ValidationResult SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO parametro, decimal? valor, string documento)
        {
            return Service.SetParametroProprietarioTransferencia(parametro, valor, documento);
        }

        public ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(AcaoSaldoResidualNovoCreditoCartaoPedagio percentuais, string documento)
        {
            return Service.SetAcaoSaldoResidualNovoCreditoCartaoPedagio(percentuais, documento);
        }

        public bool GetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa)
        {
            return _parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(documento, idEmpresa);
        }

        public ValidationResult SetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa, bool valor)
        {
            return _parametrosProprietarioService.SetProprietarioPermiteReceberPagamentoPix(documento, idEmpresa, valor);
        }
        public ValidationResult SetValorLimitePagamentoCheque(decimal? valor, int idEmpresa)
        {
            return Service.SetValorLimitePagamentoCheque(valor, idEmpresa);
        }

        public ValidationResult SetSeriePadraoCheque(string valor, int idEmpresa)
        {
            return Service.SetSeriePadraoCheque(valor, idEmpresa);
        }

        public ValidationResult SetBancoPadraoCheque(string valor, int idEmpresa)
        {
            return Service.SetBancoPadraoCheque(valor, idEmpresa);
        }

        public ValidationResult SetCancelarViagemComSaldoMenorQueValorDoExtorno(bool valor, int idEmpresa)
        {
            return Service.SetCancelarViagemComSaldoMenorQueValorDoExtorno(valor, idEmpresa);
        }

        public ValidationResult SetQuantidadeLimiteImpressoesCheque(decimal? valor, int idEmpresa)
        {
            return Service.SetQuantidadeLimiteImpressoesCheque(valor, idEmpresa);
        }

        public ValidationResult SetMarginTopImpressaoCheque(decimal? valor, int idEmpresa)
        {
            return Service.SetMarginTopImpressaoCheque(valor, idEmpresa);
        }

        public ValidationResult SetMarginLeftImpressaoCheque(decimal? valor, int idEmpresa)
        {
            return Service.SetMarginLeftImpressaoCheque(valor, idEmpresa);
        }

        public ValidationResult SetEndpointIntegracaoCheque(string valor, int idEmpresa)
        {
            return Service.SetEndpointIntegracaoCheque(valor, idEmpresa);
        }

        public ValidationResult SetHeadersIntegracaoCheque(string valor, int idEmpresa)
        {
            return Service.SetHeadersIntegracaoCheque(valor, idEmpresa);
        }

        public ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(AcaoSaldoResidualNovoCreditoCartaoPedagio valor, int idEmpresa)
        {
            return Service.SetAcaoSaldoResidualNovoCreditoCartaoPedagio(valor, idEmpresa);
        }

        public ValidationResult SetPermissaoUsuarioAlterarLimiteAlcadas(PermissaoUsuarioAlterarLimiteAlcadas valor, int idusuario)
        {
            return Service.SetPermissaoUsuarioAlterarLimiteAlcadas(valor, idusuario);
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(string documento)
        {
            return Service.GetAcaoSaldoResidualNovoCreditoCartaoPedagio(documento);
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(int idEmpresa)
        {
            return Service.GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(idEmpresa);
        }

        public PermissaoUsuarioAlterarLimiteAlcadas GetPermissaoUsuarioAlterarLimiteAlcadas(int idusuario)
        {
            return Service.GetPermissaoUsuarioAlterarLimiteAlcadas(idusuario);
        }

        public decimal? GetValorLimitePagamentoCheque(int idEmpresa)
        {
            return Service.GetValorLimitePagamentoCheque(idEmpresa);
        }

        public string GetSeriePadraoCheque(int idEmpresa)
        {
            return Service.GetSeriePadraoCheque(idEmpresa);
        }

        public string GetBancoPadraoCheque(int idEmpresa)
        {
            return Service.GetBancoPadraoCheque(idEmpresa);
        }

        public bool GetCancelarViagemComSaldoMenorQueValorDoExtorno(int idEmpresa)
        {
            return Service.GetCancelarViagemComSaldoMenorQueValorDoExtorno(idEmpresa);
        }

        public decimal? GetQuantidadeLimiteImpressoesCheque(int idEmpresa)
        {
            return Service.GetQuantidadeLimiteImpressoesCheque(idEmpresa);
        }

        public decimal? GetMarginTopImpressaoCheque(int idEmpresa)
        {
            return Service.GetMarginTopImpressaoCheque(idEmpresa);
        }

        public decimal? GetMarginLeftImpressaoCheque(int idEmpresa)
        {
            return Service.GetMarginLeftImpressaoCheque(idEmpresa);
        }

        public string GetEndpointIntegracaoCheque(int idEmpresa)
        {
            return Service.GetEndpointIntegracaoCheque(idEmpresa);
        }

        public string GetHeadersIntegracaoCheque(int idEmpresa)
        {
            return Service.GetHeadersIntegracaoCheque(idEmpresa);
        }

        public PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteGenerico()
        {
            return Service.GetPercentualTransferenciaFreteGenerico();
        }

        public int GetIdTipoEstabelecimentoPadraoJSL()
        {
            return Service.GetIdTipoEstabelecimentoPadraoJSL();
        }

        public int GetIdEmpresaPadraoUsuarioEstabelecimentoJSL()
        {
            return Service.GetIdEmpresaPadraoUsuarioEstabelecimentoJSL();
        }

        public PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietario(string documento)
        {
            return Service.GetPercentualTransferenciaFreteProprietario(documento);
        }

        public ConsultarParametroCartaoDTO GetPercentualTransferenciaCartaoProprietario(string documento)
        {
            var percentuais = GetPercentualTransferenciaFreteProprietario(documento);
            var percentuaisGenericos = GetPercentualTransferenciaFreteGenerico();

            if (!percentuais.Adiantamento.HasValue)
                percentuais.Adiantamento = percentuaisGenericos.Adiantamento ?? 0;

            if (!percentuais.Abastecimento.HasValue)
                percentuais.Abastecimento = percentuaisGenericos.Abastecimento ?? 0;

            if (!percentuais.Abono.HasValue)
                percentuais.Abono = percentuaisGenericos.Abono ?? 0;

            if (!percentuais.Estadia.HasValue)
                percentuais.Estadia = percentuaisGenericos.Estadia ?? 0;

            if (!percentuais.Saldo.HasValue)
                percentuais.Saldo = percentuaisGenericos.Saldo ?? 0;

            if (!percentuais.TarifaAntt.HasValue)
                percentuais.TarifaAntt = percentuaisGenericos.TarifaAntt ?? 0;

            if (!percentuais.RPA.HasValue)
                percentuais.RPA = percentuaisGenericos.RPA ?? 0;

            var retorno = new ConsultarParametroCartaoDTO
            {
                PercentuaisTransferenciaMotorista = new List<PercentualTransferenciaMotorista>()
            };

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Adiantamento),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento.GetDescription(),
                Valor = percentuais.Adiantamento.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Saldo),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo.GetDescription(),
                Valor = percentuais.Saldo.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Estadia),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia.GetDescription(),
                Valor = percentuais.Estadia.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.RPA),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA.GetDescription(),
                Valor = percentuais.RPA.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Abastecimento),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento.GetDescription(),
                Valor = percentuais.Abastecimento.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.TarifaAntt),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT.GetDescription(),
                Valor = percentuais.TarifaAntt.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Abono),
                Texto = PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono.GetDescription(),
                Valor = percentuais.Abono.Value
            });

            return retorno;
        }

        public ConsultarParametroCartaoDTO GetPercentualTransferenciaCartaoGenerico()
        {
            var percentuais = GetPercentualTransferenciaFreteGenerico();

            if (!percentuais.Adiantamento.HasValue)
                percentuais.Adiantamento = 0;

            if (!percentuais.Abastecimento.HasValue)
                percentuais.Abastecimento = 0;

            if (!percentuais.Abono.HasValue)
                percentuais.Abono = 0;

            if (!percentuais.Estadia.HasValue)
                percentuais.Estadia = 0;

            if (!percentuais.Saldo.HasValue)
                percentuais.Saldo = 0;

            if (!percentuais.TarifaAntt.HasValue)
                percentuais.TarifaAntt = 0;

            if (!percentuais.RPA.HasValue)
                percentuais.RPA = 0;

            var retorno = new ConsultarParametroCartaoDTO
            {
                PercentuaisTransferenciaMotorista = new List<PercentualTransferenciaMotorista>()
            };

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Adiantamento),
                Texto = GLOBAL.PercTransferenciaAdiantamento.GetDescription(),
                Valor = percentuais.Adiantamento.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Saldo),
                Texto = GLOBAL.PercTransferenciaSaldo.GetDescription(),
                Valor = percentuais.Saldo.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Estadia),
                Texto = GLOBAL.PercTransferenciaEstadia.GetDescription(),
                Valor = percentuais.Estadia.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.RPA),
                Texto = GLOBAL.PercTransferenciaRPA.GetDescription(),
                Valor = percentuais.RPA.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Abastecimento),
                Texto = GLOBAL.PercTransferenciaAbastecimento.GetDescription(),
                Valor = percentuais.Abastecimento.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.TarifaAntt),
                Texto = GLOBAL.PercTransferenciaTarifaANTT.GetDescription(),
                Valor = percentuais.TarifaAntt.Value
            });

            retorno.PercentuaisTransferenciaMotorista.Add(new PercentualTransferenciaMotorista
            {
                Chave = nameof(percentuais.Abono),
                Texto = GLOBAL.PercTransferenciaAbono.GetDescription(),
                Valor = percentuais.Abono.Value
            });

            return retorno;
        }

        public bool GetAutorizaEstabelecimentosRedeJSL(int idEmpresa)
        {
            return Service.GetAutorizaEstabelecimentosRedeJSL(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeArmazemEmpresa(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadeArmazemEmpresa(valor, idEmpresa);
        }

        public ValidationResult SetSeparadorArquivoCsv(string valor, int idEmpresa)
        {
            return Service.SetSeparadorArquivoCsv(valor, idEmpresa);
        }

        public bool? HasTransferenciaFrete(IList<ETipoEventoViagem> tiposEvento, string proprietarioDocumento, int idempresa, string motoristaDocumento)
        {
            return Service.HasTransferenciaFrete(tiposEvento, proprietarioDocumento, idempresa, motoristaDocumento);
        }

        public ValidationResult SetMostrarHeaderArquivoCsv(bool? valor, int idEmpresa)
        {
            return Service.SetMostrarHeaderArquivoCsv(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadeArmazemEmpresa(int idEmpresa)
        {
            return Service.GetObrigatoriedadeArmazemEmpresa(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeOrdemCompraEmpresa(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadeOrdemCompraEmpresa(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadeOrdemCompraEmpresa(int idEmpresa)
        {
            return Service.GetObrigatoriedadeOrdemCompraEmpresa(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeFormulaEmpresa(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadeFormulaEmpresa(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadeFormulaEmpresa(int idEmpresa)
        {
            return Service.GetObrigatoriedadeFormulaEmpresa(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadePedidoEmpresa(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadePedidoEmpresa(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadePedidoEmpresa(int idEmpresa)
        {
            return Service.GetObrigatoriedadePedidoEmpresa(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeProtocolo(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadeProtocolo(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadeProtocolo(int idEmpresa)
        {
            return Service.GetObrigatoriedadeProtocolo(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeQuantidadeEmpresa(bool? valor, int idEmpresa)
        {
            return Service.SetObrigatoriedadeQuantidadeEmpresa(valor, idEmpresa);
        }

        public bool? GetObrigatoriedadeQuantidadeEmpresa(int idEmpresa)
        {
            return Service.GetObrigatoriedadeQuantidadeEmpresa(idEmpresa);
        }
        public ValidationResult SetInformacoesTransferenciaBancaria(string valor, int idEmpresa)
        {
            return Service.SetInformacoesTransferenciaBancaria(valor, idEmpresa);
        }
        public string GetInformacoesTransferenciaBancaria(int idEmpresa)
        {
            return Service.GetInformacoesTransferenciaBancaria(idEmpresa);
        }

        public ValidationResult SetObrigatoriedadeArmazemFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadeArmazemFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadeArmazemFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadeArmazemFilial(idEmpresa, idFilial);
        }

        public ValidationResult SetObrigatoriedadeOrdemCompraFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadeOrdemCompraFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadeOrdemCompraFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadeOrdemCompraFilial(idEmpresa, idFilial);
        }

        public ValidationResult SetObrigatoriedadeFormulaFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadeFormulaFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadeFormulaFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadeFormulaFilial(idEmpresa, idFilial);
        }

        public ValidationResult SetObrigatoriedadePedidoFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadePedidoFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadePedidoFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadePedidoFilial(idEmpresa, idFilial);
        }

        public ValidationResult SetObrigatoriedadeProtocoloFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadeProtocoloFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadeProtocoloFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadeProtocoloFilial(idEmpresa, idFilial);
        }

        public ValidationResult SetObrigatoriedadeQuantidadeFilial(bool? valor, int idEmpresa, int idFilial)
        {
            return Service.SetObrigatoriedadeQuantidadeFilial(valor, idEmpresa, idFilial);
        }

        public bool? GetObrigatoriedadeQuantidadeFilial(int idEmpresa, int idFilial)
        {
            return Service.GetObrigatoriedadeQuantidadeFilial(idEmpresa, idFilial);
        }

        public string GetSeparadorArquivoCsv(int idEmpresa)
        {
            return Service.GetSeparadorArquivoCsv(idEmpresa);
        }

        public bool? GetMostrarHeaderArquivoCsv(int idEmpresa)
        {
            return Service.GetMostrarHeaderArquivoCsv(idEmpresa);
        }

        public int? GetIdUsuarioGenericoWS()
        {
            return Service.GetIdUsuarioGenericoWS();
        }

        public int? GetHorasExpiracaoCreditoPedagio(int idEmpresa)
        {
            return Service.GetHorasExpiracaoCreditoPedagio(idEmpresa);
        }
        public ValidationResult SetHorasExpiracaoCreditoPedagio(int horas, int idEmpresa)
        {
            return Service.SetHorasExpiracaoCreditoPedagio(horas, idEmpresa);
        }

        public ValidationResult SetEmailsAlertaCiotAgregado(string emails, int idEmpresa)
        {
            return Service.SetEmailsAlertaCiotAgregado(emails, idEmpresa);
        }

        public ValidationResult SetDiasCancelamentoViagem(int? dias, int idEmpresa)
        {
            return Service.SetDiasCancelamentoViagem(dias, idEmpresa);
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento)
        {
            return Service.GetAcaoSaldoResidualNovoCreditoCartaoPedagio(documento);
    }

        public bool GetObrigaRoteirizacaoPedagioViagemEmpresa(int idEmpresa)
        {
            return Service.GetObrigaRoteirizacaoPedagioViagemEmpresa(idEmpresa);
        }

        public ValidationResult SetObrigaRoteirizacaoPedagioViagemEmpresa(bool valor, int idEmpresa)
        {
            return Service.SetObrigaRoteirizacaoPedagioViagemEmpresa(valor, idEmpresa);
        }

        public string GetTokenAdministradora(int idAdministradora)
        {
            return Service.GetTokenAdministradora(idAdministradora);
        }

        public bool ValidaCnpjCpfProprietarioNaViagem()
        {
            return Service.ValidaCnpjCpfProprietarioNaViagem();
        }

        public int GetIdLayoutCartao()
        {
            return Service.GetIdLayoutCartao();
        }

        public int GetIdProdutoCartaoFrete()
        {
            return Service.GetIdLayoutCartao();
        }

        public ValidationResult SetTokenAdministradora(string valor, string idregistro, int idEmpresa)
        {
            return Service.SetTokenAdministradora(valor, idregistro,idEmpresa);
        }

        public string GetProdutoIdPadrao(int idAdministradora)
        {
            return Service.GetProdutoIdPadrao(idAdministradora);
        }

        public ValidationResult SetProdutoIdPadrao(string valor, string idregistro, int idEmpresa)
        {
            return Service.SetProdutoIdPadrao(valor, idregistro,idEmpresa);
        }

        public bool? GetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa)
        {
            return Service.GetValidarDocumentosViagemComDocumentosDasIntegracoes(idEmpresa);
        }

        public ValidationResult SetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa, bool valor)
        {
            return Service.SetValidarDocumentosViagemComDocumentosDasIntegracoes(idEmpresa, valor);
        }

        public ValidationResult SetReemiteCiotPadraoAlteracaoViagem(bool valor, int idEmpresa)
        {
            return Service.SetReemiteCiotPadraoAlteracaoViagem(valor, idEmpresa);
        }

        public bool? GetReemiteCiotPadraoAlteracaoViagem(int idEmpresa)
        {
            return Service.GetReemiteCiotPadraoAlteracaoViagem(idEmpresa);
        }

        public string GetEmailsAlertaCiotAgregado(int idEmpresa)
        {
            return Service.GetEmailsAlertaCiotAgregado(idEmpresa);
        }

        public int GetDiasCancelamentoViagem(int idEmpresa)
        {
            return Service.GetDiasCancelamentoViagem(idEmpresa);
        }

        public bool? GetPermiteConsultarTodasViagensEmpresa(int idEmpresa)
        {
            return Service.GetPermiteConsultarTodasViagensEmpresa(idEmpresa);
        }
        public int GetTipoCargaAnttDefault()
        {
            return Service.GetTipoCargaAnttDefault();
        }

        public int? GetVersaoAntt()
        {
            return Service.GetVersaoAntt();
        }

        public PermissoesEmpresaAtendimentoPortador GetPermissoesEmpresaAtendimentoCartao(int idEmpresa)
        {
            return _parametrosEmpresaService.GetPermissoesAtendimentoCartao(idEmpresa);
        }

        public ValidationResult SetPermissoesEmpresaAtendimentoCartao(int idEmpresa, PermissoesEmpresaAtendimentoPortador permissoes)
        {
            return _parametrosEmpresaService.SetPermissoesAtendimentoCartao(idEmpresa, permissoes);
        }

        public string GetTokenMicroServicoCentralAtendimento(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTokenMicroServicoCentralAtendimento(idEmpresa);
        }

        public ValidationResult SetTokenMicroServicoCentralAtendimento(int idEmpresa, string token)
        {
            return _parametrosEmpresaService.SetTokenMicroServicoCentralAtendimento(idEmpresa, token);
        }

        public PermissoesUsuarioAtendimentoPortador GetNovoUsuarioPermissoesAtendimentoCartao(int idEmpresa)
        {
            return _parametrosUsuarioService.GetNovoUsuarioPermissoesAtendimentoCartao(idEmpresa);
        }

        public PermissoesUsuarioAtendimentoPortador GetPermissoesUsuarioAtendimentoCartao(int idUsuario, int idEmpresa)
        {
            return _parametrosUsuarioService.GetPermissoesAtendimentoCartao(idUsuario, idEmpresa);
        }

        public ValidationResult SetPermissoesUsuarioAtendimentoCartao(int idUsuario, PermissoesUsuarioAtendimentoPortador permissoes)
        {
            return _parametrosUsuarioService.SetPermissoesAtendimentoCartao(idUsuario, permissoes);
        }

        public bool GetAtendimentoPermiteBloquearCartao(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteBloquearCartao(idUsuario);
        }
        public bool GetAtendimentoPermiteDesbloquearCartao(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteDesbloquearCartao(idUsuario);
        }
        public bool GetAtendimentoPermiteAlterarSenhaCartao(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteAlterarSenhaCartao(idUsuario);
        }
        public bool GetAtendimentoPermiteRealizarTransferenciaBancaria(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteRealizarTransferenciaBancaria(idUsuario);
        }
        public bool GetAtendimentoPermiteRealizarTransferenciaCartoes(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteRealizarTransferenciaCartoes(idUsuario);
        }
        public bool GetAtendimentoPermiteRealizarResgate(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteRealizarResgate(idUsuario);
        }
        public bool GetAtendimentoPermiteRealizarEstornoResgate(int idUsuario)
        {
            return _parametrosUsuarioService.GetAtendimentoPermiteRealizarEstornoResgate(idUsuario);
        }

        public bool GetOcultarListagemTerceiros(int idEmpresa)
        {
            return _parametrosEmpresaService.GetOcultarListagemTerceiros(idEmpresa);
        }

        public bool CadastraSomentePerfilEmpresa(int idEmpresa)
        {
            return _parametrosEmpresaService.CadastraSomentePerfilEmpresa(idEmpresa);
        }

        public string GetCaminhoEmailRecuperacaoSenhaAppPlataforma(int idAdministradoraPlataforma)
        {
            return _parametrosAdministradoraPlataformaService.GetCaminhoEmailRecuperacaoSenhaApp(idAdministradoraPlataforma);
        }

        public string GetCaminhoLogo(int idAdministradoraPlataforma)
        {
            return _parametrosAdministradoraPlataformaService.GetCaminhoLogo(idAdministradoraPlataforma);
        }

        public ConfiguracaoEnvioEmail GetConfiguracaoEmailPlataforma(int idAdministradoraPlataforma)
        {
            return _parametrosAdministradoraPlataformaService.GetConfiguracaoEmail(idAdministradoraPlataforma);
        }

        public string GetKeyEnvioPush(int idAdministradoraPlataforma)
        {
            return _parametrosAdministradoraPlataformaService.GetKeyEnvioPush(idAdministradoraPlataforma);
        }

        public int GetIdProjetoFireBase(int idusuario)
        {
            return Service.GetIdProjetoFireBase(idusuario);
        }

        public ValidationResult SetIdProjetoFireBase(int idUsuario, int? idAdministradora)
        {
            return Service.SetIdProjetoFireBase(idUsuario, idAdministradora);
        }

        public bool GetRegistrarValePedagio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetRegistrarValePedagio(idEmpresa);
        }

        public ValidationResult SetRegistrarValePedagio(int idEmpresa, bool enviar)
        {
            return _parametrosEmpresaService.SetRegistrarValePedagio(idEmpresa, enviar);
        }

        public int GetGrupoContabilizacaoCentralAtendimento()
        {
            return Service.GetGrupoContabilizacaoCentralAtendimento();
        }

        public ValidationResult SetRealizaTriagemEstabelecimentoInterno(int idEmpresa, bool? realizaTriagemEstabelecimentoInterno)
        {
            return _parametrosEmpresaService.SetRealizaTriagemEstabelecimentoInterno(idEmpresa, realizaTriagemEstabelecimentoInterno);
        }
        
        public decimal GetValorMinimoAlertaSaldoContaFrete(int idEmpresa)
        {
            return _parametrosEmpresaService.GetValorMinimoAlertaSaldoContaFrete(idEmpresa);
        }
        
        public decimal GetValorMinimoAlertaSaldoContaPix(int idEmpresa)
        {
            return _parametrosEmpresaService.GetValorMinimoAlertaSaldoContaPix(idEmpresa);
        }

        public ValidationResult SetValorMinimoAlertaSaldoContaFrete(int idEmpresa, decimal? valor)
        {
            return _parametrosEmpresaService.SetValorMinimoAlertaSaldoContaFrete(idEmpresa, valor);
        }
        
        public ValidationResult SetValorMinimoAlertaSaldoContaPix(int idEmpresa, decimal? valor)
        {
            return _parametrosEmpresaService.SetValorMinimoAlertaSaldoContaPix(idEmpresa, valor);
        }

        public bool GetRealizaTriagemEstabelecimentoInterno(int idEmpresa)
        {
            return _parametrosEmpresaService.GetRealizaTriagemEstabelecimentoInterno(idEmpresa);
        }
        
        public ValidationResult SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(idEmpresa, parametro);
        }
        
        public bool? GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa)
        {
            return _parametrosEmpresaService.GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(idEmpresa);
        }
        
        public ValidationResult SetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetPermiteCadastrarMotoristaComCpfFicticio(idEmpresa, parametro);
        }
        
        public bool? GetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetPermiteCadastrarMotoristaComCpfFicticio(idEmpresa);
        }
        
        public ValidationResult SetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetPermiteCadastrarProprietarioComCpfFicticio(idEmpresa, parametro);
        }
        
        public bool? GetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetPermiteCadastrarProprietarioComCpfFicticio(idEmpresa);
        }
        
        public ValidationResult SetPermiteVincularCartaoComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetPermiteVincularCartaoComCpfFicticio(idEmpresa, parametro);
        }
        
        public bool? GetPermiteVincularCartaoComCpfFicticio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetPermiteVincularCartaoComCpfFicticio(idEmpresa);
        }

        public bool? GetEstabelecimentoValidarChavePagamento(int idEstabelecimento)
        {
            return _parametrosEstabelecimentoService.GetValidarChavePagamento(idEstabelecimento);
        }

        public ValidationResult SetEstabelecimentoValidarChavePagamento(int idEstabelecimento, bool validarChavePagamento)
        {
            return _parametrosEstabelecimentoService.SetValidarChavePagamento(idEstabelecimento, validarChavePagamento);
        }

        public int GetMotivoPadraoBloqueioCartaoEmpresa()
        {
            return _parametrosEmpresaService.GetMotivoPadraoBloqueioCartaoEmpresa();
        }

        public bool GetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario)
        {
            return _parametrosUsuarioService.GetAplicativoPermiteRealizarTransferenciaBancaria(idusuario);
        }

        public bool GetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario)
        {
            return _parametrosUsuarioService.GetAplicativoPermiteRealizarTransferenciaCartoes(idusuario);
        }

        public ValidationResult SetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario, bool value)
        {
            return _parametrosUsuarioService.SetAplicativoPermiteRealizarTransferenciaBancaria(idusuario, value);
        }

        public ValidationResult SetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario, bool value)
        {
            return _parametrosUsuarioService.SetAplicativoPermiteRealizarTransferenciaCartoes(idusuario, value);
        }

        public ValidationResult SetParametroEmpresaGridListarCnpjDespesasViagem(bool listar, int idEmpresa)
        {
            return Service.SetParametroEmpresaGridListarCnpjDespesasViagem(listar, idEmpresa);
        }

        public bool GetParametroEmpresaGridListarCnpjDespesasViagem(int idEmpresa)
        {
            return Service.GetParametroEmpresaGridListarCnpjDespesasViagem(idEmpresa);
        }
        
        public ValidationResult SetCodigoOfx(string listar, int idEmpresa)
        {
            return Service.SetCodigoOfx(listar, idEmpresa);
        }

        public string GetCodigoOfx(int idEmpresa)
        {
            return Service.GetCodigoOfx(idEmpresa);
        }

        public ValidationResult SetPercentualTransferenciaFreteProprietarioMotorista(PercentualTransferenciaFreteViagemParametro percentuais, string documentoProprietario, string documentoMotorista)
        {
            return Service.SetPercentualTransferenciaFreteProprietarioMotorista(percentuais, documentoProprietario, documentoMotorista);
        }

        public ValidationResult SetParametroUsuarioPermitirAcessoAtendimento(int idUsuario, bool value)
        {
            return _parametrosUsuarioService.SetParametroUsuarioPermitirAcessoAtendimento(idUsuario, value);
        }
        
        public bool GetParametroUsuarioPermitirAcessoAtendimento(int idUsuario)
        {
            return _parametrosUsuarioService.GetParametroUsuarioPermitirAcessoAtendimento(idUsuario);
        }
        
        public bool GetPermitirEdicaoDadosAdministrativosEmpresa(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosEmpresa(idUsuario);
        }
        
        public bool GetPermitirEdicaoDadosAdministrativosFilial(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosFilial(idUsuario);
        }
        
        public bool GetPermitirEdicaoDadosAdministrativosUsuario(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosUsuario(idUsuario);
        }
        
        public bool GetPermitirEdicaoDadosAdministrativosGrupoUsuario(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosGrupoUsuario(idUsuario);
        }

        public ValidationResult SetTagExtrattaTaxaVpo(int idEmpresa, decimal? value)
        {
            if (value.HasValue && value.Value < 0.2m) value = 0.2m;
            return _parametrosEmpresaService.SetTagExtrattaTaxaVpo(idEmpresa, value);
        }

        public decimal GetTagExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaTaxaVpo(idEmpresa);
        }

        public ValidationResult SetTagExtrattaValorTag(int idEmpresa, decimal? value)
        {
            return _parametrosEmpresaService.SetTagExtrattaValorTag(idEmpresa, value);
        }

        public decimal GetTagExtrattaValorTag(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaValorTag(idEmpresa);
        }

        public ValidationResult SetTagExtrattaValorMensalidade(int idEmpresa, decimal? value)
        {
            return _parametrosEmpresaService.SetTagExtrattaValorMensalidade(idEmpresa, value);
        }

        public decimal GetTagExtrattaValorMensalidade(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaValorMensalidade(idEmpresa);
        }

        public ValidationResult SetTagExtrattaProvisionarValor(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetTagExtrattaProvisionarValor(idEmpresa, value);
        }

        public bool GetTagExtrattaProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaProvisionarValor(idEmpresa);
        }

        public ValidationResult SetTagExtrattaProvisionarTaxa(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetTagExtrattaProvisionarTaxa(idEmpresa, value);
        }

        public bool GetTagExtrattaProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaProvisionarTaxa(idEmpresa);
        }
        
        public ValidationResult SetHubMoveMaisProvisionarValor(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubMoveMaisProvisionarValor(idEmpresa, value);
        }

        public bool GetHubMoveMaisProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubMoveMaisProvisionarValor(idEmpresa);
        }

        public ValidationResult SetHubMoveMaisProvisionarTaxa(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubMoveMaisProvisionarTaxa(idEmpresa, value);
        }

        public bool GetHubMoveMaisProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubMoveMaisProvisionarTaxa(idEmpresa);
        }
        
        public ValidationResult SetHubConectCarMaisProvisionarValor(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubConectCarProvisionarValor(idEmpresa, value);
        }

        public bool GetHubConectCarProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubConectCarProvisionarValor(idEmpresa);
        }

        public ValidationResult SetHubConectCarProvisionarTaxa(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubConectCarProvisionarTaxa(idEmpresa, value);
        }

        public bool GetHubConectCarProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubConectCarProvisionarTaxa(idEmpresa);
        }
        
        public ValidationResult SetHubViaFacilProvisionarValor(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubViaFacilProvisionarValor(idEmpresa, value);
        }

        public bool GetHubViaFacilProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubViaFacilProvisionarValor(idEmpresa);
        }

        public ValidationResult SetHubViaFacilProvisionarTaxa(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubViaFacilProvisionarTaxa(idEmpresa, value);
        }

        public bool GetHubViaFacilProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubViaFacilProvisionarTaxa(idEmpresa);
        }
        
        public ValidationResult SetHubVeloeProvisionarValor(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubVeloeProvisionarValor(idEmpresa, value);
        }

        public bool GetHubVeloeProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubVeloeProvisionarValor(idEmpresa);
        }

        public ValidationResult SetHubVeloeProvisionarTaxa(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetHubVeloeProvisionarTaxa(idEmpresa, value);
        }

        public bool GetHubVeloeProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubVeloeProvisionarTaxa(idEmpresa);
        }

        public ValidationResult SetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa, decimal? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaFaixaToleranciaNotificacaoEmail(idEmpresa,parametro);
        }

        public decimal GetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaFaixaToleranciaNotificacaoEmail(idEmpresa);
        }

        public ValidationResult SetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaProvisionarTaxaPedagioWebhook(idEmpresa,parametro);
        }

        public bool GetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaProvisionarTaxaPedagioWebhook(idEmpresa);
        }
        
        public ValidationResult SetTagExtrattaEstornarPedagio(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaEstornarValorPedagioWebhook(idEmpresa,parametro);
        }

        public bool GetTagExtrattaEstornarPedagio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaEstornarValorPedagioWebhook(idEmpresa);
        }

        public ValidationResult SetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaProvisionarValorPedagioWebhook(idEmpresa,parametro);
        }

        public bool GetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaProvisionarValorPedagioWebhook(idEmpresa);
        }

        public ValidationResult SetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa, decimal? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaSaldoMinimoContaFreteWebhook(idEmpresa,parametro);
        }

        public decimal GetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaSaldoMinimoContaFreteWebhook(idEmpresa);
        }

        public bool GetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaBloquearTagUnitariaWebhook(idEmpresa);
        }

        public ValidationResult SetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaBloquearTagUnitariaWebhook(idEmpresa,parametro);
        }

        public bool GetTagExtrattaBloquearTagLoteWebhook(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaBloquearTagLoteWebhook(idEmpresa);
        }

        public ValidationResult SetTagExtrattaBloquearTagLoteWebhook(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaBloquearTagLoteWebhook(idEmpresa,parametro);
        }

        public decimal GetTagExtrattaValorSubstituicao(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaValorSubstituicao(idEmpresa);
        }

        public ValidationResult SetTagExtrattaValorSubstituicao(int idEmpresa, decimal? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaValorSubstituicao(idEmpresa,parametro);
        }

        public decimal GetTagExtrattaValorRecargaConta(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaValorRecargaConta(idEmpresa);
        }

        public ValidationResult SetTagExtrattaValorRecargaConta(int idEmpresa, decimal? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaValorRecargaConta(idEmpresa,parametro);
        }

        public string GetTagExtrattaCnpjProvisionamento()
        {
            return _parametrosGenericoService.GetParametro<string>(GLOBAL.TagExtrattaCnpjProvisionamento, 0);
        }

        public string GetUserWebhook()
        {
            return _parametrosGenericoService.GetParametro<string>(GLOBAL.UsuarioWebhookMoveMaisTagExtratta, 0);
        }

        public string GetSenhaWebhook()
        {
            return _parametrosGenericoService.GetParametro<string>(GLOBAL.SenhaWebhookMoveMaisTagExtratta, 0);
        }

        public bool GetPermitirAcessoExtratoDetalhadoUsuario(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermitirAcessoExtratoDetalhado(idUsuario);
        }

        public ValidationResult SetPermitirAcessoExtratoDetalhadoUsuario(int idUsuario, bool value)
        {
            return _parametrosUsuarioService.SetPermitirAcessoExtratoDetalhado(idUsuario, value);
        }
        
        public ValidationResult SetMoveMaisExtrattaTaxaVpo(int idEmpresa, decimal? value)
        {
            if (value.HasValue && value.Value < 0.2m) value = 0.2m;
            return _parametrosEmpresaService.SetMoveMaisExtrattaTaxaVpo(idEmpresa, value);
        }

        public decimal GetMoveMaisExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetMoveMaisExtrattaTaxaVpo(idEmpresa);
        }
        
        public ValidationResult SetViaFacilExtrattaTaxaVpo(int idEmpresa, decimal? value)
        {
            if (value.HasValue && value.Value < 0.9m) value = 0.9m;
            return _parametrosEmpresaService.SetViaFacilExtrattaTaxaVpo(idEmpresa, value);
        }

        public decimal GetViaFacilExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetViaFacilExtrattaTaxaVpo(idEmpresa);
        }
        
        public ValidationResult SetLimiteDiarioPagamentoPixUsuario(int idUsuario, decimal? parametro)
        {
            return _parametrosUsuarioService.SetLimiteDiarioPagamentoPixUsuario(idUsuario, parametro);
        }

        public ValidationResult SetLimiteUnitarioPagamentoPixUsuario(int idUsuario, decimal? parametro)
        {
            return _parametrosUsuarioService.SetLimiteUnitarioPagamentoPixUsuario(idUsuario, parametro);
        }

        public bool GetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermiteAprovarSolicitacaoAdiantamentoApp(idUsuario);
        }

        public ValidationResult SetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario, bool value)
        {
            return _parametrosUsuarioService.SetPermiteAprovarSolicitacaoAdiantamentoApp(idUsuario, value);
        }
        
        public bool GetPermiteSolicitarAdiantamentoApp(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermiteSolicitarAdiantamentoApp(idUsuario);
        }

        public ValidationResult SetPermiteSolicitarAdiantamentoApp(int idUsuario, bool value)
        {
            return _parametrosUsuarioService.SetPermiteSolicitarAdiantamentoApp(idUsuario, value);
        }
        
        public bool GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario)
        {
            return _parametrosUsuarioService.GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(idUsuario);
        }

        public ValidationResult SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario, bool value)
        {
            return _parametrosUsuarioService.SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(idUsuario, value);
        }

        public ValidationResult SetVeloeExtrattaTaxaVpo(int idEmpresa, decimal? value)
        {
            if (value.HasValue && value.Value < 0.75m) value = 0.75m;
            return _parametrosEmpresaService.SetVeloeExtrattaTaxaVpo(idEmpresa, value);
        }

        public decimal GetVeloeExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetVeloeExtrattaTaxaVpo(idEmpresa);
        }

        public bool GetTagExtrattaUtilizaTaxaPedagio(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTagExtrattaUtilizaTaxaPedagio(idEmpresa);
        }

        public ValidationResult SetTagExtrattaUtilizaTaxaPedagio(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetTagExtrattaUtilizaTaxaPedagio(idEmpresa,parametro);
        }

        public ValidationResult SetConectCarExtrattaTaxaVpo(int idEmpresa, decimal? value)
        {
            if (value.HasValue && value.Value < 0.8m) value = 0.8m;
            return _parametrosEmpresaService.SetConectCarExtrattaTaxaVpo(idEmpresa, value);
        }

        public decimal GetConectCarExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetConectCarExtrattaTaxaVpo(idEmpresa);
        }

        public bool GetUtilizaRelatoriosOfx(int idEmpresa)
        {
            return _parametrosEmpresaService.GetUtilizaRelatoriosOfx(idEmpresa);
        }

        public ValidationResult SetUtilizaRelatoriosOfx(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetUtilizaRelatoriosOfx(idEmpresa,parametro);
        }

        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetSolicitaAprovacaoGestorCargaAvulsaLote(idEmpresa,parametro);
        }
        public bool GetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa)
        {
            return _parametrosEmpresaService.GetSolicitaAprovacaoGestorCargaAvulsaLote(idEmpresa);
        }
        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetSolicitaAprovacaoGestorCargaAvulsaUnitario(idEmpresa,parametro);
        }
        public bool GetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa)
        {
            return _parametrosEmpresaService.GetSolicitaAprovacaoGestorCargaAvulsaUnitario(idEmpresa);
        }
        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(idEmpresa,parametro);
        }
        public bool GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa)
        {
            return _parametrosEmpresaService.GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(idEmpresa);
        }

        public bool GetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa)
        {
            return _parametrosEmpresaService.GetMantemViagemAbertaAposBaixaDoUltimoEvento(idEmpresa);
        }

        public ValidationResult SetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa, bool? value)
        {
            return _parametrosEmpresaService.SetMantemViagemAbertaAposBaixaDoUltimoEvento(idEmpresa, value);
        }

        public bool GetDefaultIntegracaoTipoRodagemDupla(int idEmpresa)
        {
            return _parametrosEmpresaService.GetDefaultIntegracaoTipoRodagemDupla(idEmpresa);
        }

        public ValidationResult SetDefaultIntegracaoTipoRodagemDupla(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetDefaultIntegracaoTipoRodagemDupla(idEmpresa,parametro);
        }
        
        public bool GetUtilizaRoteirizacaoPorPolyline(int idEmpresa)
        {
            return _parametrosEmpresaService.GetUtilizaRoteirizacaoPorPolyline(idEmpresa);
        }
        
        public ValidationResult SetUtilizaUtilizaRoteirizacaoPorPolyline(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetUtilizaUtilizaRoteirizacaoPorPolyline(idEmpresa,parametro);
        }

        public decimal GetLimitePadraoTransferenciaCartaoCNPJ()
        {
            return _parametrosGenericoService.GetParametro<decimal>(GLOBAL.LimitePadraoTransferenciaCartaoCNPJ, 0);
        }

        public decimal GetLimitePadraoTransferenciaCartaoCPF()
        {
            return _parametrosGenericoService.GetParametro<decimal>(GLOBAL.LimitePadraoTransferenciaCartaoCPF, 0);
        }

        public decimal GetLimitePadraoTransferenciaTEDCNPJ()
        {
            return _parametrosGenericoService.GetParametro<decimal>(GLOBAL.LimitePadraoTransferenciaTEDCNPJ, 0);
        }

        public decimal GetLimitePadraoTransferenciaTEDCPF()
        {
            return _parametrosGenericoService.GetParametro<decimal>(GLOBAL.LimitePadraoTransferenciaTEDCPF, 0);
        }

        public bool GetHubTaggyEdenredProvisionarValor(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubTaggyEdenredProvisionarValor(idEmpresa);
        }

        public ValidationResult SetHubTaggyEdenredProvisionarValor(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetHubTaggyEdenredProvisionarValor(idEmpresa,parametro);
        }

        public bool GetHubTaggyEdenredProvisionarTaxa(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHubTaggyEdenredProvisionarTaxa(idEmpresa);
        }

        public ValidationResult SetHubTaggyEdenredProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetHubTaggyEdenredProvisionarTaxa(idEmpresa,parametro);
        }

        public decimal GetTaggyEdenredExtrattaTaxaVpo(int idEmpresa)
        {
            return _parametrosEmpresaService.GetTaggyEdenredExtrattaTaxaVpo(idEmpresa);
        }

        public ValidationResult SetTaggyEdenredExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            if (!parametro.HasValue || parametro.Value < 0.3m) parametro = 0.3m;
            return _parametrosEmpresaService.SetTaggyEdenredExtrattaTaxaVpo(idEmpresa,parametro);
        }

        public bool GetBloqueiaCargaAvulsaDuplicada(int idEmpresa)
        {
            return _parametrosEmpresaService.GetBloqueiaCargaAvulsaDuplicada(idEmpresa);
        }

        public ValidationResult SetBloqueiaCargaAvulsaDuplicada(int idEmpresa, bool? parametro)
        {
            if (parametro == null) return new ValidationResult();
            return _parametrosEmpresaService.SetBloqueiaCargaAvulsaDuplicada(idEmpresa, parametro);
        }

        public decimal? GetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa)
        {
            return _parametrosEmpresaService.GetHorasBloqueioCargaAvulsaDuplicada(idEmpresa);
        }

        public ValidationResult SetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa, decimal? parametro)
        {
            return _parametrosEmpresaService.SetHorasBloqueioCargaAvulsaDuplicada(idEmpresa, parametro);
        }

        public bool GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa)
        {
            return _parametrosEmpresaService.GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(idEmpresa);
        }

        public ValidationResult SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(idEmpresa,parametro);
        }

        public bool GetNaoBaixarParcelasDeposito(int idEmpresa)
        {
            return _parametrosEmpresaService.GetNaoBaixarParcelasDeposito(idEmpresa);
        }

        public ValidationResult SetNaoBaixarParcelasDeposito(int idEmpresa, bool? parametro)
        {
            return _parametrosEmpresaService.SetNaoBaixarParcelasDeposito(idEmpresa,parametro);
        }
    }
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class PracasRotaModeloMap : EntityTypeConfiguration<PracasRotaModelo>
    {
        public PracasRotaModeloMap()
        {
            ToTable("PRACA_ROTA_MODELO");

            HasKey(t => new { t.IdRotaModelo, t.IdPraca });
            
            Property(t => t.IdRotaModelo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdPraca)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            
            Property(t => t.Descricao)
                .HasMaxLength(100);

            Property(t => t.Valor)
                .IsRequired();

            Property(t => t.ValorTag)
                .IsRequired();
        }
    }
}
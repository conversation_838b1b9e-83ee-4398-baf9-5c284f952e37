﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="8.2.0" targetFramework="net48" />
  <package id="Dapper" version="1.50.5" targetFramework="net48" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net48" />
  <package id="EntityFramework.MappingAPI" version="6.1.0.9" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.Diagnostics.Tracing.TraceEvent" version="2.0.2" targetFramework="net48" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="NLog" version="4.7.2" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Dataflow" version="4.9.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="TrackerEnabledDbContext" version="3.6.1" targetFramework="net48" />
  <package id="TrackerEnabledDbContext.Common" version="3.6.1" targetFramework="net48" />
</packages>
﻿using ATS.Application.Interface.Common;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Categoria;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Application.Interface
{
    public interface ICategoriaApp : IBaseApp<ICategoriaService>
    {
        public BusinessResult<CategoriaAddModelResponse> Add(CategoriaAddModel model);
        public BusinessResult<CategoriaUpdateModelResponse> Update(CategoriaUpdateModel model);
        public BusinessResult<CategoriaEnableModelResponse> Enable(CategoriaEnableModel model);
        public BusinessResult<CategoriaDisableModelResponse> Disable(CategoriaDisableModel model);
        public BusinessResult<CategoriaGetModelResponse> Get(int categoriaId);
        public BusinessResult<IList<CategoriaGetModelResponse>> GetAll(bool filterActive);
        public BusinessResult<CategoriaGetGridModelResponse> GetGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
    }
}

using ATS.WS.Controllers.Base;
using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Database;
using ATS.WS.Attributes;
using ATS.WS.Models.Webservice.Request.Pedagio;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class PedagioAplicativoController : BaseController
    {
        private readonly IViagemRepository _viagemRepository;
        private readonly IEmpresaApp _empresaApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly PedagioAppFactoryDependencies _pedagioAppFactoryDependencies;

        public PedagioAplicativoController(BaseControllerArgs baseArgs, IViagemRepository viagemRepository, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, PedagioAppFactoryDependencies pedagioAppFactoryDependencies, IEmpresaApp empresaApp) : base(baseArgs)
        {
            _viagemRepository = viagemRepository;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _pedagioAppFactoryDependencies = pedagioAppFactoryDependencies;
            _empresaApp = empresaApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult CalcularRota(CalcularRotaAtsRequest request)
        {
            try
            {
                //por que nao valida token aqui ?
                var validacaoChamada = request.ValidaRequest();
               
                if (!validacaoChamada.IsValid)
                    return Responde(new CalcularRotaResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });
                
                var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var empresa = _empresaApp.Get(request.CNPJEmpresa);
                
                var calcularRotaRequest = new ConsultaRotaRequest
                {
                    Localizacoes = request.Localizacoes,
                    TipoVeiculo = request.TipoVeiculo ?? 0,
                    QtdEixos = request.QtdEixos,
                    ExibirDetalhes = request.ExibirDetalhes,
                    DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas
                };

                var response = pedagioApp.CalcularRota(calcularRotaRequest, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(new CalcularRotaResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
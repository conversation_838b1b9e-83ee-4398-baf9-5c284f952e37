using System;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class RotaModelo
    {
        /// <summary>
        /// Id da rota
        /// </summary>
        public int IdRotaModelo { get; set; }
        /// <summary>
        /// Id da empresa
        /// </summary>
        public int? IdEmpresa { get; set; }
        /// <summary>
        /// Data do cadastro
        /// </summary>
        public DateTime DataCadastro { get; set; }
        /// <summary>
        /// Descrição da origem
        /// </summary>
        public string OrigemDescricao { get; set; }
        /// <summary>
        /// Descrição do destino
        /// </summary>
        public string DestinoDescricao { get; set; }
        /// <summary>
        /// codigo do Ibge origem
        /// </summary>
        public decimal OrigemIbge { get; set; }
        /// <summary>
        /// codigo do Ibge destino
        /// </summary>
        public decimal DestinoIbge { get; set; }
        /// <summary>
        /// Distancia total
        /// </summary>
        public float DistanciaTotal { get; set; }
        /// <summary>
        /// Custo da rota
        /// </summary>
        public decimal CustoRota { get; set; }
        /// <summary>
        /// Custo da rota tag
        /// </summary>
        public decimal CustoRotaTag { get; set; }
        /// <summary>
        /// Nome da rota
        /// </summary>
        public string NomeRota { get; set; }
        /// <summary>
        /// Quantidade de eixos
        /// </summary>
        public int QtdeEixos { get; set; }
        /// <summary>
        /// Tipo do veiculo
        /// </summary>
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        /// <summary>
        /// Tipo de rodagem
        /// </summary>
        public ETipoRodagem TipoRodagem { get; set; }
        /// <summary>
        /// Tipo da rota
        /// </summary>
        public bool IdaEVolta { get; set; }
        /// <summary>
        /// Latitude de origem da rota
        /// </summary>
        public decimal? OrigemLatitude { get; set; }
        /// <summary>
        /// Longitude de origem da rota
        /// </summary>
        public decimal? OrigemLongitude { get; set; }
        /// <summary>
        /// Latitude de destino rota
        /// </summary>
        public decimal? DestinoLatitude { get; set; }
        /// <summary>
        /// Longitude de destino da rota
        /// </summary>
        public decimal? DestinoLongitude { get; set; }
        
        /// <summary>
        /// Longitude de destino da rota
        /// </summary>
        public int? CodPolyline { get; set; }

        #region Referências

        /// <summary>
        /// Id da empresa
        /// </summary>
        [ForeignKey("IdEmpresa")]
        public virtual Empresa Empresa { get; set; }
        
        #endregion

        #region Tabelas Filhas
        
        /// <summary>
        /// Praças de pedagio da rota modelo
        /// </summary>
        public virtual ICollection<PracasRotaModelo> PracasRotaModelo { get; set; }
        
        /// <summary>
        ///  Ponto da rota modelo
        /// </summary>
        public virtual ICollection<PontosRotaModelo> PontosRotaModelo { get; set; }
        #endregion

    }
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ProdutoMap : EntityTypeConfiguration<Produto>
    {
        public ProdutoMap()
        {
            ToTable("PRODUTO");

            HasKey(p => new { p.IdProduto });

            HasRequired(x => x.Empresa)
                .WithMany(x => x.Produtos)
                .HasForeignKey(x => x.IdEmpresa);

            Property(p => p.IdProduto)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(p => p.Descricao)
                .IsRequired()
                .HasMaxLength(100);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioGridExtratoDTO : FiltrosGridBaseModel
    {
        public int IdUsuarioLogado { get; set; }
        public string Tipo { get; set; }
        public int Produto { get; set; }
        public int Identificador { get; set; }
        public DateTime DataFim { get; set; }
        public DateTime DataInicio { get; set; }
        public bool SomenteTransferencia { get; set; }
        public int Dias { get; set; } = 30;
        public new int Take { get; set; }
        public new int Page { get; set; }
        public new OrderFilters Order { get; set; }
        public List<QueryFilters> filters { get; set; }
        public string DocumentoPortador { get; set; }
        public string NomePortador { get; set; }
    }
}
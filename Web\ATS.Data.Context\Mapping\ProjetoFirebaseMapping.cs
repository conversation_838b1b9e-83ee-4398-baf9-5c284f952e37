using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ProjetoFirebaseMapping: EntityTypeConfiguration<ProjetoFirebase>
    {
        public ProjetoFirebaseMapping()
        {
            ToTable("PROJETO_FIREBASE");

            HasKey(x => x.IdProjetoFirebase);
            
            Property(x => x.IdProjetoFirebase)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None)
                .IsRequired();
            
            Property(x => x.Nome).HasMaxLength(100);
            Property(x => x.IdProjetoFirebase);
        }
    }
}
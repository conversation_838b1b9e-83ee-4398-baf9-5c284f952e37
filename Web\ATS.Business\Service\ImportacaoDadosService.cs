﻿using ATS.Domain.Interface.Service;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using ExcelDataReader;
using Extratta.ImportacaoDadosConsumer.Events;
using MassTransit;

namespace ATS.Domain.Service
{
    public class ImportacaoDadosService : IImportacaoDadosService
    {
        private readonly IPublishEndpoint _publisher;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ITipoCarretaRepository _tipoCarretaRepository;
        private readonly ITipoCavaloRepository _tipoCavaloRepository;
        public ImportacaoDadosService(IPublishEndpoint publisher, IEmpresaRepository empresaRepository, ITipoCarretaRepository tipoCarretaRepository, ITipoCavaloRepository tipoCavaloRepository)
        {
            _publisher = publisher;
            _empresaRepository = empresaRepository;
            _tipoCarretaRepository = tipoCarretaRepository;
            _tipoCavaloRepository = tipoCavaloRepository;
        }
        public BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarPlanilha(HttpPostedFileBase file)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();
                var stream = file.InputStream;
                IExcelDataReader reader = null;

                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelBinaryExtension))
                    reader = ExcelReaderFactory.CreateBinaryReader(stream);
                
                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelXmlExtention))
                    reader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                
                if (reader != null)
                {
                    var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
                    {
                        ConfigureDataTable = _ => new ExcelDataTableConfiguration
                        {
                            UseHeaderRow = true
                        },
                    });
                    
                    reader.Close();

                    var resultValidationHeader = ValidarHeader(dataSet);
                    
                    if(!resultValidationHeader.Success)
                        return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error(resultValidationHeader.Messages);
                    
                    var resultPastas = ValidarPastas(dataSet);
                    
                    if(!resultPastas.Success)
                        return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error(resultPastas.Messages);

                    if(resultPastas.Value.Any())
                        result.AddRange(resultPastas.Value);
                }
                
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error(e.Message);
            }
        }
        
        public BusinessResult ImportarDados(HttpPostedFileBase file,int idEmpresa)
        {
            try
            {
                var resultImport = ImportarDadosList(file,idEmpresa);
                
                if(!resultImport.Success)
                    return BusinessResult.Error(resultImport.Messages.FirstOrDefault());

                if (resultImport.Value.Veiculo.Any())
                    _publisher.PublishBatch(resultImport.Value.Veiculo);
                
                if (resultImport.Value.Filial.Any())
                    _publisher.PublishBatch(resultImport.Value.Filial);
                
                if (resultImport.Value.Cliente.Any())
                    _publisher.PublishBatch(resultImport.Value.Cliente);
                
                if (resultImport.Value.Proprietario.Any())
                    _publisher.PublishBatch(resultImport.Value.Proprietario);
                
                if (resultImport.Value.Motorista.Any())
                    _publisher.PublishBatch(resultImport.Value.Motorista);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        private BusinessResult<ImportacaoDadosResponse> ImportarDadosList(HttpPostedFileBase file,int idEmpresa)
        {
            try
            {
                var result = new ImportacaoDadosResponse()
                {
                    Filial = new List<FilialIntegrarFilaEvent>(),
                    Cliente = new List<ClienteIntegrarFilaEvent>(),
                    Motorista = new List<MotoristaIntegrarFilaEvent>(),
                    Proprietario = new List<ProprietarioIntegrarFilaEvent>(),
                    Veiculo = new List<VeiculoIntegrarFilaEvent>()
                };

                var empresa = _empresaRepository.GetWithIncludeAutenticacao(idEmpresa);
                
                if(empresa == null)
                    return BusinessResult<ImportacaoDadosResponse>.Error("Informe a empresa para a importação da planilha!");
                
                var autenticacao = empresa.AutenticacaoAplicacao.FirstOrDefault(x => x.Ativo);
                
                if(autenticacao == null)
                    return BusinessResult<ImportacaoDadosResponse>.Error("Erro interno na autenticação da empresa!");

                var stream = file.InputStream;
                IExcelDataReader reader = null;

                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelBinaryExtension))
                    reader = ExcelReaderFactory.CreateBinaryReader(stream);
                
                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelXmlExtention))
                    reader = ExcelReaderFactory.CreateOpenXmlReader(stream);

                if (reader == null) 
                    return BusinessResult<ImportacaoDadosResponse>.Valid(result);
                
                var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
                {
                    ConfigureDataTable = _ => new ExcelDataTableConfiguration
                    {
                        UseHeaderRow = true
                    },
                });

                reader.Close();

                var sheets = dataSet.Tables;

                foreach (DataTable sheet in sheets)
                {
                    var linha = 0;

                    if (sheet.TableName.ToLower() == "Veículo".ToLower())
                    {
                        foreach (DataRow row in sheet.Rows)
                        {
                            linha += 1;
                            var resultLinha = ImportarLinhaVeiculo(row, empresa.CNPJ,autenticacao.Token);

                            if (resultLinha.Success)
                                result.Veiculo.Add(resultLinha.Value);
                            else
                                return BusinessResult<ImportacaoDadosResponse>.Error($"Falha na importação da pasta Veículo na linha {linha}: {resultLinha.Messages.FirstOrDefault()}");

                        }
                    }

                    if (sheet.TableName.ToLower() == "Motorista".ToLower())
                    {
                        foreach (DataRow row in sheet.Rows)
                        {
                            linha += 1;
                            var resultLinha = ImportarLinhaMotorista(row, empresa.CNPJ,autenticacao.Token);

                            if (resultLinha.Success)
                                result.Motorista.Add(resultLinha.Value);
                            else
                                return BusinessResult<ImportacaoDadosResponse>.Error($"Falha na importação da pasta Motorista na linha {linha}: {resultLinha.Messages.FirstOrDefault()}");

                        }
                    }

                    if (sheet.TableName.ToLower() == "Proprietário".ToLower())
                    {
                        foreach (DataRow row in sheet.Rows)
                        {
                            linha += 1;
                            var resultLinha = ImportarLinhaProprietario(row, empresa.CNPJ,autenticacao.Token);

                            if (resultLinha.Success)
                                result.Proprietario.Add(resultLinha.Value);
                            else
                                return BusinessResult<ImportacaoDadosResponse>.Error($"Falha na importação da pasta Proprietário na linha {linha}: {resultLinha.Messages.FirstOrDefault()}");

                        }
                    }

                    if (sheet.TableName.ToLower() == "Cliente".ToLower())
                    {
                        foreach (DataRow row in sheet.Rows)
                        {
                            linha += 1;
                            var resultLinha = ImportarLinhaCliente(row, empresa.CNPJ,autenticacao.Token);

                            if (resultLinha.Success)
                                result.Cliente.Add(resultLinha.Value);
                            else
                                return BusinessResult<ImportacaoDadosResponse>.Error($"Falha na importação da pasta Cliente na linha {linha}: {resultLinha.Messages.FirstOrDefault()}");

                        }
                    }

                    if (sheet.TableName.ToLower() == "Filial".ToLower())
                    {
                        foreach (DataRow row in sheet.Rows)
                        {
                            linha += 1;
                            var resultLinha = ImportarLinhaFilial(row, empresa.CNPJ,autenticacao.Token);

                            if (resultLinha.Success)
                                result.Filial.Add(resultLinha.Value);
                            else
                                return BusinessResult<ImportacaoDadosResponse>.Error($"Falha na importação da pasta Filial na linha {linha}: {resultLinha.Messages.FirstOrDefault()}");

                        }
                    }
                }

                return BusinessResult<ImportacaoDadosResponse>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<ImportacaoDadosResponse>.Error(e.Message);
            }
        }
        
        private BusinessResult ValidarHeader(DataSet dataSet)
        {
            if (dataSet != null)
            {
                var sheets = dataSet.Tables;

                foreach (DataTable sheet in sheets)
                {
                    BusinessResult resultHeader = null;

                    if (sheet.TableName.ToLower() == "Veículo".ToLower())
                        resultHeader = ValidarHeaderVeiculo(sheet);
                    
                    if (sheet.TableName.ToLower() == "Motorista".ToLower())
                        resultHeader = ValidarHeaderMotorista(sheet);
                    
                    if (sheet.TableName.ToLower() == "Proprietário".ToLower())
                        resultHeader = ValidarHeaderProprietario(sheet);
                    
                    if (sheet.TableName.ToLower() == "Cliente".ToLower())
                        resultHeader = ValidarHeaderCliente(sheet);
                    
                    if (sheet.TableName.ToLower() == "Filial".ToLower())
                        resultHeader = ValidarHeaderFilial(sheet);
                    
                    if (sheet.TableName.ToLower() == "LEIA-ME".ToLower())
                        continue;
                    
                    if(resultHeader == null)
                        return BusinessResult.Error("Pastas inválidas");
                    
                    if(!resultHeader.Success)
                        return BusinessResult.Error(resultHeader.Messages);
                }
            }
            else
                return BusinessResult.Error("Planilha inválida, realize o download do layout padrão e preencha");
            
            return BusinessResult.Valid();
        }

        private BusinessResult ValidarHeaderProprietario(DataTable sheet)
        {
            try
            {
                if (sheet.Columns[0].ColumnName.ToLower() != "CNPJ/CPF".ToLower())
                    return BusinessResult.Error("A primeira coluna da pasta 'Proprietário' não se refere ao CNPJ/CPF do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[1].ColumnName.ToLower() != "Razão Social".ToLower())
                    return BusinessResult.Error("A segunda coluna da pasta 'Proprietário' não se refere a Razão Social do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[2].ColumnName.ToLower() != "Nome Fantasia".ToLower())
                    return BusinessResult.Error("A terceira coluna da pasta 'Proprietário' não se refere ao Nome Fantasia do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[3].ColumnName.ToLower() != "RNTRC".ToLower())
                    return BusinessResult.Error("A quarta coluna da pasta 'Proprietário' não se refere ao RNTRC do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[4].ColumnName.ToLower() != "CEP".ToLower())
                    return BusinessResult.Error("A quinta coluna da pasta 'Proprietário' não se refere ao CEP do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[5].ColumnName.ToLower() != "Logradouro".ToLower())
                    return BusinessResult.Error("A sexta coluna da pasta 'Proprietário' não se refere ao Logradouro do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[6].ColumnName.ToLower() != "Numero Endereço".ToLower())
                    return BusinessResult.Error("A sétima coluna da pasta 'Proprietário' não se refere ao Numero Endereço do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[7].ColumnName.ToLower() != "Bairro".ToLower())
                    return BusinessResult.Error("A oitava coluna da pasta 'Proprietário' não se refere ao Bairro do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[8].ColumnName.ToLower() != "IBGE Cidade".ToLower())
                    return BusinessResult.Error("A nona coluna da pasta 'Proprietário' não se refere ao IBGE Cidade do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[9].ColumnName.ToLower() != "IBGE UF".ToLower())
                    return BusinessResult.Error("A décima coluna da pasta 'Proprietário' não se refere ao  IBGE UF do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[10].ColumnName.ToLower() != "Data de Nascimento".ToLower())
                    return BusinessResult.Error("A décima primeira coluna da pasta 'Proprietário' não se refere ao Data de Nascimento do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[11].ColumnName.ToLower() != "Nome do Pai".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Proprietário' não se refere ao Nome do Pai do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[12].ColumnName.ToLower() != "Nome da Mãe".ToLower())
                    return BusinessResult.Error("A décima terceira coluna da pasta 'Proprietário' não se refere ao Nome da Mãe do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[13].ColumnName.ToLower() != "RG".ToLower())
                    return BusinessResult.Error("A décima quarta coluna da pasta 'Proprietário' não se refere ao RG do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[14].ColumnName.ToLower() != "OrgaoExpedidor".ToLower())
                    return BusinessResult.Error("A décima quinta coluna da pasta 'Proprietário' não se refere ao Orgao Expedidor do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[15].ColumnName.ToLower() != "IE".ToLower())
                    return BusinessResult.Error("A décima sexta coluna da pasta 'Proprietário' não se refere ao IE do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[16].ColumnName.ToLower() != "Email".ToLower())
                    return BusinessResult.Error("A décima sétima coluna da pasta 'Proprietário' não se refere ao Email do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[17].ColumnName.ToLower() != "Telefone".ToLower())
                    return BusinessResult.Error("A décima oitava coluna da pasta 'Proprietário' não se refere ao Telefone do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[18].ColumnName.ToLower() != "Celular".ToLower())
                    return BusinessResult.Error("A décima nona coluna da pasta 'Proprietário' não se refere ao Celular do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[19].ColumnName.ToLower() != "INSS".ToLower())
                    return BusinessResult.Error("A vigésima coluna da pasta 'Proprietário' não se refere ao INSS do proprietário, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[20].ColumnName.ToLower() != "Tipo Contrato".ToLower())
                    return BusinessResult.Error("A vigésima coluna da pasta 'Proprietário' não se refere ao Tipo Contrato do proprietário, realize o download do layout padrão e preencha.");

                return BusinessResult.Valid();
            }
            catch (Exception)
            {
                return BusinessResult.Error("Falha ao validar header pasta proprietário");
            }
        }

        private BusinessResult ValidarHeaderMotorista(DataTable sheet)
        {
            try
            { 
                if (sheet.Columns[0].ColumnName.ToLower() != "Nome".ToLower())
                    return BusinessResult.Error("A primeira coluna da pasta 'Motorista' não se refere ao Nome do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[1].ColumnName.ToLower() != "RG".ToLower())
                    return BusinessResult.Error("A segunda coluna da pasta 'Motorista' não se refere ao RG do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[2].ColumnName.ToLower() != "Expedidor RG".ToLower())
                    return BusinessResult.Error("A terceira coluna da pasta 'Proprietário' não se refere ao Expedidor RG do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[3].ColumnName.ToLower() != "Data do Nascimento".ToLower())
                    return BusinessResult.Error("A quarta coluna da pasta 'Motorista' não se refere ao Data do Nascimento do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[4].ColumnName.ToLower() != "CNH - REGISTRO".ToLower())
                    return BusinessResult.Error("A quinta coluna da pasta 'Motorista' não se refere ao CNH - REGISTRO do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[5].ColumnName.ToLower() != "Categoria CNH".ToLower())
                    return BusinessResult.Error("A sexta coluna da pasta 'Motorista' não se refere ao Categoria CNH do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[6].ColumnName.ToLower() != "Validade CNH".ToLower())
                    return BusinessResult.Error("A sétima coluna da pasta 'Motorista' não se refere a Validade CNH do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[7].ColumnName.ToLower() != "Celular".ToLower())
                    return BusinessResult.Error("A oitava coluna da pasta 'Motorista' não se refere ao Celular do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[8].ColumnName.ToLower() != "CPF".ToLower())
                    return BusinessResult.Error("A nona coluna da pasta 'Motorista' não se refere ao CPF do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[9].ColumnName.ToLower() != "Sexo".ToLower())
                    return BusinessResult.Error("A décima coluna da pasta 'Motorista' não se refere ao Sexo do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[10].ColumnName.ToLower() != "Tipo do Contrato".ToLower())
                    return BusinessResult.Error("A décima primeira coluna da pasta 'Motorista' não se refere ao  Tipo do Contrato do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[11].ColumnName.ToLower() != "Email".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Motorista' não se refere ao Email do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[12].ColumnName.ToLower() != "Nome da Mãe".ToLower())
                    return BusinessResult.Error("A décima terceira coluna da pasta 'Motorista' não se refere ao Nome da Mãe do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[13].ColumnName.ToLower() != "Nome do Pai".ToLower())
                    return BusinessResult.Error("A décima quarta coluna da pasta 'Motorista' não se refere ao Nome do Pai do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[14].ColumnName.ToLower() != "CEP".ToLower())
                    return BusinessResult.Error("A décima quinta coluna da pasta 'Motorista' não se refere ao CEP do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[15].ColumnName.ToLower() != "Logradouro".ToLower())
                    return BusinessResult.Error("A décima sexta coluna da pasta 'Motorista' não se refere ao Logradouro do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[16].ColumnName.ToLower() != "Numero Endereço".ToLower())
                    return BusinessResult.Error("A décima sétima coluna da pasta 'Motorista' não se refere ao  Numero Endereço do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[17].ColumnName.ToLower() != "Bairro".ToLower())
                    return BusinessResult.Error("A décima oitava coluna da pasta 'Motorista' não se refere ao Bairro do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[18].ColumnName.ToLower() != "IBGE Cidade".ToLower())
                    return BusinessResult.Error("A décima nona coluna da pasta 'Motorista' não se refere ao IBGE Cidade do motorista, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[19].ColumnName.ToLower() != "IBGE UF".ToLower())
                    return BusinessResult.Error("A vigésima coluna da pasta 'Motorista' não se refere ao IBGE UF do motorista, realize o download do layout padrão e preencha.");
                
                return BusinessResult.Valid();
            }
            catch (Exception)
            {
                return BusinessResult.Error("Falha ao validar header pasta motorista");
            }
        }
        
        private BusinessResult ValidarHeaderCliente(DataTable sheet)
        {
            try
            {
                if (sheet.Columns[0].ColumnName.ToLower() != "Nome".ToLower())
                    return BusinessResult.Error("A primeira coluna da pasta 'Cliente' não se refere ao Nome do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[1].ColumnName.ToLower() != "Nome Fantasia".ToLower())
                    return BusinessResult.Error("A segunda coluna da pasta 'Cliente' não se refere ao Nome Fantasia do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[2].ColumnName.ToLower() != "CNPJ / CPF".ToLower())
                    return BusinessResult.Error("A terceira coluna da pasta 'Cliente' não se refere ao CNPJ / CPF do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[3].ColumnName.ToLower() != "CEP".ToLower())
                    return BusinessResult.Error("A quarta coluna da pasta 'Cliente' não se refere ao CEP do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[4].ColumnName.ToLower() != "Logradouro".ToLower())
                    return BusinessResult.Error("A quinta coluna da pasta 'Cliente' não se refere ao Logradouro do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[5].ColumnName.ToLower() != "Numero Endereço".ToLower())
                    return BusinessResult.Error("A sexta coluna da pasta 'Cliente' não se refere ao Numero Endereço do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[6].ColumnName.ToLower() != "Bairro".ToLower())
                    return BusinessResult.Error("A sétima coluna da pasta 'Cliente' não se refere ao Bairro do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[7].ColumnName.ToLower() != "IBGE Cidade".ToLower())
                    return BusinessResult.Error("A oitava coluna da pasta 'Cliente' não se refere ao IBGE Cidade do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[8].ColumnName.ToLower() != "IBGE UF".ToLower())
                    return BusinessResult.Error("A nona coluna da pasta 'Cliente' não se refere ao IBGE UF do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[9].ColumnName.ToLower() != "Celular".ToLower())
                    return BusinessResult.Error("A décima coluna da pasta 'Cliente' não se refere ao Celular do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[10].ColumnName.ToLower() != "Email".ToLower())
                    return BusinessResult.Error("A décima primeira coluna da pasta 'Cliente' não se refere ao Email do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[11].ColumnName.ToLower() != "RG".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Cliente' não se refere ao RG do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[12].ColumnName.ToLower() != "Orgão Expedidor".ToLower())
                    return BusinessResult.Error("A décima terceira coluna da pasta 'Cliente' não se refere ao Orgão Expedidor do cliente, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[13].ColumnName.ToLower() != "Inscrição Estadual".ToLower())
                    return BusinessResult.Error("A décima quarta coluna da pasta 'Cliente' não se refere ao Inscrição Estadual do cliente, realize o download do layout padrão e preencha.");
                
                return BusinessResult.Valid();
            }
            catch (Exception)
            {
                return BusinessResult.Error("Falha ao validar header pasta cliente");
            }
        }

        private BusinessResult ValidarHeaderFilial(DataTable sheet)
        {
            try
            {
                if (sheet.Columns[0].ColumnName.ToLower() != "CNPJ".ToLower())
                    return BusinessResult.Error("A primeira coluna da pasta 'Filial' não se refere ao CNPJ da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[1].ColumnName.ToLower() != "Razao Social".ToLower())
                    return BusinessResult.Error("A segunda coluna da pasta 'Filial' não se refere ao Razao Social da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[2].ColumnName.ToLower() != "NomeFantasia".ToLower())
                    return BusinessResult.Error("A terceira coluna da pasta 'Filial' não se refere ao NomeFantasia da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[3].ColumnName.ToLower() != "Sigla".ToLower())
                    return BusinessResult.Error("A quarta coluna da pasta 'Filial' não se refere ao Sigla da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[4].ColumnName.ToLower() != "CEP".ToLower())
                    return BusinessResult.Error("A quinta coluna da pasta 'Filial' não se refere ao CEP da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[5].ColumnName.ToLower() != "Endereco".ToLower())
                    return BusinessResult.Error("A sexta coluna da pasta 'Filial' não se refere ao Endereco da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[6].ColumnName.ToLower() != "Numero".ToLower())
                    return BusinessResult.Error("A sétima coluna da pasta 'Filial' não se refere ao Numero da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[7].ColumnName.ToLower() != "Bairro".ToLower())
                    return BusinessResult.Error("A oitava coluna da pasta 'Filial' não se refere ao Bairro da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[8].ColumnName.ToLower() != "Telefone".ToLower())
                    return BusinessResult.Error("A nona coluna da pasta 'Filial' não se refere ao Telefone da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[9].ColumnName.ToLower() != "IBGE Cidade".ToLower())
                    return BusinessResult.Error("A décima coluna da pasta 'Filial' não se refere ao IBGE Cidade da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[10].ColumnName.ToLower() != "IBGE UF".ToLower())
                    return BusinessResult.Error("A décima primeira coluna da pasta 'Filial' não se refere ao IBGE UF da filial, realize o download do layout padrão e preencha.");

                if (sheet.Columns[11].ColumnName.ToLower() != "Email".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Filial' não se refere ao Email da filial, realize o download do layout padrão e preencha.");
                
                return BusinessResult.Valid();
            }
            catch (Exception)
            {
                return BusinessResult.Error("Falha ao validar header pasta filial");
            }
        }
        
        private BusinessResult ValidarHeaderVeiculo(DataTable sheet)
        {
            try
            {
                if (sheet.Columns[0].ColumnName.ToLower() != "Placa".ToLower())
                    return BusinessResult.Error("A primeira coluna da pasta 'Veículo' não se refere ao Placa do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[1].ColumnName.ToLower() != "Chassi".ToLower())
                    return BusinessResult.Error("A segunda coluna da pasta 'Veículo' não se refere ao Chassi do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[2].ColumnName.ToLower() != "Marca".ToLower())
                    return BusinessResult.Error("A terceira coluna da pasta 'Veículo' não se refere ao Marca do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[3].ColumnName.ToLower() != "Modelo".ToLower())
                    return BusinessResult.Error("A quarta coluna da pasta 'Veículo' não se refere ao Modelo do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[4].ColumnName.ToLower() != "Tipo Veiculo".ToLower())
                    return BusinessResult.Error("A quinta coluna da pasta 'Veículo' não se refere ao Tipo Veiculo do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[5].ColumnName.ToLower() != "Quantidade de Eixos".ToLower())
                    return BusinessResult.Error("A sexta coluna da pasta 'Veículo' não se refere ao Quantidade de Eixos do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[6].ColumnName.ToLower() != "RENAVAM".ToLower())
                    return BusinessResult.Error("A sétima coluna da pasta 'Veículo' não se refere ao RENAVAM do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[7].ColumnName.ToLower() != "Ano de Fabricacao".ToLower())
                    return BusinessResult.Error("A oitava coluna da pasta 'Veículo' não se refere ao Ano de Fabricacao do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[8].ColumnName.ToLower() != "Ano do Modelo".ToLower())
                    return BusinessResult.Error("A nona coluna da pasta 'Veículo' não se refere ao Ano do Modelo do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[9].ColumnName.ToLower() != "IBGE Cidade".ToLower())
                    return BusinessResult.Error("A décima coluna da pasta 'Veículo' não se refere ao IBGE Cidade do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[10].ColumnName.ToLower() != "IBGE UF".ToLower())
                    return BusinessResult.Error("A décima primeira coluna da pasta 'Veículo' não se refere ao IBGE UF do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[11].ColumnName.ToLower() != "CNPJ/CPF Proprietário".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao CNPJ/CPF Proprietário do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[12].ColumnName.ToLower() != "CPFMotorista".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao CNPJ/CPF CPFMotorista do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[13].ColumnName.ToLower() != "CNPJFilial".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao CNPJFilial do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[14].ColumnName.ToLower() != "NumeroFrota".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao NumeroFrota do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[15].ColumnName.ToLower() != "CodigoOperacao".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao Codigo Operacao do veículo, realize o download do layout padrão e preencha.");

                if (sheet.Columns[16].ColumnName.ToLower() != "MUNICIPIO".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao MUNICIPIO do veículo, realize o download do layout padrão e preencha.");
                
                if (sheet.Columns[17].ColumnName.ToLower() != "Tipo Contrato".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao Tipo Contrato do veículo, realize o download do layout padrão e preencha.");

                if (sheet.Columns[18].ColumnName.ToLower() != "Tipo Rodagem".ToLower())
                    return BusinessResult.Error("A décima segunda coluna da pasta 'Veículo' não se refere ao Tipo Rodagem do veículo, realize o download do layout padrão e preencha.");

                return BusinessResult.Valid();
            }
            catch (Exception)
            {
                return BusinessResult.Error("Falha ao validar header pasta veículo");
            }
        }

        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarPastas(DataSet dataSet)
        {
            var result = new List<ValidacaoImportacaoDadosItem>();
            var sheets = dataSet.Tables;
            var anyRegistro = false;
            
            foreach (DataTable sheet in sheets)
            {
                var linha = 0;

                if (sheet.TableName.ToLower() == "Veículo".ToLower())
                {
                    foreach (DataRow row in sheet.Rows)
                    {
                        anyRegistro = true;
                        linha += 1;
                        var resultLinha = ValidarLinhaVeiculo(row,linha);
                        
                        if(!resultLinha.Success)
                            result.Add(new ValidacaoImportacaoDadosItem()
                            {
                                Pasta = "Veículo",
                                Linha = linha,
                                Observacao = resultLinha.Messages.FirstOrDefault()
                            });

                        if (resultLinha.Value.Any())
                            result.AddRange(resultLinha.Value);
                    }
                }

                if (sheet.TableName.ToLower() == "Motorista".ToLower())
                {
                    foreach (DataRow row in sheet.Rows)
                    {
                        anyRegistro = true;
                        linha += 1;
                        var resultLinha = ValidarLinhaMotorista(row,linha);
                        
                        if(!resultLinha.Success)
                            result.Add(new ValidacaoImportacaoDadosItem()
                            {
                                Pasta = "Motorista",
                                Linha = linha,
                                Observacao = resultLinha.Messages.FirstOrDefault()
                            });

                        if (resultLinha.Value.Any())
                            result.AddRange(resultLinha.Value);
                    }
                }

                if (sheet.TableName.ToLower() == "Proprietário".ToLower())
                {
                    foreach (DataRow row in sheet.Rows)
                    {
                        anyRegistro = true;
                        linha += 1;
                        var resultLinha = ValidarLinhaProprietario(row,linha);
                        
                        if(!resultLinha.Success)
                            result.Add(new ValidacaoImportacaoDadosItem()
                            {
                                Pasta = "Proprietário",
                                Linha = linha,
                                Observacao = resultLinha.Messages.FirstOrDefault()
                            });

                        if (resultLinha.Value.Any())
                            result.AddRange(resultLinha.Value);
                    }
                }

                if (sheet.TableName.ToLower() == "Cliente".ToLower())
                {
                    foreach (DataRow row in sheet.Rows)
                    {
                        anyRegistro = true;
                        linha += 1;
                        var resultLinha = ValidarLinhaCliente(row,linha);
                        
                        if(!resultLinha.Success)
                            result.Add(new ValidacaoImportacaoDadosItem()
                            {
                                Pasta = "Cliente",
                                Linha = linha,
                                Observacao = resultLinha.Messages.FirstOrDefault()
                            });

                        if (resultLinha.Value.Any())
                            result.AddRange(resultLinha.Value);
                    }
                }

                if (sheet.TableName.ToLower() == "Filial".ToLower())
                {
                    foreach (DataRow row in sheet.Rows)
                    {
                        anyRegistro = true;
                        linha += 1;
                        var resultLinha = ValidarLinhaFilial(row,linha);
                        
                        if(!resultLinha.Success)
                            result.Add(new ValidacaoImportacaoDadosItem()
                            {
                                Pasta = "Filial",
                                Linha = linha,
                                Observacao = resultLinha.Messages.FirstOrDefault()
                            });

                        if (resultLinha.Value.Any())
                            result.AddRange(resultLinha.Value);
                    }
                }
            }

            if(!anyRegistro)
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error("A planilha não possui nenhum registro para cadastro!");

            return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);

        }

        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarLinhaVeiculo(DataRow row,int linha)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();
                var placa = row.ItemArray[0].ToString();

                if (string.IsNullOrWhiteSpace(placa))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Placa",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!Veiculo.PlacaValida(placa))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Placa",
                            Linha = linha,
                            Pasta = "Veículo",
                            Observacao = $"Placa {placa} é inválida."
                        });
                    }
                }
                
                var chassi = row.ItemArray[1].ToString();

                if (!string.IsNullOrWhiteSpace(chassi) && chassi.Length > 22)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Chassi",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Comprimento excede tamanho máximo de 22 carcateres."
                    });
                }
                
               
                var marca = row.ItemArray[2].ToString();

                if (!string.IsNullOrWhiteSpace(marca) && marca.Length > 50)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Marca",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Comprimento excede tamanho máximo de 50 carcateres."
                    });
                }
                
                var modelo = row.ItemArray[3].ToString();

                if (!string.IsNullOrWhiteSpace(modelo) && modelo.Length > 50)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Modelo",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Comprimento excede tamanho máximo de 50 carcateres."
                    });
                }
                
                
                var tipoVeiculo = row.ItemArray[4].ToString();
                    
                if (string.IsNullOrWhiteSpace(tipoVeiculo) || (tipoVeiculo.ToLower() != "cavalo" && tipoVeiculo.ToLower() != "carreta"))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Tipo veículo",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Campo inválido."
                    });
                }
               
                var qtdsEixos = row.ItemArray[5].ToString();
                
                if (string.IsNullOrWhiteSpace(qtdsEixos))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Quantidade de eixos",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    var isValid = !int.TryParse(qtdsEixos, out _);

                    if (isValid)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Quantidade de eixos",
                            Linha = linha,
                            Pasta = "Veículo",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var renavam = row.ItemArray[6].ToString();

                if (!string.IsNullOrWhiteSpace(renavam) && renavam.Length > 11)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Renavam",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Comprimento excede tamanho máximo de 11 carcateres."
                    });
                }
                
                var anoFabricao = row.ItemArray[7].ToString();

                if (!string.IsNullOrWhiteSpace(anoFabricao) && !int.TryParse(anoFabricao, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ano de Fabricação",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }

                var anoModelo = row.ItemArray[8].ToString();

                if (!string.IsNullOrWhiteSpace(anoModelo) && !int.TryParse(anoModelo, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ano Modelo",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var ibgeCidade = row.ItemArray[9].ToString();

                if (!string.IsNullOrWhiteSpace(ibgeCidade) && !int.TryParse(ibgeCidade, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge Cidade",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var ibgeuF = row.ItemArray[10].ToString();

                if (!string.IsNullOrWhiteSpace(ibgeuF) && !int.TryParse(ibgeuF, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge UF",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var cpfCnpjProprietario = row.ItemArray[11].ToString();
                var isValidCpfCnpjProprietario = cpfCnpjProprietario.Count() > 13
                    ? cpfCnpjProprietario.IsValidCNPJ()
                    : cpfCnpjProprietario.IsValidCPF();

                if (!string.IsNullOrWhiteSpace(cpfCnpjProprietario) && !isValidCpfCnpjProprietario)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Cpf/Cnpj Proprietário",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var cpfMotorista = row.ItemArray[12].ToString();

                if (!string.IsNullOrWhiteSpace(cpfMotorista) && !cpfMotorista.IsValidCPF())
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CPF Motorista",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var cnpjFilial = row.ItemArray[13].ToString();

                if (!string.IsNullOrWhiteSpace(cnpjFilial) && !cnpjFilial.IsValidCNPJ())
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Cnpj Filial",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
               
                var numeroFrota = row.ItemArray[14].ToString();

                if (!string.IsNullOrWhiteSpace(numeroFrota) && !long.TryParse(numeroFrota, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Numero Frota",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
                
                var idOperacao = row.ItemArray[15].ToString();

                if (!string.IsNullOrWhiteSpace(idOperacao) && !int.TryParse(idOperacao, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Codigo Operação",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = "Campo inválido"
                    });
                }
             
                
                var municipio = row.ItemArray[16].ToString();

                if (!string.IsNullOrWhiteSpace(municipio) && municipio.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Municipio",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var tipoContrato = row.ItemArray[17].ToString();

                if (string.IsNullOrWhiteSpace(tipoContrato) || (tipoContrato.ToLower() != "f" && tipoContrato.ToLower() != "c" && 
                                                                tipoContrato.ToLower() != "a" && tipoContrato.ToLower() != "t"))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Tipo Contrato",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Campo inválido."
                    });
                }
                
                var tipoRodagem = row.ItemArray[18].ToString();
                    
                if (string.IsNullOrWhiteSpace(tipoRodagem) || (tipoRodagem.ToLower() != "s" && tipoRodagem.ToLower() != "d"))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Tipo Rodagem",
                        Linha = linha,
                        Pasta = "Veículo",
                        Observacao = $"Campo inválido."
                    });
                }
                
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error($"Falha ao validar linha {linha}: {e.Message}");
            }
        }
        
        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarLinhaMotorista(DataRow row,int linha)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();

                var nome = row.ItemArray[0].ToString();

                if (string.IsNullOrWhiteSpace(nome))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (nome.Length > 150)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Nome",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 150 carcateres."
                        });
                    }
                }
                
                var rg = row.ItemArray[1].ToString();

                if (string.IsNullOrWhiteSpace(rg))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "RG",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (rg.Length > 20)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "RG",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 20 carcateres."
                        });
                    }
                }

                var expedidor = row.ItemArray[2].ToString();

                if (string.IsNullOrWhiteSpace(expedidor))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Orgão Expedidor",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (expedidor.Length > 10)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Orgão Expedidor",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 10 carcateres."
                        });
                    }
                }
                
                var dataNascimento = row.ItemArray[3].ToString();

                if (!string.IsNullOrWhiteSpace(dataNascimento) && !DateTime.TryParse(dataNascimento,out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Data de Nascimento",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo inválido"
                    });
                }
                
                var cnh = row.ItemArray[4].ToString();

                if (string.IsNullOrWhiteSpace(cnh))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CNH",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cnh.Length > 14)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CNH",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 14 carcateres."
                        });
                    }
                }
                
                var cnhCategoria = row.ItemArray[5].ToString();

                if (string.IsNullOrWhiteSpace(cnhCategoria))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CNH Categoria",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cnhCategoria.Length > 2)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CNH Categoria",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 2 carcateres."
                        });
                    }
                }
                
                var dataValidadeCnh = row.ItemArray[6].ToString();

                if (!string.IsNullOrWhiteSpace(dataValidadeCnh) && !DateTime.TryParse(dataValidadeCnh,out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Data de validade CNH",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo inválido"
                    });
                }
                
                var celular = row.ItemArray[7].ToString();

                if (!string.IsNullOrWhiteSpace(celular) && celular.Length > 11 || celular.Length < 10)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Celular invalido",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo inválido"
                    });
                }
                
                var cpf = row.ItemArray[8].ToString();

                if (string.IsNullOrWhiteSpace(cpf))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CPF",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    var isValidCpf = cpf.IsValidCPF();

                    if (!isValidCpf)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CPF",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var sexo = row.ItemArray[9].ToString();

                if (string.IsNullOrWhiteSpace(sexo))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Sexo",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (sexo.Length > 1)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Sexo",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 1 carcateres."
                        });
                    }
                }
                
                var tipoContrato = row.ItemArray[10].ToString();

                if (string.IsNullOrWhiteSpace(tipoContrato))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Tipo de Contrato",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (int.TryParse(tipoContrato,out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Tipo de Contrato",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Campo inválido."
                        });
                    }
                }
                
                var email = row.ItemArray[11].ToString();

                if (!string.IsNullOrWhiteSpace(email) && email.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                else if (!EmailHelper.IsValidEmail(email))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo inválido"
                    });
                }
                
                var nomeMae = row.ItemArray[12].ToString();

                if (!string.IsNullOrWhiteSpace(nomeMae) && nomeMae.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome da mãe",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var nomePai = row.ItemArray[13].ToString();

                if (!string.IsNullOrWhiteSpace(nomePai) && nomePai.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome do pai",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var cep = row.ItemArray[14].ToString();

                if (string.IsNullOrWhiteSpace(cep))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CEP",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cep.Length > 8)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CEP",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 8 carcateres."
                        });
                    }
                }

                var logradouro = row.ItemArray[15].ToString();

                if (string.IsNullOrWhiteSpace(logradouro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Logradouro",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (logradouro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Logradouro",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var numero = row.ItemArray[16].ToString();

                if (!string.IsNullOrWhiteSpace(numero) && !int.TryParse(numero, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Numero",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo inválido"
                    });
                }
                
                var bairro = row.ItemArray[17].ToString();

                if (string.IsNullOrWhiteSpace(bairro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Bairro",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (bairro.Length > 50)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Bairro",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = $"Comprimento excede tamanho máximo de 50 carcateres."
                        });
                    }
                }
                
                var ibgeCidade = row.ItemArray[18].ToString();
            
                if (string.IsNullOrWhiteSpace(ibgeCidade))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge Cidade",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeCidade, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge Cidade",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = "Campo inválido"
                        });
                    }
                }
            
                var ibgeuF = row.ItemArray[19].ToString();

                if (string.IsNullOrWhiteSpace(ibgeuF))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge UF",
                        Linha = linha,
                        Pasta = "Motorista",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeuF, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge UF",
                            Linha = linha,
                            Pasta = "Motorista",
                            Observacao = "Campo inválido"
                        });
                    }
                }

                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error($"Falha ao validar linha {linha}: {e.Message}");
            }
        }
        
        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarLinhaProprietario(DataRow row,int linha)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();
                var cpfCnpjProprietario = row.ItemArray[0].ToString();

                if (string.IsNullOrWhiteSpace(cpfCnpjProprietario))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Cpf / Cnpj Proprietario",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    var isValidCpfCnpjProprietario = cpfCnpjProprietario.Count() > 13
                        ? cpfCnpjProprietario.IsValidCNPJ()
                        : cpfCnpjProprietario.IsValidCPF();

                    if (!isValidCpfCnpjProprietario)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Cpf / Cnpj Proprietario",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = "Campo inválido"
                        });
                    }
                }
               
                var razaoSocial = row.ItemArray[1].ToString();

                if (string.IsNullOrWhiteSpace(razaoSocial))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Razão Social",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (razaoSocial.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Razão Social",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
            
                var nomeFantasia = row.ItemArray[2].ToString();

                if (string.IsNullOrWhiteSpace(nomeFantasia))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome Fantasia",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (nomeFantasia.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Nome Fantasia",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var rntrc = row.ItemArray[3].ToString();

                if (!string.IsNullOrWhiteSpace(rntrc) && rntrc.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "RNTRC",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var cep = row.ItemArray[4].ToString();

                if (string.IsNullOrWhiteSpace(cep))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CEP",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cep.Length > 8)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CEP",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = $"Comprimento excede tamanho máximo de 8 carcateres."
                        });
                    }
                }

                var logradouro = row.ItemArray[5].ToString();

                if (string.IsNullOrWhiteSpace(logradouro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Logradouro",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (logradouro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Logradouro",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
              
                var numero = row.ItemArray[6].ToString();

                if (!string.IsNullOrWhiteSpace(numero) && !int.TryParse(numero, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Numero",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }
            
                var bairro = row.ItemArray[7].ToString();

                if (string.IsNullOrWhiteSpace(bairro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Bairro",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (bairro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Bairro",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
               
                var ibgeCidade = row.ItemArray[8].ToString();
            
                if (string.IsNullOrWhiteSpace(ibgeCidade))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge Cidade",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeCidade, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge Cidade",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = "Campo inválido"
                        });
                    }
                }
            
                var ibgeuF = row.ItemArray[9].ToString();

                if (string.IsNullOrWhiteSpace(ibgeuF))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge UF",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeuF, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge UF",
                            Linha = linha,
                            Pasta = "Proprietário",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var dataNascimento = row.ItemArray[10].ToString();

                if (!string.IsNullOrWhiteSpace(dataNascimento) && !DateTime.TryParse(dataNascimento,out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Data de Nascimento",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }
                
                var nomePai = row.ItemArray[11].ToString();

                if (!string.IsNullOrWhiteSpace(nomePai) && nomePai.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome do pai",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var nomeMae = row.ItemArray[12].ToString();

                if (!string.IsNullOrWhiteSpace(nomeMae) && nomeMae.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome da mãe",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var rg = row.ItemArray[13].ToString();

                if (!string.IsNullOrWhiteSpace(rg) && rg.Length > 20)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "RG",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 20 carcateres."
                    });
                }
                
                var orgaoExpedidor = row.ItemArray[14].ToString();

                if (!string.IsNullOrWhiteSpace(orgaoExpedidor) && orgaoExpedidor.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Orgão expedidor",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var ie = row.ItemArray[15].ToString();

                if (!string.IsNullOrWhiteSpace(ie) && ie.Length > 15)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "IE",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 15 carcateres."
                    });
                }
                
                var email = row.ItemArray[16].ToString();

                if (!string.IsNullOrWhiteSpace(email) && email.Length > 200)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 200 carcateres."
                    });
                }
                else if (!EmailHelper.IsValidEmail(email))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }
                
                var telefone = row.ItemArray[17].ToString();

                if (!string.IsNullOrWhiteSpace(telefone) && telefone.Length > 11 || telefone.Length < 10)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Telefone",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }
                
                var celular = row.ItemArray[18].ToString();

                if (!string.IsNullOrWhiteSpace(celular) && celular.Length > 11 || celular.Length < 10)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Celular",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }

                var inss = row.ItemArray[19].ToString();

                if (!string.IsNullOrWhiteSpace(inss) && inss.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "INSS",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
                
                var tipoContrato = row.ItemArray[20].ToString();

                if (string.IsNullOrWhiteSpace(tipoContrato) || (tipoContrato.ToLower() != "f" && tipoContrato.ToLower() != "c" && 
                                                                tipoContrato.ToLower() != "a" && tipoContrato.ToLower() != "t"))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Tipo Contrato",
                        Linha = linha,
                        Pasta = "Proprietário",
                        Observacao = "Campo inválido"
                    });
                }
                
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error($"Falha ao validar linha {linha}: {e.Message}");
            }
        }
        
        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarLinhaCliente(DataRow row,int linha)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();
                
                var nome = row.ItemArray[0].ToString();

                if (string.IsNullOrWhiteSpace(nome))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (nome.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Nome",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var nomeFantasia = row.ItemArray[1].ToString();

                if (!string.IsNullOrWhiteSpace(nomeFantasia) && nomeFantasia.Length > 100)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome Fantasia",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                    });
                }
               
                var cnpjCpfCliente = row.ItemArray[2].ToString();

                if (string.IsNullOrWhiteSpace(cnpjCpfCliente))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CNPJ / CPF Cliente",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    var isValidCnpjCpfCliente = cnpjCpfCliente.Count() > 13
                        ? cnpjCpfCliente.IsValidCNPJ()
                        : cnpjCpfCliente.IsValidCPF();

                    if (!isValidCnpjCpfCliente)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CNPJ / CPF Cliente",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var cep = row.ItemArray[3].ToString();

                if (string.IsNullOrWhiteSpace(cep))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CEP",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cep.Length > 8)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CEP",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = $"Comprimento excede tamanho máximo de 8 carcateres."
                        });
                    }
                }
                
                var logradouro = row.ItemArray[4].ToString();

                if (string.IsNullOrWhiteSpace(logradouro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Logradouro",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (logradouro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Logradouro",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var numero = row.ItemArray[5].ToString();

                if (!string.IsNullOrWhiteSpace(numero) && !int.TryParse(numero, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Numero",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo inválido"
                    });
                }
                
                var bairro = row.ItemArray[6].ToString();

                if (string.IsNullOrWhiteSpace(bairro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Bairro",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (bairro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Bairro",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var ibgeCidade = row.ItemArray[7].ToString();
                
                if (string.IsNullOrWhiteSpace(ibgeCidade))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge Cidade",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeCidade, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge Cidade",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var ibgeuF = row.ItemArray[8].ToString();

                if (string.IsNullOrWhiteSpace(ibgeuF))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge UF",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeuF, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge UF",
                            Linha = linha,
                            Pasta = "Cliente",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var celular = row.ItemArray[9].ToString();

                if (!string.IsNullOrWhiteSpace(celular) && celular.Length > 11 || celular.Length < 10)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Celular",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo inválido"
                    });
                }
                
                var email = row.ItemArray[10].ToString();

                if (!string.IsNullOrWhiteSpace(email) && email.Length > 150)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = $"Comprimento excede tamanho máximo de 150 carcateres."
                    });
                }
                else if (!EmailHelper.IsValidEmail(email))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = "Campo inválido"
                    });
                }
                
                var rg = row.ItemArray[11].ToString();

                if (!string.IsNullOrWhiteSpace(rg) && rg.Length > 15)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "RG",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = $"Comprimento excede tamanho máximo de 15 carcateres."
                    });
                }
                
                var orgaoExpedidor = row.ItemArray[12].ToString();

                if (!string.IsNullOrWhiteSpace(orgaoExpedidor) && orgaoExpedidor.Length > 10)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Orgão Expedidor",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = $"Comprimento excede tamanho máximo de 10 carcateres."
                    });
                }
                
                var ie = row.ItemArray[13].ToString();

                if (!string.IsNullOrWhiteSpace(ie) && ie.Length > 15)
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "IE",
                        Linha = linha,
                        Pasta = "Cliente",
                        Observacao = $"Comprimento excede tamanho máximo de 15 carcateres."
                    });
                }
                
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error($"Falha ao validar linha {linha}: {e.Message}");
            }
        }
        
        private BusinessResult<List<ValidacaoImportacaoDadosItem>> ValidarLinhaFilial(DataRow row,int linha)
        {
            try
            {
                var result = new List<ValidacaoImportacaoDadosItem>();
                
                var cnpjFilial = row.ItemArray[0].ToString();

                if (string.IsNullOrWhiteSpace(cnpjFilial))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CNPJ Filial",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!cnpjFilial.IsValidCNPJ())
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CNPJ Filial",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = "Campo inválido"
                        });
                    }
                }
               
                var razaoSocial = row.ItemArray[1].ToString();

                if (string.IsNullOrWhiteSpace(razaoSocial))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Razão Social",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (razaoSocial.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Razão Social",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var nomeFantasia = row.ItemArray[2].ToString();

                if (string.IsNullOrWhiteSpace(nomeFantasia))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Nome Fantasia",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (nomeFantasia.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Nome Fantasia",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var sigla = row.ItemArray[3].ToString();

                if (string.IsNullOrWhiteSpace(sigla))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Sigla",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (sigla.Length > 10)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Sigla",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 10 carcateres."
                        });
                    }
                }
                
                
                var cep = row.ItemArray[4].ToString();

                if (string.IsNullOrWhiteSpace(cep))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "CEP",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (cep.Length > 8)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "CEP",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 8 carcateres."
                        });
                    }
                }
                
                var endereco = row.ItemArray[5].ToString();

                if (string.IsNullOrWhiteSpace(endereco))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Endereço",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (endereco.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Endereço",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                
                var numero = row.ItemArray[6].ToString();

                if (!string.IsNullOrWhiteSpace(numero) && !int.TryParse(numero, out _))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Numero",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo inválido"
                    });
                }
                
                var bairro = row.ItemArray[7].ToString();

                if (string.IsNullOrWhiteSpace(bairro))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Bairro",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (bairro.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Bairro",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                }
                

                var telefone = row.ItemArray[8].ToString();

                if (string.IsNullOrWhiteSpace(telefone))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Telefone",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (telefone.Length > 11 || telefone.Length < 10)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Telefone",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 11 carcateres."
                        });
                    }
                }
                
                var ibgeCidade = row.ItemArray[9].ToString();
                
                if (string.IsNullOrWhiteSpace(ibgeCidade))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge Cidade",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeCidade, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge Cidade",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var ibgeuF = row.ItemArray[10].ToString();

                if (string.IsNullOrWhiteSpace(ibgeuF))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Ibge UF",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (!int.TryParse(ibgeuF, out _))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Ibge UF",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                var email = row.ItemArray[11].ToString();

                if (string.IsNullOrWhiteSpace(email))
                {
                    result.Add(new ValidacaoImportacaoDadosItem()
                    {
                        Coluna = "Email",
                        Linha = linha,
                        Pasta = "Filial",
                        Observacao = "Campo obrigatório"
                    });
                }
                else
                {
                    if (email.Length > 100)
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Email",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = $"Comprimento excede tamanho máximo de 100 carcateres."
                        });
                    }
                    else if (!EmailHelper.IsValidEmail(email))
                    {
                        result.Add(new ValidacaoImportacaoDadosItem()
                        {
                            Coluna = "Email",
                            Linha = linha,
                            Pasta = "Filial",
                            Observacao = "Campo inválido"
                        });
                    }
                }
                
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<List<ValidacaoImportacaoDadosItem>>.Error($"Falha ao validar linha {linha}: {e.Message}");
            }
        }
        
        private BusinessResult<VeiculoIntegrarFilaEvent> ImportarLinhaVeiculo(DataRow row,string empresaCnpj,string token)
        {
            try
            {
                var result = new VeiculoIntegrarFilaEvent
                {
                    Placa = row.ItemArray[0].ToString(),
                    Chassi = row.ItemArray[1].ToString(),
                    Marca = row.ItemArray[2].ToString(),
                    Modelo = row.ItemArray[3].ToString(),
                    QuantidadeEixos = row.ItemArray[5].ToIntSafe(),
                    RENAVAM = row.ItemArray[6].ToString(),
                    AnoFabricacao = row.ItemArray[7].ToIntSafe(),
                    AnoModelo = row.ItemArray[8].ToIntSafe(),
                    IBGECidade = row.ItemArray[9].ToIntSafe(),
                    IBGEEstado = row.ItemArray[10].ToIntSafe(),
                    CPFCNPJProprietario = row.ItemArray[11].ToString(),
                    CPFMotorista = row.ItemArray[12].ToString(),
                    CNPJFilial = row.ItemArray[13].ToString(),
                    NumeroFrota = row.ItemArray[14].ToIntSafe(),
                    CodigoDaOperacao = row.ItemArray[15].ToIntSafe(),
                    Municipio = row.ItemArray[16].ToString(),
                    CNPJEmpresa = empresaCnpj,
                    Token = token
                };
                
                var tipoContrato = row.ItemArray[17].ToString().ToLower();

                if (tipoContrato == "f")
                    result.TipoContrato = ETipoContratoFilaEvent.Frota;
                else if (tipoContrato == "c")
                    result.TipoContrato = ETipoContratoFilaEvent.Cooperado;
                else if (tipoContrato == "a")
                    result.TipoContrato = ETipoContratoFilaEvent.Agregado;
                else if (tipoContrato == "t")
                    result.TipoContrato = ETipoContratoFilaEvent.Terceiro;
                
                var tipoRodagem = row.ItemArray[18].ToString();
                    
                if (tipoRodagem == "s")
                    result.TipoRodagem = ETipoRodagemFilaEvent.Simples;
                else if (tipoRodagem == "d")
                    result.TipoRodagem = ETipoRodagemFilaEvent.Duplo;
                
                var tipoVeiculo = row.ItemArray[4].ToString();

                result.ComTracao = tipoVeiculo.ToLower() == "cavalo";

                if (tipoVeiculo.ToLower() == "cavalo")
                    result.IdTipoCavalo = _tipoCavaloRepository.FirstOrDefault()?.IdTipoCavalo;
                else if (tipoVeiculo.ToLower() == "carreta")
                    result.IdTipoCarreta = _tipoCarretaRepository.FirstOrDefault()?.IdTipoCarreta;
                
                return BusinessResult<VeiculoIntegrarFilaEvent>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<VeiculoIntegrarFilaEvent>.Error(e.Message);
            }
        }
        
        private BusinessResult<MotoristaIntegrarFilaEvent> ImportarLinhaMotorista(DataRow row,string empresaCnpj,string token)
        {
            try
            {
                var result = new MotoristaIntegrarFilaEvent
                {
                    Nome = row.ItemArray[0].ToString(),
                    Rg = row.ItemArray[1].ToString(),
                    RgOrgaoExpedidor = row.ItemArray[2].ToString(),
                    DataNascimento = string.IsNullOrEmpty(row.ItemArray[3].ToString()) 
                        ? null 
                        : row.ItemArray[3].ToDateTimeSafe(),
                    Cnh = row.ItemArray[4].ToString(),
                    CnhCategoria = row.ItemArray[5].ToString(),
                    ValidadeCnh =  string.IsNullOrEmpty(row.ItemArray[6].ToString()) 
                        ? null 
                        : row.ItemArray[6].ToDateTimeSafe(),
                    Celular = row.ItemArray[7].ToString(),
                    Cpf = row.ItemArray[8].ToString(),
                    Sexo = row.ItemArray[9].ToString(),
                    Email = row.ItemArray[11].ToString(),
                    NomeMae = row.ItemArray[12].ToString(),
                    NomePai = row.ItemArray[13].ToString(),
                    Cep = row.ItemArray[14].ToString(),
                    Endereco = row.ItemArray[15].ToString(),
                    Numero = row.ItemArray[16].ToString(),
                    Bairro = row.ItemArray[17].ToString(),
                    IbgeCidade = row.ItemArray[18].ToIntSafe() ?? 0,
                    IbgeEstado = row.ItemArray[19].ToIntSafe() ?? 0,
                    CNPJEmpresa = empresaCnpj,
                    Token = token
                };
                
                var tipoContrato = row.ItemArray[10].ToString().ToLower();

                if (tipoContrato == "f")
                    result.TipoContrato = ETipoContratoFilaEvent.Frota;
                else if (tipoContrato == "c")
                    result.TipoContrato = ETipoContratoFilaEvent.Cooperado;
                else if (tipoContrato == "a")
                    result.TipoContrato = ETipoContratoFilaEvent.Agregado;
                else if (tipoContrato == "t")
                    result.TipoContrato = ETipoContratoFilaEvent.Terceiro;

                return BusinessResult<MotoristaIntegrarFilaEvent>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<MotoristaIntegrarFilaEvent>.Error(e.Message);
            }
        }
        
        private BusinessResult<ProprietarioIntegrarFilaEvent> ImportarLinhaProprietario(DataRow row,string empresaCnpj,string token)
        {
            try
            {
                var result = new ProprietarioIntegrarFilaEvent
                {
                    CnpjCpf = row.ItemArray[0].ToString(),
                    RazaoSocial = row.ItemArray[1].ToString(),
                    NomeFantasia = row.ItemArray[2].ToString(),
                    RNTRC = row.ItemArray[3].ToString(),
                    Endereco = row.ItemArray[5].ToString(),
                    Enderecos = new List<ProprietarioEnderecoIntegrarFilaEvent>()
                    {
                        new()
                        {
                            CEP = row.ItemArray[4].ToString(),
                            Endereco = row.ItemArray[5].ToString(),
                            Numero = row.ItemArray[6].ToIntSafe(),
                            Bairro = row.ItemArray[7].ToString(),
                            IBGECidade = row.ItemArray[8].ToIntSafe() ?? 0,
                            IBGEEstado = row.ItemArray[9].ToIntSafe() ?? 0
                        }
                    },
                    DataNascimento =  string.IsNullOrEmpty(row.ItemArray[10].ToString()) 
                        ? null 
                        : row.ItemArray[10].ToDateTimeSafe(),
                    NomePai = row.ItemArray[11].ToString(),
                    NomeMae = row.ItemArray[12].ToString(),
                    RG = row.ItemArray[13].ToString(),
                    RGOrgaoExpedidor = row.ItemArray[14].ToString(),
                    IE = row.ItemArray[15].ToString(),
                    Contatos = new List<ProprietarioContatoIntegrarFilaEvent>()
                    {
                        new()
                        {
                            Email = row.ItemArray[16].ToString(),
                            Telefone = row.ItemArray[17].ToString(),
                            Celular = row.ItemArray[18].ToString()
                        }
                    },
                    Inss = row.ItemArray[19].ToString(),
                    CNPJEmpresa = empresaCnpj,
                    Token = token
                };
                
                var tipoContrato = row.ItemArray[20].ToString().ToLower();

                if (tipoContrato == "f")
                    result.TipoContrato = ETipoContratoFilaEvent.Frota;
                else if (tipoContrato == "c")
                    result.TipoContrato = ETipoContratoFilaEvent.Cooperado;
                else if (tipoContrato == "a")
                    result.TipoContrato = ETipoContratoFilaEvent.Agregado;
                else if (tipoContrato == "t")
                    result.TipoContrato = ETipoContratoFilaEvent.Terceiro;
                
                return BusinessResult<ProprietarioIntegrarFilaEvent>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<ProprietarioIntegrarFilaEvent>.Error(e.Message);
            }
        }
        
        private BusinessResult<ClienteIntegrarFilaEvent> ImportarLinhaCliente(DataRow row,string empresaCnpj,string token)
        {
            try
            {
                var result = new ClienteIntegrarFilaEvent
                {
                    RazaoSocial = row.ItemArray[0].ToString(),
                    NomeFantasia = row.ItemArray[1].ToString(),
                    CNPJCPF = row.ItemArray[2].ToString(),
                    CEP    = row.ItemArray[3].ToString(),
                    Endereco = row.ItemArray[4].ToString(),
                    Numero = row.ItemArray[5].ToIntSafe(),
                    Bairro = row.ItemArray[6].ToString(),
                    IBGECidade  = row.ItemArray[7].ToIntSafe() ?? 0,
                    IBGEEstado = row.ItemArray[8].ToIntSafe() ?? 0,
                    Celular =  row.ItemArray[9].ToString(),
                    Email = row.ItemArray[10].ToString(),
                    RG = row.ItemArray[11].ToString(),
                    OrgaoExpedidorRG = row.ItemArray[12].ToString(),
                    IE = row.ItemArray[13].ToString(),
                    CNPJEmpresa = empresaCnpj,
                    Token = token,
                    TipoPessoa = row.ItemArray[2].ToString().Length > 11 
                        ? ETipoPessoaFilaEvent.Juridica 
                        : ETipoPessoaFilaEvent.Fisica
                };
                
                return BusinessResult<ClienteIntegrarFilaEvent>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<ClienteIntegrarFilaEvent>.Error(e.Message);
            }
        }
        
        private BusinessResult<FilialIntegrarFilaEvent> ImportarLinhaFilial(DataRow row,string empresaCnpj,string token)
        {
            try
            {
                var result = new FilialIntegrarFilaEvent
                {
                    Cnpj = row.ItemArray[0].ToString(),
                    RazaoSocial = row.ItemArray[1].ToString(),
                    NomeFantasia = row.ItemArray[2].ToString(),
                    Sigla = row.ItemArray[3].ToString(),
                    Cep = row.ItemArray[4].ToString(),
                    Endereco = row.ItemArray[5].ToString(),
                    Numero = row.ItemArray[6].ToShortSafe(),
                    Bairro = row.ItemArray[7].ToString(),
                    Telefone = row.ItemArray[8].ToString(),
                    CodigoIbgeCidade = row.ItemArray[9].ToIntSafe() ?? 0,
                    CodigoIbgeEstado = row.ItemArray[10].ToIntSafe() ?? 0,
                    Email =  row.ItemArray[11].ToString(),
                    CNPJEmpresa = empresaCnpj,
                    Token = token,
                    CodigoBacenPais = 1058
                };
                
                return BusinessResult<FilialIntegrarFilaEvent>.Valid(result);
            }
            catch (Exception e)
            {
                return BusinessResult<FilialIntegrarFilaEvent>.Error(e.Message);
            }
        }
    }
}
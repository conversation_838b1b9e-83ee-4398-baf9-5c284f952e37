﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Viagem.ConsultaViagem
{
    public class RelatorioConsultaViagem
    {
        public byte[] GetReport(string tipo, RelatorioConsultaViagemDataType dadosRelatorio, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaViagem"
                });

                var path = ReportUtils.CreateLogo(logo);

                var parametros = new List<ReportParameter>();
                parametros.Add(new ReportParameter("Logo", "file:///" + path));

                localReport.EnableExternalImages = true;
                if(tipo == "pdf")
                    localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Viagem.ConsultaViagem.RelatorioConsultaViagemPdf.rdlc";
                else
                    localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Viagem.ConsultaViagem.RelatorioConsultaViagemExcel.rdlc";

                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}

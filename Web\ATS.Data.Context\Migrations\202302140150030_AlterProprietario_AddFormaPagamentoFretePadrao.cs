﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AlterProprietario_AddFormaPagamentoFretePadrao : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.PROPRIETARIO", "formapagamentofretepadrao", c => c.Int(nullable: false, defaultValueSql: "1"));
            AddColumn("dbo.PROPRIETARIO", "chavepixpadrao", c => c.String(maxLength: 32, unicode: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.PROPRIETARIO", "chavepixpadrao");
            DropColumn("dbo.PROPRIETARIO", "formapagamentofretepadrao");
        }
    }
}

﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioFilialRepository : Repository<UsuarioFilial>, IUsuarioFilialRepository
    {
        public UsuarioFilialRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna todos as filiais de um determinado usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public IQueryable<UsuarioFilial> GetFiliaisPorIdUsuario(int? idUsuario)
        {
            return (from usuariofilial in All().Include(x => x.Filial).Include(x => x.Usuario)
                 where (idUsuario == null || usuariofilial.IdUsuario == idUsuario) && usuariofilial.Filial.Ativo
                select usuariofilial);
        }
    }
}
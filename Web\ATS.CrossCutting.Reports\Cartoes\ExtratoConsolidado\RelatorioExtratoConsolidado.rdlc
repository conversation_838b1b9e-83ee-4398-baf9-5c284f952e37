<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsCartoesExtratoConsolidado">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>eaccc8f5-176f-4899-9878-fb86e8e74be2</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DtsExtratoConsolidado">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsCartoesExtratoConsolidado</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Portador">
          <DataField>Portador</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PortadorDocumento">
          <DataField>PortadorDocumento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataTransacaoString">
          <DataField>DataTransacaoString</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValorConvertidoString">
          <DataField>ValorConvertidoString</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DebitoCreditoDescricao">
          <DataField>DebitoCreditoDescricao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DescricaoPlano">
          <DataField>DescricaoPlano</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Conta">
          <DataField>Conta</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NumeroCartao">
          <DataField>NumeroCartao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Identificador">
          <DataField>Identificador</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DescricaoMCC">
          <DataField>DescricaoMCC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Estabelecimento">
          <DataField>Estabelecimento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Cartoes.ExtratoConsolidado</rd:DataSetName>
        <rd:TableName>RelatorioExtratoConsolidadoDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Cartoes.ExtratoConsolidado.RelatorioExtratoConsolidadoDataType, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>3.42604cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.81221cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.44047cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.18033cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.4831cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.24612cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.75957cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.5cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.5cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.18033cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="IdCliente">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Portador.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>IdCliente</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="RazaoSocial">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PortadorDocumento.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>RazaoSocial</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CpfCnpj">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DataTransacaoString.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CpfCnpj</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Empresa">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ValorConvertidoString.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Empresa</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DebitoCreditoDescricao.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DescricaoPlano.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Estabelecimento.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox9">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Conta.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox9</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NumeroCartao.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox11">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Identificador.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox11</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details" />
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DtsExtratoConsolidado</DataSetName>
            <Left>0.3cm</Left>
            <Height>0.6cm</Height>
            <Width>26.52817cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>1.10912cm</Height>
        <Style />
      </Body>
      <Width>27.57165cm</Width>
      <Page>
        <PageHeader>
          <Height>2.05542cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Logo">
              <Source>External</Source>
              <Value>=Parameters!Logo.Value</Value>
              <MIMEType>image/png</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Top>0.25542cm</Top>
              <Left>0.3cm</Left>
              <Height>1.2cm</Height>
              <Width>2.15604cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </TopBorder>
                <BottomBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </BottomBorder>
                <LeftBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </LeftBorder>
              </Style>
            </Image>
            <Textbox Name="Textbox1">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>RELATÓRIO DO EXTRATO CONSOLIDADO</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>14pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>0.25542cm</Top>
              <Left>2.45604cm</Left>
              <Height>1.2cm</Height>
              <Width>20.95137cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Emissão: " &amp; Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox2</rd:DefaultName>
              <Top>0.85542cm</Top>
              <Left>23.40741cm</Left>
              <Height>0.6cm</Height>
              <Width>3.42076cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <BottomBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </BottomBorder>
                <RightBorder>
                  <Color>Black</Color>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </RightBorder>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Página:  </Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Globals!PageNumber</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> de </Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Globals!TotalPages</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>7pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox2</rd:DefaultName>
              <Top>0.25542cm</Top>
              <Left>23.40741cm</Left>
              <Height>0.6cm</Height>
              <Width>3.42076cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </TopBorder>
                <BottomBorder>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </BottomBorder>
                <RightBorder>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </RightBorder>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox26">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Portador</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>0.3cm</Left>
              <Height>0.6cm</Height>
              <Width>3.42604cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox27">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Portador  Documento</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>3.72604cm</Left>
              <Height>0.6cm</Height>
              <Width>2.81221cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox28">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Data Transação</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>6.53825cm</Left>
              <Height>0.6cm</Height>
              <Width>2.44047cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox30">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Valor</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>8.97872cm</Left>
              <Height>0.6cm</Height>
              <Width>2.18033cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox31">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Tipo</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>11.15905cm</Left>
              <Height>0.6cm</Height>
              <Width>1.4831cm</Width>
              <ZIndex>8</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox32">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Plano Vendas</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>12.64215cm</Left>
              <Height>0.6cm</Height>
              <Width>3.24612cm</Width>
              <ZIndex>9</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox33">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Nº Cartão</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>22.14784cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <ZIndex>10</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox34">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Identificador</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>24.64784cm</Left>
              <Height>0.6cm</Height>
              <Width>2.18033cm</Width>
              <ZIndex>11</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox35">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Conta</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>19.64784cm</Left>
              <Height>0.6cm</Height>
              <Width>2.5cm</Width>
              <ZIndex>12</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox36">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Estabelecimento</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox24</rd:DefaultName>
              <Top>1.45542cm</Top>
              <Left>15.88827cm</Left>
              <Height>0.6cm</Height>
              <Width>3.75957cm</Width>
              <ZIndex>13</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BackgroundColor>WhiteSmoke</BackgroundColor>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>21cm</PageHeight>
        <PageWidth>29.7cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Logo">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Logo</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>aa998063-5643-4944-a49b-70e1d2701e50</rd:ReportID>
</Report>
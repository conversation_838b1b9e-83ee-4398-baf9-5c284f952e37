﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IClienteProdutoEspecieService
    {
        object GetTodosProdutosPorClienteForUiGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        object GetTodasEspeciesPorClienteForUiGrid(int Take, int Page, OrderFilters Order, List<QueryFilters> Filters);
        Especie getEspecieByName(string especieName);
    }
}
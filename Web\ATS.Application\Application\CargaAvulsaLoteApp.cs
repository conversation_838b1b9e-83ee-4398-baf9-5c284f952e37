using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class CargaAvulsaLoteApp : BaseApp<ICargaAvulsaService>, ICargaAvulsaLoteApp
    {
        private IBloqueioGestorValorApp _bloqueioGestorValorApp;
        private ICargaAvulsaApp _cargaAvulsaApp;
        public CargaAvulsaLoteApp(ICargaAvulsaService service, IBloqueioGestorValorApp bloqueioGestorValorApp, ICargaAvulsaApp cargaAvulsaApp) : base(service)
        {
            _bloqueioGestorValorApp = bloqueioGestorValorApp;
            _cargaAvulsaApp = cargaAvulsaApp;
        }

        public CargaAvulsaResultadoValidacaoPlanilha ValidarCargaAvulsaPlanilha(HttpPostedFileBase file,int idEmpresa)
        {
            return Service.ValidarLinhasPlanilha(file,idEmpresa);
        }

        public ValidationResult CadastrarCargaAvulsaPlanilha(CargaAvulsaValidacaoPlanilha cargaAvulsa)
        {
            return Service.CadastrarCargaAvulsaPlanilha(cargaAvulsa);
        }

        public void RealizarCargasAvulsaPendentes()
        {
            Service.RealizarCargasAvulsaPendentes();
        }
        
        public ValidationResult<EValidationCargaAvulsa> ValidarAlcadasLimites(CargaAvulsaValidacaoAlcadasLimitesPlanilha request)
        {
            var validations = new ValidationResult<EValidationCargaAvulsa>();
            decimal limiteDiario;
            decimal limiteUnitario;
            
            var mensagemUnitarioError = EValidationCargaAvulsa.ValorExcedidoUnitarioEmpresa;
            var mensagemDiarioError = EValidationCargaAvulsa.ValorExcedidoDiarioEmpresa;

            if (request.IdFilial.HasValue)
            {
                limiteDiario = _bloqueioGestorValorApp.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria,request.IdEmpresa ?? 0, request.IdFilial,EBloqueioOrigemTipo.Portal) ?? 0;
                limiteUnitario = _bloqueioGestorValorApp.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria,request.IdEmpresa ?? 0, request.IdFilial,EBloqueioOrigemTipo.Portal) ?? 0;
                mensagemUnitarioError = EValidationCargaAvulsa.ValorExcedidoUnitarioFilial;
                mensagemDiarioError = EValidationCargaAvulsa.ValorExcedidoDiarioFilial;
            }
            else
            {
                limiteDiario = _bloqueioGestorValorApp.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria,request.IdEmpresa ?? 0, EBloqueioOrigemTipo.Portal) ?? 0;
                limiteUnitario = _bloqueioGestorValorApp.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria,request.IdEmpresa ?? 0, EBloqueioOrigemTipo.Portal) ?? 0;
            }
            
            var transacaoDoDia = _cargaAvulsaApp.GetTransacoesDiaria(request.IdEmpresa,request.IdFilial);

            foreach (var item in request.PlanilhaValores)
            {
                if (item > limiteUnitario && limiteUnitario != 0)
                {
                    validations.Add(mensagemUnitarioError, EFaultType.Error);
                    return validations;
                }
            }
            
            if ((transacaoDoDia + request.PlanilhaValores.Sum()) > limiteDiario && limiteDiario != 0)
                validations.Add(mensagemDiarioError, EFaultType.Error);


            return validations;
        }
    }
}
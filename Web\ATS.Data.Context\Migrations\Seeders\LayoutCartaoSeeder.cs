﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class LayoutCartaoSeeder
    {
        public void Execute(AtsContext context)
        {
            var layoutPadrao = new LayoutCartao { IdLayout = 1, Nome = "Padrao" };

            var listaItens = new List<LayoutCartaoItem>
            {
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.CargaAdiantamento), ItemCartao = 1},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoAdiantamento), ItemCartao = 6},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.CargaSaldo), ItemCartao = 2},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.CargaAbono), ItemCartao = 34},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoSaldo), ItemCartao = 7},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.TransferenciaAdiantamento), ItemCartao = 11},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoTransferenciaAdiantamento), ItemCartao = 15},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.TransferenciaSaldo), ItemCartao = 12},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoTransferenciaSaldo), ItemCartao = 16},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.TransferenciaContaBancaria), ItemCartao = 23},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoTransferenciaContaBancaria), ItemCartao = 24},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.CargaAvulsa), ItemCartao = 5},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.CargaAbastecimento), ItemCartao = 3},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoAbastecimento), ItemCartao = 7},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.TransferenciaAbastecimento), ItemCartao = 11},
                new LayoutCartaoItem {IdLayout = layoutPadrao.IdLayout, Key = nameof(ETipoProcessamentoCartao.EstornoTransferenciaAbastecimento), ItemCartao = 15}
            };

            layoutPadrao.Itens = listaItens;

            if (context.LayoutCartao.Any(c => c.IdLayout == layoutPadrao.IdLayout)) 
            {
                foreach (var layoutPadraoItem in layoutPadrao.Itens)
                {
                    if (!context.LayoutCartaoItem.Any(c => c.IdLayout == layoutPadrao.IdLayout && c.Key == layoutPadraoItem.Key))
                    {
                        context.LayoutCartaoItem.Add(layoutPadraoItem);
                    }
                }
                
                context.SaveChanges();
                return;
            }

            context.LayoutCartao.Add(layoutPadrao);
            context.SaveChanges();
        }
    }
}
﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace Extratta.ImportacaoDadosConsumer.Events
{
    public class ClienteIntegrarFilaEvent
    {
        public int IBGEEstado { get; set; }
        public int IBGECidade { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public ETipoPessoaFilaEvent TipoPessoa { get; set; }
        public string CNPJCPF { get; set; }
        public string RG { get; set; }
        public string OrgaoExpedidorRG { get; set; }
        public string IE { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
        public string CEP { get; set; }
        public string Endereco { get; set; }
        public int? Numero { get; set; }
        public string Bairro { get; set; }
        public string CNPJEmpresa { get; set; }
        public string Token { get; set; }
    }
    
    public enum ETipoPessoaFilaEvent
    {
        [EnumMember, Description("Física")]
        Fisica = 1,

        [EnumMember, Description("Juridica")]
        Juridica = 2
    }
}
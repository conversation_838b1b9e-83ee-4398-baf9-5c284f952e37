﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class MensagemService : ServiceBase, IMensagemService
    {
        private readonly IMensagemRepository _mensagemRepository;
        private readonly IMensagemDestinatarioRepository _mensagemDestinatarioRepository;
        private readonly IMensagemGrupoUsuarioService _mensagemGrupoUsuarioService;
        private readonly IGruposUsuariosRepository _gruposUsuariosRepository;
        private readonly IMensagemGrupoUsuarioRepository _mensagemGrupoUsuarioRepository;
        private readonly IMensagemGrupoDestinatarioRepository _mensagemGrupoDestinatarioRepository;
        private readonly IPushService _pushService;

        public MensagemService(IMensagemRepository mensagemRepository, IMensagemDestinatarioRepository mensagemDestinatarioRepository, IMensagemGrupoUsuarioService mensagemGrupoUsuarioService,
            IGruposUsuariosRepository gruposUsuariosRepository, IMensagemGrupoUsuarioRepository mensagemGrupoUsuarioRepository, IMensagemGrupoDestinatarioRepository mensagemGrupoDestinatarioRepository, IPushService pushService)
        {
            _mensagemRepository = mensagemRepository;
            _mensagemDestinatarioRepository = mensagemDestinatarioRepository;
            _mensagemGrupoUsuarioService = mensagemGrupoUsuarioService;
            _gruposUsuariosRepository = gruposUsuariosRepository;
            _mensagemGrupoUsuarioRepository = mensagemGrupoUsuarioRepository;
            _mensagemGrupoDestinatarioRepository = mensagemGrupoDestinatarioRepository;
            _pushService = pushService;
        }

        /// <summary>
        /// Retorna o objeto de mensagem a partir do código
        /// </summary>
        /// <param name="id">Código da mensagem</param>
        /// <returns></returns>
        public Mensagem Get(int id)
        {
            return _mensagemRepository.Get(id);
        }

        /// <summary>
        /// Retorna lista de mensagens novas
        /// </summary>
        /// <param name="idUsuarioDestinatario">Id do Usuario Destinatario</param>
        /// <returns></returns>
        public ICollection<Mensagem> GetMensagensNovas(int idUsuarioDestinatario, DateTime? dataInicial)
        {
            List<Mensagem> retorno = new List<Mensagem>();
            var dados = _mensagemRepository
                .Include(x => x.MensagemDestinatario)
                .Where(x => !x.Recebida && x.DataHoraEnvio >= dataInicial);

            foreach (var msg in dados)
            {
                var ids = new List<int>();
                var destsMsg = this.GetDestinatariosFromMensagemID(msg.IdMensagem);
                var gruposMsg = this.GetGruposDestinatariosFromMensagemID(msg.IdMensagem);
                ids.AddRange(from p in destsMsg select p.IdUsuarioDestinatario);

                foreach (var grupo in gruposMsg)
                    ids.AddRange(from p in
                                     _mensagemGrupoUsuarioService
                                     .GetIdsUsuariosPeloGrupo(grupo.IdGrupoUsuario)
                                 select p);

                ids = ids.Distinct().ToList();
                if (ids.Contains(idUsuarioDestinatario))
                {
                    retorno.Add(msg);
                }
            }
            return retorno;
        }

        public int GettotalMensagensNaoLidasPorUsuario(int idUsuario)
        {
            var msg = _mensagemDestinatarioRepository
                .Where(x => x.IdUsuarioDestinatario == idUsuario && !x.DataHoraLido.HasValue).Count();
            return msg;
        }

        /// <summary>
        /// Retorna todas as mensagens para o idusuario pelo filtro de data
        /// </summary>
        /// <param name="idUsuarioDestinatario"></param>
        /// <param name="dataFiltro"></param>
        /// <returns></returns>
        public ICollection<Mensagem> GetMensagensPeloUsuario(int idUsuarioDestinatario, DateTime? dataFiltro)
        {
            List<Mensagem> retorno = new List<Mensagem>();
            var dados = _mensagemRepository
                .Include(x => x.MensagemDestinatario)
                .Where(x => x.DataHoraEnvio >= dataFiltro);

            foreach (var msg in dados)
            {
                var ids = new List<int>();
                var destsMsg = this.GetDestinatariosFromMensagemID(msg.IdMensagem);
                var gruposMsg = this.GetGruposDestinatariosFromMensagemID(msg.IdMensagem);
                ids.AddRange(from p in destsMsg select p.IdUsuarioDestinatario);

                foreach (var grupo in gruposMsg)
                    ids.AddRange(from p in
                                     _mensagemGrupoUsuarioService
                                     .GetIdsUsuariosPeloGrupo(grupo.IdGrupoUsuario)
                                 select p);

                ids = ids.Distinct().ToList();
                if (ids.Contains(idUsuarioDestinatario))
                {
                    retorno.Add(msg);
                }
            }
            return retorno;
        }

        /// <summary>
        /// Buscar Mensagem com todos os filhos
        /// </summary>
        /// <param name="id">Id de Mensagem</param>
        /// <returns>Objeto Mensagem</returns>
        public Mensagem GetWithAllChilds(int id)
        {
            return _mensagemRepository
                .Find(x => x.IdMensagem == id)
                .Include(x => x.Remetente)
                .Include(x => x.GruposUsuariosMensagens)
                .Include(x => x.GruposUsuariosMensagens.Select(p => p.MensagemGruposUsuarios))
                .Include(x => x.MensagemDestinatario)
                .Include(x => x.MensagemDestinatario.Select(p => p.UsuarioDestinatario))
                .FirstOrDefault();
        }

        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="mensagem">Informações sobre a mensagem</param>
        /// <returns></returns>
        public Mensagem Add(Mensagem mensagem)
        {
            try
            {
                return _mensagemRepository.Add(mensagem);
            }
            catch (Exception)
            {
                return null;
            }
        }


        public Mensagem Update(Mensagem mensagem)
        {
            try
            {
                _mensagemRepository.Update(mensagem);

                return mensagem;

            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Retorna todas as mensagens a partir da data base de filtro
        /// </summary>
        /// <param name="dataBase">Data base para filtro de novas mensagens</param>
        /// <returns></returns>
        public IQueryable<Mensagem> GetPorDataEnvio(DateTime dataBase)
        {
            return _mensagemRepository.Find(x => x.DataHoraEnvio > dataBase)
                .Include(x => x.MensagemDestinatario).Include(x => x.GruposUsuariosMensagens);
        }

        /// <summary>
        /// Retorna todas as mensagens cadastradas a partir da data informada por usuario e filtros
        /// </summary>
        /// <param name="idUsuario">Código do remetente</param>
        /// <param name="dataInicial">Data inicial</param>
        /// <param name="dataFinal">Data final</param>
        /// <param name="assunto">Assunto</param>
        public IQueryable<Mensagem> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, string assunto)
        {
            List<Mensagem> retorno = new List<Mensagem>();
            // Recuperamos todas as mensagens que estão no intervalo de tempo do filtro
            // e onde o remetente não é o idusuario então são apenas as recebidas
            IQueryable<Mensagem> retMensagens =
                _mensagemRepository.All()
                    .Include(m => m.MensagemDestinatario)
                    .Include(m => m.MensagemDestinatario.Select(p => p.UsuarioDestinatario))
                    .Include(m => m.Remetente)
                    .Include(m => m.GruposUsuariosMensagens)
                    .Include(m => m.GruposUsuariosMensagens.Select(p => p.MensagemGruposUsuarios))
                    .Where(m => (m.DataHoraEnvio >= dataInicial && m.DataHoraEnvio <= dataFinal) && m.IdUsuarioRemetente != idUsuario);
            if (!string.IsNullOrWhiteSpace(assunto))
                retMensagens = retMensagens.Where(m => m.Assunto.Contains(assunto));

            foreach (var msg in retMensagens)
            {
                var ids = new List<int>();
                var destsMsg = this.GetDestinatariosFromMensagemID(msg.IdMensagem);
                var gruposMsg = this.GetGruposDestinatariosFromMensagemID(msg.IdMensagem);
                ids.AddRange(from p in destsMsg select p.IdUsuarioDestinatario);

                foreach (var grupo in gruposMsg)
                    ids.AddRange(from p in
                                     _mensagemGrupoUsuarioService
                                     .GetIdsUsuariosPeloGrupo(grupo.IdGrupoUsuario)
                                 select p);

                ids = ids.Distinct().ToList();
                if (ids.Contains(idUsuario))
                    retorno.Add(msg); // Um dos destinatarios desta mensagem é o id usuario, então adicionamos a mensagem
            }

            return retorno.AsQueryable();
        }

        public IQueryable<Mensagem> ConsultarEnviados(int idRemetente, DateTime dataInicial, DateTime dataFinal, string assunto)
        {
            IQueryable<Mensagem> retMensagens = _mensagemRepository.All()
                    .Include(m => m.MensagemDestinatario)
                    .Include(m => m.Remetente)
                    .Include(m => m.GruposUsuariosMensagens)
                    .Where(m => m.DataHoraEnvio >= dataInicial && m.DataHoraEnvio <= dataFinal || (m.DataHoraEnvio == null));

            retMensagens = retMensagens.Where(m => m.IdUsuarioRemetente == idRemetente);

            if (!string.IsNullOrWhiteSpace(assunto))
                retMensagens = retMensagens.Where(m => m.Assunto.Contains(assunto));

            return retMensagens;
        }

        public MensagemDestinatario AdicionarDestinatariosMensagem(MensagemDestinatario mensagemDestinatario)
        {
            var retorno = _mensagemDestinatarioRepository.Add(mensagemDestinatario);

            return retorno;
        }

        public IEnumerable<MensagemDestinatario> GetDestinatariosFromMensagemID(int mensagemId)
        {
            var retorno = _mensagemDestinatarioRepository
                .Include(x => x.UsuarioDestinatario)
                .Where(x => x.IdMensagem == mensagemId).OrderBy(x => x.UsuarioDestinatario.Nome);

            return retorno;
        }

        public IEnumerable<MensagemGrupoUsuario> GetGruposDestinatariosFromMensagemID(int mensagemId)
        {
            var t = _gruposUsuariosRepository.Find(x => x.IdMensagem == mensagemId).Select(x => x.IdMensagemGrupoUsuario);

            t = t.Distinct();

            List<MensagemGrupoUsuario> retorno = new List<MensagemGrupoUsuario>();
            foreach (var idGrupo in t)
            {
                retorno.Add(_mensagemGrupoUsuarioRepository.Get(idGrupo));
            }

            return retorno;
        }

        public void RemoveGruposUsuario(int idMensagem)
        {

            var _repository = _gruposUsuariosRepository;

            List<GruposUsuarios> grupos = _repository.Find(x =>
                x.IdMensagem == idMensagem).ToList();

            if (grupos != null)
                foreach(var grupo in grupos)
                    _repository.Delete(grupo);
        }
        
        public void RemoveDestinatarios(int idMensagem)
        {

            var _repository = _mensagemDestinatarioRepository;

            var destinatarios = _repository.Find( x =>
                x.IdMensagem == x.IdMensagem).ToList();

            if (destinatarios != null)
                foreach (var destinatario in destinatarios)
                    _repository.Delete(destinatario);

        }

        /// <summary>
        /// Reativar a mensagem
        /// </summary>
        /// <param name="idMensagem">Código da mensagem a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idMensagem)
        {
            try
            {
                IMensagemRepository mensagemRepository = _mensagemRepository;

                Mensagem mensagem = mensagemRepository.Get(idMensagem);
                if (mensagem.Ativo)
                    return new ValidationResult().Add($"Mensagem já ativado na base de dados.");

                mensagem.Ativo = true;

                mensagemRepository.Update(mensagem);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }


        /// <summary>
        /// Inativar a mensagem
        /// </summary>
        /// <param name="idMensagem">Código da mensagem a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idMensagem)
        {
            try
            {
                IMensagemRepository mensagemRepository = _mensagemRepository;

                Mensagem mensagem = mensagemRepository.Get(idMensagem);

                mensagem.Ativo = false;

                mensagemRepository.Update(mensagem); 
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }


        public ValidationResult SendBroadcast()
        {   
            try
            {   
                var mensagens = _mensagemRepository.Find( x =>  x.Ativo && x.DataHoraEnvio == null && x.DataAgendada <= DateTime.Now);

                foreach (var msg in mensagens)
                {

                    var gruposUsuarios = _gruposUsuariosRepository
                        .Find(x => x.IdMensagem == msg.IdMensagem)
                        .ToList();

                    var idsPush = new List<string>();
                    
                    foreach (var group in gruposUsuarios)
                    {
                        idsPush = idsPush.Union(_mensagemGrupoDestinatarioRepository
                        .Find(x => x.IdGrupoUsuario == group.IdMensagemGrupoUsuario && x.UsuarioMembroGrupo.IdPush != null)
                        .Include(x => x.UsuarioMembroGrupo)
                        .Select(x => x.UsuarioMembroGrupo.IdPush)
                        .ToList()).ToList();
                    }
                    
                    idsPush = idsPush.Union(_mensagemDestinatarioRepository
                        .Find(x => x.IdMensagem == msg.IdMensagem && x.UsuarioDestinatario.IdPush != null)
                        .Include(x => x.UsuarioDestinatario)
                        .Select(x => x.UsuarioDestinatario.IdPush)
                        .ToList()).ToList();
                    
                    //Foi utilizado o tipo broadcast caso necessário alterar depois.    
                    _pushService.Enviar(idsPush, msg.Assunto, msg.ConteudoMobile, tipoMensagem:ETipoMensagemPush.BroadCast);
                    
                   
                    msg.DataHoraEnvio = DateTime.Now;

                    _mensagemRepository.Update(msg); 

                }

            } catch(Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();  
        }
    }
}
{"runtime": "WinX64", "defaultVariables": null, "documentGenerator": {"fromDocument": {"json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Pedagios/Api\",\r\n    \"description\": \"API's para manipulação de cartões.\\r\\n\\r\\nTodos os métodos seguem o mesmo padrão de autenticação, sendo necessário indicar no header da requisição os seguintes valores:\\r\\n<li>x-auth-token: Token de identificação da empresa requisitando a operação</li>\\r\\n<li>x-audit-user-doc: Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação</li>\\r\\n<li>x-audit-user-name: Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</li>\\r\\n\\r\\nOs retornos de métodos respeitam o padrão do protocolo REST/HTTP, fazendo uso do \\\"Http Code\\\" adequado a situação:\\r\\n<li>http code 200: Existente na operação do servidor, com retorno de dados</li>\\r\\n<li>http code 204: A requisição foi processada com sucesso no servidor, porém não há informação para retornar. Geralmente utilizado em consultas de registros. Na especificação dos métodos abaixo estará descrito se este código é possível de retorno pela API</li>\\r\\n<li>http code 4XX: Erro de requisição por conteúdo mal formatado pelo consumidor. Geralmente JSON em formato inválidio enviado pelo cliente. O servidor não realizou nenhuma operação</li>\\r\\n<li>http code 5XX: Erro interno no servidor de aplicação, neste caso o conteúdo da requisição está OK, e algo não planejado ocorreu na API</li>\\r\\n\\r\\nPelas definições abaixo é possiviel verificar os métodos que retornam a informação \\\"processingStateOnServer\\\". Esta subpropriedade é apenas informação técnica, não representando sucesso da execução de regras de negócios. Ela indica que o servidor executou a procedimento sem problemas técnicos, através do indicador \\\"State\\\" com os valores \\\"Ok\\\" ou \\\"Error\\\", sendo adicionado mensagens em caso de erros na propriedade \\\"ErrorMessage\\\".\"\r\n  },\r\n  \"basePath\": \"/Pedagios/Api\",\r\n  \"paths\": {\r\n    \"/Empresa\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Definir parametros da empresa\",\r\n        \"operationId\": \"EmpresaPut\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresa/IntegrarParametros\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Definir parametros nos parametros\",\r\n        \"operationId\": \"EmpresaIntegrarParametrosPut\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarParametroRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarParametroResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresa/ConsultarEmpresa\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Retornar parametros Via Facil\",\r\n        \"operationId\": \"EmpresaConsultarEmpresaGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresa/ConsultarParametro\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Retornar parametros Move Mais\",\r\n        \"operationId\": \"EmpresaConsultarParametroGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarParametroResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/SalvarLogCallBack\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Maplink\"\r\n        ],\r\n        \"summary\": \"\",\r\n        \"operationId\": \"SalvarLogCallBackPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"apiRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/CallBackRequest\"\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ConsultarGridCache\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Maplink\"\r\n        ],\r\n        \"summary\": \"Consulta o cache das rotas maplink\",\r\n        \"operationId\": \"ConsultarGridCachePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/GridCustoCacheRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BaseGridResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/DeletarRotaCache\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Maplink\"\r\n        ],\r\n        \"summary\": \"Deletar o cache das rotas maplink\",\r\n        \"operationId\": \"DeletarRotaCachePost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"guidRota\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"format\": \"uuid\",\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ConsultarCompras\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Busca todas as compras conforme os parâmetros.\",\r\n        \"operationId\": \"ConsultarComprasPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PageSize\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"PageIndex\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"CustomFilters\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"OrderBy\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"consultaCompraPedagioRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para consulta de compras.\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCompraPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCompraPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/SolicitarCompra\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Realiza uma solicitação de compra de pedágio.\",\r\n        \"operationId\": \"SolicitarCompraPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"solicitarCompraPedagioRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para solicitação de compras\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/SolicitarCompraPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/SolicitarCompraPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/CancelarCompra\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Cancela uma solicitação de pedágio.\",\r\n        \"operationId\": \"CancelarCompraPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cancelarCompraPedagioRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para solicitação de compras\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarCompraPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarCompraPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ReemitirCompra\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Reemiti uma solicitação de compra.\",\r\n        \"operationId\": \"ReemitirCompraPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"reemitirCompraRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para solicitação de compras\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ReemitirCompraRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ReemitirCompraResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/CalcularRota\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Calcula o custo de pedágios de uma rota solicitada.\",\r\n        \"operationId\": \"CalcularRotaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"consultaPedagioRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para consulta de custo de pedágio.\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaRotaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaRotaResponseDto\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ConsultarHistoricoRoterizacao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pedagio\"\r\n        ],\r\n        \"summary\": \"Consulta o historico de determinada consulta de rotas.\",\r\n        \"operationId\": \"ConsultarHistoricoRoterizacaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"consultaHistoricoRotaRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de filtros para consulta de historico de pedágio.\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaHistoricoRotaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaHistoricoRotaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ConsultarReciboMoedeiro\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Transportador\"\r\n        ],\r\n        \"summary\": \"Responsabilidade de retornar os dados para impressão do recibo do moedeiro\",\r\n        \"operationId\": \"ConsultarReciboMoedeiroPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCompraPedagioMoedeiroReciboRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCompraPedagioMoedeiroReciboResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ValePedagio/ConsultarValesPedagios\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ValePedagio\"\r\n        ],\r\n        \"summary\": \"Consulta dos vales pedágio por data\",\r\n        \"operationId\": \"ValePedagioConsultarValesPedagiosGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PageSize\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"PageIndex\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"CustomFilters\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"OrderBy\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"dataInicio\",\r\n            \"in\": \"query\",\r\n            \"description\": \"ValePedagio.DataCadastro (Maior ou igual a )\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"dataFim\",\r\n            \"in\": \"query\",\r\n            \"description\": \"ValePedagio.DataCadastro, (Menor ou igual a)\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"status\",\r\n            \"in\": \"query\",\r\n            \"description\": \"ValePedagio.StatusId. (Igual a). Caso seja nulo, não deve ser filtrado\",\r\n            \"required\": false,\r\n            \"type\": \"string\",\r\n            \"enum\": [\r\n              \"Indefinido\",\r\n              \"Registrado\",\r\n              \"Cancelado\",\r\n              \"Contingencia\"\r\n            ]\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarValesPedagiosResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ValePedagio/ConsultarDadosComprovante\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ValePedagio\"\r\n        ],\r\n        \"summary\": \"Consulta dados para impressão do comprovante de Vale Pedágio\",\r\n        \"operationId\": \"ValePedagioConsultarDadosComprovanteGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"fornecedor\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"enum\": [\r\n              \"Desabilitado\",\r\n              \"Moedeiro\",\r\n              \"ViaFacil\",\r\n              \"MoveMais\",\r\n              \"Veloe\"\r\n            ]\r\n          },\r\n          {\r\n            \"name\": \"protocoloEnvio\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ComprovanteValePedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ValePedagio/ConsultarNumeroCartaoViagem\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ValePedagio\"\r\n        ],\r\n        \"summary\": \"Consulta dados para impressão do Recibo PEF\",\r\n        \"operationId\": \"ValePedagioConsultarNumeroCartaoViagemGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"fornecedor\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"enum\": [\r\n              \"Desabilitado\",\r\n              \"Moedeiro\",\r\n              \"ViaFacil\",\r\n              \"MoveMais\",\r\n              \"Veloe\"\r\n            ]\r\n          },\r\n          {\r\n            \"name\": \"protocoloEnvio\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarNumeroCartaoViagemResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"IntegrarEmpresaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigoAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"loginAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ApiProcessingStateOnServer\": {\r\n      \"required\": [\r\n        \"state\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"state\": {\r\n          \"enum\": [\r\n            \"Ok\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"errorMessage\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarParametroRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"ailog\": {\r\n          \"$ref\": \"#/definitions/ParametrosAilogDto\"\r\n        },\r\n        \"moveMais\": {\r\n          \"$ref\": \"#/definitions/ParametrosMoveMaisDto\"\r\n        },\r\n        \"veloe\": {\r\n          \"$ref\": \"#/definitions/ParametrosVeloeDto\"\r\n        }\r\n      }\r\n    },\r\n    \"ParametrosAilogDto\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"chaveAcesso\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ParametrosMoveMaisDto\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"loginAcesso\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAcesso\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ParametrosVeloeDto\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjEmbarcador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tenantId\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"userName\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"password\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"basicAuth\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarParametroResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"loginAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senhaAcessoViaFacil\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarParametroResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"loginAcessoMoveMais\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"senhaAcessoMoveMais\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"ailog\": {\r\n          \"$ref\": \"#/definitions/ParametrosAilogDto\"\r\n        },\r\n        \"moveMais\": {\r\n          \"$ref\": \"#/definitions/ParametrosMoveMaisDto\"\r\n        },\r\n        \"veloe\": {\r\n          \"$ref\": \"#/definitions/ParametrosVeloeDto\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CallBackRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"jobId\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"type\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"description\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"createAt\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"GridCustoCacheRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"order\": {\r\n          \"$ref\": \"#/definitions/OrderFiltersPedagio\"\r\n        },\r\n        \"take\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"page\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"filters\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/QueryFiltersPedagio\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"OrderFiltersPedagio\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"campo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"operador\": {\r\n          \"enum\": [\r\n            \"Ascending\",\r\n            \"Descending\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"QueryFiltersPedagio\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"campo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"operador\": {\r\n          \"enum\": [\r\n            \"StartsWith\",\r\n            \"EndsWith\",\r\n            \"Exact\",\r\n            \"Contains\",\r\n            \"GreaterThan\",\r\n            \"GreaterThanOrEqual\",\r\n            \"LessThan\",\r\n            \"LessThanOrEqual\",\r\n            \"NotEqual\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"serverFieldCollection\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"campoTipo\": {\r\n          \"enum\": [\r\n            \"Date\",\r\n            \"String\",\r\n            \"Number\",\r\n            \"Intervalo\",\r\n            \"Decimal\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"BaseGridResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"totalItems\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"items\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaCacheGrid\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCacheGrid\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidadeOrigemNome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidadeDestinoNome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"qtdEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"custoTotal\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"distanciaTotalKm\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"CustomFilter\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"operator\": {\r\n          \"enum\": [\r\n            \"StartsWith\",\r\n            \"EndsWith\",\r\n            \"Contains\",\r\n            \"NotContains\",\r\n            \"Equals\",\r\n            \"NotEquals\",\r\n            \"IsNull\",\r\n            \"IsNotNull\",\r\n            \"GreaterThan\",\r\n            \"GreaterThanOrEqual\",\r\n            \"LessThan\",\r\n            \"LessThanOrEqual\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"value\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"serverFieldCollection\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"fieldType\": {\r\n          \"enum\": [\r\n            \"Auto\",\r\n            \"Date\",\r\n            \"String\",\r\n            \"Number\",\r\n            \"Boolean\",\r\n            \"Intervalo\",\r\n            \"Unknown\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"OrderOptions\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"asc\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCompraPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"status\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"enum\": [\r\n              \"Pendente\",\r\n              \"Confirmado\",\r\n              \"Cancelado\",\r\n              \"PendenteCancelamento\",\r\n              \"Bloqueado\"\r\n            ],\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"fornecedor\": {\r\n          \"enum\": [\r\n            \"Desabilitado\",\r\n            \"Moedeiro\",\r\n            \"ViaFacil\",\r\n            \"MoveMais\",\r\n            \"Veloe\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradora\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"idCidadeOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"idCidadeDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCompraPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"filteredOptions\": {\r\n          \"$ref\": \"#/definitions/FilteredOptions\"\r\n        },\r\n        \"compraPedagioDTOList\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CompraPedagioDTOResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"FilteredOptions\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"totalRecords\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"pageCount\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"CompraPedagioDTOResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Pendente\",\r\n            \"Confirmado\",\r\n            \"Cancelado\",\r\n            \"PendenteCancelamento\",\r\n            \"Bloqueado\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"fornecedor\": {\r\n          \"enum\": [\r\n            \"Desabilitado\",\r\n            \"Moedeiro\",\r\n            \"ViaFacil\",\r\n            \"MoveMais\",\r\n            \"Veloe\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"cnpjFornecedor\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"processoSaldoResidual\": {\r\n          \"enum\": [\r\n            \"EstornarSaldo\",\r\n            \"Manter\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"qtdEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorResgatado\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"numeroCIOT\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataConfirmacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCancelamento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"consultaCustoHistoricoId\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"ibgeOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cidadeOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estadoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ibgeDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cidadeDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estadoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoFrete\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produtoFrete\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cartaoPedagio\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produtoPedagio\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataExpiracaoCompraPedagio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloEnvioValePedagio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloValePedagio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloCancelamentoValePedagio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rodagemDupla\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"SolicitarCompraPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documentoFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeProprietario\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"qtdEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"fornecedor\": {\r\n          \"enum\": [\r\n            \"Desabilitado\",\r\n            \"Moedeiro\",\r\n            \"ViaFacil\",\r\n            \"MoveMais\",\r\n            \"Veloe\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataExpiracaoCompraPedagio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"localizacoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/LocationDTO\"\r\n          }\r\n        },\r\n        \"tipoVeiculo\": {\r\n          \"enum\": [\r\n            \"Carro\",\r\n            \"Motocicleta\",\r\n            \"Onibus\",\r\n            \"Caminhao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"identificadorHistorico\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroCIOT\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataExpiracaoCreditoPendente\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"processoSaldoResidual\": {\r\n          \"enum\": [\r\n            \"EstornarSaldo\",\r\n            \"Manter\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"registraValePedagio\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"pedagioTag\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"tipoValePedagio\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Tag\",\r\n            \"Cupom\",\r\n            \"Cartao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"fornecedorValePedagio\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Antt\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"rodagemDupla\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"LocationDTO\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"latitude\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitude\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"ibge\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"raioBuscarEstradasProximas\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"SolicitarCompraPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"compraPedagio\": {\r\n          \"$ref\": \"#/definitions/CompraPedagioDTOResponse\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarCompraPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarCompraPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ReemitirCompraRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"viagem\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"praca\": {\r\n          \"$ref\": \"#/definitions/PracaRota\"\r\n        },\r\n        \"autenticarUsuario\": {\r\n          \"$ref\": \"#/definitions/AutenticarUsuarioRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"PracaRota\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeConcessionaria\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePraca\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeRodovia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tag\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tarifa\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"AutenticarUsuarioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"login\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"senha\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ReemitirCompraResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"status\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaRotaRequest\": {\r\n      \"required\": [\r\n        \"tipoVeiculo\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"localizacoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/LocationDTO\"\r\n          }\r\n        },\r\n        \"tipoVeiculo\": {\r\n          \"enum\": [\r\n            \"Carro\",\r\n            \"Motocicleta\",\r\n            \"Onibus\",\r\n            \"Caminhao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"qtdEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"exibirDetalhes\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"tipoRota\": {\r\n          \"enum\": [\r\n            \"THE_FASTEST\",\r\n            \"THE_SHORTEST\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"desabilitarCache\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaRotaResponseDto\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pracas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/Praca\"\r\n          }\r\n        },\r\n        \"custoTotal\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"distanciaTotalKm\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tempoPrevisto\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"custoTotalTag\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"identificadorHistorico\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"localizacoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaLocalizacoesResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Praca\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"localizacao\": {\r\n          \"$ref\": \"#/definitions/LocationDTO\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/Endereco\"\r\n        },\r\n        \"telefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"enderecoDescricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"concessao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoAntt\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"viaFacilId\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"fragmentoIndex\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"precos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/Preco\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaLocalizacoesResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pais\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Endereco\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeRua\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeCidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeEstado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePais\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Preco\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"precoEixoAdicional\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTag\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaHistoricoRotaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"consultaCustoHistoricoId\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"recalcularValorParaEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"incluirFragmentos\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaHistoricoRotaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"qtdEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoVeiculo\": {\r\n          \"enum\": [\r\n            \"Carro\",\r\n            \"Motocicleta\",\r\n            \"Onibus\",\r\n            \"Caminhao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"custoTotal\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"custoTotalTag\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"ibgeOrigem\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nomeCidadeOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estadoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ibgeDestino\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nomeCidadeDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estadoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"latitudeOrigem\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitudeOrigem\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"latitudeDestino\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitudeDestino\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"pracas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaHistoricoRotaPracaResponse\"\r\n          }\r\n        },\r\n        \"fragmentos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaHistoricoRotaFragmentosResponse\"\r\n          }\r\n        },\r\n        \"localizacoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaCustoHistoricoCidadesEstadosResponse\"\r\n          }\r\n        },\r\n        \"distanciaTotalKm\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaHistoricoRotaPracaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"latitude\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitude\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"nomeRua\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeCidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"concessao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"telefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTag\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"precoEixoAdicional\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaHistoricoRotaFragmentosResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"itemId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"latitudeOrigem\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitudeOrigem\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"latitudeDestino\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"longitudeDestino\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"cidadeOrigemIbge\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cidadeDestinoIbge\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCustoHistoricoCidadesEstadosResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"uf\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pais\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCompraPedagioMoedeiroReciboRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCompraPedagioMoedeiroReciboResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"compraPedagioDtoList\": {\r\n          \"$ref\": \"#/definitions/CompraPedagioDTOResponse\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarValesPedagiosResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"filteredOptions\": {\r\n          \"$ref\": \"#/definitions/FilteredOptions\"\r\n        },\r\n        \"valePedagios\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultaValesPedagios\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaValesPedagios\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"compraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Registrado\",\r\n            \"Cancelado\",\r\n            \"Contingencia\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloEnvio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRetorno\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloCancelamento\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ComprovanteValePedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"compraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"empresaNome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"empresaCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusCompra\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"documentoFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"placa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"quantidadeEixos\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataCadastro\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataCancelamento\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataConfirmacao\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataExpiracao\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"cartao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloEnvio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloResposta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"avisoTransportador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pracas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ComprovanteValePedagioPracaResponse\"\r\n          }\r\n        },\r\n        \"cpfCnpjContratante\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeContratante\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeCampoCpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ComprovanteValePedagioPracaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"concessao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarNumeroCartaoViagemResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroCartao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "url": "http://apiho.extratta.com.br:1000/Pedagios/Api/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToCSharpClient": {"clientBaseClass": "SistemaInfoMicroServiceBaseClient", "configurationClass": "HttpContext", "generateClientClasses": true, "generateClientInterfaces": false, "clientBaseInterface": null, "injectHttpClient": false, "disposeHttpClient": true, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "SwaggerException", "wrapDtoExceptions": true, "useHttpClientCreationMethod": true, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": true, "useBaseUrl": true, "generateBaseUrlProperty": true, "generateSyncMethods": true, "generatePrepareRequestAndProcessResponseAsAsyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "generateContractsOutput": false, "contractsNamespace": null, "contractsOutputFilePath": null, "parameterDateTimeFormat": "s", "parameterDateFormat": "yyyy-MM-dd", "generateUpdateJsonSerializerSettingsMethod": true, "useRequestAndResponseSerializationSettings": false, "serializeTypeInformation": false, "queryNullValue": "", "className": "{controller}Client", "operationGenerationMode": "MultipleClientsFromPathSegments", "additionalNamespaceUsages": ["System.Web", "ATS.Data.Repository.External.SistemaInfo"], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.Generic.List", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.List", "arrayInstanceType": null, "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": null, "arrayBaseType": "System.Collections.Generic.List", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "jsonLibrary": "NewtonsoftJson", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "generateNullableReferenceTypes": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "../../Web/ATS.Data.Repository.External/SistemaInfo/Pedagio/Client/Pedagio.ApiClient.cs", "newLineBehavior": "Auto"}}}
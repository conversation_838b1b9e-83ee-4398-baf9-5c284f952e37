using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaContaBancariaMap : EntityTypeConfiguration<EmpresaContaBancaria>
    {
        public EmpresaContaBancariaMap()
        {

            ToTable("EMPRESA_CONTABANCARIA");
            HasKey(t => t.Id);

            Property(x => x.IdEmpresa)
                .IsRequired();

            Property(t => t.Agencia)
                .IsRequired()
                .HasMaxLength(10);

            Property(t => t.CodigoBacenBanco)
                .IsRequired()
                .HasMaxLength(10);

            Property(t => t.Conta)
                .IsRequired()
                .HasMaxLength(30);

            Property(t => t.NomeConta)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Digito<PERSON>ont<PERSON>)
                .IsOptional();
        }
    }
}
﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Entities
{
    public class NumeroExtenso
    {
        /// <summary>
        /// Função para escrever por extenso os valores em Real (em C# - suporta até R$ 9.999.999.999,99)     
        /// Rotina Criada para ler um número e transformá-lo em extenso                                       
        /// Limite máximo de 9 Bilhões (9.999.999.999,99).
        /// Não aceita números negativos. 
        /// </summary> 
        /// <param name="valor">Valor para converter em extenso. Limite máximo de 9 Bilhões (9.999.999.999,99).</param>
        /// <returns>String do valor por Extenso</returns> 
        public static string NumeroPorExtenso(decimal valor)
        {
            var strValorExtenso = string.Empty; //Variável que irá armazenar o valor por extenso do número informado

            var blnBilhao = false;
            var blnMilhao = false;
            var blnMil = false;
            var blnUnidade = false;

            if (valor == 0 || valor <= 0)
                throw new Exception("Valor não suportado pela Função. Verificar se há valor negativo ou nada foi informado");

            if (valor > (decimal)9999999999.99)
                throw new Exception("Valor não suportado pela Função. Verificar se o Valor está acima de 9999999999.99");

            //Gerar Extenso Centavos 
            valor = decimal.Round(valor, 2);
            var dblCentavos = valor - (long) valor;

            //Gerar Extenso parte Inteira
            decimal dblValorInteiro = (long) valor;

            //Irá armazenar o número para exibir por extenso
            string strNumero; 

            if (dblValorInteiro > 0)
            {
                if (dblValorInteiro > 999)
                    blnMil = true;
                    
                if (dblValorInteiro > 999999)
                {
                    blnMilhao = true;
                    blnMil = false;
                }
                if (dblValorInteiro > 999999999)
                {
                    blnMil = false;
                    blnMilhao = false;
                    blnBilhao = true;
                }

                for (var i = (dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim().Length) - 1; i >= 0; i--)
                {
                    strNumero = Mid(dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim(),
                        (dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim().Length - i) - 1, 1);

                    switch (i)
                    {
                        case 9:
                            strValorExtenso = FcnNumeroUnidade(strNumero) +
                                              ((int.Parse(strNumero) > 1) ? " Bilhões e" : " Bilhão e");
                            blnBilhao = true;
                            break;
                        case 8:
                        case 5:
                        case 2:
                            if (int.Parse(strNumero) > 0)
                            {
                                var strCentena = Mid(dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim(),
                                    (dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim().Length - i) - 1, 3);

                                if (int.Parse(strCentena) > 100 && int.Parse(strCentena) < 200)
                                    strValorExtenso = strValorExtenso + " Cento e ";
                                else
                                    strValorExtenso = strValorExtenso + " " + FcnNumeroCentena(strNumero);
                            }
                            break;
                        case 7:
                        case 4:
                        case 1:
                            if (int.Parse(strNumero) > 0)
                            {
                                var strDezena = Mid(dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim(),
                                    (dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim().Length - i) - 1, 2);

                                if (int.Parse(strDezena) > 10 && int.Parse(strDezena) < 20)
                                {
                                    strValorExtenso =
                                        strValorExtenso + (Right(strValorExtenso, 5).Trim() == "entos" ? " e " : " ")
                                                        + FcnNumeroDezena0(Right(strDezena, 1));

                                    blnUnidade = true;
                                }
                                else
                                {
                                    strValorExtenso =
                                        strValorExtenso + (Right(strValorExtenso, 5).Trim() == "entos" ? " e " : " ")
                                                        + FcnNumeroDezena1(Left(strDezena, 1));

                                    blnUnidade = false;
                                }
                            }

                            break;
                        case 6:
                        case 3:
                        case 0:
                            if (int.Parse(strNumero) > 0 && !blnUnidade)
                            {
                                if ((Right(strValorExtenso, 5).Trim()) == "entos"
                                    || (Right(strValorExtenso, 3).Trim()) == "nte"
                                    || (Right(strValorExtenso, 3).Trim()) == "nta")
                                {
                                    strValorExtenso = strValorExtenso + " e ";
                                }
                                else
                                {
                                    strValorExtenso = strValorExtenso + " ";
                                }

                                strValorExtenso = strValorExtenso + FcnNumeroUnidade(strNumero);
                            }

                            if (i == 6)
                            {
                                if (blnMilhao || int.Parse(strNumero) > 0)
                                {
                                    strValorExtenso = strValorExtenso + ((int.Parse(strNumero) == 1) && !blnUnidade
                                                          ? " Milhão"
                                                          : " Milhões");
                                    strValorExtenso = strValorExtenso + ((int.Parse(strNumero) > 1000000) ? " " : " e");
                                    blnMilhao = true;
                                }
                            }

                            if (i == 3)
                            {
                                if (blnMil || int.Parse(strNumero) > 0)
                                {
                                    strValorExtenso = strValorExtenso + " Mil";
                                    strValorExtenso = strValorExtenso + ((int.Parse(strNumero) > 1000) ? " " : " e");
                                    blnMil = true;
                                }
                            }

                            if (i == 0)
                            {
                                if ((blnBilhao && !blnMilhao && !blnMil
                                     && Right(dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim(), 3) == "0")
                                    || (!blnBilhao && blnMilhao && !blnMil
                                        && Right(dblValorInteiro.ToString(CultureInfo.InvariantCulture).Trim(), 3) == "0"))
                                {
                                    strValorExtenso = strValorExtenso + " e ";
                                }

                                strValorExtenso = strValorExtenso +
                                                  ((long.Parse(
                                                       dblValorInteiro.ToString(CultureInfo.InvariantCulture))) > 1
                                                      ? " Reais"
                                                      : " Real");
                            }

                            blnUnidade = false;
                            break;

                        default:
                            break;
                    }
                }
            }
            if (dblCentavos > 0)
            {

                if (dblCentavos > 0 && dblCentavos < 0.1M)
                {
                    strNumero = Right(decimal.Round(dblCentavos, 2).ToString(CultureInfo.InvariantCulture).Trim(), 1);
                    strValorExtenso = strValorExtenso + (dblCentavos > 0 ? " e " : " ")
                                                      + FcnNumeroUnidade(strNumero) + ((dblCentavos > 0.01M) ? " Centavos" : " Centavo");
                }
                else if (dblCentavos > 0.1M && dblCentavos < 0.2M)
                {
                    strNumero = Right(
                        (decimal.Round(dblCentavos, 2) - (decimal) 0.1).ToString(CultureInfo.InvariantCulture)
                        .Trim(), 1);

                    strValorExtenso = strValorExtenso + ((dblCentavos > 0) ? " " : " e ")
                                                      + FcnNumeroDezena0(strNumero) + " Centavos ";
                }
                else
                {
                    strNumero = Right(dblCentavos.ToString(CultureInfo.InvariantCulture).Trim(), 2);
                    var strDezCentavo = Mid(dblCentavos.ToString(CultureInfo.InvariantCulture).Trim(), 2, 1);

                    strValorExtenso = strValorExtenso + ((int.Parse(strNumero) > 0) ? " e " : " ");
                    strValorExtenso = strValorExtenso + FcnNumeroDezena1(Left(strDezCentavo, 1));

                    if ((dblCentavos.ToString(CultureInfo.InvariantCulture).Trim().Length) > 2)
                    {
                        strNumero = Right(decimal.Round(dblCentavos, 2).ToString(CultureInfo.InvariantCulture).Trim(), 1);
                        if (int.Parse(strNumero) > 0)
                        {
                            if (dblValorInteiro <= 0)
                            {
                                if (Mid(strValorExtenso.Trim(), strValorExtenso.Trim().Length - 2, 1) == "e")
                                {
                                    strValorExtenso = strValorExtenso + " e " + FcnNumeroUnidade(strNumero);
                                }
                                else
                                {
                                    strValorExtenso = strValorExtenso + " e " + FcnNumeroUnidade(strNumero);
                                }
                            }
                            else
                            {
                                strValorExtenso = strValorExtenso + " e " + FcnNumeroUnidade(strNumero);
                            }
                        }
                    }
                    strValorExtenso = strValorExtenso + " Centavos ";
                }
            }
            if (dblValorInteiro < 1) strValorExtenso = Mid(strValorExtenso.Trim(), 2, strValorExtenso.Trim().Length - 2);

            return strValorExtenso.Trim();
        }

        private static string FcnNumeroDezena0(string pstrDezena0)
        {
            //Vetor que irá conter o número por extenso 
            var arrayDezena0 = new ArrayList
            {
                "Onze",
                "Doze",
                "Treze",
                "Quatorze",
                "Quinze",
                "Dezesseis",
                "Dezessete",
                "Dezoito",
                "Dezenove"
            };

            return arrayDezena0[int.Parse(pstrDezena0) - 1].ToString();
        }

        private static string FcnNumeroDezena1(string pstrDezena1)
        {
            //Vetor que irá conter o número por extenso
            var arrayDezena1 = new ArrayList
            {
                "Dez",
                "Vinte",
                "Trinta",
                "Quarenta",
                "Cinquenta",
                "Sessenta",
                "Setenta",
                "Oitenta",
                "Noventa"
            };

            return arrayDezena1[short.Parse(pstrDezena1) - 1].ToString();
        }

        private static string FcnNumeroCentena(string pstrCentena)
        {
            //Vetor que irá conter o número por extenso
            var arrayCentena = new ArrayList
            {
                "Cem",
                "Duzentos",
                "Trezentos",
                "Quatrocentos",
                "Quinhentos",
                "Seiscentos",
                "Setecentos",
                "Oitocentos",
                "Novecentos"
            };

            return arrayCentena[int.Parse(pstrCentena) - 1].ToString();
        }

        private static string FcnNumeroUnidade(string pstrUnidade)
        {
            //Vetor que irá conter o número por extenso
            var arrayUnidade = new ArrayList {"Um", "Dois", "Três", "Quatro", "Cinco", "Seis", "Sete", "Oito", "Nove"};

            return arrayUnidade[int.Parse(pstrUnidade) - 1].ToString();
        }

        public static string Left(string param, int length)
        {
            //we start at 0 since we want to get the characters starting from the 
            //left and with the specified lenght and assign it to a variable
            if (param == string.Empty)
                return string.Empty;

            var result = param.Substring(0, length);
            
            //return the result of the operation 
            return result;
        }

        public static string Right(string param, int length)
        {
            //start at the index based on the lenght of the sting minus
            //the specified lenght and assign it a variable 
            if (param == string.Empty)
                return string.Empty;

            var result = param.Substring(param.Length - length, length);
            
            //return the result of the operation
            return result;
        }

        public static string Mid(string param, int startIndex, int length)
        {
            //start at the specified index in the string ang get N number of
            //characters depending on the lenght and assign it to a variable 
            var result = param.Substring(startIndex, length);

            //return the result of the operation
            return result;
        }

        public static string Mid(string param, int startIndex)
        {
            //start at the specified index and return all characters after it
            //and assign it to a variable
            var result = param.Substring(startIndex);
            
            //return the result of the operation 
            return result;
        }
    }
}

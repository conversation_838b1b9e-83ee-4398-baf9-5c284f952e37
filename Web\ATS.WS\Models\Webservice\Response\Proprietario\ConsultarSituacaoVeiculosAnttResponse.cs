﻿using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ConsultarSituacaoVeiculosAnttResponse
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public SituacaoVeiculosAnttResponseDto Objeto { get; set; }
        
        public ConsultarSituacaoVeiculosAnttResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }
    }

    public class SituacaoVeiculosAnttResponseDto
    {
        public int IdProprietario { get; set; }
        public bool RntrcAtivo { get; set; }
        public List<PlacaStatusFrotaTransportadorDto> Placas { get; set; }
    }
    
    public class PlacaStatusFrotaTransportadorDto
    {
        public string Placa { get; set; }
        public EStatusVeiculoFrotaTransportador Situacao { get; set; }

        public PlacaStatusFrotaTransportadorDto(string placa, bool? pertenceFrotaTransportador)
        {
            Placa = placa;
            Situacao = pertenceFrotaTransportador == true ? EStatusVeiculoFrotaTransportador.Pertencente : EStatusVeiculoFrotaTransportador.NaoPertencente;
        }
    }
}
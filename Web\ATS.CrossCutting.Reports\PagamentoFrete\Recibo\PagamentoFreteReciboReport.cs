﻿using System;
using System.Collections.Generic;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.PagamentoFrete.Recibo
{
    public class PagamentoFreteReciboReport
    {

        public byte[] RelatorioRPA(List<PagamentoFreteReciboModel> pagamentoFreteReciboModel, string telefonesEmpresa, bool pagoNoCartao)
        {
            var localReport = new LocalReport();
            var mensagemPagoCartao = pagoNoCartao ? "PAGO NO CARTÃO " : string.Empty;

            var parametros = new ReportParameter[2];
            parametros[0] = new ReportParameter("Telefones", telefonesEmpresa, true);
            parametros[1] = new ReportParameter("PagoNoCartao", mensagemPagoCartao, true);

            localReport.DataSources.Add(new ReportDataSource
            {
                Value = pagamentoFreteReciboModel,
                Name = "DtsReciboRPA"
            });
            
            localReport.EnableExternalImages = true;
            localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Recibo.ReciboNewLayout.ReciboRPA.rdlc";
            localReport.SetParameters(parametros);
            localReport.Refresh();
            return localReport.Render("PDF");
        }

        public byte[] RelatorioSaldo(List<PagamentoFreteReciboModel> pagamentoFreteReciboModel, List<PagamentoFreteReciboAcrescimosDescontosModel> listaAcrescimos, List<PagamentoFreteReciboAcrescimosDescontosModel> listaDescontos, string barcode, string telefonesEmpresa, bool pagoNoCartao)
        {
            try
            {
                var localReport = new LocalReport();

                var mensagemPagoCartao = pagoNoCartao ? "PAGO NO CARTÃO " : string.Empty;

                var parametros = new ReportParameter[3];
                parametros[0] = new ReportParameter("BarCode", barcode, true);
                parametros[1] = new ReportParameter("Telefones", telefonesEmpresa, true);
                parametros[2] = new ReportParameter("PagoNoCartao", mensagemPagoCartao, true);

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentoFreteReciboModel,
                    Name = "DtsReciboSaldo"
                });

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaAcrescimos,
                    Name = "DtsAcrescimos"
                });

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaDescontos,
                    Name = "DtsDescontos"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Recibo.ReciboNewLayout.ReciboSaldo.rdlc";
                localReport.SetParameters(parametros);
                localReport.Refresh();
                return localReport.Render("PDF");
            }
            catch (Exception)
            {
                return new byte[0];
            }
        }

        public byte[] Relatorio(List<PagamentoFreteReciboModel> pagamentoFreteReciboModel, List<PagamentoFreteReciboAcrescimosDescontosModel> listaAcrescimos, List<PagamentoFreteReciboAcrescimosDescontosModel> listaDescontos, string barcode, string telefonesEmpresa, bool pagoNoCartao)
        {
            try
            {
                var localReport = new LocalReport();

                var mensagemPagoCartao = pagoNoCartao ? "PAGO NO CARTÃO " : string.Empty;

                var parametros = new ReportParameter[3];
                parametros[0] = new ReportParameter("BarCode", barcode, true);
                parametros[1] = new ReportParameter("Telefones", telefonesEmpresa, true);
                parametros[2] = new ReportParameter("PagoNoCartao", mensagemPagoCartao, true);

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentoFreteReciboModel,
                    Name = "DtsRecibo"
                });

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaAcrescimos,
                    Name = "DtsAcrescimos"
                });

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaDescontos,
                    Name = "DtsDescontos"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Recibo.ReciboNewLayout.Recibo.rdlc";
                localReport.SetParameters(parametros);
                localReport.Refresh();
                return localReport.Render("PDF");
            }
            catch (Exception)
            {
                return new byte[0];
            }
        }
    }
}
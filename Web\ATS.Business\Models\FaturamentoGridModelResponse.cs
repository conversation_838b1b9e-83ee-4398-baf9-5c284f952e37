﻿using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class FaturamentoGridModelResponse
    {
        public List<FaturamentoItemGridModelResponse> items { get; set; }
        public int totalItems { get; set; }
    }
    
    public class FaturamentoItemGridModelResponse
    {
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public int QtdViagens { get; set; }
        public string Adiantamento { get; set; }
        public string Saldo { get; set; }
        public string TarifaANTT { get; set; }
        public string Estadias { get; set; }
        public string Abastecimento { get; set; }
        public string Pedagio { get; set; }
        public string CargaAvulsa { get; set; }
        public string Total { get; set; }
        public string Estornos { get; set; }
        public string StatusEmpresa { get; set; }
        public bool Ativo { get; set; }
    }
}
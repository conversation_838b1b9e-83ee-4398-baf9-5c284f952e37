﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;

namespace ATS.Domain.Helpers
{
    public static class EnumHelpers
    {
        /// <summary>
        /// Retorna a descrição do Enum
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string GetDescription<T>(this T source)
        {
            if (source == null)
                return string.Empty;
            FieldInfo field = source.GetType().GetField(source.ToString());

            if (field == null)
                return string.Empty;

            DescriptionAttribute[] attributes = (DescriptionAttribute[])
                field.GetCustomAttributes(typeof(DescriptionAttribute), false);

            if (attributes.Length > 0)
                return attributes[0].Description;

            return source.ToString();
        }
        
        public static string GetDisplayAttributeFrom(this System.Enum enumValue, Type enumType)
        {
            var displayName = "";
            var info = enumType.GetMember(enumValue.ToString()).First();

            if (info != null && info.CustomAttributes.Any())
            {
                var nameAttr = info.GetCustomAttribute<DisplayAttribute>();
                displayName = nameAttr != null ? nameAttr.Name : enumValue.ToString();
            }
            else
            {
                displayName = enumValue.ToString();
            }
            return displayName;
        }
    }
}
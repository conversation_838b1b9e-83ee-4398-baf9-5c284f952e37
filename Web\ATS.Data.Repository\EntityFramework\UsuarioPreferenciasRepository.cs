﻿using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioPreferenciasRepository : Repository<UsuarioPreferencias>, IUsuarioPreferenciasRepository
    {
        public UsuarioPreferenciasRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<UsuarioPreferencias> GetPreferenciasByIdUsuario(int idUsuario, string campo = null)
        {
            return from usuarioPreferencias in All()
                   where usuarioPreferencias.IdUsuario == idUsuario
                   && (campo != null || usuarioPreferencias.Campo == campo)
                   select usuarioPreferencias;
        }

        public IQueryable<UsuarioPreferencias> GetPreferenciasByIdUsuarioPrefixLike(int idUsuario, string prefix)
        {
            return from usuarioPreferencias in All()
                   where usuarioPreferencias.IdUsuario == idUsuario
                    && usuarioPreferencias.Campo.Contains(prefix)
                   select usuarioPreferencias;
        }

        public void SaveUsuarioPreferencias(List<UsuarioPreferencias> aUsuarioPreferencias)
        {
            foreach (var up in aUsuarioPreferencias)
            {
                if (up.Valor == "False" || up.Valor == "True")
                    up.Valor = up.Valor.ToLower();

                var exists = (from usuarioPreferencias in All()
                               where usuarioPreferencias.IdUsuario == up.IdUsuario && usuarioPreferencias.Campo == up.Campo
                               select usuarioPreferencias).FirstOrDefault();

                if (exists != null)
                {
                    exists.Valor = up.Valor;
                    Update(exists);
                }
                else
                    Add(up);
            }

            Context.SaveChanges();
        }
    }
}

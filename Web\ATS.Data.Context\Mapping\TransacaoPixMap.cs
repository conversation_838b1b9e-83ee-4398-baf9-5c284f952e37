﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TransacaoPixMap : EntityTypeConfiguration<TransacaoPix>
    {
        public TransacaoPixMap()
        {
            ToTable("TRANSACAO_PIX");

            HasKey(x => x.IdTransacaoPix);
            Property(x => x.IdTransacaoPix)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.DocumentoDestino).HasMaxLength(20);
            Property(x => x.OrigemPagamento).HasMaxLength(100);
            Property(x => x.DocumentoUsuarioAuditCriacao).HasMaxLength(200);
            Property(x => x.DocumentoUsuarioAuditAtualizacao).HasMaxLength(200);
            Property(x => x.MensagemRetorno).HasMaxLength(500);

            HasRequired(x => x.ViagemEvento)
                .WithMany(x => x.TransacoesPix)
                .HasForeignKey(x => x.IdViagemEvento);

            HasRequired(x => x.TransacaoPixStatus)
                .WithMany()
                .HasForeignKey(x => x.IdTransacaoPixStatus);

            HasOptional(x => x.UsuarioAtualizacao)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioAtualizacao);

            HasOptional(x => x.UsuarioCadastro)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioCadastro);

            HasRequired(x => x.Empresa)
                .WithMany()
                .HasForeignKey(x => x.IdEmpresa);
        }
    }
}

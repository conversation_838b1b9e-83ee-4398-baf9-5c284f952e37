﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.Reports.Estabelecimento;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEstabelecimentoService : IService<Estabelecimento>
    {
        ValidationResult Add(Estabelecimento estabelecimento);
        ValidationResult Update(Estabelecimento estabelecimento);
        Estabelecimento Get(int idEstabelecimento, bool withIncludes = true);
        Estabelecimento GetByIdEstabelecimentoBaseEmpresa(int idEstabelecimentoBase, int idEmpresa);
        bool IsRegular(int idEstabelecimento);
        bool EstaCredenciado(int idEstabelecimento);
        IQueryable<Estabelecimento> GetQueryByEmpresa(int idestabelecimentoBase, int idEmpresa);
        IQueryable<EstabelecimentoProduto> GetQueryProduto();
        ValidationResult RemoverProdutos(IList<int> idprodutobase);
        ValidationResult ReplicarProdutoBase(EstabelecimentoBaseProduto estabelecimentoBaseProduto);
        IQueryable<Estabelecimento> GetEstabelecimentoPorIdBase(int idEstabelecimentoBase);
        IQueryable<Estabelecimento> GetQueryByCnpj(string cnpj);
        IQueryable<Estabelecimento> GetQueryById(int idEstabelecimento);
        int? GetIdBase(int idEstabelecimento);
        int? GetIdPorCnpj(string cnpj, int idEmpresa);
        bool GetEstabelecimentoAtivo(int idEstabelecimento);
        int? GetByBase(int idBase);
        ValidationResult UpdateByEstabelecimentoBase(int idEstabelecimentoBase);
        List<EstabelecimentoBase> GetAssociados(int idEstabelecimento);
        ValidationResult Inativar(int idEstabelecimento);
        ValidationResult Reativar(int idEstabelecimento);
        DataModel<EstabelecimentoModel> ConsultaGrid(int? idEmpresa, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool filtrarApenasCredenciadosEmpresa = false);
        IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarPorEmpresa(int idEmpresa, string cnpjEstabelecimento);

        IQueryable<Estabelecimento> ConsultarEstabelecimentosRota(
            int idEmpresa,
            decimal latitudeOrigem, decimal longitudeOrigem,
            decimal? latitudeDestino = null, decimal? longitudeDestino = null,
            decimal? raio = 50, int[] idsTipoEstabelecimento = null, bool buscaCredenciados = false);

        Estabelecimento ConsultarPorId(int idEstabelecimento);

        /// <summary>
        /// Retorna o estabelecimento pela empresa e pelo estabelecimento base
        /// </summary>
        /// <param name="idEstabelecimentoBase"></param>
        /// <param name="idEmpresa"></param>
        /// <returns>
        /// <para>Caso não encontre o registro, retorna 0 para a chave</para>
        /// </returns>
        KeyValuePair<int, bool> GetEstabelecimentoGeracaoProtocolo(int idEstabelecimentoBase, int idEmpresa);

        Estabelecimento GetByIdEstabelecimentoBase(int idEstabelecimentoBase, int idEmpresa);
        List<KeyValuePair<int, int>> GetIdEstabelecimentosAssociadosLiberacaoProtocolo(int idEstabelecimentoBase);
        void ExcluirAssociados(int idEstabelecimento);
        Estabelecimento GetByIdEstabelecimentoEmpresa(int idEstabelecimento, int idEmpresa);
        Estabelecimento Get(string cnpj, int idEmpresa);

        object ConsultarAssociacoes(int? idTipoEstabelecimento, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters);

        object GetEstabelecimentoPorViagem(string tokenViagemEvento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<Estabelecimento> GetAssociacoesEmpresa(int idEmpresa, int? ignoreId);
        List<Estabelecimento> GetAssociacoes(int idEmpresa, int? ignoreId, int idUsuario, bool onlyEmpresa);
        byte[] GerarRelatorioGridEstabelecimento(int? idEmpresa, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
        List<Estabelecimento> EstabelecimentosDaRota(int idRota);
        bool ValidarChaveTokenPorHorarario(int idEstabelecimentoBase, int idEmpresa);
    }
}

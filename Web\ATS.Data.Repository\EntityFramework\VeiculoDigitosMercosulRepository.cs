using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class VeiculoDigitosMercosulRepository : Repository<VeiculoDigitosMercosul>, IVeiculoDigitosMercosulRepository
    {
        public VeiculoDigitosMercosulRepository(AtsContext context) : base(context)
        {
        }
        
        public string GetDigitoMercosul(int numero)
        {
            return Where(c => c.NumeroOrigem == numero).Select(c => c.LetraDestino).FirstOrDefault();
        }

        public int GetNumeroByDigitoMercosul(string letra)
        {
            return Where(c => c.LetraDestino == letra).Select(c => c.NumeroOrigem).FirstOrDefault();
        }
    }
}
﻿using System;
using ATS.Application.Application.Common;
using ATS.Domain.Grid;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem;
using ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Models;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesasViagem;
using ATS.Domain.Validation;
using AutoMapper;
using MongoDB.Bson;
using NLog.Fluent;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class DespesasViagemApp : AppBase, IDespesasViagemApp
    {
        private readonly IDespesasViagemService _despesasViagemService;
        private readonly IDespesaUsuarioService _despesasUsuarioViagemService;
        private readonly IDataMediaServerService _dataMediaServerService;
        private readonly IUserIdentity _userIdentity;
        private readonly IEmpresaService _empresaService;
        private readonly IMotoristaService _motoristaService;
        private readonly IProprietarioService _proprietarioService;
        private readonly IUsuarioApp _usuarioApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IUsuarioPermissaoFinanceiroApp _usuarioPermissaoFinanceiroApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IExtrattaBizApiClient _extrattaBizApiClient;

        public DespesasViagemApp(IDespesasViagemService despesasViagemService,IDespesaUsuarioService despesasUsuarioViagemService,IDataMediaServerService dataMediaServerService
            ,IEmpresaService empresaService,IUserIdentity userIdentity,IUsuarioApp usuarioApp,CartoesAppFactoryDependencies cartoesAppFactoryDependencies,IMotoristaService motoristaService
            ,IProprietarioService proprietarioService,IUsuarioPermissaoFinanceiroApp usuarioPermissaoFinanceiroApp, IParametrosApp parametrosApp, IExtrattaBizApiClient extrattaBizApiClient)
        {
            _despesasViagemService = despesasViagemService;
            _despesasUsuarioViagemService = despesasUsuarioViagemService;
            _dataMediaServerService = dataMediaServerService;
            _empresaService = empresaService;
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _motoristaService = motoristaService;
            _proprietarioService = proprietarioService;
            _usuarioPermissaoFinanceiroApp = usuarioPermissaoFinanceiroApp;
            _parametrosApp = parametrosApp;
            _extrattaBizApiClient = extrattaBizApiClient;
        }

        public DespesasViagemGridSaldoResponse ConsultarPortadorGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idUsuario)
        {
            #region Consulta de dados

            idEmpresa ??= _userIdentity.IdEmpresa;
            idUsuario ??= _userIdentity.IdUsuario;

            var empresa = _empresaService.Get(idEmpresa ?? 0,null);

            if (empresa == null)
            {
                return new DespesasViagemGridSaldoResponse()
                {
                    totalItems = 0,
                    items = new List<DespesasViagemGridResponse>()
                };
            }
            
            var usuarioLogado = _usuarioApp.Get((int)idUsuario);
            
            var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuarioLogado);
            
            //Permissão segurança
            var usuarioAcessoExtrato = usuarioLogado.Perfil != EPerfil.Administrador
                                       && !_usuarioPermissaoFinanceiroApp.PossuiPermissao(usuarioLogado.IdUsuario,
                                           EBloqueioFinanceiroTipo.consultarExtratoPortador);

            var usuarioAcessoResgate = usuarioLogado.Perfil != EPerfil.Administrador
                                       && !_usuarioPermissaoFinanceiroApp.PossuiPermissao(usuarioLogado.IdUsuario,
                                           EBloqueioFinanceiroTipo.realizarResgatePortadorDespesasViagem);

            var acessarExtratoDetalhado = _parametrosApp.GetPermitirAcessoExtratoDetalhadoUsuario(usuarioLogado.IdUsuario);

            var listarPortadorCnpj = _parametrosApp.GetParametroEmpresaGridListarCnpjDespesasViagem(empresa.IdEmpresa);
            
            var listaPortadores = new RelatorioPortadorCartaoRequest
            {
                ListDocumentoPortador = new List<string>()
            };
            
            var portadoresMotorista = _motoristaService
                .GetAllByIdEmpresa(empresa.IdEmpresa)
                .Where(x => x.Ativo && x.TipoContrato == ETipoContrato.Frota)
                .Select(x => x.CPF)
                .ToList();
            
            var portadoresProprietario = _proprietarioService
                .GetAllByIdEmpresa(empresa.IdEmpresa)
                .Where(x => x.Ativo && x.TipoContrato == ETipoContrato.Frota)
                .Select(x => x.CNPJCPF)
                .ToList();

            // Listar apenas CPF
            if (!listarPortadorCnpj)
                portadoresProprietario = portadoresProprietario.Where(x => x.Length <= 11).ToList();
            
            // Adicionado motorista
            listaPortadores.ListDocumentoPortador.AddRange(portadoresMotorista);
            
            // Adicionando proprietario
            listaPortadores.ListDocumentoPortador.AddRange(portadoresProprietario);
            
            var listMotoristaComCartao = cartoesApp.RelatorioPortadorCartaoPorListaDocumento(listaPortadores);
            
            #endregion

            #region Tratamento e filtros
            
            var retornoTotal = string.IsNullOrWhiteSpace(order?.Campo)
                ? listMotoristaComCartao.ListaPortadores.OrderBy(x => x.Nome).AsQueryable()
                : listMotoristaComCartao.ListaPortadores.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}").AsQueryable();
            
            retornoTotal = retornoTotal.AplicarFiltrosDinamicos(filters);
            
            var retornoGridComSaldo = retornoTotal
                .Skip((page - 1) * take)
                .Take(take)
                .ToList()
                .Select(c => new DespesasViagemGridResponse()
                {
                    CpfCnpj = c.CpfCnpj.FormatarCpfCnpj(),
                    Identificador = c.Cartao.Identificador,
                    Produto = c.Cartao.Produto,
                    Nome = c.Nome,
                    Status = c.Cartao.Status,
                    AcessarExtrato = empresa.AcessarExtrato,
                    PermiteResgate = empresa.PermiteResgate,
                    AcessarExtratoDetalhado = acessarExtratoDetalhado,
                    DesabilitarBotoesResgate = usuarioAcessoResgate,
                    DesabilitarBotoesExtrato = usuarioAcessoExtrato
                })
                .ToList();

            var requestCartaoSaldo = new CartoesVinculadosListaSaldoRequest
            {
                CartoesVinculadosLista = retornoGridComSaldo.Select(x => new CartoesVinculadosItemListaSaldo
                {
                    Identificador = x.Identificador,
                    Produto = x.Produto,
                    CpfCnpj = x.CpfCnpj
                }).ToList()
            };

            var retorno = cartoesApp.ConsultarListaPortadorCartaoComSaldo(requestCartaoSaldo);

            var totalSaldo = 0M;
            
            if (retorno != null && retorno.Any())
            {
                foreach (var itemGrid in retornoGridComSaldo)
                {
                    var itemSaldo = retorno.FirstOrDefault(x => x.Documento == itemGrid.CpfCnpj && x.Identificador == itemGrid.Identificador);
            
                    if (itemSaldo?.Saldo != null)
                    {
                        totalSaldo += itemSaldo.Saldo.Value;
                        itemGrid.Saldo = itemSaldo.Saldo.FormatMoney();
                    }
                        
                        
                }
            }
            
            #endregion
            
            return new DespesasViagemGridSaldoResponse ()
            {
                totalItems = retornoTotal.Count(),
                items = retornoGridComSaldo,
                saldoTotal = totalSaldo.FormatMoney()
            };
        }

        public byte[] GerarRelatorioGridDespesaViagem(int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa, int? idUsuario)
        {
            try
            {
                var dados = ConsultarPortadorGrid(take,page,order,filters,idEmpresa,idUsuario);
                
                var empresa = _empresaService.All().Select(x => new 
                {
                    x.IdEmpresa,
                    x.Logo
                }).FirstOrDefault(x => x.IdEmpresa == idEmpresa);
                
                var logo = empresa?.Logo == null|| empresa?.Logo.Length == 0 ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

                var dadosReport = Mapper.Map<List<RelatorioDespesasViagemDataType>>(dados.items.ToList());

                return _despesasViagemService.GerarRelatorioGridDespesaViagem(logo, extensao, dadosReport);
            }
            catch (Exception)
            {
                return new byte[] {};
            }
        }
        
        public ExtratoImagensVinculadasResponse ConsultarImagensVinculadasExtrato(string hashID)
        {
            var retorno = new ExtratoImagensVinculadasResponse()
            {
                ListImagensBase64 = new List<ItemImagensVinculadas>()
            };
            
            var listImagemToken = _despesasUsuarioViagemService
                .GetAllWithInclude()
                .Where(x => x.HashId == hashID && x.ImageToken != null).ToList();

            if (listImagemToken.Any())
            {
                var lImagensMongo = _dataMediaServerService.GetListMedia(listImagemToken.Select(x => x.ImageToken).Distinct().ToList());
                
                if(lImagensMongo == null || !lImagensMongo.Any())
                    return retorno;

                var listaRetorno = listImagemToken.Select(x => new ItemImagensVinculadas()
                {
                    Descricao = x.Descricao,
                    Categoria = x.Categoria?.Descricao,
                    Imagem = lImagensMongo.FirstOrDefault(y => y._id == new ObjectId(x.ImageToken))?.Data
                }).ToList();
                
                retorno.ListImagensBase64.AddRange(listaRetorno);
            }

            return retorno;
        }
        
        public BusinessResult<ResgatePortadorResponse> ConsultarResgatePortador(string cpfCnpjPortador)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                return BusinessResult<ResgatePortadorResponse>.Error("Empresa não encontrada!");

            var empresa = _empresaService.All().Select(x => new 
            {
                x.IdEmpresa,
                CnpjEmpresa = x.CNPJ,
                Token = x.TokenMicroServices,
                EmpresaSenhaResgate = x.SenhaResgate,
                x.RazaoSocial
            }).FirstOrDefault(x => x.IdEmpresa == (int) _userIdentity.IdEmpresa);

            if (empresa == null)
                return BusinessResult<ResgatePortadorResponse>.Error("Empresa não encontrada!");

            var result = new ResgatePortadorResponse()
            {
                CnpjEmpresa = empresa.CnpjEmpresa.ToCNPJFormato(),
                RazaoSocial = empresa.RazaoSocial,
                EmpresaSenhaResgate = empresa.EmpresaSenhaResgate
            };

            
            if (cpfCnpjPortador.OnlyNumbers().Length <= 11)
            {
                var motorista = _motoristaService.GetAllByIdEmpresa(empresa.IdEmpresa)
                    .Select(x => new
                    {
                        x.CPF,
                        x.Nome,
                        x.Ativo,
                        x.TipoContrato
                    }).FirstOrDefault(x => x.CPF == cpfCnpjPortador && x.Ativo && x.TipoContrato == ETipoContrato.Frota);

                if (motorista != null)
                    result.Nome = motorista.Nome;
            }

            if (result.Nome == null)
            {
                var proprietario = _proprietarioService.GetAllByIdEmpresa(empresa.IdEmpresa)
                    .Select(x => new
                    {
                        x.CNPJCPF,
                        x.RazaoSocial,
                        x.Ativo,
                        x.TipoContrato
                    } ).FirstOrDefault(x => x.CNPJCPF == cpfCnpjPortador && x.Ativo && x.TipoContrato == ETipoContrato.Frota);
                
                if (proprietario != null)
                    result.Nome = proprietario.RazaoSocial;
            }

            if (result.Nome == null)
                return BusinessResult<ResgatePortadorResponse>.Error("Portador não encontrado!");
            
            //Cartão utilizado pelo portador da empresa 
            var cartoesAppEmpresa = new CartoesApp(empresa.IdEmpresa, empresa.Token, _userIdentity.CpfCnpj, _userIdentity.Nome, _cartoesAppFactoryDependencies);

            var resultCartao = cartoesAppEmpresa.GetCartaoFretePorVinculoDaEmpresa(cpfCnpjPortador.OnlyNumbers());
            
            if(resultCartao.Status == PessoaCartaoResponseStatus.Falha)
                return BusinessResult<ResgatePortadorResponse>.Error(resultCartao.Mensagem);

            if (resultCartao.Identificador == null || resultCartao.ProdutoId == null)
                return BusinessResult<ResgatePortadorResponse>.Error("Erro ao obter dados do cartão!");
            
            //Dados cartão
            result.Identificador = resultCartao.Identificador;
            result.Produto = resultCartao.ProdutoId;
                
            return BusinessResult<ResgatePortadorResponse>.Valid(result);
        }

        public BusinessResult<ResgatarValorResponseDTO> ResgatarValor(ResgatarValorDespesasViagemRequest request)
        {
            var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
            var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
            var infoAdministradora = cartoesApp.AdministradoraCartao(request.Cartao, request.Produto);
            var cartoesAppAdministradora = CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(), 
                _userIdentity.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
            int grupoContabilizacao = _parametrosApp.GetGrupoContabilizacaoCentralAtendimento();
            var empresa = _empresaService.All().Select(x => new 
            {
                x.IdEmpresa,
                CnpjEmpresa = x.CNPJ,
                Token = x.TokenMicroServices,
                EmpresaSenhaResgate = x.SenhaResgate,
                x.RazaoSocial
            }).FirstOrDefault(x => x.IdEmpresa == (int) _userIdentity.IdEmpresa);

            if (empresa == null)
                return BusinessResult<ResgatarValorResponseDTO>.Error("Empresa do usuário não localizada!");

            var empresaMicroServico = cartoesAppAdministradora.GetOrGenerateTokenEmpresa(empresa.CnpjEmpresa, "Central Atendimento", grupoContabilizacao);
            
            if(!empresaMicroServico.Sucesso)
                return BusinessResult<ResgatarValorResponseDTO>.Error(empresaMicroServico.Mensagem);
            
            //Verificar senha
            ValidarSenhaCartaoResponseDto senhaIsValid;

            if (empresa.EmpresaSenhaResgate)
            {
                senhaIsValid = cartoesApp.ValidarSenhaCartao(request.Cartao, request.Produto, request.Senha);
                if (!senhaIsValid.Sucesso) return BusinessResult<ResgatarValorResponseDTO>.Error(senhaIsValid.Mensagem);
            }
            
            //Verificar valor na conta
            var possuiSaldo = cartoesApp.ConsultarSaldoCartao(request.Documento);
                
            if(possuiSaldo.Status == ConsultarSaldoCartaoResponseStatus.Falha)
                return BusinessResult<ResgatarValorResponseDTO>.Error(possuiSaldo.Mensagem);
            
            if(request.Valor > possuiSaldo.ValorSaldoDisponivel)
                return BusinessResult<ResgatarValorResponseDTO>.Error("Valor excede o saldo que o portador possui na conta!");
            
            //Atribuições para conciliação de transações
            request.CpfUsuario = usuario.CPFCNPJ;
            request.Especificacao = "DV";
            
            var requestResgateValor = Mapper.Map<ResgatarValorDTO>(request);
            
            requestResgateValor.Empresa = empresa.CnpjEmpresa;
            
            var cartoesAppEmpresa = new CartoesApp(empresaMicroServico.IdEmpresa, empresaMicroServico.Token, 
                _userIdentity.CpfCnpj, _userIdentity.Nome, _cartoesAppFactoryDependencies);

            var resultado = cartoesAppEmpresa.ResgatarValor(requestResgateValor);

            if (resultado.Status != ResgatarValorResponseStatus.Sucesso)
                return BusinessResult<ResgatarValorResponseDTO>.Error(resultado.Mensagem);
            
            return BusinessResult<ResgatarValorResponseDTO>.Valid(resultado);
        }

        public byte[] ConsultarRelatorioExtratoGrid(RelatorioGridExtratoDTO request)
        {
            try
            {
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(request.IdUsuarioLogado);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                var empresa = _empresaService.All().Select(x => new 
                {
                    x.IdEmpresa,
                    x.Logo
                }).FirstOrDefault(x => x.IdEmpresa == usuario.IdEmpresa);

                var logo = empresa?.Logo == null || empresa?.Logo.Length == 0 ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

                byte[] retorno; 
            
                var requestApi = new ConsultarExtratoRequest
                {
                    DataFim = request.DataFim,
                    DataInicio = request.DataInicio,
                    Tipo = request.Tipo,
                    Cartao = new IdentificadorCartao
                    {
                        Produto = request.Produto,
                        Identificador = request.Identificador
                    },
                    SomenteTransferencia = request.SomenteTransferencia
                };

                if (request.Extensao.ToLower() == "csv")
                    return cartoesApp.GerarRelatorioExtratoGrid(requestApi,request);
                 
                var response = cartoesApp.ConsultarExtrato(requestApi);

                if (!response.Sucesso)
                    return new byte[] {};

                var listaExtrato = response.Objeto.Detalhes.Select(x => new RelatorioExtratoDespesasViagemDataType
                    {
                        Descricao = x.Historico.DefaultIfNullOrWhiteSpace(x.DescricaoProcessadora),
                        DataTransacao = x.Data.FormatDateTimeBr(),
                        DataTransacaoDateTime = x.Data ?? DateTime.Now,
                        Informacoes = string.Join(" / ",
                            x.InformacoesAdicionais.Select(i => i.Key + ": " + i.Value)),
                        SaldoFinal = x.SaldoFinal.FormatMoney(),
                        Tipo = x.Tipo == "D" ? "Débito" : "Crédito",
                        Valor = x.Valor.FormatMoney(),
                        ValorDecimal = x.Valor ?? 0
                    })
                    .AsQueryable()
                    .AplicarFiltrosDinamicos(request.filters)
                    .AplicarOrderByDinamicos(request.Order)
                    .ToList();
                
                if (string.Equals(request.Extensao, EExtensaoArquivoRelatorio.Ofx.ToString(), StringComparison.CurrentCultureIgnoreCase))
                {
                    if (_userIdentity.IdEmpresa != null && !_parametrosApp.GetUtilizaRelatoriosOfx(_userIdentity.IdEmpresa.Value)) return new byte[] { };
                    
                    var conta = response.Objeto.Detalhes.Where(c => !string.IsNullOrWhiteSpace(c.Conta))
                        .Select(c => c.Conta).FirstOrDefault();
                    var saldo = response.Objeto.SaldoFinalPeriodo;
                    var req = new OfxReportRequest
                    {
                        Conta = conta,
                        DataFim = request.DataFim,
                        DataInicio = request.DataInicio,
                        Saldo = saldo,
                        Itens = new List<OfxReportRequestItem>(),
                        ContaParametrizado = _parametrosApp.GetCodigoOfx(empresa?.IdEmpresa ?? 0)
                    };

                    foreach (var item in listaExtrato)
                    {
                        var ofxItem = new OfxReportRequestItem()
                        {
                            Valor = item.Tipo == "Crédito" ? item.ValorDecimal : -item.ValorDecimal,
                            DataHoraTransacao = item.DataTransacaoDateTime,
                            Detalhes = item.Informacoes
                        };
                    
                        req.Itens.Add(ofxItem);
                    }

                    return OfxHelper.GenerateOfxResponse(req);
                }

                retorno =  _despesasViagemService.GerarRelatorioExtratoGridDespesaViagem(logo,request.Extensao, listaExtrato);

                return retorno;
            }
            catch (Exception)
            {
                return new byte[] {};
            }
        }

        public byte[] ConsultarRelatorioExtratoV2Grid(RelatorioGridExtratoDetalhadoDTO request)
        {
            try
            {
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(request.IdUsuarioLogado);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                var empresa = _empresaService.All().Select(x => new
                {
                    x.IdEmpresa,
                    x.Logo
                }).FirstOrDefault(x => x.IdEmpresa == usuario.IdEmpresa);

                var logo = empresa?.Logo == null || empresa?.Logo.Length == 0 ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

                byte[] retorno;

                var requestApi = new ConsultaExtratoV2DTORequest
                {
                    DataFim = request.DataFim,
                    DataInicio = request.DataInicio,
                    Produto = request.Produto,
                    Identificador = request.Identificador,
                    Page = request.Page,
                    Take = request.Take,
                };
       
                var response = cartoesApp.ConsultarExtratoV2(requestApi);

                if (!response.Success)
                    return new byte[] { };

                List<RelatorioExtratoDetalhadoDespesasViagemDataType> listaExtrato;

                 listaExtrato = response.Value.Itens.Select(x => new RelatorioExtratoDetalhadoDespesasViagemDataType
                {
                    DataHoraTransacao = x.DataHoraTransacao,
                    ValorTransacao = x.ValorTransacao,
                    DescricaoPlanoVendas = x.DescricaoPlanoVendas,
                    NumeroCartao = x.NumeroCartao,
                    DocumentoPortador = x.DocumentoPortador,
                    NumeroConta = x.NumeroConta,
                    Mcc = x.Mcc,
                    DescricaoMcc = x.DescricaoMcc,
                    DescricaoGrupoMcc = x.DescricaoGrupoMcc,
                    ValorTransacaoDolar = x.ValorTransacaoDolar,
                    CodigoMoedaDolar = x.CodigoMoedaDolar,
                    CotacaoDolarnoDia = x.CotacaoDolarnoDia,
                    ValorLocal = x.ValorLocal,
                    CodigoMoedaLocal = x.CodigoMoedaLocal,
                    TerminalId = x.TerminalId,
                })
                    .AsQueryable()
                .ToList();

                retorno = _despesasViagemService.GerarRelatorioExtratoV2GridDespesaViagem(logo, request.Extensao, listaExtrato);


                return retorno;
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult GerarRelatorioExtratoDetalhadoOfx(RelatorioGridExtratoDetalhadoDTO request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    throw new InvalidOperationException("Dados inválidos.");

                var utilizaRelatoriosOfx = _parametrosApp.GetUtilizaRelatoriosOfx(_userIdentity.IdEmpresa.Value);
                if (!utilizaRelatoriosOfx)
                    throw new InvalidOperationException("Empresa sem permissão.");

                if (request.Identificador == 0)
                    throw new InvalidOperationException("Identificador não informado.");

                //Validação de data
                request.DataInicio = request.DataInicio.StartOfDay();
                request.DataFim = request.DataFim.EndOfDay();
                if (request.DataInicio > request.DataFim)
                    return BusinessResult.Error("Data inicial não pode ser maior que a final!");
                if (request.DataFim > DateTime.Now.EndOfDay())
                    return BusinessResult.Error("Data final não pode ser maior que a data atual!");
                if (request.DataFim > request.DataInicio.AddDays(30).EndOfDay())
                    return BusinessResult.Error("Não é permitido consultar um periodo maior que 30 dias!");
                
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(request.IdUsuarioLogado);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                var req = new ConsultarSaldoCartaoRequest
                {
                    Cartao = new IdentificadorCartao()
                    {
                        Identificador = request.Identificador,
                        Produto = request.Produto
                    }
                };
                var saldo = cartoesApp.ConsultarSaldoCartaoAtendimento(req);
                
                if(saldo?.ValorSaldoDisponivel == null)
                    throw new InvalidOperationException("Não foi possível consultar o saldo do portador.");
                    
                var email = _usuarioApp.Find(c => c.IdUsuario == _userIdentity.IdUsuario)
                    .Select(c => c.Contatos.FirstOrDefault()).Select(c => c.Email).FirstOrDefault();
                if(string.IsNullOrWhiteSpace(email))
                    throw new InvalidOperationException("E-mail do usuário não está configurado no cadastro, por favor preencha-o para receber o relatório.");

                var emails = new List<string> { email };
                
                var solicitacaoExtrato = _extrattaBizApiClient.GetRelatorioDespesasViagemOfx(request.Identificador,
                    request.Produto, request.DataInicio, request.DataFim, saldo.ValorSaldoDisponivel.Value, emails);

                if (!solicitacaoExtrato.Success)
                    throw new InvalidOperationException(solicitacaoExtrato.Messages.FirstOrDefault());

                var emailCensurado = email.CensorEmail();
                
                return BusinessResult.Valid($"Seu arquivo será gerado e será enviado para o e-mail {emailCensurado}");
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
    }
}

﻿using System.Data.Entity;
using System.Linq;
using System.Web.UI.WebControls;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CredenciamentoAnexoRepository : Repository<CredenciamentoAnexo>, ICredenciamentoAnexoRepository
    {
        public CredenciamentoAnexoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<CredenciamentoAnexo> GetAll(int idEstabelecimentoBase)
        {
            var query = GetAll()
                .Include(x => x.Credenciamento)
                .Where(x => x.Credenciamento.IdEstabelecimentoBase == idEstabelecimentoBase);
            
            return query;
        }
    }
}

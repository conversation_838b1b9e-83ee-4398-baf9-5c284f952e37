using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class RelatorioRoteirizadorModel
    {
        public int? IdEmpresa { get; set; }

        public string Mapa { get; set; }
        
        public string NumeroEixos { get; set; }

        public int ModoViagem { get; set; }

        public decimal Distancia { get; set; }

        public decimal PrecoTotalPedagio { get; set; }
        public decimal PrecoTotalTagPedagio { get; set; }
        public List<RelatorioRoteirizadorPostosModel> Postos { get; set; }

        public List<RelatorioRoteirizadorFiltrosModel> Filtros { get; set; }

        public List<RelatorioRoteirizadorPracasPedagioModel> PracasPedagio { get; set; }
    }

    public class RelatorioRoteirizadorPostosModel
    {
        public int IdEstabelecimento { get; set; }
        
        public string NomeEstabelecimento { get; set; }

        public string Endereco { get; set; }

        public string TipoEstabelecimento { get; set; }

        public string NomeProduto { get; set; }

        public decimal? Preco { get; set; }

        public decimal? PrecoPromocional { get; set; }
    }

    public class RelatorioRoteirizadorFiltrosModel
    {
        public string Sequencia { get; set; }

        public string Local { get; set; }
    }

    public class RelatorioRoteirizadorPracasPedagioModel
    {
        public string Praca { get; set; }
        public decimal Preco { get; set; }
        public decimal PrecoTag { get; set; }

    }
}
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Models;
using ATS.Data.Repository.External.Serpro;
using ATS.Data.Repository.External.Serpro.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using NLog;
using Sistema.Framework.Util.Extension;

namespace ATS.Application.Application
{
    public class SerproApp : AppBase, ISerproApp
    {
        private readonly Logger _logger;
        private readonly IUserIdentity _userIdentity;
        private readonly ISerproClient _serproClient;
        private readonly ISerproCacheRepository _serproCacheRepository;

        // Dados quaisquer só pra api deles não dar erro
        private const string Foto = "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";
        private const string NomeMae = "Nome Da Mãe";
        private const string Cnh = "12345678901";
        private const string Date = "2000-01-01T00:00:00Z";

        public SerproApp(IUserIdentity userIdentity, ISerproCacheRepository serproCacheRepository)
        {
            _serproClient = new SerproClient(HttpContext.Current);
            _userIdentity = userIdentity;
            _serproCacheRepository = serproCacheRepository;
            _logger = LogManager.GetCurrentClassLogger();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="cpf">Cpf do proprietario/motorista</param>
        /// <returns></returns>
        public ValidationResult ValidarPortador(string cpf)
        {
            cpf = cpf.OnlyNumbers();
            
            if(!_userIdentity.IdEmpresa.HasValue)
                return new ValidationResult();

            if (string.IsNullOrWhiteSpace(cpf))
                return new ValidationResult().Add("CPF não definido.");

            var cache = ConsultarCache(cpf);

            if (cache.Success)
            {
                if (cache.Value.Cpf == null || cache.Value.Cpf.Valid == false)
                    return new ValidationResult().Add("Não é possível cadastrar o motorista/proprietário. CPF inválido.");
                
                return new ValidationResult();
            }
            
            var resp = _serproClient.ValidarPortador(new ValidacaoSerproRequest()
            {
                Cpf = cpf,
                BirthDate = Date,
                MotherName = NomeMae,
                PhotoBase64 = Foto,
                DriverLicenseRegisterNumber = Cnh,
                FirstDriverLicenseDate = Date
            });

            if (!resp.Success)
                return new ValidationResult().Add(
                    $"Ocorreu um erro ao validar o portador: {resp.Messages.FirstOrDefault()}");
            
            SalvarCache(resp.Value,cpf);
            
            if (resp?.Value.Cpf == null || resp.Value.Cpf.Valid == false)
                return new ValidationResult().Add("Não é possível cadastrar o motorista/proprietário. CPF inválido.");

            return new ValidationResult();
        }

        public IntegracaoResult<ValidacaoSerproResponse> ValidarPortador(ValidacaoSerproRequest request)
        {
            try
            {
                if (request == null)
                    throw new InvalidOperationException("Dados inválidos.");
                
                if (string.IsNullOrWhiteSpace(request.BirthDate))
                    throw new InvalidOperationException("Data de nascimento inválida.");

                if (string.IsNullOrWhiteSpace(request.Cpf) || request.Cpf.Length != 11)
                    throw new InvalidOperationException("CPF inválido.");

                if (string.IsNullOrWhiteSpace(request.DriverLicenseRegisterNumber))
                    throw new InvalidOperationException("CNH inválida.");

                if (string.IsNullOrWhiteSpace(request.PhotoBase64))
                    throw new InvalidOperationException("Foto inválida.");
                
                var cache = ConsultarCache(request.Cpf.OnlyNumbers());

                if (cache.Success)
                    return IntegracaoResult<ValidacaoSerproResponse>.Valid(cache.Value);

                var resp = _serproClient.ValidarPortador(request);

                if (!resp.Success)
                    return IntegracaoResult<ValidacaoSerproResponse>.Error($"Ocorreu um erro ao validar os dados. {resp.Messages?.FirstOrDefault()}");

                if (!string.IsNullOrWhiteSpace(resp.Value.Message))
                    return IntegracaoResult<ValidacaoSerproResponse>.Error($"Não foi possível validar os dados. {resp.Value.Message}");
                
                SalvarCache(resp.Value,request.Cpf.OnlyNumbers());

                return IntegracaoResult<ValidacaoSerproResponse>.Valid(resp.Value);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return IntegracaoResult<ValidacaoSerproResponse>.Error(e.Message);
            }
        }

        private void SalvarCache(ValidacaoSerproResponse cache,string cpfcnpj)
        {
            try
            {
                var entity = new SerproCache()
                {
                    DataCadastro = DateTime.Now,
                    CpfCnpj = cpfcnpj,
                    SerproCacheResultado = new List<SerproCacheResultado>()
                };

                #region Resultado
                
                if (cache.BirthDate != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.BirthDate,
                        Similaridade = (decimal) (cache.BirthDate.Similarity ?? 0),
                        Valido = cache.BirthDate.Valid ?? false
                    });
                }
                
                if (cache.PhotoBase64 != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.PhotoBase64,
                        Similaridade = (decimal) (cache.PhotoBase64.Similarity ?? 0),
                        Valido = cache.PhotoBase64.Valid ?? false
                    });
                }
                
                if (cache.Cpf != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.Cpf,
                        Similaridade = (decimal) (cache.Cpf.Similarity ?? 0),
                        Valido = cache.Cpf.Valid ?? false
                    });
                }
                
                if (cache.MotherName != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.MotherName,
                        Similaridade = (decimal) (cache.MotherName.Similarity ?? 0),
                        Valido = cache.MotherName.Valid ?? false
                    });
                }
                
                if (cache.DriverLicenseRegisterNumber != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.DriverLicenseRegisterNumber,
                        Similaridade = (decimal) (cache.DriverLicenseRegisterNumber.Similarity ?? 0),
                        Valido = cache.DriverLicenseRegisterNumber.Valid ?? false
                    });
                }
                
                if (cache.FirstDriverLicenseDate != null)
                {
                    entity.SerproCacheResultado.Add(new SerproCacheResultado()
                    {
                        Tipo = ETipoSerproCacheResultado.FirstDriverLicenseDate,
                        Similaridade = (decimal) (cache.FirstDriverLicenseDate.Similarity ?? 0),
                        Valido = cache.FirstDriverLicenseDate.Valid ?? false
                    });
                }

                #endregion

                _serproCacheRepository.Add(entity);
            }
            catch (Exception e)
            {
                _logger.Error($"Falha ao cadastrar cache serpro: {e.Message}");
            }
        }

        private BusinessResult<ValidacaoSerproResponse> ConsultarCache(string cpfcnpj)
        {
            try
            {
                var periodoDiasValidoCache =  
                    WebConfigurationManager.AppSettings["ValidadeCacheSerproDias"].ToIntSafe() ?? 5;

                var dataValida = DateTime.Now.AddDays(-periodoDiasValidoCache);
                var cache = _serproCacheRepository
                    .All()
                    .Include(x => x.SerproCacheResultado)
                    .Where(x => x.CpfCnpj == cpfcnpj 
                                && x.DataCadastro > dataValida)
                    .OrderByDescending(x => x.DataCadastro)
                    .FirstOrDefault();

                if (cache == null)
                    return BusinessResult<ValidacaoSerproResponse>.Error("Cache não encontrado!");
                
                return BusinessResult<ValidacaoSerproResponse>.Valid(new ValidacaoSerproResponse()
                {
                    Cpf = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.Cpf)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.Cpf)?.Valido
                    }, 
                    BirthDate = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.BirthDate)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.BirthDate)?.Valido
                    }, 
                    MotherName = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.MotherName)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.BirthDate)?.Valido
                    },
                    DriverLicenseRegisterNumber = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.DriverLicenseRegisterNumber)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.DriverLicenseRegisterNumber)?.Valido
                    },
                    FirstDriverLicenseDate = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.FirstDriverLicenseDate)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.FirstDriverLicenseDate)?.Valido
                    },
                    PhotoBase64 = new ValidacaoSerproItem
                    {
                        Similarity = (double?) cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.PhotoBase64)?.Similaridade,
                        Valid = cache.SerproCacheResultado?.FirstOrDefault(x => x.Tipo == ETipoSerproCacheResultado.PhotoBase64)?.Valido
                    }
                });
            }
            catch (Exception e)
            {
                return BusinessResult<ValidacaoSerproResponse>.Error($"Falha ao consultar cache {e.Message}");
            }
        }
    }
}
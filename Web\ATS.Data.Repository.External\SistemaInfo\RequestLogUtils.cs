﻿using System;
using System.Web;

namespace ATS.Data.Repository.External.SistemaInfo
{
    public class RequestLogUtils
    {
        public class RequestLogInfo
        {
            public Guid? CurrentLogId { get; set; }
            public int CurrentLogNivel { get; set; }
            //public Guid? ParentLogId { get; set; }
            public Guid? RootLogId { get; set; }
        }

        /// <summary>
        /// Obter metadados da requisição em execução para persitência na hierarquia de log's
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public static RequestLogInfo GetLogInfo(HttpContext httpContext)
        {
            Guid currrentLogId;
            int currentNivel = 0;

            if (httpContext?.Items == null)
                return null;

            if (!httpContext.Items.Contains(SistemaInfoConsts.CurrentLogIdSessionKey))
                return null;

            var currentLogIdObj = httpContext.Items[SistemaInfoConsts.CurrentLogIdSessionKey];
            if (!Guid.TryParse(currentLogIdObj.ToString(), out currrentLogId))
                return null;

            if (httpContext.Items.Contains(SistemaInfoConsts.CurrentLogIdSessionKey))
            {
                var nivelObj = httpContext.Items[SistemaInfoConsts.CurrentLogNivelSessionKey];
                int.TryParse(nivelObj.ToString(), out currentNivel);
            }

            return new RequestLogInfo
            {
                CurrentLogId = currrentLogId,
                CurrentLogNivel = currentNivel,
                RootLogId = currrentLogId
            };
        }

        /// <summary>
        /// Obter metadados da requisição em execução para persitência na hierarquia de log's
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public static RequestLogInfo GetNotNullLogInfo(HttpContext httpContext)
        {
            Guid currrentLogId;
            int currentNivel = 0;

            if (httpContext?.Items == null)
                return new RequestLogInfo
                {
                    CurrentLogId = null,
                    CurrentLogNivel = currentNivel,
                    RootLogId = null
                };

            if (!httpContext.Items.Contains(SistemaInfoConsts.CurrentLogIdSessionKey))
                return new RequestLogInfo
                {
                    CurrentLogId = null,
                    CurrentLogNivel = currentNivel,
                    RootLogId = null
                };

            var currentLogIdObj = httpContext.Items[SistemaInfoConsts.CurrentLogIdSessionKey];
            if (!Guid.TryParse(currentLogIdObj.ToString(), out currrentLogId))
                return new RequestLogInfo
                {
                    CurrentLogId = null,
                    CurrentLogNivel = currentNivel,
                    RootLogId = null
                };

            if (httpContext.Items.Contains(SistemaInfoConsts.CurrentLogIdSessionKey))
            {
                var nivelObj = httpContext.Items[SistemaInfoConsts.CurrentLogNivelSessionKey];
                int.TryParse(nivelObj.ToString(), out currentNivel);
            }

            return new RequestLogInfo
            {
                CurrentLogId = currrentLogId,
                CurrentLogNivel = currentNivel,
                RootLogId = currrentLogId
            };
        }
    }
}
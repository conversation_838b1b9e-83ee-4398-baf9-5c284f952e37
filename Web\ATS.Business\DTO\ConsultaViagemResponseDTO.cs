using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class ConsultaViagemExternalResponseDTO
    {
        public int totalItems { get; set; }
        public IList<ConsultaViagemExternalItensResponseDTO> items { get; set; }
    }

    public class ConsultaViagemExternalItensResponseDTO
    {
        public int IdViagem { get; set; }
        public string DataEmissao { get; set; }
        public string DataIntegracao { get; set; }
        public string DataAtualizacao { get; set; }
        public string EmpresaCnpj { get; set; }
        public string EmpresaNome { get; set; }
        public string FilialCnpj { get; set; }
        public string FilialNome { get; set; }
        public string DataInicioFrete { get; set; }
        public string DataFimFrete { get; set; }
        public string ClienteOrigemCnpjCpf { get; set; }
        public string ClienteOrigemNome { get; set; }
        public string ClienteDestinoCnpjCpf { get; set; }
        public string ClienteDestinoNome { get; set; }
        public string Placa  { get; set; }
        public string ProprietarioCnpjCpf { get; set; }
        public string ProprietarioNome { get; set; }
        public string RazaoSocial { get; set; }
        public string ProprietarioRntrc { get; set; }
        public string MotoristaCpf { get; set; }
        public string MotoristaNome { get; set; }
        public string DocumentoCliente { get; set; }
        public bool ValePedagioConfirmado { get; set; }
        public bool CiotHabilitado { get; set; }
        public bool CiotGerado { get; set; }
        public string CIOT { get; set; }
        public decimal ValorSaldo { get; set; }
        public decimal ValorAdiantamento { get; set; }
        public decimal ValorEstadia { get; set; }
        public decimal ValorAbasteciemnto { get; set; }
        public decimal ValorTarifa { get; set; }
        public decimal ValorPedagio { get; set; }
        public string ValorSaldoStr { get; set; }
        public string ValorAdiantamentoStr { get; set; }
        public string ValorTarifaStr { get; set; }
        public string ValorPedagioStr { get; set; }
        public string StatusViagem { get; set; }
        public string InssStr { get; set; }
        public string IrrfStr { get; set; }
        public string SestsenatStr { get; set; }
        public decimal Inss { get; set; }
        public decimal Irrf { get; set; }
        public decimal Sestsenat { get; set; }
        public FornecedorEnum FornecedorPedagio { get; set; }
        public string FornecedorPedagioStr { get; set; }
        public string FornecedorCnpj { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public EResultadoCompraPedagio ResultadoCompraPedagio { get; set; }
        public string ResultadoCompraPedagioStr { get; set; }
        public string ValorAbastecimentoStr { get; set; }
        public string ValorEstadiaStr { get; set; }
    }

    public class ConsultaViagemInternalResponseDTO
    {
        public int IdViagem { get; set; }
        public DateTime? DataEmissao { get; set; }
        public DateTime DataPrevisaoEntrega { get; set; }
        public DateTime DataIntegracao { get; set; }
        public DateTime DataAtualizacao { get; set; }
        public DateTime DataColeta { get; set; }
        public string EmpresaCnpj { get; set; }
        public string EmpresaNome { get; set; }
        public string FilialCnpj { get; set; }
        public string FilialNome { get; set; }
        public string ClienteOrigemCnpjCpf { get; set; }
        public string ClienteOrigemNome { get; set; }
        public string ClienteDestinoCnpjCpf { get; set; }
        public string ClienteDestinoNome { get; set; }
        public string Placa { get; set; }
        public string ProprietarioNome { get; set; }
        public string RazaoSocial { get; set; }
        public string ProprietarioCnpjCpf { get; set; }
        public int? ProprietarioRntrc { get; set; }
        public string MotoristaNome { get; set; }
        public string MotoristaCpf { get; set; }
        public string DocumentoCliente { get; set; }
        
        public int CiotHabilitado { get; set; }
        public bool CiotHabilitadoBoolean => CiotHabilitado == 1;

        public int HasCIOT { get; set; }
        public bool HasCIOTBoolean => HasCIOT == 1;
        
        public string CIOT { get; set; }
        public string Verificador { get; set; }
        public decimal ValorAdiantamento { get; set; }
        public decimal ValorEstadia { get; set; }
        public decimal ValorAbastecimento { get; set; }
        public decimal ValorSaldo { get; set; }
        public decimal ValorTarifa { get; set; }
        public decimal ValorPedagio { get; set; }
        public EStatusViagem StatusViagem { get; set; }
        public decimal Inss { get; set; }
        public decimal Irrf { get; set; }
        public decimal Sestsenat { get; set; }
        public FornecedorEnum FornecedorPedagio { get; set; }
        public string FornecedorCnpj { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public EResultadoCompraPedagio ResultadoCompraPedagio { get; set; }
        public string MensagemCompraPedagio { get; set; }
    }

    public class ConsultaViagemExternalCiotResponseDTO
    {
        public enum ETipoViagemConsultaCiot
        {
            [EnumMember, Description("Padrão")]
            Padrao = 1,
        
            [EnumMember, Description("TAC Agregado")]
            TacAgregado = 3
        }
        
        public string Ciot { get; set; }
        public string Verificador { get; set; }
        public string DataInicioFrete { get; set; }
        public string DataFimFrete { get; set; }
        public string StatusCiot { get; set; }
        public string AvisoAoTransportador { get; set; }
        public string TipoCiot { get; set; }
    }
}
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Categoria;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Service
{
    public class CategoriaService : BaseService<ICategoriaRepository>, ICategoriaService
    {
        private readonly ILogger _logger;

        public CategoriaService(ICategoriaRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
            _logger = LogManager.GetCurrentClassLogger();
        }

        public BusinessResult<CategoriaAddModelResponse> Add(CategoriaAddModel model)
        {
            try
            {
                var categoria = Mapper.Map<Categoria>(model);

                var entity = Repository.Add(categoria);

                return BusinessResult<CategoriaAddModelResponse>.Valid(new CategoriaAddModelResponse
                {
                    IdCategoria = entity.IdCategoria
                });
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaAddModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<CategoriaUpdateModelResponse> Update(CategoriaUpdateModel model)
        {
            try
            {
                var categoria = Repository.FirstOrDefault(c => c.IdCategoria == model.IdCategoria);

                if (categoria == null)
                    return BusinessResult<CategoriaUpdateModelResponse>.Error("Não foi encontrada transação com o ID repassado");

                categoria.Descricao = model.Descricao;

                Repository.Update(categoria);

                return BusinessResult<CategoriaUpdateModelResponse>.Valid(new CategoriaUpdateModelResponse
                {
                    IdCategoria = categoria.IdCategoria
                });
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaUpdateModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<CategoriaEnableModelResponse> Enable(CategoriaEnableModel model)
        {
            try
            {
                var categoria = Repository.FirstOrDefault(c => c.IdCategoria == model.IdCategoria);

                if (categoria == null)
                    return BusinessResult<CategoriaEnableModelResponse>.Error("Não foi encontrada transação com o ID repassado");

                categoria.Ativo = true;

                Repository.Update(categoria);

                return BusinessResult<CategoriaEnableModelResponse>.Valid(new CategoriaEnableModelResponse
                {
                    IdCategoria = categoria.IdCategoria
                });
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaEnableModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<CategoriaDisableModelResponse> Disable(CategoriaDisableModel model)
        {
            try
            {
                var categoria = Repository.FirstOrDefault(c => c.IdCategoria == model.IdCategoria);

                if (categoria == null)
                    return BusinessResult<CategoriaDisableModelResponse>.Error("Não foi encontrada transação com o ID repassado");

                categoria.Ativo = false;

                Repository.Update(categoria);

                return BusinessResult<CategoriaDisableModelResponse>.Valid(new CategoriaDisableModelResponse
                {
                    IdCategoria = categoria.IdCategoria
                });
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaDisableModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<CategoriaGetModelResponse> Get(int categoriaId)
        {
            try
            {
                var categoria = Repository.FirstOrDefault(c => c.IdCategoria == categoriaId);

                if (categoria == null)
                    return BusinessResult<CategoriaGetModelResponse>.Error("Não foi encontrada transação com o ID repassado");

                var response = Mapper.Map<CategoriaGetModelResponse>(categoria);

                return BusinessResult<CategoriaGetModelResponse>.Valid(response);
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaGetModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<CategoriaGetGridModelResponse> GetGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var query = Repository.Consultar(filters, order);

                var response = new CategoriaGetGridModelResponse
                {
                    TotalItems = query.Count(),
                    Items = query.Skip((page - 1) * take).Take(take).Select(x => new CategoriaGetItemGridModelResponse
                    {
                        IdCategoria = x.IdCategoria,
                        Descricao = x.Descricao,
                        Ativo = x.Ativo
                    }).ToList()
                };

                return BusinessResult<CategoriaGetGridModelResponse>.Valid(response);
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<CategoriaGetGridModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }

        public BusinessResult<IList<CategoriaGetModelResponse>> GetAll(bool filterActive)
        {
            try
            {
                var query = Repository.Query();

                if (filterActive)
                    query = query.Where(c => c.Ativo == filterActive);

                var list = Mapper.Map<IList<CategoriaGetModelResponse>>(query.ToList());

                return BusinessResult<IList<CategoriaGetModelResponse>>.Valid(list);
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<IList<CategoriaGetModelResponse>>.Error($"Ocorreu um erro interno: {message}");
            }
        }
    }
}

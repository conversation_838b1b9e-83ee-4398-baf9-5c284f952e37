using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class WebHookMap : EntityTypeConfiguration<Webhook>
    {
        public WebHookMap()
        {

            ToTable("WebHook");

            HasKey(t => t.Id);

            Property(t => t.Id)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Endpoint)
                .IsRequired()
                .HasMaxLength(2000);

            Property(t => t.Headers)
                .IsRequired()
                .HasMaxLength(2000);


            Property(t => t.Verbo)
                .IsRequired()
                .HasMaxLength(30);

            Property(t => t.Aplicacao)
                .IsRequired()
                .HasMaxLength(100);
        }
    }
}
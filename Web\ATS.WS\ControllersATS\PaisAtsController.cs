﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class PaisAtsController : DefaultController
    {
        private readonly IPaisApp _paisApp;

        public PaisAtsController(IPaisApp paisApp)
        {
            _paisApp = paisApp;
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(string nome, string sigla, int? bacen, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                return ResponderSucesso(_paisApp.ConsultaGrid(nome, sigla, bacen, take, page, order, filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult BuscarBrasil()
        {
            try
            {
                return ResponderSucesso(_paisApp.BuscarBrasil());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int IdPais)
        {
            try
            {
                _paisApp.Inativar(IdPais);

                return ResponderSucesso("País inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int IdPais)
        {
            try
            {
                _paisApp.Reativar(IdPais);

                return ResponderSucesso("País reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarAtualizar(PaisModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                var pais = new Pais();
                pais.Nome = @params.Nome;
                pais.Sigla = @params.Sigla;
                pais.BACEN = @params.BACEN;
                if (@params.IdPais.HasValue)
                {
                    pais.IdPais = @params.IdPais.Value;
                    var validationResult = _paisApp.Update(pais);

                    if (validationResult.IsValid)
                        return ResponderSucesso($"País atualizado com sucesso!");
                    else
                        return ResponderErro(validationResult.ToString());
                }
                else
                {
                    var validationResult = _paisApp.Add(pais);

                    return validationResult.IsValid 
                        ? ResponderSucesso("País cadastrado com sucesso!")
                        : ResponderErro(validationResult.ToString());

                }
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Consultar()
        {
            try
            {
                var paises = _paisApp
                    .Consultar(string.Empty)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { x.IdPais, x.Nome });

                return ResponderSucesso(paises);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTodos()
        {
            try
            {
                var paises = _paisApp
                    .Consultar(string.Empty)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { Codigo = x.IdPais, Descricao = x.Nome });

                return ResponderSucesso(paises);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Get(int IdPais)
        {
            try
            {
                var pais = _paisApp.Get(IdPais);
                var retorno = new
                {
                    pais.IdPais,
                    pais.Nome,
                    pais.BACEN,
                    pais.Sigla,
                    pais.Ativo
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idPais)
        {
            try
            {
                var pais = _paisApp.Get(idPais);

                if (pais == null)
                    throw new Exception($"Não foi possível encontrar um país com o id {idPais}");

                return ResponderSucesso(pais);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
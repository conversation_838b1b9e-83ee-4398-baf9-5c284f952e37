﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    public class ContratoAberturaModel
    {
        public int? IdEmpresa { get; set; }
        public int? IdProprietario { get; set; }
        public DateTime? DataInicio { get; set; }
        public DateTime? DataFinal { get; set; }
        public int? IdDeclaracaoCiot { get; set; }
        public DateTime? DataCadastro { get; set; }
        public int? IdUsuario { get; set; }
        public EStatusContratoAgregado Status { get; set; }
        public string MensagemIntegracao { get; set; }
        public int QuantidadeTarifas { get; set; }
        public decimal ValorTarifas { get; set; }
        public decimal ValorCombustivel { get; set; }
        public decimal ValorPedagio { get; set; }
        public decimal? ValorINSS;
        public decimal? ValorIRRF;
        public decimal? ValorSESTSENAT;
        public ICollection<VeiculoModelAgregado> Vei<PERSON><PERSON> { get; set; }
    }
}
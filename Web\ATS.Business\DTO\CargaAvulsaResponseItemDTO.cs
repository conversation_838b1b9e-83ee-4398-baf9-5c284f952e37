using System;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.DTO
{
    public class CargaAvulsaResponseItemDTO
    {
        public int? IdCargaAvulsa { get; set; }
        public string NroControleIntegracao { get; set; }
        public string DataCadastro => DataCadastroDateTime.ToString("G");
        public DateTime DataCadastroDateTime { get; set; }
        public string Nome { get; set; }
        public decimal Valor { get; set; }
        public string ValorStr { get; set; }
        public string CPF { get => _cpf; set => _cpf = value.OnlyNumbers().FormatarCpfCnpj(); } 
        private string _cpf { get; set; }
        public string NomeMotorista { get; set; }
        public string Observacao { get; set; }
        public EStatusPagamentoCartao? StatusPagamentoEnum { get; set; }
        public string StatusPagamento { get => StatusPagamentoEnum.HasValue && StatusPagamentoEnum != 0 ? StatusPagamentoEnum.GetDescription() : "Não Processado"; }
        public string Processamento { get; set; }
        public ETipoCarga TipoCargaEnum { get; set; }
        public string TipoCarga { get => TipoCargaEnum.GetDescription(); }
        public string StatusCargaAVulsaStr { get; set; }
        public EStatusCargaAvulsa? StatusCargaAvulsa { get; set; }
        public string Placa { get; set; }
        public string StatusCargaAvulsaStr { get; set; }
        public string CodigoPlanilhaImportada { get; set; }
        public string MotivoDescricao { get; set; }
        public int? IdentificadorCartao { get; set; }
    }
}
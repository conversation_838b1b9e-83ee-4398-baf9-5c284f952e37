﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem
{
    public class RelatorioExtratoDespesasViagem
    {
        public byte[] GetReport(List<RelatorioExtratoDespesasViagemDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var dataSources = new Tuple<object, string>(listaDados, "DtsExtratoDespesasViagem");

            var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                "ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem.RelatorioExtratoDespesasViagem.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

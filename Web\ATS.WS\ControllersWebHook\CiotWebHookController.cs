using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Ciot;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersWebHook
{
    public class CiotWebHookController : DefaultController
    {
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;

        public CiotWebHookController(IParametrosApp parametrosApp, ICiotV3App ciotV3App, IVersaoAnttLazyLoadService versaoAntt)
        {
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
        }

        /// <summary>
        /// Método responsável por receber uma notificação do MS de CIOT após o mesmo tentar reenviar uma declaração que estava em contingência à ANTT,
        /// trazendo o resultado obtido para atualizar a plataforma
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [IgnoreAuthSessionValidation]
        public JsonResult NotificarCiotContingencia(NotificarCiotContingencia request)
        {
            try
            {
                if (_versaoAntt.Value == EVersaoAntt.Versao3)
                {
                    var response = _ciotV3App.NotificarCiotContingencia(request);
                
                    return response.IsValid
                        ? ResponderSucesso("Notificação realizada com sucesso.")
                        : ResponderErro(response.Errors.FirstOrDefault()?.Message);
                }

                return ResponderErro("Opção não disponível para a atual versão");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Service
{
    public class PlanoService : ServiceBase, IPlanoService
    {

        private readonly IPlanoRepository _planoRepository;
        private readonly IPlanoEmpresaRepository _planoEmpresaRepository;

        public PlanoService(IPlanoRepository planoRepository, IPlanoEmpresaRepository planoEmpresaRepository)
        {
            _planoRepository = planoRepository;
            _planoEmpresaRepository = planoEmpresaRepository;
        }

        public List<PlanoEmpresaDto> GetPlanosAtivos()
        {
            return _planoRepository.GetPlanosAtivos();
        }

        public List<PlanoEmpresaDto> GetPlanosEmpresa(int idEmpresa)
        {
            return _planoRepository.GetPlanosAtivosPorEmpresa(idEmpresa);
        }

        public ValidationResult AddPlanoEmpresa(PlanoEmpresa planoEmpresa)
        {
            try
            {
                _planoEmpresaRepository.Add(planoEmpresa);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public ValidationResult UpdatePlanoEmpresa(PlanoEmpresa planoEmpresa)
        {
            try
            {
                _planoEmpresaRepository.Update(planoEmpresa);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public PlanoEmpresa GetPlanoEmpresa(int idPlano, int idEmpresa)
        {
            return _planoEmpresaRepository.FirstOrDefault(pe => pe.IdPlano == idPlano && pe.IdEmpresa == idEmpresa);
        }
    }
}

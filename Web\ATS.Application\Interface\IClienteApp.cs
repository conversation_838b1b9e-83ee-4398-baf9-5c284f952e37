﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Clientes;

namespace ATS.Application.Interface
{
    public interface IClienteApp : IBaseApp<IClienteService>
    {
        Cliente GetAllChilds(int id);
        ValidationResult Add(Cliente cliente);
        ValidationResult Update(Cliente cliente);
        Cliente Get(int id);
        int? GetIdPorCpfCnpj(string cpfCnpj, int idEmpresa);
        IQueryable<Cliente> All();
        ValidationResult Inativar(int idCliente);
        ValidationResult Reativar(int idCliente);
        IQueryable<Cliente> GetAll();
        IQueryable<Cliente> GetClientesPorEmpresa(int idEmpresa);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        byte[] GerarRelatorioGridClientes(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string logo, string extensao);
        ClienteDetalhesModel ConsultarDetalhes(int idCliente);
        bool VerificarClienteCadastrado(int idCliente);
        IQueryable<Cliente> GetQuery(int idCliente);
    }
}
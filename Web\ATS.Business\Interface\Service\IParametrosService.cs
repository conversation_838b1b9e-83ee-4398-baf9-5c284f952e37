using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Models.AtendimentoPortador;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IParametrosService : IBaseService<IParametrosRepository>
    {
        #region Métodos SET

        ValidationResult SetPercentualTransferenciaFreteGenerico(PercentualTransferenciaFreteViagemParametro percentuais);
        ValidationResult SetIdTipoEstabelecimentoPadraoJSL(int idTipoEstabelecimento);
        ValidationResult SetPercentualTransferenciaFreteProprietario(PercentualTransferenciaFreteViagemParametro percentuais, string documento);
        ValidationResult SetPercentualTransferenciaFreteProprietarioMotorista(PercentualTransferenciaFreteViagemParametro percentuais, string documentoProprietario, string documentoMotorista);
        ValidationResult SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO parametro, decimal? valor, string documento);
        ValidationResult SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, decimal? valor, string documentoProprietario, string documentoMotorista);
        ValidationResult SetAutorizaEstabelecimentosRedeJSL(bool valor, int idEmpresa);
        ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagio(AcaoSaldoResidualNovoCreditoCartaoPedagio? valor, string documento);
        ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagio(AcaoSaldoResidualNovoCreditoCartaoPedagio? valor, int idEmpresa);
        ValidationResult SetPermissaoUsuarioAlterarLimiteAlcadas(PermissaoUsuarioAlterarLimiteAlcadas valor, int idusuario);
        ValidationResult SetValorLimitePagamentoCheque(decimal? valor, int idEmpresa);
		ValidationResult SetHorasExpiracaoCreditoPedagio(int horas, int idEmpresa);
        ValidationResult SetEmailsAlertaCiotAgregado(string emails, int idEmpresa);
        ValidationResult SetDiasCancelamentoViagem(int? valor, int idEmpresa);
        ValidationResult SetSeriePadraoCheque(string valor, int idEmpresa);
        ValidationResult SetQuantidadeLimiteImpressoesCheque(decimal? valor, int idEmpresa);
        ValidationResult SetMarginTopImpressaoCheque(decimal? valor, int idEmpresa);
        ValidationResult SetMarginLeftImpressaoCheque(decimal? valor, int idEmpresa);
        ValidationResult SetEndpointIntegracaoCheque(string valor, int idEmpresa);
        ValidationResult SetHeadersIntegracaoCheque(string valor, int idEmpresa);
        ValidationResult SetObrigatoriedadeArmazemEmpresa(bool? valor, int idEmpresa);
        ValidationResult SetSeparadorArquivoCsv(string valor, int idEmpresa);
        ValidationResult SetMostrarHeaderArquivoCsv(bool? valor, int idEmpresa);
        ValidationResult SetObrigatoriedadeOrdemCompraEmpresa(bool? valor, int idEmpresa);
        ValidationResult SetObrigatoriedadePedidoEmpresa(bool? valor, int idEmpresa);
        ValidationResult SetObrigatoriedadeProtocolo(bool? valor, int idEmpresa);
        ValidationResult SetObrigatoriedadeQuantidadeEmpresa(bool? valor, int idEmpresa);
        ValidationResult SetObrigatoriedadeArmazemFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetObrigatoriedadeOrdemCompraFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetObrigatoriedadeFormulaFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetObrigatoriedadePedidoFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetObrigatoriedadeProtocoloFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetObrigatoriedadeQuantidadeFilial(bool? valor, int idEmpresa, int idFilial);
        ValidationResult SetInformacoesTransferenciaBancaria(string valor, int idEmpresa);
        ValidationResult SetObrigaRoteirizacaoPedagioViagemEmpresa(bool valor, int idEmpresa);
        ValidationResult SetBancoPadraoCheque(string valor, int idEmpresa);
        ValidationResult SetCancelarViagemComSaldoMenorQueValorDoExtorno(bool valor, int idEmpresa);
        ValidationResult SetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa, bool valor);
        ValidationResult SetIdProjetoFireBase(int idUsuario, int? idAdministradora);

        #endregion

        #region Métodos GET

        PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteGenerico();
        int GetIdTipoEstabelecimentoPadraoJSL();
        int GetIdEmpresaPadraoUsuarioEstabelecimentoJSL();
        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento);
        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(int idEmpresa);
        PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietario(string documento);
        PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietarioMotorista(string documentoProprietario, string documentoMotorista);
        bool GetAutorizaEstabelecimentosRedeJSL(int idEmpresa);
        PermissaoUsuarioAlterarLimiteAlcadas GetPermissaoUsuarioAlterarLimiteAlcadas(int idusuario);
        decimal? GetValorLimitePagamentoCheque(int idEmpresa);
		int? GetHorasExpiracaoCreditoPedagio(int idEmpresa);
        string GetSeriePadraoCheque(int idEmpresa);
        decimal? GetQuantidadeLimiteImpressoesCheque(int idEmpresa);
        decimal? GetMarginTopImpressaoCheque(int idEmpresa);
        decimal? GetMarginLeftImpressaoCheque(int idEmpresa);
        string GetEndpointIntegracaoCheque(int idEmpresa);
        string GetHeadersIntegracaoCheque(int idEmpresa);
        bool? GetObrigatoriedadeArmazemEmpresa(int idEmpresa);
        bool? GetObrigatoriedadeOrdemCompraEmpresa(int idEmpresa);
        ValidationResult SetObrigatoriedadeFormulaEmpresa(bool? valor, int idEmpresa);
        bool? GetObrigatoriedadeFormulaEmpresa(int idEmpresa);
        bool? GetObrigatoriedadeProtocolo(int idEmpresa);
        bool? GetObrigatoriedadePedidoEmpresa(int idEmpresa);
        bool? GetObrigatoriedadeQuantidadeEmpresa(int idEmpresa);
        bool? GetObrigatoriedadeArmazemFilial(int idEmpresa, int idFilial);
        bool? GetObrigatoriedadeOrdemCompraFilial(int idEmpresa, int idFilial);
        bool? GetObrigatoriedadeFormulaFilial(int idEmpresa, int idFilial);
        bool? GetObrigatoriedadePedidoFilial(int idEmpresa, int idFilial);
        bool? GetObrigatoriedadeProtocoloFilial(int idEmpresa, int idFilial);
        bool? GetObrigatoriedadeQuantidadeFilial(int idEmpresa, int idFilial);
        string GetSeparadorArquivoCsv(int idEmpresa);
        bool? GetMostrarHeaderArquivoCsv(int idEmpresa);
        bool GetObrigaRoteirizacaoPedagioViagemEmpresa(int idEmpresa);
        int? GetIdUsuarioGenericoWS();
        bool? HasTransferenciaFrete(IList<ETipoEventoViagem> tiposEvento, string proprietarioDocumento, int idempresa, string motoristaDocumento);
        string GetInformacoesTransferenciaBancaria(int idEmpresa);
        string GetBancoPadraoCheque(int idEmpresa);
        bool GetCancelarViagemComSaldoMenorQueValorDoExtorno(int idEmpresa);
        ValidationResult SetTokenAdministradora(string valor, string idregistro, int idEmpresa);
        string GetTokenAdministradora(int idAdministradora);
        ValidationResult SetProdutoIdPadrao(string valor, string idregistro, int idEmpresa);
        string GetProdutoIdPadrao(int idAdministradora);
        int GetGrupoContabilizacaoCentralAtendimento();
        bool ValidaCnpjCpfProprietarioNaViagem();
        int GetIdLayoutCartao();
        int GetIdProdutoCartaoFrete();
        bool GetMetodoRefatoradoRelatorioConciliacaoAnalitico();
        bool? GetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa);
        ValidationResult SetReemiteCiotPadraoAlteracaoViagem(bool valor, int idEmpresa);
        bool? GetReemiteCiotPadraoAlteracaoViagem(int idEmpresa);
        string GetEmailsAlertaCiotAgregado(int idEmpresa);
        int GetDiasCancelamentoViagem(int idEmpresa);
        bool? GetPermiteConsultarTodasViagensEmpresa(int idEmpresa);
        int GetTipoCargaAnttDefault();
        int? GetVersaoAntt();
        int GetIdProjetoFireBase(int idusuario);
        bool? GetValidarApenasQuantidadeDeCaracteresCnh();

        ValidationResult SetParametroEmpresaGridListarCnpjDespesasViagem(bool listar, int idEmmpresa);
        bool GetParametroEmpresaGridListarCnpjDespesasViagem(int idEmpresa);
        string GetCodigoOfx(int idEmpresa);
        ValidationResult SetCodigoOfx(string valor, int idEmpresa);

        #endregion
    }

    public interface IParametrosGenericoService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(GLOBAL parametro, T valor);

        T GetParametro<T>(GLOBAL parametro, int idregistro);

        Dictionary<GLOBAL, T> GetParametros<T>(IList<GLOBAL> parametros);
    }

    public interface IParametrosProprietarioService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, T valor, string idregistro);

        ValidationResult SetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, T valor, int idregistro);

        T GetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, string documento);

        Dictionary<PROPRIETARIO_DOCUMENTO, T> GetParametros<T>(IList<PROPRIETARIO_DOCUMENTO> parametros, string idregistro);

        bool AnyParametro(IEnumerable<PROPRIETARIO_DOCUMENTO> parametros, string idregistro);

        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento);

        bool GetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa);

        ValidationResult SetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa, bool valor);
    }

    public interface IParametrosProprietarioMotoristaService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, T valor, string idregistro);

        ValidationResult SetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, T valor, int idregistro);

        T GetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, string documento);

        Dictionary<PROPRIETARIO_MOTORISTA_DOCUMENTO, T> GetParametros<T>(IList<PROPRIETARIO_MOTORISTA_DOCUMENTO> parametros, string idregistro);

        bool AnyParametro(IEnumerable<PROPRIETARIO_MOTORISTA_DOCUMENTO> parametros, string idregistro);
    }

    public interface IParametrosEmpresaService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(EMPRESA_ID parametro, T valor, string idregistro);

        ValidationResult SetParametro<T>(EMPRESA_ID parametro, T valor, int idregistro);

        T GetParametro<T>(EMPRESA_ID parametro, int idEmpresa);

        Dictionary<EMPRESA_ID, T> GetParametros<T>(IList<EMPRESA_ID> parametros, int idEmpresa);

        bool AnyParametro(IEnumerable<EMPRESA_ID> parametros, int idEmpresa);

        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(int idEmpresa);

        decimal? GetNumeroDeRegistrosDeCargaAvulsaParaProcessar(int idEmpresa);
        PermissoesEmpresaAtendimentoPortador GetPermissoesAtendimentoCartao(int idEmpresa);
        ValidationResult SetPermissoesAtendimentoCartao(int idEmpresa, PermissoesEmpresaAtendimentoPortador permissoes);
        string GetTokenMicroServicoCentralAtendimento(int idEmpresa);
        ValidationResult SetTokenMicroServicoCentralAtendimento(int idEmpresa, string token);
        bool GetOcultarListagemTerceiros(int idEmpresa);
        bool CadastraSomentePerfilEmpresa(int idEmpresa);
        bool GetRegistrarValePedagio(int idEmpresa);
        ValidationResult SetRegistrarValePedagio(int idEmpresa, bool enviar);
        ValidationResult SetRealizaTriagemEstabelecimentoInterno(int idEmpresa, bool? realizaTriagemEstabelecimentoInterno);
        bool GetRealizaTriagemEstabelecimentoInterno(int idEmpresa);
        ValidationResult SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa, bool? parametro);
        bool? GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa);
        int GetMotivoPadraoBloqueioCartaoEmpresa();
        decimal GetValorMinimoAlertaSaldoContaFrete(int idEmpresa);
        decimal GetValorMinimoAlertaSaldoContaPix(int idEmpresa);
        ValidationResult SetValorMinimoAlertaSaldoContaFrete(int idEmpresa, decimal? valor);
        ValidationResult SetValorMinimoAlertaSaldoContaPix(int idEmpresa, decimal? valor);
        ValidationResult SetTagExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetTagExtrattaValorTag(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaValorTag(int idEmpresa);
        ValidationResult SetTagExtrattaValorMensalidade(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaValorMensalidade(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarValor(int idEmpresa, bool? parametro);
        bool GetTagExtrattaProvisionarValor(int idEmpresa);
        bool GetTagExtrattaProvisionarTaxa(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarTaxa(int idEmpresa, bool? parametro);
        ValidationResult SetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaBloquearTagLoteWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaBloquearTagLoteWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa);
        decimal GetTagExtrattaValorSubstituicao(int idEmpresa);
        ValidationResult SetTagExtrattaValorSubstituicao(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaValorRecargaConta(int idEmpresa);
        ValidationResult SetTagExtrattaValorRecargaConta(int idEmpresa, decimal? parametro);
        ValidationResult SetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa);
        ValidationResult SetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa);
        ValidationResult SetPermiteVincularCartaoComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteVincularCartaoComCpfFicticio(int idEmpresa);
        bool GetTagExtrattaEstornarValorPedagioWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaEstornarValorPedagioWebhook(int idEmpresa, bool? parametro);
        ValidationResult SetMoveMaisExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        decimal GetMoveMaisExtrattaTaxaVpo(int idEmpresa);
        decimal GetViaFacilExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetViaFacilExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        ValidationResult SetVeloeExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        decimal GetVeloeExtrattaTaxaVpo(int idEmpresa);
        bool GetTagExtrattaUtilizaTaxaPedagio(int idEmpresa);
        ValidationResult SetTagExtrattaUtilizaTaxaPedagio(int idEmpresa, bool? parametro);
        bool GetPermiteRealizarPagamentoPix(int idEmpresa);
        ValidationResult SetPermiteRealizarPagamentoPix(int idEmpresa, bool parametro);
        ValidationResult SetHubVeloeProvisionarTaxa(int idEmpresa, bool? parametro);
        bool GetHubVeloeProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubVeloeProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubVeloeProvisionarValor(int idEmpresa);
        ValidationResult SetHubViaFacilProvisionarTaxa(int idEmpresa, bool? parametro);
        bool GetHubViaFacilProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubViaFacilProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubViaFacilProvisionarValor(int idEmpresa);
        ValidationResult SetHubMoveMaisProvisionarTaxa(int idEmpresa, bool? parametro);
        bool GetHubMoveMaisProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubMoveMaisProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubMoveMaisProvisionarValor(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa);
        bool GetHubConectCarProvisionarValor(int idEmpresa);
        ValidationResult SetHubConectCarProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubConectCarProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubConectCarProvisionarTaxa(int idEmpresa, bool? parametro);
        decimal GetConectCarExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetConectCarExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        ValidationResult SetUtilizaRelatoriosOfx(int idEmpresa, bool? parametro);
        ValidationResult SetUtilizaUtilizaRoteirizacaoPorPolyline(int idEmpresa, bool? parametro);
        bool GetUtilizaRelatoriosOfx(int idEmpresa);
        bool GetUtilizaRoteirizacaoPorPolyline(int idEmpresa);
        ValidationResult SetDefaultIntegracaoTipoRodagemDupla(int idEmpresa, bool? parametro);
        bool GetDefaultIntegracaoTipoRodagemDupla(int idEmpresa);
        ValidationResult SetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa, bool? parametro);
        bool GetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa);
        bool GetSolicitarSenhaTransacionalPix(int idEmpresa);
        ValidationResult SetSolicitarSenhaTransacionalPix(int idEmpresa, bool? parametro);
        bool GetAprovarSolicitacoesChavePixAutomaticamente(int idEmpresa);
        bool GetHubTaggyEdenredProvisionarValor(int idEmpresa);
        ValidationResult SetHubTaggyEdenredProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubTaggyEdenredProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubTaggyEdenredProvisionarTaxa(int idEmpresa, bool? parametro);
        decimal GetTaggyEdenredExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetTaggyEdenredExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        bool GetBloqueiaCargaAvulsaDuplicada(int idEmpresa);
        ValidationResult SetBloqueiaCargaAvulsaDuplicada(int idEmpresa, bool? parametro);
        decimal GetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa);
        ValidationResult SetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa, decimal? parametro);
        bool GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa);
        ValidationResult SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa, bool? parametro);
        bool GetNaoBaixarParcelasDeposito(int idEmpresa);
        ValidationResult SetNaoBaixarParcelasDeposito(int idEmpresa, bool? parametro);
    }

    public interface IParametrosFilialService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(FILIAL_PARAMS parametro, T valor, int idregistro, int idEmpresa);

        T GetParametro<T>(FILIAL_PARAMS parametro, int idFilial, int idEmpresa);
    }

    public interface IParametrosUsuarioService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(USUARIO_ID parametro, T valor, string idregistro);

        ValidationResult SetParametro<T>(USUARIO_ID parametro, T valor, int idregistro);

        T GetParametro<T>(USUARIO_ID parametro, int idregistro);

        T GetParametro<T>(USUARIO_ID parametro, string idregistro);

        Dictionary<USUARIO_ID, T> GetParametros<T>(IList<USUARIO_ID> parametros, string idregistro);

        Dictionary<USUARIO_ID, T> GetParametros<T>(IList<USUARIO_ID> parametros, int idregistro);

        bool AnyParametro(IEnumerable<USUARIO_ID> parametros, int idregistro);

        bool AnyParametro(IEnumerable<USUARIO_ID> parametros, string idregistro);
        PermissoesUsuarioAtendimentoPortador GetNovoUsuarioPermissoesAtendimentoCartao(int idEmpresa);
        PermissoesUsuarioAtendimentoPortador GetPermissoesAtendimentoCartao(int idUsuario, int idEmpresa);
        ValidationResult SetPermissoesAtendimentoCartao(int idUsuario, PermissoesUsuarioAtendimentoPortador permissoes);
        bool GetAtendimentoPermiteBloquearCartao(int idUsuario);
        bool GetAtendimentoPermiteDesbloquearCartao(int idUsuario);
        bool GetAtendimentoPermiteAlterarSenhaCartao(int idUsuario);
        bool GetAtendimentoPermiteRealizarTransferenciaBancaria(int idUsuario);
        bool GetAtendimentoPermiteRealizarTransferenciaCartoes(int idUsuario);
        bool GetAtendimentoPermiteRealizarResgate(int idUsuario);
        bool GetAtendimentoPermiteRealizarEstornoResgate(int idUsuario);
        bool GetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario);
        bool GetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario);
        ValidationResult SetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario, bool value);
        ValidationResult SetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario, bool value);
        ValidationResult SetParametroUsuarioPermitirAcessoAtendimento(int idUsuario, bool value);
        ValidationResult SetPermitirAcessoExtratoDetalhado(int idUsuario, bool value);
        bool GetParametroUsuarioPermitirAcessoAtendimento(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosEmpresa(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosFilial(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosUsuario(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosGrupoUsuario(int idUsuario);
        bool GetPermitirAcessoExtratoDetalhado(int idUsuario);
        bool GetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario, bool value);
        bool GetPermiteSolicitarAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteSolicitarAdiantamentoApp(int idUsuario, bool value);
        bool GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario, bool value);
        ValidationResult SetLimiteDiarioPagamentoPixUsuario(int idUsuario, decimal? parametro);
        decimal? GetLimiteDiarioPagamentoPixUsuario(int idUsuario);
        ValidationResult SetLimiteUnitarioPagamentoPixUsuario(int idUsuario, decimal? parametro);
        decimal? GetLimiteUnitarioPagamentoPixUsuario(int idUsuario);
        ValidationResult SetPermitirEdicaoDadosAdministrativosPix(int idUsuario, bool parametro);
        bool GetPermitirEdicaoDadosAdministrativosPix(int idUsuario);
        ValidationResult SetPermiteRealizarPagamentoPix(int idUsuario, bool parametro);
        bool GetPermitirCadastroChavePix(int idUsuario);
        ValidationResult SetPermitirCadastroChavePix(int idUsuario, bool parametro);
        bool GetPermiteRealizarPagamentoPix(int idUsuario);
        ValidationResult SetPermiteSolicitarAlteracoesLimitePix(int idUsuario, bool parametro);
        bool GetPermiteSolicitarAlteracoesLimitePix(int idUsuario);
        ValidationResult SetGestorAlcadasPix(int idUsuario, bool parametro);
        bool GetGestorAlcadasPix(int idUsuario);
    }

    public interface IParametrosAdministradoraMeioHomologadoService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetParametro<T>(ADMINISTRADORA_MH_ID parametro, T valor ,string idregistro, int idEmpresa);

        T GetParametro<T>(ADMINISTRADORA_MH_ID parametro, int idAdministradora);
    }

    public interface IParametrosAdministradoraPlataformaService : IBaseService<IParametrosRepository>
    {
        string GetCaminhoEmailRecuperacaoSenhaApp(int idAdministradoraPlataforma);
        string GetCaminhoLogo(int idAdministradoraPlataforma);
        string GetKeyEnvioPush(int idAdministradoraPlataforma);
        ConfiguracaoEnvioEmail GetConfiguracaoEmail(int idAdministradoraPlataforma);
    }

    public interface IParametrosEstabelecimentoService : IBaseService<IParametrosRepository>
    {
        ValidationResult SetValidarChavePagamento(int idEstabelecimento, bool validarChavePagamento);

        bool? GetValidarChavePagamento(int idEstabelecimento);
    }


    public interface IParametrosMobileGlobalService : IBaseService<IParametrosRepository>
    {
        bool? GetValidarSenhaIgualCPF();
        string GetTelefoneAtendimento();
        bool? GetUtilizaKeycloack();
    }
}

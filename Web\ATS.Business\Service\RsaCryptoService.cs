﻿using System;
using System.IO;
using System.Text;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Encodings;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.OpenSsl;

namespace ATS.Domain.Service
{
    public class RsaCryptoService : ServiceBase, IRsaCryptoService
    {
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly string _privateKey;
        private readonly string _publicKey;
        public RsaCryptoService(IParametrosGenericoService parametrosGenericoService)
        {
            _parametrosGenericoService = parametrosGenericoService;
            _privateKey = _parametrosGenericoService.GetParametro<string>(GLOBAL.ChaveRsaPrivada, 0);
            _publicKey = _parametrosGenericoService.GetParametro<string>(GLOBAL.ChaveRsaPublica, 0);
        }

        public string Encrypt(string input)
        {
            if (string.IsNullOrWhiteSpace(_publicKey))
                throw new InvalidOperationException("Não foi possível concluir a validação segura dos dados.");
            
            var bytesToEncrypt = Encoding.UTF8.GetBytes(input);
            using (var reader = new StringReader(_publicKey))
            {
                var pemReader = new PemReader(reader);
                var keyParameter = (AsymmetricKeyParameter)pemReader.ReadObject();

                var cipher = new Pkcs1Encoding(new RsaEngine());
                cipher.Init(true, keyParameter); // true for encryption

                var encryptedBytes = cipher.ProcessBlock(bytesToEncrypt, 0, bytesToEncrypt.Length);
                return Convert.ToBase64String(encryptedBytes);
            }
        }

        public string Decrypt(string input)
        {
            if (string.IsNullOrWhiteSpace(_privateKey))
                throw new InvalidOperationException("Não foi possível concluir a validação segura dos dados.");
            
            var bytesToDecrypt = Convert.FromBase64String(input);
            using (var reader = new StringReader(_privateKey))
            {
                var pemReader = new PemReader(reader);
                var keyParameter = (AsymmetricCipherKeyPair)pemReader.ReadObject();

                var cipher = new Pkcs1Encoding(new RsaEngine());
                cipher.Init(false, keyParameter.Private); // false for decryption

                var decryptedBytes = cipher.ProcessBlock(bytesToDecrypt, 0, bytesToDecrypt.Length);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }
    }
}

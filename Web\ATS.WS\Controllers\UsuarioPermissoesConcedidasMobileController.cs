using System;
using System.Web.Http;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services;

namespace ATS.WS.Controllers
{
    public class UsuarioPermissoesConcedidasMobileController : BaseController
    {
        private readonly SrvUsuarioPermissoesConcedidasMobile _srvUsuarioPermissoesConcedidasMobile;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public UsuarioPermissoesConcedidasMobileController(BaseControllerArgs baseArgs, SrvUsuarioPermissoesConcedidasMobile srvUsuarioPermissoesConcedidasMobile, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _srvUsuarioPermissoesConcedidasMobile = srvUsuarioPermissoesConcedidasMobile;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpPost]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string SalvarOuAtualizar(UsuarioPermissoesConcedidasMobileRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuarioPermissoesConcedidasMobile.SalvarOuEditar(request));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string ConsultarPorUsuario(string token, string cnpjAplicacao, int? usuarioId)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();
                
                return new JsonResult().Responde(_srvUsuarioPermissoesConcedidasMobile.ConsultarPorUsuario(usuarioId));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
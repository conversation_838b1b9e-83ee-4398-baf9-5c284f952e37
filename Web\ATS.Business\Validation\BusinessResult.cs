﻿using ATS.Domain.Validation.Interface;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using FluentValidation.Results;

namespace ATS.Domain.Validation
{
    public class BusinessResult<TValue> : IBusinessResult
    {
        public virtual bool Success { get; set; }
        public virtual IList<string> Messages { get; set; }
        public TValue Value { get; private set; }
        public HttpStatusCode? StatusCode { get; private set; }

        private BusinessResult()
        {
            Success = false;
            Messages = new List<string>();
        }

        private BusinessResult(bool sucess, IList<string> messages)
        {
            Success = sucess;
            Messages = messages;
        }

        private BusinessResult(bool sucess, IList<string> messages, TValue value)
        {
            Success = sucess;
            Messages = messages;

            Value = value;
        }

        private BusinessResult(bool sucess, IList<string> messages, TValue value, HttpStatusCode statusCode)
        {
            Success = sucess;
            Messages = messages;
            StatusCode = statusCode;

            Value = value;
        }

        public BusinessResult(bool sucess, IList<string> messages, HttpStatusCode statusCode)
        {
            Success = sucess;
            Messages = messages;
            StatusCode = statusCode;
        }

        public static BusinessResult<TValue> Valid(TValue value)
        {
            var messages = new List<string>();

            return new BusinessResult<TValue>(true, messages, value);
        }

        public static BusinessResult<TValue> Valid(string message, TValue obj)
        {
            var messages = new List<string>();

            if (!string.IsNullOrWhiteSpace(message))
                messages.Add(message);

            return new BusinessResult<TValue>(true, messages, obj);
        }

        public static BusinessResult<TValue> Error(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult<TValue>(false, messages);
        }

        public static BusinessResult<TValue> Error(string message, TValue value)
        {
            var messages = new List<string> { message };

            return new BusinessResult<TValue>(false, messages, value);
        }

        public static BusinessResult<TValue> Error(IList<string> messages)
        {
            return new BusinessResult<TValue>(false, messages);
        }

        public static BusinessResult<TValue> NotFound(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult<TValue>(false, messages, HttpStatusCode.NotFound);
        }

        public static BusinessResult<TValue> NoContent(TValue value)
        {
            var messages = new List<string>();

            return new BusinessResult<TValue>(true, messages, value, HttpStatusCode.NoContent);
        }

        public static BusinessResult<TValue> NoContent(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult<TValue>(false, messages, HttpStatusCode.NoContent);
        }

        public override string ToString()
        {
            return string.Join(", ", Messages);
        }
    }

    public class BusinessResult : IBusinessResult
    {
        public bool Success { get; private set; }
        public HttpStatusCode? StatusCode { get; private set; }
        public IList<string> Messages { get; private set; }

        private BusinessResult()
        {
            Messages = new List<string>();
        }

        private BusinessResult(bool sucess)
        {
            Success = sucess;
            Messages = new List<string>();
        }

        private BusinessResult(bool sucess, IList<string> messages)
        {
            Success = sucess;
            Messages = messages;
        }

        public BusinessResult(bool sucess, IList<string> messages, HttpStatusCode statusCode)
        {
            Success = sucess;
            Messages = messages;
            StatusCode = statusCode;
        }

        public static BusinessResult Valid()
        {
            return new BusinessResult(true);
        }

        public static BusinessResult Valid(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult(true, messages);
        }

        public static BusinessResult Error(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult(false, messages);
        }

        public static BusinessResult Error(IList<string> messages)
        {
            return new BusinessResult(false, messages);
        }

        public static BusinessResult NotFound(string message)
        {
            var messages = new List<string> { message };

            return new BusinessResult(false, messages, HttpStatusCode.NotFound);
        }

        public override string ToString()
        {
            return string.Join(", ", Messages);
        }
    }
}
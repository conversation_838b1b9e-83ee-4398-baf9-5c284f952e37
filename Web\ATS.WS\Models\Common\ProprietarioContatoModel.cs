﻿using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class ProprietarioContatoModel
    {
        public int IdProprietario   { get; set; }
        public int IdEmpresa  { get; set; }
        public int IdContato        { get; set; }
        public string Celular { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Telefone      { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)] 
        public string Email         { get; set; }
    }
}
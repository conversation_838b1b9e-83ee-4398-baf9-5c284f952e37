﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Veiculos.RelatorioListaVeiculos;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Veiculo;
using AutoMapper.QueryableExtensions;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class VeiculoService : ServiceBase, IVeiculoService
    {
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly ITipoCavaloRepository _tipoCavaloRepository;
        private readonly ITipoCarretaRepository _tipoCarretaRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ITipoCavaloService _tipoCavaloService;
        private readonly ITipoCarretaService _tipoCarretaService;
        private readonly ITagRepository _tagRepository;
        public VeiculoService(ITipoCavaloRepository tipoCavaloRepository, IProprietarioRepository proprietarioRepository, IVeiculoRepository veiculoRepository,
            ITipoCarretaRepository tipoCarretaRepository, IEmpresaRepository empresaRepository, ITipoCavaloService tipoCavaloService, ITipoCarretaService tipoCarretaService, ITagRepository tagRepository)
        {
            _tipoCavaloRepository = tipoCavaloRepository;
            _proprietarioRepository = proprietarioRepository;
            _veiculoRepository = veiculoRepository;
            _tipoCarretaRepository = tipoCarretaRepository;
            _empresaRepository = empresaRepository;
            _tipoCavaloService = tipoCavaloService;
            _tipoCarretaService = tipoCarretaService;
            _tagRepository = tagRepository;
        }

        public Veiculo GetWithAllChilds(int idVeiculo)
        {
            return _veiculoRepository.GetWithAllChilds(idVeiculo);
        }

        public int? GetIdPorPlaca(string nPlaca, int? idEmpresa)
        {
            int? idVeiculo = _veiculoRepository
                .Find(x => x.Placa == nPlaca && x.Ativo && (x.IdEmpresa == idEmpresa || x.IdEmpresa == null))
                .OrderByDescending(x => x.IdEmpresa)
                .Select(x => x.IdVeiculo).FirstOrDefault();

            return idVeiculo.GetValueOrDefault(0) <= 0 ? (int?) null : idVeiculo.GetValueOrDefault();
        }

        public TipoCarreta GetTipoCarreta(int idTipoCarreta)
        {
            return _tipoCarretaRepository.FirstOrDefault(c => c.IdTipoCarreta == idTipoCarreta);
        }

        public TipoCavalo GetTipoCavalo(int idTipoCavalo)
        {
            return _tipoCavaloRepository.FirstOrDefault(c => c.IdTipoCavalo == idTipoCavalo);
        }

        public ValidationResult Add(Veiculo veiculo)
        {
            try
            {
                var repository = _veiculoRepository;

                var empresaPossuiPlacaEspeciais = _empresaRepository
                    .Where(x => x.IdEmpresa == veiculo.IdEmpresa && x.UtilizaPlacasEspeciais == true).Any();

                FormatValues(veiculo);

                if (Veiculo.RegexMercosul.IsMatch(veiculo.Placa))
                {
                    var placaBrasil = repository.ConvertToPadraoBrasil(veiculo.Placa);

                    veiculo.PlacaPadraoBrasil = placaBrasil;
                    veiculo.TipoPadraoPlaca = ETipoPadraoPlaca.Mercosul;
                }

                if (Veiculo.RegexBrasil.IsMatch(veiculo.Placa))
                {
                    veiculo.PlacaPadraoBrasil = string.Empty;
                    veiculo.TipoPadraoPlaca = ETipoPadraoPlaca.Brasil;
                }

                var validationResult = IsValidCrud(veiculo, EProcesso.Create, empresaPossuiPlacaEspeciais);

                if (!validationResult.IsValid)
                    return validationResult;

                veiculo.DataUltimaAtualizacao = DateTime.Now;
                repository.Add(veiculo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Veiculo veiculo)
        {
            try
            {
                var veiculoRepository = _veiculoRepository;

                var empresaPossuiPlacaEspeciais = _empresaRepository
                    .Where(x => x.IdEmpresa == veiculo.IdEmpresa && x.UtilizaPlacasEspeciais == true).Any();

                FormatValues(veiculo);

                var validationResult = IsValidCrud(veiculo, EProcesso.Update, empresaPossuiPlacaEspeciais);

                if (veiculo.IdMotorista.HasValue)
                {
                    var veiculoOriginal = veiculoRepository.FirstOrDefault(c => c.IdMotorista == veiculo.IdMotorista && c.IdVeiculo != veiculo.IdVeiculo);

                    // Desvincula o motorista do veículo original
                    if (veiculoOriginal != null)
                    {
                        veiculoOriginal.IdMotorista = null;
                        veiculoOriginal.DataUltimaAtualizacao = DateTime.Now;

                        _veiculoRepository.Update(veiculoOriginal);
                    }
                }

                if (Veiculo.RegexMercosul.IsMatch(veiculo.Placa))
                {
                    var placaBrasil = veiculoRepository.ConvertToPadraoBrasil(veiculo.Placa);

                    veiculo.PlacaPadraoBrasil = placaBrasil;
                    veiculo.TipoPadraoPlaca = ETipoPadraoPlaca.Mercosul;
                }

                if (Veiculo.RegexBrasil.IsMatch(veiculo.Placa))
                {
                    veiculo.PlacaPadraoBrasil = string.Empty;
                    veiculo.TipoPadraoPlaca = ETipoPadraoPlaca.Brasil;
                }

                if (!validationResult.IsValid)
                {
                    return validationResult;
                }

                veiculo.DataUltimaAtualizacao = DateTime.Now;
                _veiculoRepository.Update(veiculo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Veiculo Get(int idVeiculo)
        {
            return _veiculoRepository.Get(idVeiculo);
        }

        public Veiculo GetVeiculoPorEmpresaMotorista(int idEmpresa, int idMotorista)
        {
            return _veiculoRepository
                .Find(x => x.IdEmpresa == idEmpresa && x.IdMotorista == idMotorista && x.Ativo)
                .Include(x => x.TipoCavalo).Include(x => x.TipoCarreta).FirstOrDefault();
        }

        public List<string> GetPlacasPorEmpresa(int idEmpresa, bool incluirTerceiros = false, int? idOperacao = null)
        {
            var veiculos = _veiculoRepository.All();

            veiculos = incluirTerceiros ? veiculos.Where(x => x.IdEmpresa == null || x.IdEmpresa == idEmpresa) : veiculos.Where(x => x.IdEmpresa == idEmpresa);

            if (idOperacao.HasValue)
                veiculos = veiculos.Where(x => x.IdOperacao == idOperacao);

            return veiculos.Select(x => x.Placa).ToList();
        }

        public Veiculo GetVeiculoPorPlaca(string placa, int? idEmpresa = null)
        {
            var veiculos = _veiculoRepository
                .Find(p => p.Placa == placa.Trim() && p.Ativo)
                .Include(p => p.Usuario)
                .Include(x => x.Empresa)
                .Include(p => p.Motorista)
                .Include(x => x.TipoCarreta)
                .Include(x => x.TipoCavalo)
                .Include(x => x.Proprietario)
                .Include(x => x.VeiculoConjuntos);

            if (idEmpresa.HasValue)
                veiculos = veiculos.Where(x => x.IdEmpresa == idEmpresa);

            return veiculos.FirstOrDefault();
        }

        public IQueryable<Veiculo> Query(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false)
        {
            placa = placa?.Trim();
            var query = _veiculoRepository
                .Find(v => v.Placa == placa);
            if (idEmpresa.HasValue)
                query = query.Where(v => v.IdEmpresa == idEmpresa.Value);
            if (somenteTerceiros)
                query = query.Where(v => v.IdEmpresa == null);
            if (ativo.HasValue)
                query = query.Where(v => v.Ativo == ativo.Value);
            return query;
        }
        
        public IQueryable<Veiculo> GetVeiculosByListIdVeiculos(List<int> lIdVeiculo, int? idEmpresa = null)
        {
            var query = _veiculoRepository
                .Find(v => lIdVeiculo.Contains(v.IdVeiculo) && v.Ativo == true);
            
            if (idEmpresa.HasValue)
                query = query.Where(v => v.IdEmpresa == idEmpresa.Value);

            return query;
        }

        public VeiculoDTO Get(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false)
        {
            return Query(placa, idEmpresa, ativo, somenteTerceiros).ProjectTo<VeiculoDTO>().FirstOrDefault();
        }

        /// <summary>
        /// Retorna um veículo e filial a partir de sua placa
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="idEmpresa"></param>
        /// <returns>Veiculo</returns>
        public Veiculo GetCodigoFilialVeiculo(string placa, int? idEmpresa = null)
        {
            var veiculos = _veiculoRepository
                .Find(p => p.Placa == placa.Trim() && p.Ativo)
                .Select(x => new {
                    x.IdEmpresa,
                    x.IdFilial
                });

            if (idEmpresa.HasValue)
                veiculos = veiculos.Where(x => x.IdEmpresa == idEmpresa);

            var idsVeiculo = veiculos.FirstOrDefault();

            return idsVeiculo == null ? null :
                new Veiculo
                {
                    IdEmpresa = idsVeiculo.IdEmpresa,
                    IdFilial = idsVeiculo.IdFilial
                };
        }

        public bool VeiculoValidoIntegracao(string placa, int? idEmpresa = null)
        {
            var existe = _veiculoRepository.Where(c => c.Placa == placa && c.IdEmpresa == idEmpresa).Any();
            return existe;
        }

        public Veiculo GetVeiculoTerceiro(string placa)
        {
            var veiculos = _veiculoRepository
                .Find(p => p.Placa == placa.Trim() && p.Ativo && p.IdEmpresa == null)
                .Include(p => p.Usuario)
                .Include(x => x.Empresa)
                .Include(p => p.Motorista)
                .Include(x => x.TipoCarreta)
                .Include(x => x.TipoCavalo)
                .Include(x => x.Proprietario)
                .Include(x => x.VeiculoConjuntos);

            return veiculos.FirstOrDefault();
        }

        public Veiculo GetVeiculoPorPlacaPorEmpresa(string placa, int idEmpresa)
        {
            return _veiculoRepository
                .Find(p => p.Placa == placa && p.IdEmpresa == idEmpresa).FirstOrDefault();
        }

        public IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota)
        {
            return _veiculoRepository.GetVeiculosPorNumerosFrotas(numeroFrota);
        }

        public IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas)
        {
            return _veiculoRepository.GetVeiculosPorPlaca(placas);
        }

        public object ConsultarGrid(int? idEmpresa, bool comTracao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var veiculosQuery = _veiculoRepository
                .Where(x => x.IdEmpresa == idEmpresa && x.Ativo && x.ComTracao == comTracao);

            veiculosQuery = veiculosQuery.AplicarFiltrosDinamicos(filters);

            veiculosQuery = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? veiculosQuery.OrderByDescending(o => o.IdVeiculo)
                : veiculosQuery.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            var veiculos = veiculosQuery.Skip((page - 1) * take)
                .Take(take)
                .Select(o => new
                {
                    o.IdVeiculo,
                    o.Placa,
                    o.Marca,
                    o.Modelo
                }).ToList();

            var retorno = veiculos.Select(a => new
            {
                a.IdVeiculo,
                Placa = a.Placa.ToPlacaFormato(),
                a.Marca,
                a.Modelo
            }).ToList();

            return new
            {
                totalItems = veiculosQuery.Count(),
                items = retorno
            };
        }

        public ConsultaDadosVeiculoResponseDTO ConsultaDadosVeiculo(int idVeiculo)
        {
            var veiculo = QueryById(idVeiculo)
                .ProjectTo<ConsultaDadosVeiculoResponseDTO>()
                .FirstOrDefault();

            if (veiculo == null)
                return null;

            if (veiculo.ComTracao && veiculo.QuantidadeEixos < 2)
                throw new Exception("Por favor, informe a quantidade de eixos igual ou maior a 2 no cadastro do veículo");

            if (!veiculo.ComTracao && veiculo.QuantidadeEixos == 0)
                throw new Exception("Por favor, informe a quantidade de eixos igual ou maior a 1 no cadastro da carreta");

            return veiculo;
        }

        public IQueryable<Veiculo> QueryById(int idVeiculo)
        {
            return _veiculoRepository.Where(c => c.IdVeiculo == idVeiculo);
        }

        public string ConvertToPadraoMercosul(string placa)
        {
            return _veiculoRepository.ConvertToPadraoMercosul(placa);
        }

        public string ConvertToPadraoBrasil(string placaMercosul)
        {
            return _veiculoRepository.ConvertToPadraoBrasil(placaMercosul);
        }

        public ConsultaPlacaDTO PlacaExistente(string placa, int? idEmpresa)
        {
            var retorno = new ConsultaPlacaDTO
            {
                Placa = string.Empty,
                TipoPadraoPlaca = ETipoPadraoPlaca.Brasil,
                Existente = false
            };

            placa = placa.RemoveSpecialCharacters();

            var empresaPossuiPlacaEspeciais = _empresaRepository
                .Where(x => x.IdEmpresa == idEmpresa && x.UtilizaPlacasEspeciais == true).Any();

            if (!empresaPossuiPlacaEspeciais)
            {
                if (!Veiculo.PlacaValida(placa))
                    throw new ArgumentException($"Placa {placa} informada é inválida");
            }

            var repository = _veiculoRepository;

            var veiculoQuery = repository.Where(c => c.IdEmpresa == idEmpresa);

            retorno.Existente = veiculoQuery.Any(c => c.Placa == placa);

            if (retorno.Existente)
            {
                retorno.Placa = placa;
                retorno.TipoPadraoPlaca = Veiculo.GetTipoPadraoPlaca(_empresaRepository, placa, idEmpresa);

                return retorno;
            }

            if (Veiculo.RegexMercosul.IsMatch(placa))
            {
                var placaBrasil = ConvertToPadraoBrasil(placa);

                retorno.Existente = veiculoQuery.Any(c => c.Placa == placaBrasil);

                if (retorno.Existente)
                {
                    retorno.Placa = placaBrasil;
                    retorno.TipoPadraoPlaca = Veiculo.GetTipoPadraoPlaca(_empresaRepository, placaBrasil, idEmpresa);

                    return retorno;
                }
            }

            if (Veiculo.RegexBrasil.IsMatch(placa))
            {
                // Por padrão considera a placa como Brasil
                var placaMercosul = ConvertToPadraoMercosul(placa);

                retorno.Existente = veiculoQuery.Any(c => c.Placa == placaMercosul);

                if (retorno.Existente)
                {
                    retorno.Placa = placaMercosul;
                    retorno.TipoPadraoPlaca = Veiculo.GetTipoPadraoPlaca(_empresaRepository, placaMercosul, idEmpresa);
                }
            }

            return retorno;
        }

        public IQueryable<Veiculo> GetTodosVeiculosPorPlaca(string placa, int idEmpresa)
        {
            var placa2 = string.Empty;

            var empresaPossuiPlacaEspeciais = _empresaRepository
                .Where(x => x.IdEmpresa == idEmpresa && x.UtilizaPlacasEspeciais == true).Any();

            if (!empresaPossuiPlacaEspeciais)
            {
                if (Veiculo.RegexMercosul.IsMatch(placa))
                    placa2 = ConvertToPadraoBrasil(placa);
                else if (Veiculo.RegexBrasil.IsMatch(placa))
                {
                    placa2 = ConvertToPadraoMercosul(placa);
                }
            }

            return _veiculoRepository
                .Find(veiculo =>
                    (veiculo.Placa == placa || veiculo.Placa == placa2) &&
                    (veiculo.IdEmpresa == idEmpresa || veiculo.IdEmpresa == null))
                .Include(x => x.VeiculoConjuntos)
                .OrderByDescending(p => p.IdEmpresa)
                .ThenByDescending(p => p.Ativo)
                .ThenBy(p => p.IdVeiculo);
        }

        public ValidationResult AlterarStatus(int idVeiculo)
        {
            try
            {
                var repository = _veiculoRepository;
                var veiculo = repository.FirstOrDefault(o => o.IdVeiculo == idVeiculo);

                if (veiculo == null)
                {
                    return new ValidationResult().Add("Veículo não encontrado.");
                }

                veiculo.Ativo = !veiculo.Ativo;
                repository.Update(veiculo);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public byte[] GerarRelatorioGridVeiculos(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao)
        {
            var listaVeiculos = new List<RelatorioVeiculoDataType>();
            var veiculos = GetDataToGridAndReport(idEmpresa, orderFilters, filters);

            foreach (var veiculo in veiculos)
            {
                listaVeiculos.Add(new RelatorioVeiculoDataType
                {
                    IdVeiculo = veiculo.IdVeiculo.ToString(),
                    Placa = veiculo.Placa?.ToPlacaFormato(),
                    Filial = veiculo.Filial?.NomeFantasia,
                    Motorista = veiculo.Motorista?.Nome,
                    TipoContrato = veiculo.TipoContrato > 0 ? veiculo.TipoContrato.DescriptionAttr() : string.Empty,
                    Proprietario = veiculo.Proprietario?.NomeFantasia,
                    NumeroFrota = veiculo.NumeroFrota.ToString(),
                    Cidade = veiculo.Municipio,
                    MarcaModelo = $"{veiculo.Marca} - {veiculo.Modelo}",
                    QuantidadeEixos = veiculo.QuantidadeEixos.ToStringSafe(),
                    CpfCnpjProprietario = veiculo.Proprietario?.CNPJCPF?.FormatarCpfCnpj(),
                    RNTRC =veiculo.Proprietario?.RNTRC,
                    CiotAgregado = veiculo.HabilitarContratoCiotAgregado == null ? "Conforme proprietário" :
                        veiculo.HabilitarContratoCiotAgregado.Value ? "Sempre agregar este veículo ao embarcador" : "Nunca agregar este veículo ao embarcador",
                    TipoVeiculo = veiculo.IdTipoCarreta.HasValue ? "Carreta" 
                        : veiculo.IdTipoCavalo.HasValue ? "Cavalo" : string.Empty,
                    Status = veiculo.Tag?.Status
                });
            }

            return new RelatorioVeiculos().GetReport(listaVeiculos, extensao, logo);
        }

        public IQueryable<GridDadosVeiculoResponseDTO> GetDataToGridAndReport(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var veiculos = _veiculoRepository.GetAll()
                .Include(o => o.Empresa)
                .Include(o => o.Filial)
                .Include(o => o.Motorista)
                .Include(o => o.Proprietario);

            if (idEmpresa.HasValue)
                veiculos = veiculos.Where(o => o.IdEmpresa == idEmpresa);

            #region Filtros
            
            var filtroTipoVeiculo = filters?.FirstOrDefault(x => x.Campo == "TipoVeiculo");

            if (filtroTipoVeiculo != null)
            {
                switch (filtroTipoVeiculo.Valor.ToInt())
                {
                    case 0:
                        veiculos = veiculos.Where(o => o.IdTipoCarreta.HasValue);
                        break;
                    case 1:
                        veiculos = veiculos.Where(o => o.IdTipoCavalo.HasValue);
                        break;
                }
                filters.Remove(filtroTipoVeiculo);
            }
            
            var filtroCiotAgregado = filters?.FirstOrDefault(x => x.Campo == "CiotAgregado");

            if (filtroCiotAgregado != null)
            {
                switch (filtroCiotAgregado.Valor.ToInt())
                {
                    case 0:
                        veiculos = veiculos.Where(o => o.HabilitarContratoCiotAgregado == null);
                        break;
                    case 1:
                        veiculos = veiculos.Where(o => o.HabilitarContratoCiotAgregado == true);
                        break;
                    case 2:
                        veiculos = veiculos.Where(o => o.HabilitarContratoCiotAgregado == false);
                        break;
                }
                filters.Remove(filtroCiotAgregado);
            }

            #endregion

            #region TAG
            
            var result = veiculos.GroupJoin(
                            _tagRepository.GetAll().Where(x => x.Status == EStatusTag.Vinculado),
                            veiculo => veiculo.Placa,
                            tag => tag.Placa,
                            (veiculo, tags) => new { Veiculo = veiculo, Tags = tags.DefaultIfEmpty() }
                        )
                        .SelectMany(
                            x => x.Tags,
                            (veiculo, tag) => new GridDadosVeiculoResponseDTO
                            {
                                IdVeiculo = veiculo.Veiculo.IdVeiculo,
                                Placa = veiculo.Veiculo.Placa,
                                Marca = veiculo.Veiculo.Marca,
                                Modelo = veiculo.Veiculo.Modelo,
                                TipoContrato = veiculo.Veiculo.TipoContrato,
                                NumeroFrota = veiculo.Veiculo.NumeroFrota,
                                Municipio = veiculo.Veiculo.Municipio,
                                QuantidadeEixos = veiculo.Veiculo.QuantidadeEixos,
                                HabilitarContratoCiotAgregado = veiculo.Veiculo.HabilitarContratoCiotAgregado,
                                IdTipoCarreta = veiculo.Veiculo.IdTipoCarreta,
                                IdTipoCavalo = veiculo.Veiculo.IdTipoCavalo,
                                IdEmpresa = veiculo.Veiculo.IdEmpresa,
                                Ativo = veiculo.Veiculo.Ativo,
                                ComTracao = veiculo.Veiculo.ComTracao,
                                Filial = new GridDadosFilialVeiculoResponseDTO()
                                {
                                    NomeFantasia = veiculo.Veiculo.Filial.NomeFantasia,
                                    RazaoSocial = veiculo.Veiculo.Filial.RazaoSocial
                                },
                                Motorista = new GridDadosMotoristaVeiculoResponseDTO()
                                {
                                    Nome = veiculo.Veiculo.Motorista.Nome
                                },
                                Proprietario = new GridDadosVeiculoProprietarioResponseDTO
                                {
                                    RNTRC = veiculo.Veiculo.Proprietario.RNTRC,
                                    NomeFantasia = veiculo.Veiculo.Proprietario.NomeFantasia,
                                    CNPJCPF = veiculo.Veiculo.Proprietario.CNPJCPF,
                                    RazaoSocial = veiculo.Veiculo.Proprietario.RazaoSocial,
                                },
                                Empresa = new GridDadosEmpresaVeiculoResponseDTO
                                {
                                    RazaoSocial = veiculo.Veiculo.Empresa.RazaoSocial,
                                },
                                Tag = new GridDadosTagVeiculoResponseDTO()
                                {
                                    SerialNumber = tag.SerialNumber.ToString(),
                                    Status = tag.SerialNumber.ToString() == "" ? null : tag.Desbloqueado ? "Desbloqueado" : "Bloqueado"
                                }
                            }
                        );

            #endregion
            
            result = result.AplicarFiltrosDinamicos(filters);

            result = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? result.OrderByDescending(o => o.IdVeiculo)
                : result.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            
            return result;
        }

        public IQueryable<Veiculo> GetAll()
        {
            return _veiculoRepository.All();
        }

        public object ConsultarGridVeiculoEmpresa(int? idEmpresa, int? idFilial, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool marcarTodos,
            int apertou)
        {
            var ativo = false;
            var veiculos = GetDataToGridVeiculo(idEmpresa, idFilial, orderFilters, filters);

            if (apertou == 1)
            {
                ativo = marcarTodos;
            }

            var result = veiculos.Skip((page - 1) * take)
                .Take(take)
                .ToList()
                .Select(
                    o =>
                        new
                        {
                            o.IdVeiculo,
                            Placa = o.Placa?.ToPlacaFormato(),
                            Ativo = ativo,
                            DescricaoFilial = o.Filial?.RazaoSocial
                        });

            return new
            {
                totalItems = veiculos.Count(),
                items = result
            };
        }

        private IQueryable<Veiculo> GetDataToGridVeiculo(int? idEmpresa, int? idFilial, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var veiculos = _veiculoRepository
                .Include(x => x.Filial);

            if (idEmpresa.HasValue)
                veiculos = veiculos.Where(o => o.IdEmpresa == idEmpresa && o.Ativo);

            if (idFilial.HasValue)
                veiculos = veiculos.Where(o => o.IdFilial == idFilial);

            veiculos = veiculos.AplicarFiltrosDinamicos(filters);

            veiculos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? veiculos.OrderBy(o => o.Placa)
                : veiculos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return veiculos;
        }

        private ValidationResult IsValidCrud(Veiculo veiculo, EProcesso processo, bool empresaPossuiPlacaEspecipal)
        {
            var validationResult = new ValidationResult();
            var veiculoRepository = _veiculoRepository;

            if (veiculo.IdTipoCavalo <= 0)
                veiculo.IdTipoCavalo = null;

            if (veiculo.IdTipoCarreta <= 0)
                veiculo.IdTipoCarreta = null;

            if (veiculo.IdEmpresa.HasValue)
            {
                if (veiculo.IdTipoCavalo.HasValue)
                {
                    var tipoCavalo = _tipoCavaloService.Get(veiculo.IdTipoCavalo.Value);
                    if (tipoCavalo == null || (tipoCavalo.IdEmpresa != null && tipoCavalo.IdEmpresa != veiculo.IdEmpresa))
                    {
                        validationResult.Add("Tipo de veículo informado é inválido.");
                    }
                }

                if (veiculo.IdTipoCarreta.HasValue)
                {
                    var tipoCarreta = _tipoCarretaService.Get(veiculo.IdTipoCarreta.Value);
                    if (tipoCarreta?.IdEmpresa != null && tipoCarreta.IdEmpresa != veiculo.IdEmpresa)
                    {
                        validationResult.Add("Tipo de carreta informada é inválida.");
                    }
                }
            }

            if (!empresaPossuiPlacaEspecipal)
            {
                if (!Veiculo.PlacaValida(veiculo.Placa))
                {
                    validationResult.Add($"Placa {veiculo.Placa} é inválida.");
                }
            }

            if (processo == EProcesso.Create)
            {
                if (veiculo.TipoRodagem <= 0)
                {
                    veiculo.TipoRodagem = ETipoRodagem.Simples;
                }

                var veiculoEmpresa = veiculoRepository.Find(x => x.IdEmpresa == veiculo.IdEmpresa && x.Placa == veiculo.Placa && x.Ativo);
                if (veiculoEmpresa != null && veiculoEmpresa.Any())
                {
                    validationResult.Add($"Placa {veiculo.Placa} já cadastrada para essa empresa.");
                }
            }

            if (processo == EProcesso.Update && veiculo.IdEmpresa.HasValue)
            {
                var veiculoEmpresa = veiculoRepository.Find(x => x.IdEmpresa == veiculo.IdEmpresa && x.Placa == veiculo.Placa && x.Ativo);
                if (veiculoEmpresa != null && veiculoEmpresa.Any(x => x.IdVeiculo != veiculo.IdVeiculo))
                {
                    validationResult.Add($"Placa {veiculo.Placa} já cadastrada para essa empresa.");
                }
            }

            #region Motorista
            
            /*
             BO - 37625 - ATS - Retirar do código a parte que realiza uma validação
             na alteração do veículo, verificando se o ID Empresa do Veículo é o mesmo do Motorista.
            */
            
            // if (veiculo.IdMotorista != null)
            // {
            //     if (veiculo.IdEmpresa != null && veiculo.IdEmpresa != _motoristaService.Get((int) veiculo.IdMotorista).IdEmpresa)
            //         validationResult.Add("Motorista não associado a esse empresa");
            // }

            #endregion

            #region Proprietário

            if (veiculo.IdProprietario != null)
            {
                var idEmpresaProprietario = _proprietarioRepository.GetQuery(veiculo.IdProprietario.Value).Select(o => o.IdEmpresa).FirstOrDefault();

                if (veiculo.IdEmpresa != null && veiculo.IdEmpresa != idEmpresaProprietario)
                {
                    validationResult.Add($"{WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]} proprietário {veiculo.IdProprietario} não associado a essa empresa");
                }
            }

            #endregion

            #region Número de frota

            if (veiculo.NumeroFrota.HasValue && veiculo.NumeroFrota != 0 && veiculo.TipoContrato != ETipoContrato.Terceiro)
            {
                Veiculo buscarNumeroFrota = null;

                if (processo == EProcesso.Create)
                {
                    buscarNumeroFrota = veiculoRepository.Find(x => x.NumeroFrota == veiculo.NumeroFrota && x.IdEmpresa == veiculo.IdEmpresa)
                        .FirstOrDefault();
                }
                else if (processo == EProcesso.Update)
                {
                    buscarNumeroFrota = veiculoRepository.Find(x => x.NumeroFrota == veiculo.NumeroFrota && x.IdEmpresa == veiculo.IdEmpresa)
                        .FirstOrDefault();
                }

                if (buscarNumeroFrota != null)
                {
                    validationResult.Add($"Número Frota {veiculo.NumeroFrota} já cadastrado para essa empresa.");
                }
            }

            #endregion

            #region RENAVAM

            if (!string.IsNullOrWhiteSpace(veiculo.RENAVAM))
            {
                veiculo.RENAVAM = veiculo?.RENAVAM?.Length < 11 ? veiculo?.RENAVAM?.PadLeft(11, '0') : veiculo?.RENAVAM?.Substring(0, 11);
            }

            #endregion

            return validationResult;
        }
        
        public string GetRntrcProprietarioVeiculo(string placa, int idEmpresa)
        {
            return _veiculoRepository.GetRntrcProprietarioVeiculo(placa, idEmpresa);
        }
        
        public string GetRntrcProprietarioVeiculo(int idVeiculo)
        {
            return _veiculoRepository.GetRntrcProprietarioVeiculo(idVeiculo);
        }

        private static void FormatValues(Veiculo veiculo)
        {
            veiculo.Placa = veiculo.Placa?.RemoveSpecialCharacters()?.Trim().ToUpper();
        }
    }
}
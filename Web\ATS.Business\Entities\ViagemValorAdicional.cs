﻿using ATS.Domain.Enum;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemValorAdicional
    {
        /// <summary>
        /// Código do valor adicional da viagem
        /// </summary>
        public int IdViagemValorAdicional { get; set; }

        /// <summary>
        /// Código do evento da viagem
        /// </summary>
        public int IdViagemEvento { get; set; }

        /// <summary>
        /// Número do documento do valor adicional da viagem
        /// </summary>
        public int NumeroDocumento { get; set; }

        /// <summary>
        /// Número do documento do valor adicional da viagem
        /// </summary>
        public ETipoValorAdicional Tipo { get; set; }


        /// <summary>
        /// Descrição do valor adicional da viagem
        /// </summary>
        public string Descricao { get; set; }

        /// <summary>
        /// Valor adicional da viagem
        /// </summary>
        public decimal Valor { get; set; }

        #region Virtual Fields
        public virtual ViagemEvento ViagemEvento { get; set; }
        public long? CodigoERP { get; set; }
        #endregion
    }
}
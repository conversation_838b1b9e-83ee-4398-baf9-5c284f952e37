﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    public class RequestCore
    {
        public PushRequest Requisicao { get; set; }
        public string Token { get; set; }
    }

    public class RequestCoreExcelsior
    {
        public PushRequestExcelsior Requisicao { get; set; }
        public string Token { get; set; }
    }

    public class PushRequest
    {
        public string App { get; set; } = "excelsior";
        public int Id { get; set; }
        public int TempoExpiracao { get; set; }
        public List<string> ListaIdPush { get; set; }

        public PushMensagem Mensagem { get; set; }

        public PushExtras Extras { get; set; }
    }

    public class PushRequestExcelsior
    {
        public int idPush { get; set; }
        public int expirationTime { get; set; }
        public List<string> idList { get; set; }
        public PushMensagemExcelsior message { get; set; }
        public string app { get; set; } = "excelsior";
    }

    public class PushMensagem
    {
        public string TituloNotificacao { get; set; }
        public string Mensagem { get; set; }
        public string ObjetoJSON { get; set; }
        public string MensagemTipo { get; set; }
    }

    public class PushMensagemExcelsior
    {
        public string title { get; set; }
        public string message { get; set; }
        public string messageType { get; set; }
    }

    public class PushMessage
    {
        public string title { get; set; }
        public string message { get; set; }
        public string messageType { get; set; }
        public string body { get; set; }
        public string sound { get; set; }
        public bool content_available { get; set; }
    }

    public class PushChat
    {
        public string Token { get; set; }
        public string CpfRemetente { get; set; }
        public string CpfDestinatario { get; set; }
        public string Assunto { get; set; }
        public string Conteudo { get; set; }
        public DateTime DataHoraEnvio { get; set; }
    }

    public class PushExtras
    {
        public int IdMotorista { get; set; }
        public int IdCarga { get; set; }
    }

    public class PushCarga
    {
        public int IdFrete { get; set; }
        public string Origem { get; set; }
        public string Destino { get; set; }
        public decimal? Peso { get; set; }
        public decimal? Valor { get; set; }
    }

    public class GoogleNotification
    {
        public GoogleNotificationMessage message { get; set; }
    }

    public class GoogleNotificationMessage
    {
        public string token { get; set; }
        public GoogleNotificationPayload notification { get; set; }
        public object data { get; set; }
    }

    public class GoogleNotificationPayload
    {
        public string body { get; set; }
        public string title { get; set; }
    }

    public class GoogleNotificationData
    {
        public string keysandvalues { get; set; }
    }

    public enum ETipoMensagemPush
    {
        [Description("Aviso")] Warning = 1,

        [Description("Nova Mensagem")] ChatMessage = 2,

        [Description("Selecionado para a carga")]
        SelectedToFreight = 3,

        [Description("Broadcast")] BroadCast = 4,

        [Description("Nota")] Nota = 5,

        [Description("Mensagem Genérica")] MessageGeneric = 6,

        [Description("Alteração de senha")] PasswordReset = 7,

        [Description("CheckList Motorista")] CheckListMotorista = 8,

        [Description("Ocorrência autorizada")] AuthorizedNote = 9,

        [Description("Novo Rotograma")] Rotogram = 10,

        [Description("Ocorrência do rotograma")]
        RotogramOccurrence = 11,

        [Description("Selecionado para o lote")]
        SelectedToLot = 12,

        [Description("Removido do lote")] RemovedFromLot = 13,

        [Description("Vínculado ao motorista")]
        AttachedToDriver = 14,

        [Description("Status atualizado no gerenciamento de risco")]
        StateUpdated = 15,

        [Description("Lote suspenso")] LotSuspended = 16,

        [Description("Aplicativo sem uso")] AppWithoutUse = 17,

        [Description("Carga disponível")] AvailableFreight = 18,

        [Description("Inicio de viagem")] BeginningOfFreight = 19,

        [Description("Novo agendamento cadastrado para o vistoriador")]
        NovoAgendamentoVistoriador = 20,

        [Description("Agendamento alterado")] AgendamentoVistoriadorAlterado = 21,

        [Description("Agendamento cancelado")] AgendamentoVistoriadorCancelado = 22,

        [Description("Ordem de carregamento gerada")]
        MotNaFilaOcGerada = 23,

        [Description("Notificações de alertas")]
        NotificacaoAlerta = 24,
        
        [Description("Nota cancelada")]
        NotaCancelada = 25
    }
}
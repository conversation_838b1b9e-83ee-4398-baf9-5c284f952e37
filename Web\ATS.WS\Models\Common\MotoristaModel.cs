﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class MotoristaModel
    {
        public int IdMotorista { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEmpresa { get; set; }
        public string Nome { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string CPF { get; set; }
        public string Sexo { get; set; }
        public string CNH { get; set; }
        public DateTime? ValidadeCNH { get; set; }
        public string DataValidadeCNH { get; set; }
        public string CNHCategoria { get; set; }
        public string Celular { get; set; }
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Frota;
        public string Email { get; set; }
        public bool Ativo { get; set; } = true;
        public ETipoPessoa TipoMotorista { get; set; } = ETipoPessoa.Fisica;

        #region Endereço

        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IdPais { get; set; }
        public int IdCidade { get; set; }
        public int IdEstado { get; set; }

        #endregion

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual EmpresaModel Empresa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual PaisModel Pais { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual EstadoModel Estado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual CidadeModel Cidade { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual VeiculoModel Veiculo { get; set; }

        //Campo criado para retorno de conjunto do motorista base.
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<MotoristaVeiculoModel> Veiculos { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdMotoristaBase { get; set; }
    }
}
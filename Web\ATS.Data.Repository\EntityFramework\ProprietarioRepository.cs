﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Context;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using AutoMapper.QueryableExtensions;
using Sistema.Framework.Util.Extension;

namespace ATS.Data.Repository.EntityFramework
{
    public class ProprietarioRepository : Repository<Proprietario>, IProprietarioRepository
    {
        private IUserIdentity _sessionUser;
        
        public ProprietarioRepository(AtsContext context, IUserIdentity sessionUser) : base(context)
        {
            _sessionUser = sessionUser;
        }
        
        /// <summary>
        /// Retorna os proprietários a partir dos dados do filtro
        /// </summary>
        /// <param name="razaoSocial">Razão Social do Proprietário</param>
        /// <param name="idEmpresa">Id do empresa</param>
        /// <param name="onlyAtivo">Indica se deve filtrar somente por ativos</param>
        /// <returns>IQueryable de ProprietarioGrid</returns>
        public IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, bool? onlyAtivo)
        {
            var retorno = from Proprietario in All()
                        .Include(p => p.Contatos)
                        .Include(p => p.Enderecos)
                  where Proprietario.RazaoSocial.Contains(razaoSocial) && (idEmpresa == 0 || Proprietario.IdEmpresa == idEmpresa)
                 select Proprietario;

            //Filtra por somente ativos
            if (onlyAtivo != null && (bool)onlyAtivo)
                retorno = retorno.Where(x => x.Ativo);

            return retorno;
        }

        /// <summary>
        /// Retorna o proprietário, informando o CPF/CNPJ do proprietário
        /// </summary>
        /// <param name="idEmpresa">ID do empresa</param>
        /// <param name="cpfCNPJEmpresa">CPF/CNPJ do Empresa</param>
        /// <returns></returns>
        public Proprietario GetProprietario(int idEmpresa, string cpfCNPJEmpresa)
        {
            return (from proprietario in All()
                   where proprietario.IdEmpresa == idEmpresa &&
                         proprietario.CNPJCPF == cpfCNPJEmpresa && proprietario.Ativo
                  select proprietario)?.FirstOrDefault();
        }

        /// <summary>
        /// Retorna o proprietário
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public override Proprietario Get(int id)
        {
            return (from proprietario in All()
                        .Include(p => p.Contatos)
                        .Include(p => p.Enderecos)
                   where proprietario.IdProprietario == id
                  select proprietario).FirstOrDefault();
        }
        
        public ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa)
        {
            return Find(x => x.CNPJCPF == cnpjCpf && x.Empresa.CNPJ == cnpjEmpresa)
                .ProjectTo<ProprietarioAnttDto>().FirstOrDefault();
        }
        
        public ProprietarioAnttDto GetDadosProprietarioAntt(int idProprietario)
        {
            return Find(x => x.IdProprietario == idProprietario)
                .ProjectTo<ProprietarioAnttDto>().FirstOrDefault();
        }

        public bool Any(string cnpjCpf, int idEmpresa)
        {
            return Any(x => x.CNPJCPF == cnpjCpf && x.IdEmpresa == idEmpresa);
        }
        
        /// <summary>
        /// Retorna o tipo de contrato do proprietario
        /// </summary>
        /// <param name="cpfCnpj">CPF/CNPJ do Proprietario</param>
        /// <param name="idEmpresa">ID do Empresa</param>
        /// <returns></returns>
        public ETipoContrato GetTipoContrato(string cpfCnpj, int idEmpresa)
        {
            var cpfCnpjOnlyNumbers = cpfCnpj.OnlyNumbers();

            var tipoContrato = (int) Find(p => p.CNPJCPF == cpfCnpjOnlyNumbers && p.IdEmpresa == idEmpresa)
                .Select(p => p.TipoContrato).FirstOrDefault();

            return (tipoContrato > 0 ? (ETipoContrato)tipoContrato : ETipoContrato.Terceiro);
        }
        
        public Proprietario GetPorCpfCnpj(string cpf, List<string> cnpjs)
        {
            var cpfCnpj = cpf?.OnlyNumbers();
            
            return Find(x => (x.CNPJCPF == cpfCnpj || cnpjs.Contains(x.CNPJCPF)) && x.Ativo).Include(x => x.Veiculos)
                .Include(x => x.Contatos).Include(x => x.Enderecos).Include(x => x.Enderecos.Select(y => y.Pais))
                .Include(x => x.Enderecos.Select(y => y.Estado)).Include(x => x.Enderecos.Select(y => y.Cidade))
                .FirstOrDefault();
        }
        
        /// <summary>
        /// Buscar Proprietário por CPF ou CNPJ
        /// </summary>
        /// <param name="cpfCnpj">Documento CPF ou CNPJ</param>
        /// <param name="idEmpresa"></param>
        /// <returns>Objeto de Proprietário</returns>
        public Proprietario GetPorCpfCnpj(string cpfCnpj, int? idEmpresa = null)
        {
            var cpfCnpjOnlyNumbers = cpfCnpj?.OnlyNumbers();

            var query = Find(x => x.CNPJCPF == cpfCnpjOnlyNumbers && x.Ativo)
                .Include(x => x.Veiculos)
                .Include(x => x.Contatos)
                .Include(x => x.Enderecos)
                .Include(x => x.Enderecos.Select(y => y.Pais))
                .Include(x => x.Enderecos.Select(y => y.Estado))
                .Include(x => x.Enderecos.Select(y => y.Cidade));

            if (idEmpresa.HasValue && idEmpresa > 0)
            {
                query = query.Where(p => p.IdEmpresa == idEmpresa);
            }
            else if (_sessionUser.IdEmpresa.HasValue)
            {
                query = query.Where(p => p.IdEmpresa == _sessionUser.IdEmpresa);
            }

            return query.FirstOrDefault();
        }
        
        /// <summary>
        /// Retorna o ID do proprietário a partir do código do CNPJ e CPF
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public int? GetIdPorCpfCnpj(string cpfCnpj, int? idEmpresa = null)
        {
            var cpfCnpjOnlyNumbers = cpfCnpj?.OnlyNumbers();
            
            var query = Where(p => p.CNPJCPF == cpfCnpjOnlyNumbers);

            if (idEmpresa.HasValue && idEmpresa.Value > 0)
                query = query.Where(p => p.IdEmpresa == idEmpresa);

            var retId = query.Select(p => p.IdProprietario).FirstOrDefault();

            return retId > 0 ? (int?) retId : null;
        }
        
        public Proprietario GetById(int idProprietario)
        {
            return Find(x => x.IdProprietario == idProprietario)
                .Include(x => x.Contatos)
                .Include(x => x.Enderecos)
                .Include(x => x.Enderecos.Select(o => o.Cidade))
                .Include(x => x.Enderecos.Select(o => o.Estado))
                .FirstOrDefault();
        }
        
        /// <summary>
        /// Listar todos os proprietários ativos
        /// </summary>
        /// <returns>IQueryable de Proprietario</returns>
        public IQueryable<Proprietario> AllAtivos()
        {
            return Find(x => x.Ativo);
        }
        
        public bool PermiteTransferenciaSemCartaFrete(string cnpjCpf)
        {
            var prop = Where(c => c.CNPJCPF == cnpjCpf).FirstOrDefault();

            if (prop != null)
                return prop.TransferirEntreCartoes;

            return false;
        }
        
        public IQueryable<Proprietario> GetQuery(int idProprietario)
        {
            return Where(c => c.IdProprietario == idProprietario);
        }
    }
}
﻿using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.WS.Models.Webservice.Request.Cartoes;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class CartaoAplicativoController : BaseController
    {
        private readonly SrvCartaoAplicativo _srvCartaoAplicativo;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public CartaoAplicativoController(BaseControllerArgs baseArgs, SrvCartaoAplicativo srvCartaoAplicativo, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies) : base(baseArgs)
        {
            _srvCartaoAplicativo = srvCartaoAplicativo;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        [HttpPost]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult TransferirValorCartao(CartaoTransferenciaAplicativoModel request)
        {
            try
            {
                var resultado = _srvCartaoAplicativo.TransferirValorCartao(request);

                return Responde(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult TransferirValorContaBancaria(CartaoTransferenciaContaBancariaAplicativoModel request)
        {
            try
            {
                var resultado = _srvCartaoAplicativo.TransferirContaBancaria(request);

                return Responde(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult MotoristaValidoParaTransferencia(MotoristaValidoParaTransferenciaRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var resultado = _srvCartaoAplicativo.MotoristaValidoParaTransferencia(request);

                return Responde(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult ConsultarParametrosCartao(CartaoParametrosRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var response = _srvCartaoAplicativo.CartaoParametros(request);

                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(e);
            }
        }


        [HttpGet]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult ConsultarExtrato(ConsultarExtratoAtsRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultarExtratoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                IdentificadorCartao cartao;
                if (request.Identificador == null)
                    cartao = cartoesApp.GetUltimoCartao(request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);
                else
                    cartao = new IdentificadorCartao()
                    {
                        Identificador = request.Identificador,
                        Produto = request.Produto
                    };

                if(cartao?.Identificador == null || cartao?.Produto == null)
                    return Responde(new ConsultarExtratoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = $"Não foi possível encontrar um cartão para o documento {request.Documento}"
                    });

                var consultarExtratoRequest = new ConsultarExtratoRequest()
                {
                    Cartao = cartao,
                    DataInicio = request.DataInicio,
                    DataFim = request.DataFim,
                    Tipo = request.Tipo,
                    ExibirMetadados = request.ExibirMetadados
                };

                var extrato = cartoesApp.ConsultarExtrato(consultarExtratoRequest,request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(extrato);
            }
            catch (Exception e)
            {
                return Responde(new ConsultarExtratoResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult ConsultarSaldoCartao(ConsultarSaldoCartaoAtsRequest request)
        {
            try
            {
                ValidationResult validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultarSaldoCartaoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                IdentificadorCartao cartao;
                if (request.Identificador == null)
                    cartao = cartoesApp.GetUltimoCartao(request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);
                else
                    cartao = new IdentificadorCartao()
                    {
                        Identificador = request.Identificador,
                        Produto = request.Produto
                    };

                if(cartao?.Identificador == null || cartao?.Produto == null)
                    return Responde(new ConsultarSaldoCartaoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = $"Não foi possível encontrar um cartão para o documento {request.Documento}"
                    });

                var consultarSaldoCartaoRequest = new ConsultarSaldoCartaoRequest() {Cartao = cartao};

                var saldoCartao = cartoesApp.ConsultarSaldoCartao(consultarSaldoCartaoRequest, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(saldoCartao);
            }
            catch (Exception e)
            {
                return Responde(new ConsultarSaldoCartaoResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult CartoesVinculados(CartoesVinculadosAtsRequest request)
        {
            try
            {
                ValidationResult validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new CartaoVinculadoPessoaListResponseDto
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                // Sempre irá buscar pela administradora
                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, string.Empty, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var cartoesVinculados = cartoesApp.GetCartoesVinculados(request.Documento, request.Produtos, false, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var response = Mapper.Map<CartaoVinculadoPessoaListResponseDto>(cartoesVinculados);
                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(new CartaoVinculadoPessoaListResponseDto
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult ContasBancarias(ContasBancariasAtsRequest request)
        {
            try
            {
                ValidationResult validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultarContasBancariasResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var contasBancarias = cartoesApp.ContasBancarias(request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(contasBancarias);
            }
            catch (Exception e)
            {
                return Responde(new ConsultarContasBancariasResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpPost]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult InativarContaBancaria(InativarContaBancariaAtsRequest request)
        {
            try
            {
                ValidationResult validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new InativarContaBancariaAtsResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var inativarContaBancariaRequest = new InativarContaBancariaRequest()
                {
                    Documento = request.Documento,
                    CodigoBacenBanco = request.CodigoBacenBanco,
                    Agencia = request.Agencia,
                    Conta = request.Conta,
                    TipoContaBancaria = request.TipoContaBancaria
                };

                var response = cartoesApp.InativarContaBancaria(inativarContaBancariaRequest, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(new InativarContaBancariaAtsResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ValidarSenhaCartao(ValidarSenhaCartaoAtsRequest request)
        {
            try
            {
                ValidationResult validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ValidarSenhaCartaoResponseDto
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                IdentificadorCartao cartao;
                if(request.Identificador == null)
                    cartao = cartoesApp.GetUltimoCartao(request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);
                else
                    cartao = new IdentificadorCartao()
                    {
                        Identificador = request.Identificador,
                        Produto = request.Produto
                    };

                if(cartao?.Identificador == null || cartao?.Produto == null)
                    return Responde(new ValidarSenhaCartaoResponseDto
                    {
                        Sucesso = false,
                        Mensagem = $"Não foi possível encontrar um cartão para o documento {request.Documento}"
                    });

                var response = cartoesApp.ValidarSenhaCartao(cartao.Identificador.Value, cartao.Produto.Value, request.Senha);

                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(new ValidarSenhaCartaoResponseDto
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
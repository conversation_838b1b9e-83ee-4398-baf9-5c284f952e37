﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface ITipoCarretaService : IService<TipoCarreta>
    {
        TipoCarreta Get(int id);
        ValidationResult Add(TipoCarreta entity);
        ValidationResult Update(TipoCarreta entity);
        IQueryable<TipoCarretaGrid> Consultar(string nome, int? idEmpresa);
        ValidationResult Inativar(int idTipoCarreta);
        ValidationResult Reativar(int idTipoCarreta);
        IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria);
        IEnumerable<TipoCarreta> GetIdsAtualizados(DateTime dataAtualizacao, List<int> idsEmpresa);
        IQueryable<TipoCarreta> All();

        /// <summary>
        /// Retorna apenas o objeto de tipo carreta
        /// </summary>
        /// <param name="id"></param>
        /// <returns>TipoCarreta</returns>
        TipoCarreta GetTipoCarreta(int id);

        object ConsultarSemEmpresa();

        TipoCarreta GetPorDescricao(string nome, int idEmpresa);
        object ConsultaGrid(int? idEmpresa, int? idTipoCarreta, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
    }
}
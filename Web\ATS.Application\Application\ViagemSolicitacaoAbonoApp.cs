﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Service;
using System;
using System.Collections.Generic;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class ViagemSolicitacaoAbonoApp : AppBase, IViagemSolicitacaoAbonoApp
    {
        private readonly IViagemSolicitacaoAbonoService _viagemSolicitacaoAbonoService;

        public ViagemSolicitacaoAbonoApp(IViagemSolicitacaoAbonoService viagemSolicitacaoAbonoService)
        {
            _viagemSolicitacaoAbonoService = viagemSolicitacaoAbonoService;
        }

        public List<ViagemSolicitacaoAbono> GetSolicitacoes(string token, string numero, DateTime? dataInicio, DateTime? dataFim, EStatusAbono? status)
        {
            return _viagemSolicitacaoAbonoService.GetSolicitacoes(token, numero, dataInicio, dataFim, status);
        }

        public ViagemSolicitacaoAbono AlterarStatus(int idViagemSolicitacao, EStatusAbono status)
        {
            return _viagemSolicitacaoAbonoService.AlterarStatus(idViagemSolicitacao, status);
        }
    }
}
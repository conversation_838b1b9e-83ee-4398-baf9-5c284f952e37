﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.CrossCutting.IoC.Utils;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System;
using System.Data.SqlClient;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models.Usuario;
using ATS.Domain.Validation;
using AutoMapper.QueryableExtensions;
using Dapper;
using Sistema.Framework.Util.Extension;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioRepository : Repository<Usuario>, IUsuarioRepository
    {
        private readonly IUsuarioDapper _usuarioDapper;
        public UsuarioRepository(AtsContext context, IUsuarioDapper usuarioDapper) : base(context)
        {
            _usuarioDapper = usuarioDapper;
        }
        
        public class UsuarioCamposDeInteresse
        {
            public int? IdEmpresa { get; set; }
            public EPerfil Perfil { get; set; }
            public string Nome { get; set; }
        }

        /// <summary>
        /// Retorna o registro com todos os elementos filhos
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public Usuario GetAllChilds(int id)
        {
            return (from usuario in All()
                    .Include(x => x.GrupoUsuario)
                    .Include(x => x.Empresa)
                    .Include(x => x.Filiais)
                    .Include(x => x.Veiculos)
                    .Include(x => x.Contatos)
                    .Include(x => x.Enderecos)
                    .Include(x => x.HorariosCheckIn)
                    .Include(x => x.Enderecos.Select(e => e.Pais))
                    .Include(x => x.Enderecos.Select(e => e.Estado))
                    .Include(x => x.Enderecos.Select(e => e.Cidade))
                    .Include(x => x.UsuarioEstabelecimentos)
                    .Include(x => x.Documentos)
                    .Include(x => x.Documentos.Select(p => p.TipoDocumento))
                where usuario.IdUsuario == id
                select usuario).FirstOrDefault();
        }

        /// <summary>
        /// Retorna o registro com as tabelas filhas
        /// </summary>
        /// <param name="id">ID do usuário</param>
        /// <returns></returns>
        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public override Usuario Get(int id)
        {
            return (from usuario in All()
                    .Include(x => x.Filiais)
                    .Include(x => x.Veiculos)
                    .Include(x => x.Contatos)
                    .Include(x => x.Enderecos)
                    .Include(x => x.HorariosCheckIn)
                    .Include(x => x.UsuarioEstabelecimentos)
                    .Include(x => x.Veiculos.Select(y => y.Empresa))
                where usuario.IdUsuario == id
                select usuario).FirstOrDefault();
        }

        public Usuario GetUsuarioBase(int id)
        {
            return (from usuario in All()
                where usuario.IdUsuario == id
                select usuario).FirstOrDefault();
        }

        /// <summary>
        /// Retorna a consulta na base de dados pelas variaveis no formato do objeto UsuarioGrid
        /// </summary>
        /// <param name="nome">Nome do usuário (Like)</param>
        /// <param name="idEmpresa">Id do Empresa</param>
        /// <returns></returns>
        public IQueryable<Usuario> Consultar(string nome, int? idEmpresa)
        {
            var usuarios = from Usuario in All()
                    .Include(x => x.GrupoUsuario)
                    .Include(x => x.Empresa)
                    .Include(x => x.UsuarioEstabelecimentos)
                    .Include(x => x.UsuarioEstabelecimentos.Select(o => o.Estabelecimento))
                where (idEmpresa == null || (idEmpresa != null && Usuario.IdEmpresa == idEmpresa))
                select Usuario;

            if (!string.IsNullOrWhiteSpace(nome))
                usuarios = usuarios.Where(x => x.Nome.Contains(nome));

            return usuarios;
        }

        public bool ExisteUsuarioAtivoComCpf(string cpfCnpjDesabilitado)
        {
            return All().Any(x => x.Ativo && x.CPFCNPJ == cpfCnpjDesabilitado);
        }


        public IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario)
        {
            var grupoUsuario = from usuario in All()
                where usuario.CPFCNPJ.Contains(cpfUsuario)
                select usuario.GrupoUsuario;

            var retorno = grupoUsuario.SelectMany(x =>
                x.Usuarios.Where(y => y.Ativo).Select(y => new ConsultaVistoriadores
                {
                    IdUsuario = y.IdUsuario,
                    Nome = y.Nome,
                    CPF = y.CPFCNPJ
                })).ToList();

            return retorno.Distinct();
        }

        public void AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso)
        {
            var database = ((AtsContext) Context).Database;

            switch (tipoAcesso)
            {
                case ETipoAcessoSistema.Web:
                    database.ExecuteSqlCommand(
                        "update usuario set DataUltimoAcessoWeb = current_timestamp where idusuario = " + idUsuario);
                    break;
                case ETipoAcessoSistema.Aplicativo:
                    database.ExecuteSqlCommand(
                        "update usuario set DataUltimoAcessoAplicativo = current_timestamp where idusuario = " +
                        idUsuario);
                    break;
            }
        }

        public ValidationResult AtualizarDataUltimaAberturaAplicativo(int usuarioId)
        {
	        var usuario = Where(o => o.IdUsuario == usuarioId).FirstOrDefault();
	        
	        if (usuario == null)
		        throw new Exception($"Usuario Id { usuarioId} não encontrado na base.");
	        
	        usuario.DataUltimaAberturaAplicativo = DateTime.Now;
	        Update(usuario);
	        
	        return new ValidationResult();
        }

        public ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento)
        {
	        return _usuarioDapper
		        .ConsultarInformacoesMobile(itensPorPagina, pagina, empresaId, documento);
        }
        
        public UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario)
        {
	        return Where(usuario => usuario.IdUsuario == idUsuario)
		        .ProjectTo<UsuarioMicroServicoInstanciaAppDto>().FirstOrDefault();
        }
        
        public UsuarioFotoDto GetFotoUsuario(string cpfcnpj)
        {
	        return Where(usuario => usuario.CPFCNPJ == cpfcnpj)
		        .ProjectTo<UsuarioFotoDto>().FirstOrDefault();
        }
        
        /// <summary>
        /// Retorna o código do
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public int? GetGrupoUsuario(int idUsuario)
        {
            var retorno = Find(u => u.IdUsuario == idUsuario && u.Ativo)
                .Select(u => u.IdGrupoUsuario).FirstOrDefault();
            return retorno;
        }
        
        /// <summary>
        /// Retorna o código do
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public int? GetGrupoUsuarioInativo(int idUsuario)
        {
            var retorno = Find(u => u.IdUsuario == idUsuario)
                .Select(u => u.IdGrupoUsuario).FirstOrDefault();
            return retorno;
        }

        /// <summary>
        /// Retorn os dados completos do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public Usuario GetWithRelationships(int id)
        {
            return this
                .Include(o => o.Filiais)
                .Include(o => o.Enderecos)
                .Include(o => o.GrupoUsuario)
                .Include(x => x.Veiculos)
                .Include(o => o.GrupoUsuario.Menus)
                .Include(x => x.UsuarioEstabelecimentos)
                .Include(x => x.Contatos)
                .FirstOrDefault(o => o.IdUsuario == id);
        }
        
        /// <summary>
        /// Retorna o perfil do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public EPerfil GetPerfil(int idUsuario)
        {
            return Find(x => x.IdUsuario == idUsuario)
                .Select(x => x.Perfil).FirstOrDefault();
        }

        /// <summary>
        /// Retorna todos os usuários que possuem o mesmo código de grupo de usuário
        /// </summary>
        /// <param name="idGrupoUsuario">Código do grupo de usuário</param>
        /// <returns></returns>
        public IQueryable<Usuario> GetPorGrupoUsuario(int idGrupoUsuario)
        {
            return Find(u => u.IdGrupoUsuario == idGrupoUsuario && u.Ativo)
                .AsNoTracking();
        }
        
        /// <summary>
        /// Retorna o código do usuário a partir do CPF
        /// </summary>
        /// <param name="nCNPJCPF">CPF do usuário</param>
        /// <returns></returns>
        public int? GetIdPorCNPJCPF(string nCNPJCPF, int? idEmpresa = null)
        {
            string cCNPJCPF = nCNPJCPF?.OnlyNumbers();

            var query = Find(x => x.CPFCNPJ == cCNPJCPF && x.Ativo);
            
            if (idEmpresa.HasValue)
                query = query.Where(x => x.IdEmpresa == idEmpresa);
            
            int? retId = query.AsNoTracking().Select(x => x.IdUsuario)?.FirstOrDefault();

            return retId.GetValueOrDefault() > 0 ? retId.Value : (int?)null;
        }
        
        /// <summary>
        /// Retorna o Usuário a partir do código do CPF
        /// </summary>
        /// <param name="nCNPJCPF">CPF</param>
        /// <returns></returns>
        public Usuario GetPorCNPJCPF(string nCNPJCPF, bool nWithIncludes = true, int? idEmpresa = null)
        {
            if (string.IsNullOrEmpty(nCNPJCPF))
                return null;

            var cpf = nCNPJCPF.OnlyNumbers();
            var query = Find(x => x.CPFCNPJ == nCNPJCPF && x.Ativo);
            
            if (idEmpresa.HasValue)
                query = query.Where(x => x.IdEmpresa == idEmpresa);
                
            if (nWithIncludes)
                query = query
                    .Include(x => x.Contatos)
                    .Include(x => x.Enderecos)
                    .Include(x => x.Filiais)
                    .Include(x => x.Filiais.Select(y => y.Filial))
                    .Include(x => x.Veiculos)
                    .Include(x => x.Remetente)
                    .Include(x => x.Destinatarios)
                    .Include(x => x.Empresa)
                    .Include(x => x.Documentos)
                    .Include(x => x.Documentos.Select(p => p.TipoDocumento));
            //.Include(x => x.Remetente.Select(e => e.Destinatario))
            //.Include(x => x.Destinatario.Select(e => e.Remetente))
            
            return query.FirstOrDefault();
        }
        
        /// <summary>
        /// Retorna o código do empresa a qual o usuário esta vinculado
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public int? GetIdEmpresa(int id)
        {
            return Find(u => u.IdUsuario == id).Select(u => u.IdEmpresa)?.FirstOrDefault();
        }
        
        public int? GetIdEmpresa(string cpf)
        {
            return Find(o => o.CPFCNPJ == cpf).Select(x => x.IdEmpresa).FirstOrDefault();
        }
        
        /// <summary>
        /// Retorna o CPF a partir do código do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public string GetCNPJCPF(int idUsuario)
        {
            return Find(x => x.IdUsuario == idUsuario)
                .Select(x => x.CPFCNPJ).FirstOrDefault();
        }
    }
}
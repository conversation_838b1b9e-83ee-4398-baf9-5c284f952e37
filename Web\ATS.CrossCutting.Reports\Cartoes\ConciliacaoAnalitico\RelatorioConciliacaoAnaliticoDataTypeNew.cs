﻿namespace ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico
{
    public class RelatorioConciliacaoAnaliticoDataTypeNew
    {
        public string Documento { get; set; }
        
        public string Informacoes { get; set; }
        public string Ciot { get; set; }

        public string Data { get; set; }

        public string Transacao { get; set; }

        public string TipoEvento { get; set; }

        public string Valor { get; set; }

        public string Situacao { get; set; }

        public string Cartao { get; set; }

        public string DocPortador { get; set; }

        public string NomePortador { get; set; }

        public string Placa { get; set; }
        public long? IdViagem { get; set; }
        public string Filial { get; set; }

        public string DocumentoUsuario { get; set; }

        public string NomeUsuario { get; set; }
    }

    public class RelatorioConciliacaoAnaliticoTotalizadorDataType
    {
        public string SaldoIinicial { get; set; }

        public string SaldoFinal { get; set; }

        public string TotalMovimentado { get; set; }

        public string TotalNaoMovimentado { get; set; }
    }

    public class RelatorioConciliacaoAnaliticoCargasEstornosDataType
    {
        public string Label { get; set; }

        public string Carga { get; set; }

        public string Estorno { get; set; }
    }
}

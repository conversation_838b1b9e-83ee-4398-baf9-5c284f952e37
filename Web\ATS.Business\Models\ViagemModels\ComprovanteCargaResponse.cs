using ATS.Domain.Enum;

namespace ATS.Domain.Models.ViagemModels
{
    public class ComprovanteCargaResponse
    {
        public int IdEmpresa { get; set; }
        public FornecedorEnum? Fornecedor { get; set; } 
        public string ProtocoloEnvioValePedagio { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public decimal ValorComprovanteCarga { get; set; }
        public EResultadoCompraPedagio StatusCompraPedagio { get; set; } = EResultadoCompraPedagio.NaoRealizado;
    }
}
﻿using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Response.Estabelecimento;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Request.Estabelecimento
{
    public class ConsultarEstabelecimentosRotaResponse : Retorno<List<ConsultaEstabelecimentoResponse>>
    {
        public ConsultarEstabelecimentosRotaResponse()
        {
        }

        public ConsultarEstabelecimentosRotaResponse(string mensagem) : base(mensagem)
        {
        }

        public ConsultarEstabelecimentosRotaResponse(bool sucesso) : base(sucesso)
        {
        }

        public ConsultarEstabelecimentosRotaResponse(bool sucesso, List<ConsultaEstabelecimentoResponse> retorno) :
            base(sucesso, retorno)
        {
        }

        public ConsultarEstabelecimentosRotaResponse(ValidationResult validationResult,
            List<ConsultaEstabelecimentoResponse> retorno) : base(validationResult, retorno)
        {
        }

        public ConsultarEstabelecimentosRotaResponse(bool sucesso, string mensagem,
            List<ConsultaEstabelecimentoResponse> retorno) : base(sucesso, mensagem, retorno)
        {
        }
    }
}
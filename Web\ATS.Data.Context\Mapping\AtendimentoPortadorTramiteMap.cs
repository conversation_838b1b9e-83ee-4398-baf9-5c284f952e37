using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class AtendimentoPortadorTramiteMap : EntityTypeConfiguration<AtendimentoPortadorTramite>
    {
        public AtendimentoPortadorTramiteMap()
        {
            ToTable("ATENDIMENTO_PORTADOR_TRAMITE");

            HasKey(t => new {t.IdAtendimentoPortador, t.Sequencial});
            
            Property(x => x.IdAtendimentoPortador)
                .HasColumnName("idatendimentoportador");
            
            Property(x => x.Sequencial)
                .HasColumnName("sequencial");
            
            Property(x => x.DataTramite)
                .HasColumnName("datatramite")
                .IsRequired()
                .HasColumnType("datetime");
            
            Property(x => x.IdentificadorCartao)
                .HasColumnName("identificadorcartao")
                .IsOptional();
            
            Property(x => x.ProdutoCartao)
                .HasColumnName("produtocartao")
                .IsOptional();
            
            Property(x => x.Operacao)
                .HasColumnName("operacao")
                .IsRequired()
                .HasMaxLength(2000);

            Property(x => x.Tipo)
                .HasColumnName("tipo")
                .IsRequired()
                .HasIndex("IX_ATENDIMENTO_PORTADOR_TRAMITE_Tipo_motivo", false, 0);
            
            Property(x => x.IdMotivo)
                .HasColumnName("idmotivo")
                .IsOptional()
                .HasIndex("IX_ATENDIMENTO_PORTADOR_TRAMITE_Tipo_motivo", false, 1);
            
            Property(x => x.Permanente)
                .HasColumnName("permanente")
                .IsOptional();
            
            HasOptional(x => x.Motivo)
                .WithMany()
                .HasForeignKey(x => x.IdMotivo);
            
            HasRequired(x => x.AtendimentoPortador)
                .WithMany(y => y.AtendimentoPortadorTramite)
                .HasForeignKey(x => x.IdAtendimentoPortador);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ITipoNotificacaoService
    {
        ValidationResult Add(TipoNotificacao TipoNotificacao);
        ValidationResult Update(TipoNotificacao TipoNotificacao);
        ValidationResult Reativar(int idTipoNotificacao);
        ValidationResult Inativar(int idTipoNotificacao);
        TipoNotificacao Get(int id);
        List<TipoNotificacao> GetAll();

        object ConsultaGrid(int? idEmpresa,
            int? idFilial,
            string descricao,
            int Take,
            int Page,
            OrderFilters orderFilters,
            List<QueryFilters> Filters);

        IQueryable<TipoNotificacao> Consultar(List<QueryFilters> queryFilters, OrderFilters orderFilters);
        IQueryable<TipoNotificacao> ConsultarAtivos();
        IQueryable<TipoNotificacao> ConsultarPorEmpresaFilial(int idEmpresa, int? idFilial);
        IQueryable<TipoNotificacao> GetPorDataBase(DateTime? dataBase, int idEmpresa);
    }
}
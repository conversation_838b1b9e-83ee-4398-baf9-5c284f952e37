﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using AutoMapper.QueryableExtensions;
using Dapper;

namespace ATS.Data.Repository.EntityFramework
{
    public class ViagemRepository : Repository<Viagem>, IViagemRepository
    {
        public ViagemRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna os dados da Viagem
        /// </summary>
        /// <param name="id"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public Viagem GetChilds(int id, int idEmpresa)
        {
            return Find(x => x.IdViagem == id && x.IdEmpresa == idEmpresa)
                .Include(x => x.Empresa)
                .Include(x => x.ClienteOrigem)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ViagemChecks)
                .FirstOrDefault();
        }

        /// <summary>
        /// Método criado para Otimização de métodos na gestão de veículos
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="statusViagem"></param>
        /// <returns></returns>
        public List<FrotaUtilizadaModel> GetFrotaUtilizada(int idEmpresa, EStatusViagem statusViagem)
        {
            var retorno = (from vgm in All()
                            .Include(v => v.ViagemCarretas)
                           where vgm.StatusViagem == statusViagem && vgm.IdEmpresa == idEmpresa
                           select new FrotaUtilizadaModel()
                           {
                               IdViagem = vgm.IdViagem,
                               Placa = vgm.Placa,
                               ViagemCarretas = vgm.ViagemCarretas.ToList()
                           }).ToList();

            return retorno;
        }


        /// <summary>
        /// Método de recuperar o registro
        /// </summary>
        /// <param name="id"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public Viagem Get(int id, int idEmpresa)
        {
            return (from vgm in All()
                        .Include(v => v.ViagemChecks)
                        .Include(v => v.ViagemCargas)
                    where vgm.IdViagem == id && vgm.IdEmpresa == idEmpresa
                    select vgm).FirstOrDefault();
        }

        /// <summary>
        /// Retorna as viagens segundo os parâmetros especificados
        /// </summary>
        /// <param name="nCPFMotorista">CPF do motorista vinculado a viagem</param>
        /// <param name="statusCheckViagem">Lista de situações dos checks de viagem que deve ser filtrada</param>
        /// <param name="statusViagem">Lista de situações de viagem que deve ser filtrada</param>
        /// <returns></returns>
        public IQueryable<Viagem> Consultar(string nCPFMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> idsViagem, bool? permiteConsultarSemFiltro,List<string> numerosControle)
        {
            if (!permiteConsultarSemFiltro.HasValue || !permiteConsultarSemFiltro.Value)
            {
                if (string.IsNullOrEmpty(nCPFMotorista) && !dataLancamentoInicial.HasValue && !dataLancamentoFinal.HasValue && (idsViagem == null || !idsViagem.Any()) && (numerosControle == null || !numerosControle.Any()))
                    throw new Exception("Não é permitido consultar viagem sem nenhum filtro."); 
            }
            
            IQueryable<Viagem> retViagens =
                from viagem in All(true)
                    .Include(v => v.Empresa)
                    .Include(v => v.ClienteOrigem)
                    .Include(v => v.ClienteOrigem.Pais)
                    .Include(v => v.ClienteOrigem.Estado)
                    .Include(v => v.ClienteOrigem.Cidade)
                    .Include(v => v.ClienteDestino)
                    .Include(v => v.ClienteDestino.Pais)
                    .Include(v => v.ClienteDestino.Estado)
                    .Include(v => v.ClienteDestino.Cidade)
                    .Include(v => v.ClienteTomador)
                    .Include(v => v.ClienteTomador.Pais)
                    .Include(v => v.ClienteTomador.Estado)
                    .Include(v => v.ClienteTomador.Cidade)
                    .Include(v => v.Proprietario)
                    .Include(v => v.ViagemChecks)
                    .Include(v => v.ViagemCargas)
                    .Include(v => v.ViagemCarretas)
                    .Include(x => x.ViagemEstabelecimentos)
                    .Include(x => x.ViagemDocumentosFiscais)
                    .Include(x => x.ViagemRegras)
                    .Include(x => x.ViagemEventos)
                    .Include(x => x.ViagemPagamentoConta)
                    .Include(x => x.Filial)
                    .Include(x => x.ViagemEventos.Select(y => y.ViagemDocumentos))
                    .Include(x => x.ViagemEventos.Select(y => y.ViagemValoresAdicionais))
                    .Include(x => x.ViagemEventos.Select(y => y.ProtocoloEventos))
                select viagem;

            if (!string.IsNullOrWhiteSpace(nCPFMotorista))
                retViagens = retViagens.Where(x => x.CPFMotorista == nCPFMotorista);

            if (!string.IsNullOrWhiteSpace(tokenViagem))
                retViagens = retViagens.Where(x => x.ViagemEventos.Any(y => y.Token == tokenViagem));

            if (!string.IsNullOrEmpty(token) && !string.IsNullOrWhiteSpace(cnpjAplicacao))
            {
                var empresaAutenticacao = (from autenticacao in ((AtsContext)Context).AutenticacaoEmpresa
                               where autenticacao.Token == token.Trim() && autenticacao.CNPJAplicacao == cnpjAplicacao.Trim()
                               select autenticacao)
                    .Include(x => x.Empresa)
                    .FirstOrDefault();

                if (empresaAutenticacao != null)
                    retViagens = retViagens.Where(x => x.IdEmpresa == empresaAutenticacao.IdEmpresa);
            }

            if (dataLancamentoInicial.HasValue)
                retViagens = retViagens.Where(x => x.DataLancamento > dataLancamentoInicial);
            if (dataLancamentoFinal.HasValue)
                retViagens = retViagens.Where(x => x.DataLancamento < dataLancamentoFinal);


            if (statusViagem != null && statusViagem.Count > 0)
                retViagens = retViagens.Where(x => statusViagem.Contains(x.StatusViagem));

            if (statusCheckViagem != null && statusCheckViagem.Count > 0)
                retViagens = retViagens.Where(v => v.ViagemChecks.Select(c => c.Status).Any(c => statusCheckViagem.Contains(c)));

            if (idsViagem != null && idsViagem.Count > 0)
                retViagens = retViagens.Where(x => idsViagem.Contains(x.IdViagem));
            
            if (numerosControle != null && numerosControle.Count > 0)
                retViagens = retViagens.Where(x => numerosControle.Contains(x.NumeroControle));

            return retViagens;
        }


        public IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario, DateTime? dataCadastroBase = null)
        {
            if (dataCadastroBase == null)
                dataCadastroBase = DateTime.Today.AddYears(-1);

            var sql = @"select coalesce(m.nome, v.nomemotorista) as Nome, v.cpfmotorista as Cpf, max(v.datalancamento) as UltimaDataLancamento
                        from viagem v
	                         left join proprietario p on p.idproprietario = v.idproprietario and p.idempresa = v.idempresa and p.ativo = 1
                             left join motorista m on m.cpf = v.cpfmotorista and m.idempresa = v.idempresa and m.ativo = 1
                        where (v.cpfcnpjproprietario = @CpfCnpjProprietario or p.cnpjcpf = @CpfCnpjProprietario)
                        and   v.statusviagem <> 3
                        and   v.datalancamento >= @DataBase
                        and   coalesce(m.nome, v.nomemotorista) <> ''
                        group by coalesce(m.nome, v.nomemotorista), v.cpfmotorista";

            var context = (AtsContext) Context;
            var motoristas = context.Database.Connection.Query<ViagemMotoristasModel>(sql,
                new
                {
                    CpfCnpjProprietario = cnpjProprietario,
                    DataBase = dataCadastroBase.Value
                });

            // Por consta da estrutura de armazenamento de cpf e nome do motorista na tabela de viagem,
            // é necessário este tratamento para retorar apenas um linha referente ao CPF, para caso de haver nomes integrados no periodo com valores diferentes,
            // mantem o da ultima viagem integrada
            var motsByCpf = motoristas.GroupBy(m => m.Cpf);

            var result = new List<ViagemMotoristasModel>();
            foreach (var motCpf in motsByCpf)
            {
                var value = motCpf.OrderByDescending(m => m.UltimaDataLancamento).FirstOrDefault();
                result.Add(value);
            }

            return result;
        }

        public DadosCiotV2Dto GetDadosCiotV2(int idViagem)
        {
            return Where(viagem => viagem.IdViagem == idViagem)
                .ProjectTo<DadosCiotV2Dto>()
                .FirstOrDefault();
        }
        
        public DadosCiotV3Dto GetDadosCiotV3(int idViagem)
        {
            return Where(viagem => viagem.IdViagem == idViagem)
                .ProjectTo<DadosCiotV3Dto>()
                .FirstOrDefault();
        }
        
        public int? GetIbgeOrigemCiot(int idViagem)
        {
            return Where(viagem => viagem.IdViagem == idViagem)
                .Select(viagem => viagem.ClienteOrigem.Cidade.IBGE)
                .FirstOrDefault();
        }
        
        public int? GetIbgeDestinoCiot(int idViagem)
        {
            return Where(viagem => viagem.IdViagem == idViagem)
                .Select(viagem => viagem.ClienteDestino.Cidade.IBGE)
                .FirstOrDefault();
        }
        
        /// <summary>
        /// Retorna Viagem por IdViagem
        /// </summary>
        /// <param name="id">Id de Viagem</param>
        /// <param name="comIncludes"></param>
        /// <returns>Objeto de Viagem</returns>
        public Viagem Get(int id, bool comIncludes = true)
        {
            var query = Find(x => x.IdViagem == id);
                
            // foram retirados os includes da empresa, cliente origem e cliente destino e carregado as propriedades separadamente
            if(comIncludes)
                query = query.Include(x => x.ViagemCarretas)
                    .Include(x => x.ViagemRegras)
                    .Include(x => x.ViagemEstabelecimentos)
                    .Include(x => x.ViagemDocumentosFiscais)
                    .Include(x => x.ViagemEventos)
                    .Include(x => x.ViagemEventos.Select(y => y.ViagemDocumentos))
                    .Include(x => x.ViagemEventos.Select(y => y.ViagemValoresAdicionais))
                    .Include(x => x.DeclaracaoCiot)
                    .Include(x => x.ViagemPagamentoConta);
                    
            return query.FirstOrDefault();
        }
        
        public IQueryable<Viagem> GetQuery()
        {
            return GetAll();
        }
        
        public IQueryable<Viagem> GetQuery(int idViagem)
        {
            return Find(v => v.IdViagem == idViagem);
        }
    }
}
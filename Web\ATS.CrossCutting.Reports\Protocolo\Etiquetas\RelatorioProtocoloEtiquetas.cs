﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.Protocolo.Etiquetas
{
    public class RelatorioProtocoloEtiquetas
    {
        public byte[] GetReport(List<RelatorioProtocoloEtiquetasDataType> listaDados, string logo)
        {
            var path = ReportUtils.CreateLogo(logo);

            // A classe ReportParameter espera por 3 argumentos: o nome do parâmetro (string), o valor do parâmetro (string), e se esse parâmetro vai ser visível (true), por isso passamos um Tupple com esses três tipos, para se tornar genérico a passagem dos parâmetros
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + path, true);
            var bytes = new Base.Reports().GetReport(listaDados, parametros, true, "DtsProtocoloEtiquetas",
                "ATS.CrossCutting.Reports.Protocolo.Etiquetas.RelatorioProtocoloEtiquetas.rdlc",
                "pdf");

            return bytes;
        }
    }
}

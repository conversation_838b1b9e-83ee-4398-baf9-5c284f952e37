﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using ATS.MongoDB.Context;
using ATS.MongoDB.Context.Entities;
using ATS.Domain.Interface.DataMediaServer;
using MongoDB.Bson;

namespace ATS.Data.Repository.EntityFramework
{
    public class DataMediaServerRepository : IDataMediaServerRepository
    {
        public ObjectId Add(int type, string base64Data, string fileName, string mimeType = null)
        {
            var lConn = new MongoContext();
            return lConn.Add(type, base64Data, fileName, mimeType);
        }

        public Media GetMedia(string _id)
        {
            var lConn = new MongoContext();
            return lConn.GetMedia(_id);
        }

        public void DeleteByToken(string _id)
        {
            var lConn = new MongoContext();
            lConn.DeleteByToken(_id);
        }

        public List<Media> GetListMedia(List<string> listId)
        {
            var lConn = new MongoContext();
            return lConn.GetListMedia(listId.Select(ObjectId.Parse));        
        }
    }
}

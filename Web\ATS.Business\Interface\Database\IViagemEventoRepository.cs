﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models.ViagemModels;

namespace ATS.Domain.Interface.Database
{
    public interface IViagemEventoRepository : IRepository<ViagemEvento>
    {
        /// <summary>
        /// Obter IdViagemEvento de todos os registros pendentes de pagamento.
        /// </summary>
        /// <param name="dataAgendamentoInicio">Filtar agendamentos maiores que esta data</param>
        /// <param name="dataAgendamentoFim"></param>
        /// <returns></returns>
        IEnumerable<ViagemEventoIdentifierModel> GetIdsParaProcessarAgendamentoDePagamento(DateTime dataAgendamentoInicio, DateTime dataAgendamentoFim);
        IQueryable<ViagemEvento> GetEventosQuery(int idViagem, bool @readonly = false);
        IEnumerable<ViagemEvento> GetViagensEventos(int viagemid);
        bool AnyParcelaNaoBaixada(List<int> idsViagemEvento);
    }
}
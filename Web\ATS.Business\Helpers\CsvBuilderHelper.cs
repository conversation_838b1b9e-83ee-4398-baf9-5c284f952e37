using System;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.IO;
using System.Linq;
using System.Text;

namespace ATS.Domain.Helpers
{
    public class CsvBuilderHelper
    {
        /// <summary>
        /// To keep the ordered list of column names
        /// </summary>
        private readonly List<string> _fields = new List<string>();

        /// <summary>
        /// The list of rows
        /// </summary>
        private readonly List<Dictionary<string, object>> _rows = new List<Dictionary<string, object>>();

        /// <summary>
        /// The current row
        /// </summary>
        private Dictionary<string, object> CurrentRow => _rows[_rows.Count - 1];

        /// <summary>
        /// The string used to separate columns in the output
        /// </summary>
        private readonly string _columnSeparator;

        private readonly bool _showHeader;
        
        private string _topHeader;
        
        /// <summary>
        /// Whether to include the preamble that declares which column separator is used in the output
        /// </summary>
        private readonly bool _includeColumnSeparatorDefinitionPreamble;

        /// The string used to separate columns in the output.
        /// By default this is a comma so that the generated output is a CSV file.
        /// <param name="columnSeparator">Column Separator</param>
        /// <param name="includeColumnSeparatorDefinitionPreamble">Print type column separator</param>
        /// <param name="showHeader">Print header</param>
        /// Whether to include the preamble that declares which column separator is used in the output.
        /// By default this is <c>true</c> so that Excel can open the generated CSV
        /// without asking the user to specify the delimiter used in the file.
        public CsvBuilderHelper(string columnSeparator = ",", bool includeColumnSeparatorDefinitionPreamble = true, bool showHeader = true)
        {
            _columnSeparator = columnSeparator;
            _includeColumnSeparatorDefinitionPreamble = includeColumnSeparatorDefinitionPreamble;
            _showHeader = showHeader;
        }

        /// <summary>
        /// Set a value on this column
        /// </summary>
        public object this[string field]
        {
            set
            {
                // Keep track of the field names, because the dictionary loses the ordering
                if (!_fields.Contains(field)) _fields.Add(field);
                CurrentRow[field] = value;
            }
        }

        /// <summary>
        /// Call this before setting any fields on a row
        /// </summary>
        public void AddRow()
        {
            _rows.Add(new Dictionary<string, object>());
        }
        
        public void SetTopHeader(string topHeader)
        {
            _topHeader = topHeader;
        }

        /// <summary>
        /// Add a list of typed objects, maps object properties to CsvFields
        /// </summary>
        public void AddRows<T>(IEnumerable<T> list)
        {
            if (list.Any())
            {
                foreach (var obj in list)
                {
                    AddRow();
                    var values = obj.GetType().GetProperties();
                    foreach (var value in values)
                    {
                        this[value.Name] = value.GetValue(obj, null);
                    }
                }
            }
        }

        /// <summary>
        /// Converts a value to how it should output in a csv file
        /// If it has a comma, it needs surrounding with double quotes
        /// Eg Sydney, Australia -> "Sydney, Australia"
        /// Also if it contains any double quotes ("), then they need to be replaced with quad quotes[sic] ("")
        /// Eg "Dangerous Dan" McGrew -> """Dangerous Dan"" McGrew"
        /// </summary>
        /// <param name="value"></param>
        /// <param name="columnSeparator">
        /// The string used to separate columns in the output.
        /// By default this is a comma so that the generated output is a CSV document.
        /// </param>
        public static string MakeValueCsvFriendly(object value, string columnSeparator = ",")
        {
            if (value == null) return string.Empty;
            if (value is INullable && ((INullable) value).IsNull) return string.Empty;
            if (value is DateTime)
            {
                if (((DateTime) value).TimeOfDay.TotalSeconds == 0)
                    return ((DateTime) value).ToString("yyyy-MM-dd");
                return ((DateTime) value).ToString("yyyy-MM-dd HH:mm:ss");
            }

            var output = value.ToString().Trim();
            if (output.Contains(columnSeparator) || output.Contains("\"") || output.Contains("\n") ||
                output.Contains("\r"))
                output = '"' + output.Replace("\"", "\"\"") + '"';

            if (output.Length > 30000) //cropping value for stupid Excel
            {
                if (output.EndsWith("\""))
                {
                    output = output.Substring(0, 30000);
                    if (output.EndsWith("\"") && !output.EndsWith("\"\"")
                    ) //rare situation when cropped line ends with a '"'
                        output += "\""; //add another '"' to escape it
                    output += "\"";
                }
                else
                    output = output.Substring(0, 30000);
            }

            return output;
        }

        /// <summary>
        /// Outputs all rows as a CSV, returning one string at a time
        /// </summary>
        private IEnumerable<string> ExportToLines()
        {
            if (_includeColumnSeparatorDefinitionPreamble) yield return "sep=" + _columnSeparator;

            // The top header
            if (_topHeader != null)
                yield return _topHeader;
            
            // The header
            if (_showHeader)
                yield return string.Join(_columnSeparator, _fields.Select(f => MakeValueCsvFriendly(f, _columnSeparator)));

            // The rows
            foreach (var row in _rows)
            {
                foreach (string k in _fields.Where(f => !row.ContainsKey(f)))
                {
                    row[k] = null;
                }

                yield return string.Join(_columnSeparator,
                    _fields.Select(field => MakeValueCsvFriendly(row[field], _columnSeparator)));
            }
        }

        /// <summary>
        /// Output all rows as a CSV returning a string
        /// </summary>
        public string Export()
        {
            var sb = new StringBuilder();

            foreach (string line in ExportToLines())
            {
                sb.AppendLine(line);
            }

            return sb.ToString();
        }

        /// <summary>
        /// Exports to a file
        /// </summary>
        public void ExportToFile(string path)
        {
            File.WriteAllLines(path, ExportToLines(), Encoding.UTF8);
        }

        /// <summary>
        /// Exports as raw UTF8 bytes
        /// </summary>
        public byte[] ExportToBytes()
        {
            var data = Encoding.UTF8.GetBytes(Export());
            return Encoding.UTF8.GetPreamble().Concat(data).ToArray();
        }
    }
}
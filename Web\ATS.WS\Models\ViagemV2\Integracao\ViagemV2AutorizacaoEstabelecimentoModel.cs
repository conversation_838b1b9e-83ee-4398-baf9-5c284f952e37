using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.Viagem;
using ATS.Domain.Validation;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2AutorizacaoEstabelecimentoModel
    {
        public string Cnpj
        {
            get { return _cnpj; }
            set {_cnpj = value.OnlyNumbers();}
        }
        public ETipoEventoViagem? TipoEvento { get; set; }
        
        [JsonIgnore]
        public int? IdEstabelecimento { get; set; }
        
        // ReSharper disable once InconsistentNaming
        private string _cnpj { get; set; }

        public ValidationResult<EValidationViagemEstabelecimento> ValidarEntrada(IViagemApp viagemApp, string cnpjEmpresa)
        {
            var validation = new ValidationResult<EValidationViagemEstabelecimento>();
            
            if (string.IsNullOrEmpty(Cnpj))
                return validation.Add(EValidationViagemEstabelecimento.CnpjInvalido, EFaultType.Error, new object[]{"null"});

            var validacaoCnpj = AssertionConcern.AssertArgumentIsValidCNPJ(Cnpj, string.Empty);
            if (validacaoCnpj != null)
                return validation.Add(EValidationViagemEstabelecimento.CnpjInvalido, EFaultType.Error, new object[]{Cnpj});

            if (!TipoEvento.HasValue)
                validation.Add(EValidationViagemEstabelecimento.TipoEventoObrigatorio, EFaultType.Error);
            
            IdEstabelecimento = viagemApp.GetIdViagemEstabelecimentoPorCnpj(Cnpj, cnpjEmpresa);
            if (!IdEstabelecimento.HasValue)
                return validation.Add(EValidationViagemEstabelecimento.CnpjNaoEncontrado, EFaultType.Error, new object[]{Cnpj});
            
            if(!viagemApp.GetViagemEstabelecimentoAtivo(IdEstabelecimento.Value))
                validation.Add(EValidationViagemEstabelecimento.EstabelecimentoInativo, EFaultType.Error, new object[]{Cnpj});
            
            if(!viagemApp.GetViagemEstabelecimentoCredenciado(IdEstabelecimento.Value))
                validation.Add(EValidationViagemEstabelecimento.CredenciamentoInvalido, EFaultType.Error, new object[]{Cnpj});

            return validation;
        }
    }
}
﻿using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Pedagio.GridPassagemWebhookTAG
{
    public class GridPassagemWebhookTagDataType
    {
        public List<RelatorioGridPassagemWebhookTagItemDataType> items { get; set; }
    }

    public class RelatorioGridPassagemWebhookTagItemDataType
    {
        public long Id { get; set; }
        public string Lancamento { get; set; }
        public string DataOcorrencia { get; set; }
        public string DataProcessamento { get; set; }
        public string Tipo { get; set; }
        public string Placa { get; set; }
        public string Categoria { get; set; }
        public string Valor { get; set; }
        public string CnpjEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public string CnpjEmissorVp  { get; set; } 
        public string NomeEmissorVp  { get; set; }
    }
}
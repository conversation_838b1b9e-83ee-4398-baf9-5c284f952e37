﻿using System;
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Models.Common
{
    public class CustoPedagioRotaResponseModel
    {
        public IList<Praca> Pracas { get; set; }
        public decimal CustoTotal { get; set; }
        public decimal CustoTotalTag { get; set; }

//        public bool Sucesso { get; set; }
        public ConsultaRotaResponseDtoStatus Status { get; set; }
        public string Mensagem { get; set; }
        public Guid IdentificadorHistorico { get; set; }
        public IList<ConsultaLocalizacoesResponse> Localizacoes { get; set; }
        public decimal KmTotal { get; set; }
        public string TempoPrevisto { get; set; }
        public int? CodPolyline { get; set; }
    }

    public class TesteGoogle
    {
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
    }
}

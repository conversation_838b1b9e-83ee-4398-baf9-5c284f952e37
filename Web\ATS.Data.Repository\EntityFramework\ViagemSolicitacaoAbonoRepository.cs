﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class ViagemSolicitacaoAbonoRepository : Repository<ViagemSolicitacaoAbono>, IViagemSolicitacaoAbonoRepository
    {
        public ViagemSolicitacaoAbonoRepository(AtsContext context) : base(context)
        {
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.Migrations;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class ModuloSeeder
    {
        public void Execute(AtsContext context)
        {
            context.Modulo.AddOrUpdate(x => new { x.Des<PERSON>o, x.<PERSON> }, new[]
            {
                new Modulo { Descricao = "Configuração", Sequencia = 1, ClassIcon = "fa fa-cog" },
                new Modulo { Descricao = "Cadastros", Sequencia = 2, ClassIcon = "fa fa-pencil-square-o" },
                new Modulo { Descricao = "Movimentações", Sequencia = 3 },
                new Modulo { Descricao = "Relatórios", Sequencia = 4 },
                // Módulos principais
                new Modulo { Descricao = "Oferta de Cargas", Sequencia = 6, ClassIcon = "fa fa-truck", Codigo = 3 },
                new Modulo { Descricao = "Credenciamento", Sequencia = 9, ClassIcon = "fa fa-shopping-bag", Codigo = 6 },
                new Modulo { Descricao = "Pagamento de frete", Sequencia = 12, ClassIcon = "fa fa-shopping-bag", Codigo = 7 },
            });

            context.SaveChanges();
        }
    }
}
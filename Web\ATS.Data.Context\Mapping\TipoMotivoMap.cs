﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoMotivoMap : EntityTypeConfiguration<TipoMotivo>
    {
        public TipoMotivoMap()
        {
            ToTable("MOTIVO_TIPO");

            HasKey(x => x.IdTipoMotivo);

            HasRequired(x => x.Motivo)
                .WithMany(x => x.TipoMotivo)
                .HasForeignKey(x => x.IdMotivo);

            Property(o => o.Tipo)
                .IsRequired();
        }
    }
}
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services.ViagemServices;

namespace ATS.WS.Services.ViagemV2Services
{
    public class ValoresViagemV2
    {
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ValoresViagem _valoresViagem;

        public ValoresViagemV2(IClienteApp clienteApp, IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemApp viagemApp,
            ICadastrosApp cadastrosApp, IVersaoAnttLazyLoadService versaoAntt, IEmpresaRepository empresaRepository, ValoresViagem valoresViagem)
        {
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _cadastrosApp = cadastrosApp;
            _versaoAntt = versaoAntt;
            _empresaRepository = empresaRepository;
            _valoresViagem = valoresViagem;
        }

        public Retorno<object> AlterarValoresViagem(AlterarValoresViagemRequestModel request, bool isApi = false)
        {
            var validacaoEntrada = request.Valida();
            
            if (!validacaoEntrada.IsValid)
                return new Retorno<object>(validacaoEntrada, null,"Falha ao alterar valores.");

            var resultadAlteracao =  _valoresViagem.AlterarValoresViagem(request, isApi);
            
            if (!resultadAlteracao.Sucesso)
                return new Retorno<object>(new ValidationResult().Add(resultadAlteracao.Mensagem, EFaultType.Error), null, "Falha ao alterar valores.");
            
            return new Retorno<object>(true, "Valores alterados com sucesso.", null);
        }
    }
}
﻿using System;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class BannerRepository : Repository<Banner>, IBannerRepository
    {
        public BannerRepository(AtsContext context) : base(context)
        {
        }

        public IQueryable<Banner> GetAtivo()
        {
            return Where(c => c.Ativo);
        }

        public IQueryable<Banner> GetById(int id)
        {
            return Where(c => c.Id == id);
        }
    }
}
﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IUsuarioPermissaoFinanceiroApp : IAppBase<UsuarioPermissaoFinanceiro>
    {
        ValidationResult Integrar(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo, bool bloqueioFinanceiro);
        UsuarioPermissaoFinanceiro GetParametroPermissaoFinanceiro(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo);
        bool <PERSON>ssuiPermis<PERSON>o(int idUsuario, EBloqueioFinanceiroTipo bloqueioFinanceiroTipo);
    }
}
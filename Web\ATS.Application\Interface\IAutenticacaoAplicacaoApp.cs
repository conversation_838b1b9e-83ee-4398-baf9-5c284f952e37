﻿using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;

namespace ATS.Application.Interface
{
    public interface IAutenticacaoAplicacaoApp : IAppBase<AutenticacaoAplicacao>
    {
        bool AcessoConcedido(string cnpjAplicacao, string token);
        IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCnpjAplicacao(string cnpjAplicacao, string token);
        AutenticacaoAplicacao Get(string cnpjAplicacao);
        IQueryable<AutenticacaoAplicacao> GetPorIdEmpresa(int idEmpresa);
    }
}

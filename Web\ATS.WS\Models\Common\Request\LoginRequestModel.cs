﻿using System;
using ATS.WS.Models.Common.Request.Base;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Common.Request
{
    public class LoginRequestModel : RequestBase
    {
        /// <summary>
        /// CPF/CNPJ do usuário que irá realizar o login
        /// </summary>
        public string CPFCNPJ { get; set; }

        /// <summary>
        /// Usuário que irá realizar o login
        /// </summary>
        public string Usuario { get; set; }

        /// <summary>
        /// Senha do usuário
        /// </summary>
        public string Senha { get; set; }

        /// <summary>
        /// Identificação do Push
        /// </summary>
        public string IdPush { get; set; }

        public string IMEI { get; set; }

        /// <summary>
        /// Versão do aplicativo que o usuário está usando.
        /// </summary>
        public int? VersaoAplicativo { get; set; }

        public string TokenFirebase { get; set; }

        public bool FromPanel { get; set; }
        
        /// <summary>
        /// Utilizado para definir o KeyPush 
        /// </summary>
        public int? ProjetoFirebase {
            get { return _projetoFirebase ?? Administradora; }
            set { _projetoFirebase = value; }
        }
        private int? _projetoFirebase { get; set; }
        
        /// <summary>
        /// propriedade legado para ProjetoFireBase, será removido futuramente 
        /// </summary>
        public int? Administradora { get; set; }
        public ProprietarioModel Proprietario { get; set; } = null;

        /// <summary>
        /// Data enviada pelo Mobile, para retorno das mensagens
        /// </summary>
        public DateTime? DataDasMensagens { get; set; }

        public ESistemaOperacional? SistemaOperacional { get; set; }
        public bool RetornarFoto { get; set; } = true;
    }

}
﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;
using TagExtrattaClient;

namespace ATS.CrossCutting.Reports.Pedagio.FaturaTAG
{
    public class RelatorioFaturaTag
    {
        public byte[] GetReport(RelatorioFaturaTagDataType dadosRelatorio, FornecedorTagEnum fornecedorTag)
        {
            var localReport = new LocalReport();
            try
            {

                var tipoRelatorio = ConstantesUtils.FormatoPdf;

                var parametros = new List<ReportParameter>
                {
                    // Adicionando parâmetros de EmpresaFaturamentoTagDataTypeResponse
                    new ("Cnpj", dadosRelatorio.Empresa.Cnpj),
                    new ("RazaoSocial", dadosRelatorio.Empresa.RazaoSocial),
                    new ("Email", dadosRelatorio.Empresa.Email),
                    new ("Telefone", dadosRelatorio.Empresa.Telefone),
                    new ("Endereco", dadosRelatorio.Empresa.Endereco),
                    new ("Endereco", dadosRelatorio.Empresa.Endereco),
                    // Adicionando parâmetros de DadosGeraisTagDataTypeResponse
                    new ("Vigencia", dadosRelatorio.DadosGerais.Vigencia),
                    new ("Emissao", dadosRelatorio.DadosGerais.Emissao),
                    new ("Vencimento", dadosRelatorio.DadosGerais.Vencimento),
                    new ("NumeroFatura", dadosRelatorio.DadosGerais.NumeroFatura),  
                };

                if (fornecedorTag == FornecedorTagEnum._5)
                {
                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = dadosRelatorio.MensalidadeTag,
                        Name = "FaturaTagMensalidadeDts"
                    });

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = dadosRelatorio.PassagensPedagio,
                        Name = "FaturaTagPedagioDts"
                    });

                    var passagens = dadosRelatorio.PassagensVPFornecedor
                       .SelectMany(f => f.PassagensValePedagio)
                       .ToList();

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = passagens,
                        Name = "FaturaTagValePedagioDts"
                    });

                    parametros.AddRange(new List<ReportParameter>
                    {
                        // Adicionando parâmetros de TributaveisTagDataTypeResponse
                        new ("MensalidadeQtd", dadosRelatorio.Tributaveis.MensalidadeQtd.ToString()),
                        new ("MensalidadeValorTotal", dadosRelatorio.Tributaveis.MensalidadeValorTotal),
                        new ("ValorTotalTributaveis", dadosRelatorio.Tributaveis.ValorTotal),
                        // Adicionando parâmetros de NaoTributaveisTagDataTypeResponse
                        new ("PassagensValePedagioQtd", dadosRelatorio.NaoTributaveis.PassagensValePedagioQtd.ToString()),
                        new ("PassagensValePedagioValorTotal", dadosRelatorio.NaoTributaveis.PassagensValePedagioValorTotal),
                        new ("PassagensPedagioQtd", dadosRelatorio.NaoTributaveis.PassagensPedagioQtd.ToString()),
                        new ("PassagensPedagioValorTotal", dadosRelatorio.NaoTributaveis.PassagensPedagioValorTotal),
                        new ("EstornoValePedagioQtd", dadosRelatorio.NaoTributaveis.EstornoValePedagioQtd.ToString()),
                        new ("EstornoValePedagioTotal", dadosRelatorio.NaoTributaveis.EstornoValePedagioTotal),
                        new ("EstornoPedagioQtd", dadosRelatorio.NaoTributaveis.EstornoPedagioQtd.ToString()),
                        new ("EstornoPedagioTotal", dadosRelatorio.NaoTributaveis.EstornoPedagioTotal),
                        new ("ValorTotalNaoTributaveis", dadosRelatorio.NaoTributaveis.ValorTotal),
                        // Adicionando parâmetros de TotalizadorTagDataTypeResponse
                        new ("EstornoPago", dadosRelatorio.Totalizador.EstornoPago),
                        new ("EstornoNaoPago", dadosRelatorio.Totalizador.EstornoNaoPago),
                        new ("TotalPago", dadosRelatorio.Totalizador.TotalPago),
                        new ("TotalNaoPago", dadosRelatorio.Totalizador.TotalNaoPago),
                        new ("ValorFatura", dadosRelatorio.Totalizador.ValorFatura)
                    });

                }
                else
                {
                    var passagensViaFacil = dadosRelatorio.PassagensVPFornecedor
                      .Where(f => f.Fornecedor == FornecedorTagEnum._2)
                      .SelectMany(f => f.PassagensValePedagio)
                      .ToList();

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = passagensViaFacil,
                        Name = "FaturaTagValePedagioViaFacilDts"
                    });

                    var passagensMoveMais = dadosRelatorio.PassagensVPFornecedor
                        .Where(f => f.Fornecedor == FornecedorTagEnum._3)
                        .SelectMany(f => f.PassagensValePedagio)
                        .ToList();

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = passagensMoveMais,
                        Name = "FaturaTagValePedagioMoveMaisDts"
                    });

                    var passagensVeloe = dadosRelatorio.PassagensVPFornecedor
                        .Where(f => f.Fornecedor == FornecedorTagEnum._4)
                        .SelectMany(f => f.PassagensValePedagio)
                        .ToList();

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = passagensVeloe,
                        Name = "FaturaTagValePedagioVeloeDts"
                    });

                    var passagensConectCar = dadosRelatorio.PassagensVPFornecedor
                        .Where(f => f.Fornecedor == FornecedorTagEnum._6)
                        .SelectMany(f => f.PassagensValePedagio)
                        .ToList();

                    localReport.DataSources.Add(new ReportDataSource
                    {
                        Value = passagensConectCar,
                        Name = "FaturaTagValePedagioConectCarDts"
                    });
                }

                localReport.EnableExternalImages = true;

                var fileName = fornecedorTag == FornecedorTagEnum._5 ? "FaturaTAG" : "FaturaTAGFornecedores";
                localReport.ReportEmbeddedResource =  $"ATS.CrossCutting.Reports.Pedagio.FaturaTAG.{fileName}.rdlc" ;

                localReport.SetParameters(parametros);

                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IProtocoloApp
    {
        object ConsultarProtocoloEvento(int idProtocolo);
        List<ProtocoloEvento> GetProtocolosEventos(List<int> ids);
        object GetAnteciopacoes(int? idEmpresa, int ano, int mes);
        object ConsultarRecebimentoProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters);
        byte[] GerarRelatorioGridRecebimentoProtocolo(OrderFilters order, List<QueryFilters> filters, string tipoArquivo, string logo);
        object ConsultarGridProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters);
        PagamentoFreteModel ConsultarPorToken(string token, Usuario usuarioLogado, int? idProtocolo, List<int> idsEstabelecimentosUsuario = null, bool analisado = false);
        object ConsultarEventosOriginal(int idProtocolo, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult ResolverProtocolo(int idProtocolo, Usuario usuarioLogado);
        ValidationResult Resolver(int idProtocoloEvento);
        KeyValuePair<ValidationResult, int?> Add(Protocolo protocolo, int? idUsuario = null);
        ValidationResult AddOcorrencia(int idProtocoloEvento, int idMotivo, string descricao, Usuario usuarioLogado);
        ValidationResult Receber(List<int> ids);
        object ConsultarDocumentoProtocolo(int idEmpresa, int? idProtocolo);
        List<string> ConsultarPagamentosPorProtocolo(int idProtocolo);
        ValidationResult Update(Protocolo protocolo);
        Protocolo Get(int idProtocolo);
        IEnumerable<Protocolo> GetAll();
        List<object> ConsultarDocumentosProtocolo(int idEmpresa, int? idProtocolo);
        object ConsultarAnexos(int idProtocolo);
        object ConsultarGrid(int? idEstabelecimento, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        byte[] GerarRelatorioGrid(int? idEstabelecimento, int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string tipoArquivo, string logo);
        void Processar(int idProtocolo);
        void AgendarPrevisaoPagamento(int idProtocolo, DateTime dataPrev);

        object ConsultarTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase, int? idEstabelecimento, int? idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, List<KeyValuePair<int, int>> associacoes, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters);

        byte[] GerarRelatorioTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo);

        List<Protocolo> ConsultarRelatorioProtocolos(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);

        object ConsultarTriagemProtocoloAssociacao(int? idEmpresa, List<int> idsEstabelecimentosBase, int? idEstabelecimento, IList<int?> idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, List<KeyValuePair<int, int>> associacoes, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters);

        List<Protocolo> GetProtocolos(List<int> idsProtocolo);
        object ConsultarDescontoPorId(int idProtocoloEvento);

        object ConsultarTriagemAntecipacaoProtocolo(int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters, Usuario usuarioLogado);

        object ConsultarTriagemAntecipacaoOcorrenciasProtocolo(int? idProtocolo, int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        List<RelatorioEventosVinculados> ConsultarEventosOriginalReport(int idProtocolo, int take, int page, OrderFilters order, List<QueryFilters> filters);

        List<RelatorioProtocoloOcorrencia> ConsultarTriagemAntecipacaoOcorrenciaProtocoloRelatorio(int? idProtocolo, int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        void EmAnalise(int idProtocolo);
        object ConsultarPagamentos(int idProtocolo, int? take, int? page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult Aprovar(int idProtocolo, DateTime? dataPrevisaoPagamento, int? idUsuarioLogado );
        ValidationResult ReverterRejeicao(int idProtocolo);
        ValidationResult Rejeitar(int idProtocolo, int idMotivo, string detalhamento, Usuario usuarioLogado);
        ValidationResult RejeitarPagamento(int idProtocoloEvento, int? idMotivo, string detalhamento, Usuario usuarioLogado);
        decimal CalcularTaxaAntecipacao(int idProtocolo, DateTime dataAntecipacao);

        ValidationResult EnviarSolicitacaoAntecipacao(int idProtocolo, decimal valorAntecipacao,
            DateTime dataAntecipacao);

        ValidationResult AprovarAntecipacao(int idProtocoloAntecipacao);
        ValidationResult RejeitarAntecipacao(int idProtocoloAntecipacao, int idMotivo, string detalhamento);
        Protocolo GetComInclude(int idProtocolo);
        Protocolo GetComEvento(int idProtocolo);
        ValidationResult ReenviarAntecipacao(int idProtocoloAntecipacao, DateTime dataPagamentoAntecipado);
        ValidationResult RealizarPagamento(int idProtocolo);
        ValidationResult AlterarPesoChegada(int idProtocolo, decimal? pesoChegada, int? numeroSacas, bool hasAbono = false);
        ValidationResult RealizarDesconto(int idProtocoloEvento, decimal? valorDesconto, decimal? pesoChegada, int? idMotivoDesconto, string observacaoDesconto, string tokenAnexoDesconto);
        ValidationResult RemoverDesconto(int idProtocoloEvento);
        ValidationResult VincularPagamentoProtocolo(int idProtocolo, string token, int idUsuario);
        int GerarProtocoloPorViagemEvento(string token, int idEstabelecimento);
        ValidationResult SetAnalisado(int? idProtocoloEvento, bool analisado);
        void SetProtocoloAsAprovado(int idProtocolo);
        object ConsultarGridAssociacao(int? idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idAssociacao = 0 );
        byte[] GerarRelatorioEtiquetas(List<int> idsProtocolos, string logo);
        byte[] GerarRelatorioCapa(List<int> idsProtocolos, string logo);
        bool VerificarPagamentosSemProtocolo(int idEstabelecimentoBase);
        object GetDadosAnaliseAbono(int idProtocolo, int idViagemEvento);
        IQueryable<ProtocoloEvento> GetByIdViagemEvento(int idViagemEvento);
    }
}
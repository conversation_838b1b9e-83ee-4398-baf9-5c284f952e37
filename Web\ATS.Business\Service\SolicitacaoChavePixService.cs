using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO.Pix;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using ExcelDataReader;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class SolicitacaoChavePixService : ServiceBase, ISolicitacaoChavePixService
    {
        private readonly IExtrattaBizApiClient _bizApiClient;
        private readonly IUserIdentity _userIdentity;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IEmailService _emailService;
        private readonly IUsuarioService _usuarioService;
        private readonly IParametrosRepository _parametrosRepository;
        private readonly ISolicitacaoChavePixRepository _solicitacaoChavePixRepository;
        private readonly ISolicitacaoChavePixEventoRepository _solicitacaoChavePixEventoRepository;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;

        public SolicitacaoChavePixService(IExtrattaBizApiClient bizApiClient,
            IUserIdentity userIdentity,
            IEmpresaRepository empresaRepository,
            IProprietarioRepository proprietarioRepository,
            IParametrosEmpresaService parametrosEmpresaService,
            IEmailService emailService,
            IUsuarioService usuarioService,
            IParametrosRepository parametrosRepository,
            ISolicitacaoChavePixRepository solicitacaoChavePixRepository,
            ISolicitacaoChavePixEventoRepository solicitacaoChavePixEventoRepository, 
            IParametrosProprietarioService parametrosProprietarioService)
        {
            _bizApiClient = bizApiClient;
            _userIdentity = userIdentity;
            _empresaRepository = empresaRepository;
            _proprietarioRepository = proprietarioRepository;
            _parametrosEmpresaService = parametrosEmpresaService;
            _emailService = emailService;
            _usuarioService = usuarioService;
            _parametrosRepository = parametrosRepository;
            _solicitacaoChavePixRepository = solicitacaoChavePixRepository;
            _solicitacaoChavePixEventoRepository = solicitacaoChavePixEventoRepository;
            _parametrosProprietarioService = parametrosProprietarioService;
        }

        public BusinessResult<CadastrarChavePixProprietarioResponse> CadastrarChaveProprietario(
            CadastrarChavePixProprietarioRequest request)
        {
            //validacao de auditoria api de integracao
            if (_userIdentity.IdEmpresa == null)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Empresa não encontrada.");

            //validacoes de request
            if (string.IsNullOrWhiteSpace(request.Chave))
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Chave não informada.");

            if (string.IsNullOrWhiteSpace(request.CPFCNPJProprietario) && request.IdProprietario == null)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Proprietário não informado.");

            if (_userIdentity.IdUsuario == 0)
            {
                if (string.IsNullOrWhiteSpace(request.DocumentoUsuarioAudit))
                    return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                        "DocumentoUsuarioAudit é obrigatório.");
                if (!_usuarioService.Find(c =>
                        c.CPFCNPJ == request.DocumentoUsuarioAudit && c.IdEmpresa == _userIdentity.IdEmpresa).Any())
                    return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                        "DocumentoUsuarioAudit é inválido.");
            }

            var proprietario = request.IdProprietario == null
                ? _proprietarioRepository.FirstOrDefault(c =>
                    c.IdEmpresa == _userIdentity.IdEmpresa && c.CNPJCPF == request.CPFCNPJProprietario && c.Ativo)
                : _proprietarioRepository.FirstOrDefault(c =>
                    c.IdEmpresa == _userIdentity.IdEmpresa && c.IdProprietario == request.IdProprietario.Value &&
                    c.Ativo);
            if (proprietario == null)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Proprietário não encontrado.");

            //validacao de formatacao
            if (request.Tipo == ETipoChavePix.Cpf &&
                (request.Chave.Length != 11 || request.Chave.OnlyNumbers().Length != 11))
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos.");
            if (request.Tipo == ETipoChavePix.Cnpj &&
                (request.Chave.Length != 14 || request.Chave.OnlyNumbers().Length != 14))
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Chave de tipo CNPJ com formato inválido. Deve ter 14 caracteres numéricos.");
            if (request.Tipo == ETipoChavePix.Phone &&
                (request.Chave.Length != 11 || request.Chave.OnlyNumbers().Length != 11))
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Chave de tipo Telefone com formato inválido. Deve ter 11 caracteres numéricos.");
            if (request.Tipo == ETipoChavePix.Email && (!request.Chave.Contains('@') || !request.Chave.Contains('.')))
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Chave de tipo Email com formato inválido.");
            if (request.Tipo == ETipoChavePix.Evp && request.Chave.Length != 36)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Chave de tipo Aleatoria com formato inválido. Deve ter 36 caracteres incluindo os hífens.");

            // ver se a empresa aprova automatico e ja cadastrar
            var aprovarAutomaticamente = _parametrosEmpresaService
                .GetAprovarSolicitacoesChavePixAutomaticamente(_userIdentity.IdEmpresa.Value);
            if (aprovarAutomaticamente)
            {
                // ver se o documento do titular é dono da chave
                var cnpj = _empresaRepository.GetCnpj(_userIdentity.IdEmpresa.Value);
                var verificar = _bizApiClient.VerificarDonoChave(cnpj, 
                    proprietario.CNPJCPF, request.Tipo, request.Chave);
                if (!verificar.Success)
                    return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(verificar.Messages.FirstOrDefault());
                
                // criar a solicitacao pra historico
                var novaSolicitacao = new SolicitacaoChavePix()
                {
                    Chave = request.Chave,
                    TipoChave = request.Tipo,
                    DocumentoTitular = proprietario.CNPJCPF,
                    IdEmpresa = _userIdentity.IdEmpresa.Value,
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Aprovada,
                };

                if (_userIdentity.IdUsuario != 0) novaSolicitacao.IdUsuarioCadastro = _userIdentity.IdUsuario;

                _solicitacaoChavePixRepository.Add(novaSolicitacao);

                var novoEvento = new SolicitacaoChavePixEvento()
                {
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePix = novaSolicitacao.IdSolicitacaoChavePix,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Aprovada,
                };

                if (_userIdentity.IdUsuario != 0) novoEvento.IdUsuarioCadastro = _userIdentity.IdUsuario;

                _solicitacaoChavePixEventoRepository.Add(novoEvento);
                
                //enviar os dados pra api biz e cadastrar eles la
                var resultadoSalvarDadosPix = IntegrarDadosBancariosPixProprietario(proprietario.CNPJCPF, request.Tipo, request.Chave);
            
                if (!resultadoSalvarDadosPix.Success)
                {
                    novaSolicitacao.IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Recusada;
                    _solicitacaoChavePixRepository.Update(novaSolicitacao);
                    var novaSolicEventoErro = new SolicitacaoChavePixEvento()
                    {
                        IdUsuarioCadastro = _userIdentity.IdUsuario,
                        DataCadastro = DateTime.Now,
                        IdSolicitacaoChavePix = novaSolicitacao.IdSolicitacaoChavePix,
                        IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Recusada,
                    };
                    _solicitacaoChavePixEventoRepository.Add(novaSolicEventoErro);
                    
                    return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Ocorreu um erro ao salvar os dados do titular. Tente novamente em alguns minutos.");
                }

                //Caso chave aprovada, seta o proprietario como configuradao para Pix nessa empresa
                _parametrosProprietarioService.SetProprietarioPermiteReceberPagamentoPix(proprietario.CNPJCPF,
                    proprietario.IdEmpresa, true);
            
                _ = EnviarEmailGestorChaveAprovada(proprietario.CNPJCPF, request.Tipo, request.Chave,
                    novaSolicitacao.DataCadastro);
                
                var retornoAprovacaoDireta = new CadastrarChavePixProprietarioResponse
                {
                    Mensagem = "Chave Pix cadastrada com sucesso."
                };
                
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Valid(retornoAprovacaoDireta);
            }

            // ver se a empresa tem gestor de chave pix
            var idsUsuariosGestoresN1 = _parametrosRepository
                .Where(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                            c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel1.ToString() &&
                            c.IdRegistro == _userIdentity.IdEmpresa)
                .Select(c => c.ValorDecimal)
                .ToList();
            if (!idsUsuariosGestoresN1.Any())
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Nenhum gestor configurado para aprovação da chave nesta empresa.");

            // ver se ja tem uma solicitacao pro tipo de chave
            var solic = _solicitacaoChavePixRepository.Any(c =>
                (c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel1 ||
                 c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel2) &&
                c.IdEmpresa == _userIdentity.IdEmpresa && c.DocumentoTitular == proprietario.CNPJCPF);
            if (solic)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    $"Já existe uma solicitação pendente de chave Pix para o titular na sua empresa.");

            // ver se o documento do titular é dono da chave
            var cnpjEmpresa = _empresaRepository.GetCnpj(_userIdentity.IdEmpresa.Value);
            var verificarChave =
                _bizApiClient.VerificarDonoChave(cnpjEmpresa, proprietario.CNPJCPF, request.Tipo, request.Chave);
            if (!verificarChave.Success)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(verificarChave.Messages
                    .FirstOrDefault());

            // criar a solicitacao
            var novaSolic = new SolicitacaoChavePix()
            {
                Chave = request.Chave,
                TipoChave = request.Tipo,
                DocumentoTitular = proprietario.CNPJCPF,
                IdEmpresa = _userIdentity.IdEmpresa.Value,
                DataCadastro = DateTime.Now,
                IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1,
                DocumentoUsuarioAudit = request.DocumentoUsuarioAudit,
            };

            if (_userIdentity.IdUsuario != 0) novaSolic.IdUsuarioCadastro = _userIdentity.IdUsuario;

            _solicitacaoChavePixRepository.Add(novaSolic);

            var novaSolicEvento = new SolicitacaoChavePixEvento()
            {
                DocumentoUsuarioAudit = request.DocumentoUsuarioAudit,
                DataCadastro = DateTime.Now,
                IdSolicitacaoChavePix = novaSolic.IdSolicitacaoChavePix,
                IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1,
            };

            if (_userIdentity.IdUsuario != 0) novaSolicEvento.IdUsuarioCadastro = _userIdentity.IdUsuario;

            _solicitacaoChavePixEventoRepository.Add(novaSolicEvento);

            _ = EnviarEmailGestorNivel1NovaSolicitacao(idsUsuariosGestoresN1, proprietario.CNPJCPF, request.Tipo,
                request.Chave);

            var retorno = new CadastrarChavePixProprietarioResponse
            {
                Mensagem = "Solicitação de criação de chave efetuada com sucesso. Aguarde a decisão dos gestores."
            };

            return BusinessResult<CadastrarChavePixProprietarioResponse>.Valid(retorno);
        }

        public BusinessResult<CadastrarChavePixProprietarioResponse> CadastrarPlanilhaChavePixProprietario(
            CadastrarPlanilhaChavePixProprietarioRequest request)
        {
            //validacao de auditoria api de integracao
            if (_userIdentity.IdEmpresa == null)
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Empresa não encontrada.");

            if (request?.Itens == null || !request.Itens.Any())
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Nenhum registro informado.");

            // ver se a empresa tem gestor de chave pix
            var idsUsuariosGestoresN1 = _parametrosRepository
                .Where(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                            c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel1.ToString() &&
                            c.IdRegistro == _userIdentity.IdEmpresa)
                .Select(c => c.ValorDecimal)
                .ToList();
            if (!idsUsuariosGestoresN1.Any())
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error(
                    "Nenhum gestor configurado para aprovação da chave nesta empresa.");

            string mensagemRetorno = null;
            var itensValidos = new List<CadastrarPlanilhaChavePixProprietarioRequestItem>();
            int pos = 0;

            foreach (var item in request.Itens)
            {
                pos++;

                //validacoes de request
                if (string.IsNullOrWhiteSpace(item.Chave))
                {
                    mensagemRetorno += ($"Item {pos}: Chave não informada; ");
                    continue;
                }

                if (string.IsNullOrWhiteSpace(item.CPFCNPJProprietario))
                {
                    mensagemRetorno += ($"Item {pos}: Proprietário não informado; ");
                    continue;
                }

                var proprietario = _proprietarioRepository.FirstOrDefault(c => c.IdEmpresa == _userIdentity.IdEmpresa
                                                                               && c.CNPJCPF ==
                                                                               item.CPFCNPJProprietario && c.Ativo);
                if (proprietario == null)
                {
                    mensagemRetorno += ($"Item {pos}: Proprietário não encontrado; ");
                    continue;
                }

                //validacao de formatacao
                if (item.TipoChave == ETipoChavePix.Cpf &&
                    (item.Chave.Length != 11 || item.Chave.OnlyNumbers().Length != 11))
                {
                    mensagemRetorno +=
                        ($"Item {pos}: Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos; ");
                    continue;
                }

                if (item.TipoChave == ETipoChavePix.Cnpj &&
                    (item.Chave.Length != 14 || item.Chave.OnlyNumbers().Length != 14))
                {
                    mensagemRetorno +=
                        ($"Item {pos}: Chave de tipo CNPJ com formato inválido. Deve ter 14 caracteres numéricos; ");
                    continue;
                }

                if (item.TipoChave == ETipoChavePix.Phone &&
                    (item.Chave.Length != 11 || item.Chave.OnlyNumbers().Length != 11))
                {
                    mensagemRetorno +=
                        ($"Item {pos}: Chave de tipo Telefone com formato inválido. Deve ter 11 caracteres numéricos; ");
                    continue;
                }

                if (item.TipoChave == ETipoChavePix.Email && (!item.Chave.Contains('@') || !item.Chave.Contains('.')))
                {
                    mensagemRetorno += ($"Item {pos}: Chave de tipo Email com formato inválido; ");
                    continue;
                }

                if (item.TipoChave == ETipoChavePix.Evp && item.Chave.Length != 36)
                {
                    mensagemRetorno +=
                        ($"Item {pos}: Chave de tipo Aleatoria com formato inválido. Deve ter 36 caracteres incluindo os hífens; ");
                    continue;
                }

                // ver se ja tem uma solicitacao pro tipo de chave
                var solic = _solicitacaoChavePixRepository.Any(c =>
                    (c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel1 ||
                     c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel2) &&
                    c.IdEmpresa == _userIdentity.IdEmpresa && c.DocumentoTitular == proprietario.CNPJCPF);
                if (solic)
                {
                    mensagemRetorno +=
                        ($"Item {pos}: Já existe uma solicitação pendente de chave Pix para o titular {item.CPFCNPJProprietario} na sua empresa; ");
                    continue;
                }

                // criar a solicitacao
                var novaSolic = new SolicitacaoChavePix()
                {
                    Chave = item.Chave,
                    TipoChave = item.TipoChave,
                    DocumentoTitular = proprietario.CNPJCPF,
                    IdEmpresa = _userIdentity.IdEmpresa.Value,
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1,
                };

                if (_userIdentity.IdUsuario != 0) novaSolic.IdUsuarioCadastro = _userIdentity.IdUsuario;

                _solicitacaoChavePixRepository.Add(novaSolic);

                var novaSolicEvento = new SolicitacaoChavePixEvento()
                {
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePix = novaSolic.IdSolicitacaoChavePix,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1,
                };

                if (_userIdentity.IdUsuario != 0) novaSolicEvento.IdUsuarioCadastro = _userIdentity.IdUsuario;

                _solicitacaoChavePixEventoRepository.Add(novaSolicEvento);

                itensValidos.Add(item);
            }

            if (!itensValidos.Any())
                return BusinessResult<CadastrarChavePixProprietarioResponse>.Error("Nenhuma solicitação cadastrada. " + mensagemRetorno);

            _ = EnviarEmailGestorNivel1NovaSolicitacaoPlanilha(idsUsuariosGestoresN1, itensValidos);

            var msgFinal = "Solicitações efetuadas com sucesso. ";
            if (!string.IsNullOrWhiteSpace(mensagemRetorno)) msgFinal += "Exceto: " + mensagemRetorno;
            var retorno = new CadastrarChavePixProprietarioResponse
            {
                Mensagem = msgFinal
            };
            return BusinessResult<CadastrarChavePixProprietarioResponse>.Valid(retorno);
        }

        public BusinessResult<SolicitacaoChavePixGridResponse> ConsultarGridSolicitacaoChavePixProprietario(int page,
            int take, List<QueryFilters> filters, OrderFilters order)
        {
            #region Validacoes de permissao

            if (!_userIdentity.IdEmpresa.HasValue)
                return BusinessResult<SolicitacaoChavePixGridResponse>.Error($"Empresa não encontrada.");

            if (!_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(_userIdentity.IdEmpresa.Value))
                return BusinessResult<SolicitacaoChavePixGridResponse>.Error($"Empresa sem permissão para efetuar pagamentos Pix.");

            var usuarioGestorN1 = _parametrosRepository
                .Any(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                          c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel1.ToString() &&
                          c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal == _userIdentity.IdUsuario);

            var usuarioGestorN2 = _parametrosRepository
                .Any(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                          c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel2.ToString() &&
                          c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal == _userIdentity.IdUsuario);

            if (!usuarioGestorN1 && !usuarioGestorN2)
                return BusinessResult<SolicitacaoChavePixGridResponse>.Error($"Usuário sem permissão de gestão de solicitações de chaves Pix.");

            #endregion

            var solicitacoes =
                _solicitacaoChavePixRepository.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa.Value);

            if (!usuarioGestorN1)
                solicitacoes = solicitacoes.Where(c =>
                    c.IdSolicitacaoChavePixStatus != ESolicitacaoChavePixStatus.PendenteNivel1);

            if (!usuarioGestorN2)
                solicitacoes = solicitacoes.Where(c =>
                    c.IdSolicitacaoChavePixStatus != ESolicitacaoChavePixStatus.PendenteNivel2);

            solicitacoes = solicitacoes.AplicarFiltrosDinamicos(filters);

            solicitacoes = string.IsNullOrWhiteSpace(order?.Campo)
                ? solicitacoes.OrderBy(x => x.IdSolicitacaoChavePixStatus).ThenByDescending(c => c.DataCadastro)
                : solicitacoes.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            var count = solicitacoes.Count();

            var itens = solicitacoes.Select(c => new SolicitacaoChavePixProprietarioGridResponseItem
            {
                Codigo = c.IdSolicitacaoChavePix,
                Chave = c.Chave,
                DataCadastroDateTime = c.DataCadastro,
                DocumentoUsuarioCadastro = c.UsuarioCadastro == null ? c.DocumentoUsuarioAudit : c.UsuarioCadastro.CPFCNPJ,
                NomeUsuarioCadastro = c.UsuarioCadastro.Nome,
                StatusEnum = c.IdSolicitacaoChavePixStatus,
                TipoEnum = c.TipoChave,
                DocumentoTitular = c.DocumentoTitular
            }).Skip((page - 1) * take).Take(take).ToList();

            var cnpjs = itens.Select(c => c.DocumentoTitular).ToList();

            var nomes = _proprietarioRepository.Where(x => 
                    cnpjs.Contains(x.CNPJCPF) && x.IdEmpresa == _userIdentity.IdEmpresa.Value)
                .Select(x => new
                {
                    x.RazaoSocial,
                    x.CNPJCPF
                }).ToList();

            foreach (var item in itens)
            {
                item.DataCadastro = item.DataCadastroDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                item.Tipo = item.TipoEnum.GetDescription();
                item.Status = item.StatusEnum.GetDescription();
                item.NomeTitular = nomes.Where(c => c.CNPJCPF == item.DocumentoTitular).Select(c => c.RazaoSocial).FirstOrDefault();
                if (item.NomeUsuarioCadastro == null)
                    item.NomeUsuarioCadastro = _usuarioService.Find(c => 
                            c.CPFCNPJ == item.DocumentoUsuarioCadastro && c.IdEmpresa == _userIdentity.IdEmpresa)
                        .Select(c => c.Nome).FirstOrDefault();
            }

            var resp = new SolicitacaoChavePixGridResponse
            {
                items = itens,
                totalItems = count
            };

            return BusinessResult<SolicitacaoChavePixGridResponse>.Valid(resp);
        }

        public BusinessResult AlterarStatusSolicitacaoChavePixProprietario(SolicitacaoChavePixAlterarStatusAppRequest request)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                return BusinessResult.Error($"Empresa não encontrada.");

            var solic = _solicitacaoChavePixRepository.Where(c =>
                c.IdEmpresa == _userIdentity.IdEmpresa.Value &&
                c.IdSolicitacaoChavePix == request.IdSolicitacaoChavePix).FirstOrDefault();
            
            if (solic == null)
                return BusinessResult.Error($"Solicitação não encontrada.");
            
            if (solic.IdSolicitacaoChavePixStatus ==  ESolicitacaoChavePixStatus.Aprovada || solic.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.Recusada)
                return BusinessResult.Error($"Solicitação já finalizada.");

            var usuarioGestorN1 = _parametrosRepository
                .Any(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                          c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel1.ToString() &&
                          c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal == _userIdentity.IdUsuario);

            var usuarioGestorN2 = _parametrosRepository
                .Any(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                          c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel2.ToString() &&
                          c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal == _userIdentity.IdUsuario);
            
            var empresaTemGestorN2 = _parametrosRepository
                .Any(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                          c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel2.ToString() &&
                          c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal != 0 && c.ValorDecimal != null);

            if (!usuarioGestorN1 && !usuarioGestorN2)
                return BusinessResult.Error($"Usuário sem permissão de gestão de solicitações de chaves Pix.");

            if (!usuarioGestorN1 && solic.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel1)
                return BusinessResult.Error($"Somente gestores nível 1 tem permissão para alterar solicitações pendentes do nível 1.");

            if (!usuarioGestorN2 && solic.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel2)
                return BusinessResult.Error($"Somente gestores nível 2 tem permissão para alterar solicitações pendentes do nível 2.");

            //usuario n1 ou n2 reprovando
            if (!request.Aprovar)
            {
                solic.IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Recusada;
                _solicitacaoChavePixRepository.Update(solic);
                var novaSolicEventoRecusada = new SolicitacaoChavePixEvento()
                {
                    IdUsuarioCadastro = _userIdentity.IdUsuario,
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePix = solic.IdSolicitacaoChavePix,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Recusada,
                };
                _solicitacaoChavePixEventoRepository.Add(novaSolicEventoRecusada);
                return BusinessResult.Valid($"Solicitação recusada com sucesso.");
            }
            
            // caso aprovar, ver se o documento do titular é dono da chave
            var cnpjEmpresa = _empresaRepository.GetCnpj(_userIdentity.IdEmpresa.Value);
            var verificarChave = _bizApiClient.VerificarDonoChave(cnpjEmpresa, solic.DocumentoTitular, solic.TipoChave, solic.Chave);
            if (!verificarChave.Success)
                return BusinessResult.Error(verificarChave.Messages.FirstOrDefault());

            //usuario n1 aprovando pro n2
            if (!usuarioGestorN2 && empresaTemGestorN2)
            {
                solic.IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel2;
                _solicitacaoChavePixRepository.Update(solic);
                var novaSolicEventoPendenteNivel2 = new SolicitacaoChavePixEvento()
                {
                    IdUsuarioCadastro = _userIdentity.IdUsuario,
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePix = solic.IdSolicitacaoChavePix,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel2,
                };
                _solicitacaoChavePixEventoRepository.Add(novaSolicEventoPendenteNivel2);
                
                _ = EnviarEmailSolicitacaoPendenteGestorNivel2(solic.DocumentoTitular, solic.TipoChave, solic.Chave, solic.DataCadastro);
                return BusinessResult.Valid(
                    $"Solicitação aprovada com sucesso e agora está pendente da aprovação do gestor nível 2.");
            }
            
            //usuario n1 aprovando direto
            solic.IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Aprovada;
            _solicitacaoChavePixRepository.Update(solic);
            var novaSolicEvento = new SolicitacaoChavePixEvento()
            {
                IdUsuarioCadastro = _userIdentity.IdUsuario,
                DataCadastro = DateTime.Now,
                IdSolicitacaoChavePix = solic.IdSolicitacaoChavePix,
                IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.Aprovada,
            };
            _solicitacaoChavePixEventoRepository.Add(novaSolicEvento);
            
            //enviar os dados pra api biz e cadastrar eles la
            var resultadoSalvarDadosPix = IntegrarDadosBancariosPixProprietario(solic.DocumentoTitular, solic.TipoChave, solic.Chave);
            
            if (!resultadoSalvarDadosPix.Success)
            {
                solic.IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1;
                _solicitacaoChavePixRepository.Update(solic);
                var novaSolicEventoErro = new SolicitacaoChavePixEvento()
                {
                    IdUsuarioCadastro = _userIdentity.IdUsuario,
                    DataCadastro = DateTime.Now,
                    IdSolicitacaoChavePix = solic.IdSolicitacaoChavePix,
                    IdSolicitacaoChavePixStatus = ESolicitacaoChavePixStatus.PendenteNivel1,
                };
                _solicitacaoChavePixEventoRepository.Add(novaSolicEventoErro);
                return BusinessResult.Error("Ocorreu um erro ao salvar os dados do titular. Aprove a solicitação novamente.");
            }

            //Caso chave aprovada, seta o proprietario como configuradao para Pix nessa empresa
            _parametrosProprietarioService.SetProprietarioPermiteReceberPagamentoPix(solic.DocumentoTitular,
                solic.IdEmpresa, true);
            
            _ = EnviarEmailGestorChaveAprovada(solic.DocumentoTitular, solic.TipoChave, solic.Chave,
                solic.DataCadastro);
                
            return BusinessResult.Valid("Solicitação aprovada com sucesso.");
        }

        public BusinessResult<ValidarPlanilhaSolicitacaoChavePixResponse> ValidarPlanilha(HttpPostedFileBase file)
        {
            if (!ValidarTipoArquivo(file.FileName))
                throw new Exception("Tipo de arquivo inválido.");

            if (file.ContentLength <= 0)
                throw new Exception("Planilha vazia.");

            var retorno = new ValidarPlanilhaSolicitacaoChavePixResponse();
            
            IExcelDataReader reader = null;

            if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelBinaryExtension))
            {
                reader = ExcelReaderFactory.CreateBinaryReader(file.InputStream);
            }

            if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelXmlExtention))
            {
                reader = ExcelReaderFactory.CreateOpenXmlReader(file.InputStream);
            }

            if (file.FileName.ToLower().EndsWith(ConstantesUtils.CsvExtention))
            {
                reader = ExcelReaderFactory.CreateCsvReader(file.InputStream, new ExcelReaderConfiguration
                {
                    AutodetectSeparators = new[] { ';', '|', '#', ',' }
                });
            }

            if (reader == null)
                throw new Exception("Planilha vazia.");

            var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration
                {
                    UseHeaderRow = true
                }
            });
            
            reader.Close();

            ValidarHeader(dataSet);
            
            var resultadoValidacao = ValidarLinhas(dataSet);

            retorno.Total = resultadoValidacao.Count;
            retorno.TotalInvalidos = resultadoValidacao.Count(o => !o.Valido);
            retorno.TotalValidos = resultadoValidacao.Count(o => o.Valido);
            retorno.CodigoPlanilhaImportada = CriarCodigoImportacaoPlanilha();
            retorno.Registros = resultadoValidacao;

            return BusinessResult<ValidarPlanilhaSolicitacaoChavePixResponse>.Valid(retorno);
        }

        #region Metodos auxiliares

        private BusinessResult IntegrarDadosBancariosPixProprietario(string documentoProprietario,
            ETipoChavePix tipoChave, string chavePix)
        {
            var proprietario = _proprietarioRepository.GetPorCpfCnpj(documentoProprietario);

            var cnpjEmpresa = _empresaRepository.GetCnpj(proprietario.IdEmpresa);

            var request = new IntegrarDadosBancariosPixModelRequest
            {
                DocumentoProprietario = proprietario.CNPJCPF,
                DocumentoEmpresa = cnpjEmpresa,
                Nome = proprietario.RazaoSocial,
                NomeFantasia = proprietario.NomeFantasia,
                ChavePix = chavePix,
                TipoChavePix = tipoChave
            };

            var resultadoIntegracao = _bizApiClient.PostDadosBancariosPix(request);

            if (!resultadoIntegracao.Success)
                return BusinessResult.Error(resultadoIntegracao.Messages.FirstOrDefault());

            return BusinessResult.Valid();
        }

        private async Task EnviarEmailGestorNivel1NovaSolicitacao(List<decimal?> idsUsuariosGestoresN1, string documentoProprietario, ETipoChavePix tipo, string chave)
        {
            try
            {
                if (!idsUsuariosGestoresN1.Any() || string.IsNullOrWhiteSpace(documentoProprietario)) return;

                var infos = _proprietarioRepository.Find(c => c.CNPJCPF == documentoProprietario && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => new
                    {
                        NomeTitular = c.RazaoSocial,
                        NomeEmpresa = c.Empresa.RazaoSocial,
                    }).FirstOrDefault();

                if (infos == null) return;

                var emailsGestoresN1 = _usuarioService.Find(c => idsUsuariosGestoresN1.Contains(c.IdUsuario))
                    .Select(c => c.Contatos.FirstOrDefault(x => x.Email != null))
                    .Select(c => c.Email)
                    .ToList();
                
                if (!emailsGestoresN1.Any()) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Solicitação de cadastro de chave Pix",
                    Destinatarios = emailsGestoresN1,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\cadastro-chave-pix-nova-solicitacao.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", infos.NomeEmpresa);
                    html = html.Replace("{DOCUMENTO_TITULAR}", documentoProprietario);
                    html = html.Replace("{NOME_TITULAR}", infos.NomeTitular);
                    html = html.Replace("{TIPO_CHAVE}", tipo.GetDescription());
                    html = html.Replace("{CHAVE}", chave);
                    html = html.Replace("{DATA}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO EMAIL SOLICITACAO CHAVE PIX");
            }
        }

        private async Task EnviarEmailGestorNivel1NovaSolicitacaoPlanilha(List<decimal?> idsUsuariosGestoresN1, List<CadastrarPlanilhaChavePixProprietarioRequestItem> itens)
        {
            try
            {
                if (!idsUsuariosGestoresN1.Any() || !itens.Any()) return;

                var cnpjs = itens.Select(c => c.CPFCNPJProprietario).ToList();

                var nomeEmpresa = _empresaRepository.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => c.RazaoSocial).FirstOrDefault();
                
                var nomesProps = _proprietarioRepository.Find(c => cnpjs.Contains(c.CNPJCPF) && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => new
                    {
                        c.CNPJCPF,
                        c.RazaoSocial
                    }).ToList();

                var infosUsuario = _usuarioService.Find(c => c.IdUsuario == _userIdentity.IdUsuario)
                    .Select(c => new
                    {
                        c.CPFCNPJ,
                        c.Nome
                    }).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(nomeEmpresa) || !nomesProps.Any() || infosUsuario == null) return;

                var emailsGestoresN1 = _usuarioService.Find(c => idsUsuariosGestoresN1.Contains(c.IdUsuario))
                    .Select(c => c.Contatos.FirstOrDefault(x => x.Email != null))
                    .Select(c => c.Email)
                    .ToList();
                
                if (!emailsGestoresN1.Any()) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Solicitações de cadastro de chave Pix",
                    Destinatarios = emailsGestoresN1,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\cadastro-chave-pix-nova-solicitacao-planilha.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", nomeEmpresa);
                    html = html.Replace("{DOC_USUARIO_IMPORTACAO}", infosUsuario.CPFCNPJ);
                    html = html.Replace("{NOME_USUARIO_IMPORTACAO}", infosUsuario.Nome);
                    html = html.Replace("{DATA}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        
                    var dados = string.Empty;

                    foreach (var item in itens)
                    {
                        dados += "<ul>";
                        dados += $"<li>Documento Titular: {item.CPFCNPJProprietario}</li>";
                        dados += $"<li>Nome Titular: {nomesProps.Where(c => c.CNPJCPF == item.CPFCNPJProprietario).Select(c => c.RazaoSocial).FirstOrDefault()}</li>";
                        dados += $"<li>Tipo de Chave: {item.TipoChave.GetDescription()}</li>";
                        dados += $"<li>Chave: {item.Chave}</li>";
                    }
                    
                    html = html.Replace("{DADOS}", dados);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO EMAIL SOLICITACAO CHAVE PIX");
            }
        }

        private async Task EnviarEmailSolicitacaoPendenteGestorNivel2(string documentoProprietario, ETipoChavePix tipo, string chave, DateTime dataSolicitacao)
        {
            try
            {
                var idsUsuariosGestoresN2 = _parametrosRepository
                    .Where(c => c.NomeTabela == nameof(EMPRESA_ID) &&
                                c.Chave == EMPRESA_ID.IdUsuarioGestorChavePixNivel2.ToString() &&
                                c.IdRegistro == _userIdentity.IdEmpresa && c.ValorDecimal != 0 && c.ValorDecimal != null)
                    .Select(c => c.ValorDecimal)
                    .ToList();

                if (!idsUsuariosGestoresN2.Any()) return;
                
                var infos = _proprietarioRepository.Find(c => c.CNPJCPF == documentoProprietario && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => new
                    {
                        NomeTitular = c.RazaoSocial,
                        NomeEmpresa = c.Empresa.RazaoSocial,
                    }).FirstOrDefault();

                if (infos == null) return;

                var emailsGestoresN2 = _usuarioService.Find(c => idsUsuariosGestoresN2.Contains(c.IdUsuario))
                    .Select(c => c.Contatos.FirstOrDefault(x => x.Email != null))
                    .Select(c => c.Email)
                    .ToList();
                
                if (!emailsGestoresN2.Any()) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Aprovação nível 2 para cadastro de chave Pix",
                    Destinatarios = emailsGestoresN2,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\cadastro-chave-pix-pendente-nivel-2.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", infos.NomeEmpresa);
                    html = html.Replace("{DOCUMENTO_TITULAR}", documentoProprietario);
                    html = html.Replace("{NOME_TITULAR}", infos.NomeTitular);
                    html = html.Replace("{TIPO_CHAVE}", tipo.GetDescription());
                    html = html.Replace("{CHAVE}", chave);
                    html = html.Replace("{DATA_SOLICITACAO}", dataSolicitacao.ToString("yyyy-MM-dd HH:mm:ss"));
                    html = html.Replace("{DATA_APROVACAO_N1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO EMAIL APROVACAO NIVEL 2 CHAVE PIX");
            }
        }

        private async Task EnviarEmailGestorChaveAprovada(string documentoProprietario, ETipoChavePix tipo, string chave, DateTime dataSolicitacao)
        {
            try
            {
                
                var emailsUsuariosGestao = _usuarioService
                    .Find(c => c.IdEmpresa == _userIdentity.IdEmpresa && c.RecebeEmailGestao && c.Ativo)
                    .Select(c => c.Contatos.FirstOrDefault(x => x.Email != null)).Select(c => c.Email).ToList();
                
                if (!emailsUsuariosGestao.Any()) return;
                
                var infos = _proprietarioRepository.Find(c => c.CNPJCPF == documentoProprietario && c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => new
                    {
                        NomeTitular = c.RazaoSocial,
                        NomeEmpresa = c.Empresa.RazaoSocial,
                    }).FirstOrDefault();

                if (infos == null) return;

                var emailModel = new EmailModel()
                {
                    Assunto = $"Chave Pix aprovada para proprietário",
                    Destinatarios = emailsUsuariosGestao,
                    NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma,
                    Prioridade = MailPriority.High
                };

                using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\cadastro-chave-pix-aprovacao-final.html"))
                {
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    var html = await ms.ReadToEndAsync();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{EMPRESA_NOME}", infos.NomeEmpresa);
                    html = html.Replace("{DOCUMENTO_TITULAR}", documentoProprietario);
                    html = html.Replace("{NOME_TITULAR}", infos.NomeTitular);
                    html = html.Replace("{TIPO_CHAVE}", tipo.GetDescription());
                    html = html.Replace("{CHAVE}", chave);
                    html = html.Replace("{DATA_SOLICITACAO}", dataSolicitacao.ToString("yyyy-MM-dd HH:mm:ss"));
                    html = html.Replace("{DATA_APROVACAO_FINAL}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoEmail);
                    emailModel.AlternateView = view;
                }

                _emailService.EnviarEmail(emailModel);
            }
            catch (Exception e)
            {
                _logger.Error(e, "ENVIO EMAIL APROVACAO FINAL CHAVE PIX");
            }
        }

        private static string CriarCodigoImportacaoPlanilha()
        {
            var randomNumber = new Random().Next(1, 9);
            var dataHoraAtual = DateTime.Now;

            var codigoText = $"{randomNumber}{dataHoraAtual.Day.ToString().PadLeft(2, '0')}{dataHoraAtual.Month.ToString().PadLeft(2, '0')}{dataHoraAtual.Year.ToString().Substring(2)}{dataHoraAtual.Hour.ToString().PadLeft(2, '0')}{dataHoraAtual.Minute.ToString().PadLeft(2, '0')}{dataHoraAtual.Second.ToString().PadLeft(2, '0')}";

            return codigoText;
        }
        
        private static bool ValidarTipoArquivo(string arquivo)
        {
            return
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelBinaryExtension ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelXmlExtention ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.CsvExtention;
        }
        
        private void ValidarHeader(DataSet dataSet)
        {
            if (dataSet == null)
                throw new Exception("Planilha inválida, realize o download do layout padrão e preencha.");

            foreach (DataTable sheet in dataSet.Tables)
            {
                if (sheet.Columns.Count < 3)
                    throw new Exception(
                        $"A planilha {sheet.TableName} não contém as colunas esperadas pelo layout padrão.");

                if (sheet.Columns[0]?.ColumnName != "DOCUMENTO_TITULAR")
                    throw new Exception(
                        "A primeira coluna não se refere ao documento do titular, realize o download do layout padrão e preencha.");

                if (sheet.Columns[1]?.ColumnName != "TIPO_CHAVE")
                    throw new Exception(
                        "A segunda coluna não se refere ao tipo de chave Pix, realize o download do layout padrão e preencha.");

                if (sheet.Columns[2]?.ColumnName != "CHAVE")
                    throw new Exception(
                        "A terceira coluna não se à chave Pix, realize o download do layout padrão e preencha.");
            }
        }

        private List<ValidarPlanilhaSolicitacaoChavePixResponseItem> ValidarLinhas(DataSet dataSet)
        {
            var retorno = new List<ValidarPlanilhaSolicitacaoChavePixResponseItem>();

            var docs = new HashSet<string>();

            foreach (DataTable sheet in dataSet.Tables)
            {
                var numeroLinha = 1; //header

                foreach (DataRow row in sheet.Rows)
                {
                    numeroLinha++;
                    try
                    {
                        var validacao = ValidarLinha(row);
                        var documentoUnico = docs.Add(validacao.Value.DocumentoTitular);
                        retorno.Add(new ValidarPlanilhaSolicitacaoChavePixResponseItem
                        {
                            Valido = documentoUnico && validacao.Key,
                            Linha = numeroLinha,
                            NomeSheet = sheet.TableName,
                            DocumentoTitular = validacao.Value.DocumentoTitular,
                            Chave = validacao.Value.Chave,
                            MensagemValidacao = documentoUnico ? validacao.Value.MensagemValidacao : "Planilha já contém uma solicitação pra esse documento; ",
                            TipoChave = validacao.Value.TipoChave
                        });
                    }
                    catch (Exception e)
                    {
                        retorno.Add(new ValidarPlanilhaSolicitacaoChavePixResponseItem
                        {
                            Linha = numeroLinha,
                            NomeSheet = sheet.TableName,
                            MensagemValidacao = e.GetBaseException().Message
                        });
                    }
                }
            }

            return retorno;
        }

        private KeyValuePair<bool, ValidarPlanilhaSolicitacaoChavePixResponseItem> ValidarLinha(DataRow row)
        {
            try
            {
                var mensagem = string.Empty;
                
                var obj = new ValidarPlanilhaSolicitacaoChavePixResponseItem();

                #region Validação do Documento do Titular

                var documento = row.ItemArray[0]?.ToString().OnlyNumbers().PadLeft(11, '0');
                
                if (!string.IsNullOrEmpty(documento))
                {
                    var propExisteEmpresa = _proprietarioRepository.Any(c =>
                        c.IdEmpresa == _userIdentity.IdEmpresa && c.CNPJCPF == documento && c.Ativo);
                    
                    if (!propExisteEmpresa)
                        mensagem += "Proprietário não encontrado para a empresa; ";
                    else
                        obj.DocumentoTitular = documento;
                }
                else
                {
                    mensagem += "Documento do proprietário não preenchido; ";
                    obj.DocumentoTitular = string.Empty;
                }

                #endregion
                
                #region Validação de Tipo de Chave

                var tipoChave = row.ItemArray[1]?.ToInt();

                if (tipoChave.HasValue && System.Enum.IsDefined(typeof(ETipoChavePix), tipoChave))
                {
                    obj.TipoChave = (ETipoChavePix)tipoChave;
                }
                else
                {
                    mensagem += "Tipo de chave inválido; ";
                    obj.TipoChave = null;
                }

                #endregion

                #region Validação da chave

                var chave = row.ItemArray[2]?.ToString();
                
                if (!string.IsNullOrEmpty(chave) && obj.TipoChave != null)
                {
                    if (obj.TipoChave == ETipoChavePix.Cpf && (chave.Length != 11 || chave.OnlyNumbers().Length != 11))
                        mensagem += "Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos; ";
                    else if (obj.TipoChave == ETipoChavePix.Cnpj && (chave.Length != 14 || chave.OnlyNumbers().Length != 14))
                        mensagem += "Chave de tipo CNPJ com formato inválido. Deve ter 14 caracteres numéricos; ";
                    else if (obj.TipoChave == ETipoChavePix.Phone && (chave.Length != 11 || chave.OnlyNumbers().Length != 11))
                        mensagem += "Chave de tipo Telefone com formato inválido. Deve ter 11 caracteres numéricos; ";
                    else if (obj.TipoChave == ETipoChavePix.Email && (!chave.Contains('@') || !chave.Contains('.')))
                        mensagem += "Chave de tipo Email com formato inválido; ";
                    else if (obj.TipoChave == ETipoChavePix.Evp && chave.Length != 36)
                        mensagem += "Chave de tipo Aleatoria com formato inválido. Deve ter 36 caracteres incluindo os hífens; ";
                    else
                        obj.Chave = chave;
                }
                else
                {
                    mensagem += "Chave inválida; ";
                    obj.Chave = string.Empty;
                }

                #endregion
                
                #region Validacao se ja existe uma solicitacao pro proprietario

                var existeSolic = _solicitacaoChavePixRepository
                    .Any(c => c.DocumentoTitular == documento && c.IdEmpresa == _userIdentity.IdEmpresa &&
                                (c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel1 ||
                                 c.IdSolicitacaoChavePixStatus == ESolicitacaoChavePixStatus.PendenteNivel2));
                if (existeSolic)
                {
                    mensagem += "Já existe uma solicitação pendente para o documento; ";
                }
                
                #endregion

                var valido = string.IsNullOrEmpty(mensagem);
                
                obj.MensagemValidacao = mensagem;

                return new KeyValuePair<bool, ValidarPlanilhaSolicitacaoChavePixResponseItem>(valido, obj);
            }
            catch (Exception e)
            {
                _logger.Error(e, $"VALIDAR LINHA SOLICITACAO CHAVE PIX");
                return new KeyValuePair<bool, ValidarPlanilhaSolicitacaoChavePixResponseItem>(false,
                    new ValidarPlanilhaSolicitacaoChavePixResponseItem
                    {
                        Valido = false, 
                        MensagemValidacao = e.GetBaseException().Message
                    });
            }
        }
        
        #endregion
    }
}
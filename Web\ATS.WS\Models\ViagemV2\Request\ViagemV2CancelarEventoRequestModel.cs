using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2CancelarEventoRequestModel : RequestBase
    {
        public new string DocumentoUsuarioAudit 
        {
            get { return !string.IsNullOrWhiteSpace(_documentoUsuarioAudit) ? _documentoUsuarioAudit : UsuarioDocumento; }
            set {_documentoUsuarioAudit = value.OnlyNumbers();}
        }
        private string _documentoUsuarioAudit { get; set; }
        [JsonIgnore]
       // [Obsolete("Utilizar os campos de auditoria da request base")]
        public string UsuarioDocumento { get; set; }

        public new string NomeUsuarioAudit 
        {
            get { return !string.IsNullOrWhiteSpace(_nomeUsuarioAudit) ? _nomeUsuarioAudit : UsuarioNome; }
            set {_nomeUsuarioAudit = StringUtil.RemoveAccents(value).RemoveSpecialCaracter(true);}
        }
        private string _nomeUsuarioAudit { get; set; }
        
        [JsonIgnore]
        //[Obsolete("Utilizar os campos de auditoria da request base")]
        public string UsuarioNome { get; set; }

        public int? ViagemId { get; set; }

        public int? ViagemEventoId { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (string.IsNullOrEmpty(DocumentoUsuarioAudit))
                return new ValidationResult().Add("Documento do usuário não informado.", EFaultType.Error);
            
            if (!DocumentoUsuarioAudit.ValidateDocument())
                return new ValidationResult().Add("Documento do usuário inválido.", EFaultType.Error);

            if (string.IsNullOrEmpty(NomeUsuarioAudit))
                return new ValidationResult().Add("Nome do usuário não informado.", EFaultType.Error);
            
            if (NomeUsuarioAudit.Length > 100)
                return new ValidationResult().Add("Nome do usuário não pode ter mais que 100 caracteres.", EFaultType.Error);
            
            if (!ViagemId.HasValue || ViagemId == 0)
                return new ValidationResult().Add("ViagemId não informado.", EFaultType.Error);
            
            if (!ViagemEventoId.HasValue || ViagemEventoId == 0)
                return new ValidationResult().Add("ViagemEventoId não informado.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
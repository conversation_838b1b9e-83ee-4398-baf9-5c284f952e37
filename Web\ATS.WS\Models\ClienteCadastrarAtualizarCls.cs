using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.WS.Models
{
    public class ClienteCadastrarAtualizarCls
    {
        public bool ativarParametrosGestao { get; set; }

        public int? idCliente { get; set; }
        public int? idEmpresa { get; set; }
        public ETipoCliente tipoCliente { get; set; }
        public ETipoPessoa tipoPessoa { get; set; }
        public string imagemLogoB64 { get; set; }
        public string razaoSocial { get; set; }
        public string nomeFantasia { get; set; }
        public string cpfcnpj { get; set; }
        public string ie { get; set; }
        public string celular { get; set; }
        public string email { get; set; }
        public string rg { get; set; }
        public string orgaoExpedidor { get; set; }
        public bool autenticarCodigoBarraNF { get; set; }
        public bool obrigarCPFReceber { get; set; }
        public string cep { get; set; }
        public string endereco { get; set; }
        public string enderecoNumero { get; set; }
        public string enderecoComplemento { get; set; }
        public string enderecoBairro { get; set; }
        public int? enderecoIdPais { get; set; }
        public int? enderecoIdEstado { get; set; }
        public int? enderecoIdCidade { get; set; }
        public decimal latitude { get; set; }
        public decimal longitude { get; set; }
        public bool limparLogo { get; set; } = false;
        public bool PontoReferencia { get; set; } = false;
        

        public List<int> operacoes { get; set; }


        public List<cliente_ocorrencias> ocorrencias { get; set; } = new List<cliente_ocorrencias>();
        public List<cliente_motivos_ocorrencias> motivos { get; set; } = new List<cliente_motivos_ocorrencias>();
    }
    
    public class cliente_ocorrencias
    {
        public int IdOcorrencia { get; set; }
        public bool ObrigaFoto { get; set; }
        public bool ObrigaAutorizacao { get; set; }
        public bool Visivel { get; set; }
        public bool EncerraManual { get; set; }
        public bool Monetaria { get; set; }
        public bool Ativo { get; set; }
    }

    public class cliente_motivos_ocorrencias
    {
        public int IdMotivoOcorrencia { get; set; }
        public bool Ativo { get; set; }
    }
}
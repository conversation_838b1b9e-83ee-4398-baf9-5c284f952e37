﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class DeclaracaoCiotMap : EntityTypeConfiguration<DeclaracaoCiot>
    {
        public DeclaracaoCiotMap()
        {
            ToTable("DECLARACAO_CIOT");

            HasKey(t => t.IdDeclaracaoCiot);

            Property(t => t.IdDeclaracaoCiot).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.Ciot).HasMaxLength(12);
            Property(t => t.Verificador).HasMaxLength(4);
            Property(t => t.AvisoTransportador).HasMaxLength(300);
            Property(t => t.ProtocoloErro).HasMaxLength(50);
            Property(t => t.Senha).HasMaxLength(20);
            Property(t => t.MotivoCancelamento).HasMaxLength(100);
            Property(t => t.DataDesvinculo).IsOptional().HasColumnType("datetime2");

            Property(t => t.IdViagem).IsOptional();
            Property(t => t.IdContratoCiotAgregado).IsOptional();
            
            Property(x => x.Status).HasColumnName("Status").IsRequired();

            Property(t => t.Ciot).HasIndex("Ix_DeclaracaoCIot_Ciot", true);

            HasRequired(t => t.Empresa)
                .WithMany()
                .HasForeignKey(t => t.IdEmpresa);

            HasOptional(t => t.ViagemOrigem)
                .WithMany(t => t.CIOTs)
                .HasForeignKey(t => new {t.IdViagem, t.IdEmpresa});

            HasOptional(t => t.ContratoCiotAgregado)
                .WithMany()
                .HasForeignKey(t => t.IdContratoCiotAgregado);
        }
    }
}

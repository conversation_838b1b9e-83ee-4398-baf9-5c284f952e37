﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.PagamentoFreteSemChave;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class PagamentoEventoSemChaveController : BaseAtsController<IViagemApp>
    {
        private readonly SrvPagamentoFrete _srvPagamentoFrete;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IMotoristaApp _motoristaApp;

        public PagamentoEventoSemChaveController(IViagemApp app, IUserIdentity userIdentity, SrvPagamentoFrete srvPagamentoFrete, IViagemEventoApp viagemEventoApp, IMotoristaApp motoristaApp) : base(app, userIdentity)
        {
            _srvPagamentoFrete = srvPagamentoFrete;
            _viagemEventoApp = viagemEventoApp;
            _motoristaApp = motoristaApp;
        }
        
        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarAtualizar(PagamentoFreteSemChaveCrud pagFreteSemChave)
        {
            try
            {
                if (!string.IsNullOrEmpty(pagFreteSemChave.Token))
                {
                    _srvPagamentoFrete.AtualizarPagamentoSemChave(pagFreteSemChave, SessionUser.IdUsuario);
                    return ResponderSucesso("Edição realizada com sucesso!");
                }

                throw new Exception("É necessário informar um token para continuar!");
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (filters == null)
                    filters = new List<QueryFilters>();

                if (SessionUser.Perfil != (int)EPerfil.Administrador)
                    filters.Add(new QueryFilters
                    {
                        Campo = "IdEmpresa",
                        Operador = EOperador.Exact,
                        CampoTipo = EFieldTipo.Number,
                        Valor = SessionUser.IdEmpresa?.ToString()
                    });

                var eventosSemChave = _viagemEventoApp.ConsultaGridSemChave(take, page, order, filters);
                
                return ResponderSucesso(eventosSemChave);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorToken(string token)
        {
            try
            {
                var evtQuery = _viagemEventoApp.GetEventoPorTokenPagSemChave(token);

                if (!evtQuery.Any())
                    throw new Exception($"Não foi possível encontrar um evento para o token {token}!");

                var evt = evtQuery.Select(c => new
                {
                    c.Status,
                    estabEventos = c.Viagem.ViagemEstabelecimentos.Select(a => new
                    {
                        a.TipoEventoViagem,
                        a.IdEstabelecimento,
                        EstabelecimentoDescricao = a.Estabelecimento.Descricao
                    }).ToList(),
                    c.IdViagemEvento,
                    c.TipoEventoViagem,
                    c.Viagem.CPFMotorista,
                    c.ValorPagamento,
                    c.Viagem.Placa,
                    Carretas = c.Viagem.ViagemCarretas.Select(a => a.Placa).ToList(),
                    c.ObsLibSemChave,
                    c.LiberarPagtoSemChave,
                    c.IdEstabelecimentoBase,
                    EstabelecimentoBaseDescricao = c.EstabelecimentoBase.Descricao,
                    c.DataLibSemChave,
                    c.IdUsuarioLibSemChave,
                    UsuarioLiberacaoSemChave = c.UsuarioLibSemChave.Nome
                }).First();
                
                if (evt.Status == EStatusViagemEvento.Bloqueado)
                    throw new Exception($"Este evento se encontra bloqueado!");

                if (evt.Status == EStatusViagemEvento.Baixado)
                    throw new Exception($"Este evento já foi baixado!");

                var motViagem = _motoristaApp.GetPorCpf(evt.CPFMotorista);

                var estabEvento = evt.estabEventos.FirstOrDefault(p => p.TipoEventoViagem == evt.TipoEventoViagem);

                return ResponderSucesso(new
                {
                    evt.IdViagemEvento,
                    evt.TipoEventoViagem,
                    evt.Status,
                    evt.ValorPagamento,
                    NomeMotorista = motViagem.Nome,
                    evt.CPFMotorista,
                    RgMot = motViagem.RG,
                    CelMot = motViagem.Celular,
                    Cavalo = evt.Placa,
                    evt.Carretas,
                    evt.ObsLibSemChave,
                    evt.LiberarPagtoSemChave,
                    EstabId = evt.IdEstabelecimentoBase,
                    EstabDescricao = evt.EstabelecimentoBaseDescricao,
                    DataLibSemChave = evt.DataLibSemChave?.ToString("dd/MM/yyyy HH:mm:ss"),
                    evt.IdUsuarioLibSemChave,
                    NomeUsuarioLibSemChave = evt.IdUsuarioLibSemChave.HasValue ? evt.UsuarioLiberacaoSemChave : "",
                    EstabelecimentoNotBase = new
                    {
                        EstabDescricao = estabEvento?.EstabelecimentoDescricao,
                        IdEstab = estabEvento?.IdEstabelecimento
                    }
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar]
        [Expor(EApi.Portal)]
        public ActionResult RelatorioPagamentosSemChave(int idUsuarioLogado, ETipoArquivo? tipoRelatorio)
        {
            try
            {
                var report = _srvPagamentoFrete.GerarRelatorioPagamentosSemChave(idUsuarioLogado, tipoRelatorio);

                switch (tipoRelatorio)
                {
                    case ETipoArquivo.PDF:
                        Response.AddHeader("Content-Disposition", $"inline; filename=Pagamentos-ATS.pdf");
                        return File(report, "application/pdf");
                    case ETipoArquivo.Excel:
                        Response.AddHeader("Content-Disposition", $"attachment; filename=Pagamentos.xls");
                        return File(report, "application/vnd.ms-excel");
                    default:
                        Response.AddHeader("Content-Disposition", $"inline; filename=Pagamentos-ATS.pdf");
                        return File(report, "application/pdf");

                }
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }
    }
}
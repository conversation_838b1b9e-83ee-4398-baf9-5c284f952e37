﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Models.DestinoRotaModelo.ATS.Domain.Models.DestinoRotaModelo;

namespace ATS.Domain.Service
{
    public class RotaModeloService : ServiceBase, IRotaModeloService
    {
        private readonly IRotaModeloRepository _repositoryRotaModelo;
        private readonly IPontoRotaModeloRepository _repositoryPontoRotaModelo;
        private readonly IPracaRotaModeloRepository _repositoryPracaRotaModelo;
        private readonly ICidadeService _cidadeService;

        public RotaModeloService(IRotaModeloRepository repositoryRotaModelo, IPontoRotaModeloRepository repositoryPontoRotaModelo, IPracaRotaModeloRepository repositoryPracaRotaModelo,
            ICidadeService cidadeService)
        {
            _repositoryRotaModelo = repositoryRotaModelo;
            _repositoryPontoRotaModelo = repositoryPontoRotaModelo;
            _repositoryPracaRotaModelo = repositoryPracaRotaModelo;
            
            _cidadeService = cidadeService;
        }
        public ValidationResult Add(RotaModelo rotaPadrao)
        {
            try
            {
                var validationResult = IsValidCrud(rotaPadrao);
               
                if (!validationResult.IsValid) 
                    return validationResult;
                
                rotaPadrao.DataCadastro = DateTime.Now;
                _repositoryRotaModelo.Add(rotaPadrao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
        
        public ValidationResult Editar(RotaModelo rotaPadrao)
        {
            try
            {
                ValidationResult validationResult = IsValidCrud(rotaPadrao);

                if (!validationResult.IsValid)
                    return validationResult;

                var retornoPontos = AtualizarPontos(rotaPadrao);

                if (!retornoPontos.IsValid)
                    return new ValidationResult().Add(retornoPontos);
                
                var retornoPracas = AtualizarPracas(rotaPadrao);
                
                if (!retornoPracas.IsValid)
                    return new ValidationResult().Add(retornoPontos);
                
                _repositoryRotaModelo.Update(rotaPadrao);
                _repositoryRotaModelo.SaveChanges();
                
                return validationResult;

            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AtualizarPontos(RotaModelo rotaPadrao)
        {
            try
            {
                // Pontos Originais
                var pontosOriginais =  _repositoryPontoRotaModelo
                    .Find(m => m.IdRotaModelo == rotaPadrao.IdRotaModelo).ToList();

                // Pontos Excluídos
                foreach (var pontoOriginal in pontosOriginais)
                {
                    _repositoryPontoRotaModelo.Delete(pontoOriginal);
                    _repositoryPontoRotaModelo.SaveChanges();
                }

                // Pontos Inseridos ou Alterados
                foreach (var pontoInserido in rotaPadrao.PontosRotaModelo)
                {
                    _repositoryPontoRotaModelo.Add(pontoInserido);
                    _repositoryPontoRotaModelo.SaveChanges();
                }


                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        
        public ValidationResult AtualizarPracas(RotaModelo rotaPadrao)
        {
            try
            {
                // Pracas Originais
                var pracasOriginais =  _repositoryPracaRotaModelo
                    .Find(m => m.IdRotaModelo == rotaPadrao.IdRotaModelo).ToList();

                // Pracas Excluídos
                foreach (var pontoOriginal in pracasOriginais)
                {
                    _repositoryPracaRotaModelo.Delete(pontoOriginal);
                    _repositoryPracaRotaModelo.SaveChanges();
                }

                // Pracas Inseridos ou Alterados
                foreach (var pracasInserido in rotaPadrao.PracasRotaModelo)
                {
                    _repositoryPracaRotaModelo.Add(pracasInserido);
                    _repositoryPracaRotaModelo.SaveChanges();
                }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        
        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var mots = _repositoryRotaModelo
                .GetAll()
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                mots = mots.Where(o => o.IdEmpresa == idEmpresa);

            mots = string.IsNullOrWhiteSpace(order?.Campo)
                ? mots.OrderByDescending(x => x.IdRotaModelo)
                : mots.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            mots = mots.AplicarFiltrosDinamicos(filters, true);

            return new
            {
                totalItems = mots.Count(),
                items = mots.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdRotaModelo,
                        x.NomeRota,
                        x.OrigemDescricao,
                        x.DestinoDescricao
                    })
            };
        }
        
        public RotaModelo GetWithChilds(int idRota)
        {
            var rota =
                _repositoryRotaModelo
                    .Find(m => m.IdRotaModelo == idRota)
                    .Include(m => m.PontosRotaModelo)
                    .Include(m => m.PracasRotaModelo).FirstOrDefault();
            
            return rota;
        }
        
        public RotaModelo GetByIdOrNomeRota(int? idRota,string nomeRota,int? idEmpresa)
        {
            RotaModelo rota = null;

            if (idEmpresa != 0)
            {
                if (idRota > 0)
                {
                    rota = _repositoryRotaModelo
                        .Find(m => m.IdRotaModelo == idRota && m.IdEmpresa == idEmpresa)
                        .Include(m => m.PontosRotaModelo)
                        .Include(m => m.PracasRotaModelo).FirstOrDefault();
                }

                if (!string.IsNullOrWhiteSpace(nomeRota) && rota == null)
                {
                    rota = _repositoryRotaModelo
                        .Find(m => m.NomeRota == nomeRota && m.IdEmpresa == idEmpresa)
                        .Include(m => m.PontosRotaModelo)
                        .Include(m => m.PracasRotaModelo).FirstOrDefault();
                }
            }
            else
            {
                if (idRota > 0)
                {
                    rota = _repositoryRotaModelo
                        .Find(m => m.IdRotaModelo == idRota)
                        .Include(m => m.PontosRotaModelo)
                        .Include(m => m.PracasRotaModelo).FirstOrDefault();
                }

                if (!string.IsNullOrWhiteSpace(nomeRota) && rota == null)
                {
                    rota = _repositoryRotaModelo
                        .Find(m => m.NomeRota == nomeRota)
                        .Include(m => m.PontosRotaModelo)
                        .Include(m => m.PracasRotaModelo).FirstOrDefault();
                }
            }
            
            return rota;
        }

        public List<DestinoRotaModeloModel> SetarRetornoDestino(RotaModelo rotaModelo)
        {
            var retorno = new List<DestinoRotaModeloModel>();
            var cidade = _cidadeService;
            var count = 1;
            
            //Origem
            rotaModelo.OrigemIbge = rotaModelo.OrigemIbge == 0 
                ? ReconsultarIbge(rotaModelo.OrigemDescricao) 
                : rotaModelo.OrigemIbge;
            
            retorno.Add(new DestinoRotaModeloModel()
            {
                Local = rotaModelo.OrigemDescricao,
                Tipo = "Origem",
                Ibge = rotaModelo.OrigemIbge,
                Longitude = rotaModelo.OrigemLongitude ?? cidade.GetCidadeFromIBGE(rotaModelo.OrigemIbge.ToInt()).Longitude ?? 0,
                Latitude = rotaModelo.OrigemLatitude ?? cidade.GetCidadeFromIBGE(rotaModelo.OrigemIbge.ToInt()).Latitude ?? 0
            });
                
            //Pontos
            foreach (var item in rotaModelo.PontosRotaModelo)
            {
                item.Ibge = item.Ibge == 0 ? ReconsultarIbge(item.Descricao) : item.Ibge;
                
                retorno.Add(new DestinoRotaModeloModel()
                {
                    Local = item.Descricao,
                    Tipo = "Ponto " + count++,
                    Ibge = item.Ibge,
                    Longitude = item.Longitude ?? cidade.GetCidadeFromIBGE(item.Ibge.ToInt()).Longitude ?? 0,
                    Latitude = item.Latitude ?? cidade.GetCidadeFromIBGE(item.Ibge.ToInt()).Latitude ?? 0
                });
                
            }
                
            //Destino
            rotaModelo.DestinoIbge = rotaModelo.DestinoIbge == 0
                ? ReconsultarIbge(rotaModelo.DestinoDescricao)
                : rotaModelo.DestinoIbge;

            retorno.Add(new DestinoRotaModeloModel()
            {
                Local = rotaModelo.DestinoDescricao,
                Tipo = "Destino",
                Ibge = rotaModelo.DestinoIbge,
                Longitude = rotaModelo.DestinoLongitude ?? cidade.GetCidadeFromIBGE(rotaModelo.DestinoIbge.ToInt()).Longitude ?? 0,
                Latitude = rotaModelo.DestinoLatitude ?? cidade.GetCidadeFromIBGE(rotaModelo.DestinoIbge.ToInt()).Latitude ?? 0
            });
            
            return retorno;
        }

        public void ExtrairNomesLocais(string local, out string cidade, out string estado, out string pais)
        {
            //Validacao
            if (string.IsNullOrEmpty(local))
            {
                cidade = "n/a";
                estado = "n/a";
                pais = "n/a";
                return;
            }
            
            //Se for uma coordenada
            if (!local.Any(char.IsLetter))
            {
                cidade = null;
                estado = null;
                pais = null;
                return;
            }
            
            //Reverter a string pra pegar do final pq é mais facil doq lidar com as coisas desde o inicio
            //Vendo de tras pra frente, o país fica antes da primeira virgula
            var localDescReversa = string.Join("",local.Reverse());
            var indexDepoisPais = localDescReversa.IndexOf(" ,", StringComparison.CurrentCulture);
            pais = string.Join("", localDescReversa.Substring(0, indexDepoisPais).Reverse());
            
            //Sigla do estado

            //Remove o país pra pegar o estado
            var localDescReversaSemPais = localDescReversa.Substring(indexDepoisPais + 2);
            var indexDepoisEstado = localDescReversaSemPais.IndexOf(" -", StringComparison.CurrentCulture);
            if(indexDepoisEstado < 0) indexDepoisEstado = localDescReversaSemPais.IndexOf(" ,", StringComparison.CurrentCulture);
            estado = string.Join("", localDescReversaSemPais.Substring(0, indexDepoisEstado).Reverse());
            
            //Remove o estado pra pegar a Cidade
            var localDescReversaSemPaisSemEstado = localDescReversaSemPais.Substring(indexDepoisEstado + 2);
            
            //Se quiser retornar como cidade tudo que ta depois do estado
            cidade = string.Join("", localDescReversaSemPaisSemEstado.Reverse());

            //Se quiser retornar só o nome da cidade
            //var indexDepoisCidade = localDescReversaSemPaisSemEstado.IndexOf(" ,", StringComparison.CurrentCulture);
            //cidade = indexDepoisCidade > 0
            //    ? string.Join("", localDescReversaSemPaisSemEstado.Substring(0, indexDepoisCidade).Reverse())
            //    : string.Join("", localDescReversaSemPaisSemEstado.Reverse());
        }
        
        public List<DetalhesRotaModeloModel> ConsultarDetalhes(RotaModelo rotaModelo)
        {
            var retorno = new List<DetalhesRotaModeloModel>();
            var count = 1;
            
            //Origem
            ExtrairNomesLocais(rotaModelo.OrigemDescricao, out var cidadeOrigem, out var estadoOrigem, out var paisOrigem);

            if (string.IsNullOrWhiteSpace(cidadeOrigem) || string.IsNullOrWhiteSpace(estadoOrigem) || string.IsNullOrWhiteSpace(paisOrigem))
            {
                var origem = _cidadeService.GetCidadeFromIBGE(Convert.ToInt32(rotaModelo.OrigemIbge));
                
                if (origem != null)
                {
                    cidadeOrigem = $"{origem.Nome} / {rotaModelo.OrigemDescricao}";
                    estadoOrigem = origem.Estado.Nome;
                    paisOrigem = origem.Estado.Pais.Nome;
                }
            }
            retorno.Add(new DetalhesRotaModeloModel()
            {
                Sequencia = count,
                IbgeCidade = rotaModelo.OrigemIbge.ToInt(),
                Cidade = cidadeOrigem,
                Estado = estadoOrigem,
                Pais = paisOrigem,
                Longitude = rotaModelo.OrigemLongitude ?? 0,
                Latitude = rotaModelo.OrigemLatitude ?? 0
            });
                
            //Pontos
            foreach (var item in rotaModelo.PontosRotaModelo)
            {
                count++;
                ExtrairNomesLocais(item.Descricao, out var cidadeItem, out var estadoItem, out var paisItem);
                
                if (string.IsNullOrWhiteSpace(cidadeItem) || string.IsNullOrWhiteSpace(estadoItem) || string.IsNullOrWhiteSpace(paisItem))
                {
                    var ponto = _cidadeService.GetCidadeFromIBGE(Convert.ToInt32(item.Ibge));
                    
                    if (ponto != null)
                    {
                        cidadeItem = $"{ponto.Nome} / {item.Descricao}";
                        estadoItem = ponto.Estado.Nome;
                        paisItem = ponto.Estado.Pais.Nome;
                    }
                }
                
                retorno.Add(new DetalhesRotaModeloModel()
                {
                    Sequencia = count,
                    IbgeCidade = item.Ibge.ToInt(),
                    Cidade = cidadeItem,
                    Estado = estadoItem,
                    Pais = paisItem,
                    Longitude = item.Longitude ?? 0,
                    Latitude = item.Latitude ?? 0
                });
            }
            
            //Destino
            ExtrairNomesLocais(rotaModelo.DestinoDescricao, out var cidadeDestino, out var estadoDestino, out var paisDestino);
            if (string.IsNullOrWhiteSpace(cidadeDestino) || string.IsNullOrWhiteSpace(estadoDestino) || string.IsNullOrWhiteSpace(paisDestino))
            {
                var destino = _cidadeService.GetCidadeFromIBGE(Convert.ToInt32(rotaModelo.DestinoIbge));

                if (destino != null)
                {
                    cidadeDestino = $"{destino.Nome} / {rotaModelo.DestinoDescricao}";
                    estadoDestino = destino.Estado.Nome;
                    paisDestino = destino.Estado.Pais.Nome;
                }
            }
            retorno.Add(new DetalhesRotaModeloModel()
            {
                Sequencia = count + 1,
                IbgeCidade = rotaModelo.DestinoIbge.ToInt(),
                Cidade = cidadeDestino,
                Estado = estadoDestino,
                Pais =  paisDestino,
                Longitude = rotaModelo.DestinoLongitude ?? 0,
                Latitude = rotaModelo.DestinoLatitude ?? 0
            });
            
            return retorno;
        }
        
        private ValidationResult IsValidCrud(RotaModelo rotaModelo)
        {
            var validationResult = new ValidationResult();
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(rotaModelo.DestinoDescricao, @"O destino deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(rotaModelo.OrigemDescricao, @"A origem deve ser informada"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(rotaModelo.NomeRota, @"A descrição da rota deve ser informado"));

            if (rotaModelo.IdEmpresa != 0)
            {
                var existRota = _repositoryRotaModelo.GetAll().FirstOrDefault(x =>
                    x.NomeRota == rotaModelo.NomeRota && x.IdEmpresa == rotaModelo.IdEmpresa && x.IdRotaModelo != rotaModelo.IdRotaModelo);

                if (existRota != null)
                    validationResult.Add("Nome de rota já informado para esse transportador.");
            }
            
            //Validação caso ibge venha zero: Tentativa de encontrar novamente caso falha retorna informado o usuário! Ajustar metodo rogers
            if (rotaModelo.OrigemIbge == 0)
                rotaModelo.OrigemIbge =  ReconsultarIbge(rotaModelo.OrigemDescricao);

            if (rotaModelo.DestinoIbge == 0)
                rotaModelo.DestinoIbge = ReconsultarIbge(rotaModelo.DestinoDescricao);
            
            foreach (var ponto in rotaModelo.PontosRotaModelo)
            {
                if (ponto.Ibge == 0)
                    ponto.Ibge = ReconsultarIbge(ponto.Descricao);
            }

            return validationResult;
        }


        public decimal ReconsultarIbge(string ibge)
        {
            ExtrairNomesLocais(ibge, out var cidadeOrigem, out var estadoOrigem, out var paisOrigem);

            var ibgeRequest = new CidadeIbgeRequestModel()
            {
                NomeCidade = cidadeOrigem,
                SiglaEstado = estadoOrigem,
            };

            var retornoIbge = _cidadeService.GetIbgeCidade(ibgeRequest);

            if (retornoIbge == 0)
                throw new Exception($"Cidade {ibgeRequest.NomeCidade} não encontrada na base!");
            
            return retornoIbge;
        }
    }
}
﻿using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;

namespace ATS.Data.Repository.Dapper
{
    public class NotificacaoDapper : DapperFactory<Notificacao>, INotificacaoDapper
    {
        public bool Inserir(string idusuario, string tipo, string conteudo, DateTime datahoraenvio, int idTipoNotificacao)
        {
            var sSql = "INSERT " +
                       "INTO [NOTIFICACAO] " +
                       "([idusuario], " +
                       "[tipo], " +
                       "[conteudo], " +
                       "[datahoraenvio], " +
                       "[recebida], " +
                       "[lida], " +
                       "[link], " +
                       "[idTipoNotificacao])" +
                       "VALUES ( '" +idusuario+"', '"+tipo+ "', '"+conteudo+"', '"+DateTime.Now.ToString()+"', '0', '0', '', '"+idTipoNotificacao.ToString()+"')";

            using (IDbConnection dbConnection = this.GetConnection())
            {
                return dbConnection.Execute(sSql) > 0;

            }
        }
    }
}
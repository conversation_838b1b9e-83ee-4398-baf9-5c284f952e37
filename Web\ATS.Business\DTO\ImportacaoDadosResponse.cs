using System.Collections.Generic;
using Extratta.ImportacaoDadosConsumer.Events;

namespace ATS.Domain.DTO
{
    public class ImportacaoDadosResponse
    {
        public List<FilialIntegrarFilaEvent> Filial { get; set; }
        public List<MotoristaIntegrarFilaEvent> Motorista { get; set; }
        public List<VeiculoIntegrarFilaEvent> Veiculo { get; set; }
        public List<ProprietarioIntegrarFilaEvent> Proprietario { get; set; }
        public List<ClienteIntegrarFilaEvent> Cliente { get; set; }
    }
}
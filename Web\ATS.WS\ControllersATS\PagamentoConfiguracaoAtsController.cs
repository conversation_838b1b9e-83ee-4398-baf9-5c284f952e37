﻿using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common;
using ATS.WS.Models.Webservice.Response.PagamentoConfiguracao;
using ATS.WS.Services;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class PagamentoConfiguracaoAtsController : DefaultController
    {
        public readonly IPagamentoConfiguracaoApp AppLayer;
        private readonly IUserIdentity _userIdentity;
        private readonly IFilialApp _filialApp;
        private readonly SrvPagamentoConfiguracao _srvPagamentoConfiguracao;

        public PagamentoConfiguracaoAtsController(IPagamentoConfiguracaoApp appLayer, SrvPagamentoConfiguracao srvPagamentoConfiguracao, IUserIdentity userIdentity, IFilialApp filialApp)
        {
            AppLayer = appLayer;
            _srvPagamentoConfiguracao = srvPagamentoConfiguracao;
            _userIdentity = userIdentity;
            _filialApp = filialApp;
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idEmpresa, int? idFilial)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;
                
                if(idFilial.HasValue && !_filialApp.PertenceAEmpresa(idEmpresa, idFilial.Value))
                    return ResponderErro("Filial não pertence à empresa.");
                
                var configuracao = AppLayer
                    .GetPorEmpresa(idEmpresa, idFilial)
                    .ToList();

                var configuracaoResponse = Mapper.Map<List<PagamentoConfiguracao>, List<PagamentoConfiguracaoResponse>>(configuracao);
                
                return ResponderSucesso(configuracaoResponse);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar( PagamentoConfiguracaoModel configuracao )
        {
            try
            {
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && configuracao.IdEmpresa != _userIdentity.IdEmpresa)
                    return ResponderErro("Usuário não autenticado.");
                
                if(configuracao.IdFilial.HasValue && !_filialApp.PertenceAEmpresa(configuracao.IdEmpresa, configuracao.IdFilial.Value))
                    return ResponderErro("Filial não pertence à empresa.");
                
                _srvPagamentoConfiguracao.Cadastrar(configuracao);
                return ResponderSucesso("Configuração inserida com sucesso!");
            } catch(Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(PagamentoConfiguracaoModel configuracao)
        {
            try
            {
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && configuracao.IdEmpresa != _userIdentity.IdEmpresa)
                    return ResponderErro("Usuário não autenticado.");
                
                if(configuracao.IdFilial.HasValue && !_filialApp.PertenceAEmpresa(configuracao.IdEmpresa, configuracao.IdFilial.Value))
                    return ResponderErro("Filial não pertence à empresa.");
                
                _srvPagamentoConfiguracao.Atualizar(configuracao);
                return ResponderSucesso("Configuração atualizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                if(idFilial.HasValue && idEmpresa.HasValue && !_filialApp.PertenceAEmpresa(idEmpresa.Value, idFilial.Value))
                    return ResponderErro("Filial não pertence à empresa.");
                
                var configuracoes = AppLayer.ConsultarGrid(idEmpresa, idFilial, take, page, order, filters);

                return ResponderSucesso(configuracoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idPagamentoConfiguracao)
        {
            try
            {
                var validationResult = AppLayer.Inativar(idPagamentoConfiguracao);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Configuração inativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idPagamentoConfiguracao)
        {
            try
            {
                var validationResult = AppLayer.Reativar(idPagamentoConfiguracao);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Configuração reativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idPagamentoConfiguracao)
        {
            try
            {
                var retorno = _srvPagamentoConfiguracao.ConsultarPorId(idPagamentoConfiguracao);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

    }
}
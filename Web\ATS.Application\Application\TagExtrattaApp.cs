﻿using System;
using ATS.Application.Application.Common;
using ATS.Domain.Grid;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using TagExtrattaClient;
using EOperador = ATS.Domain.Enum.EOperador;
using EOperadorOrder = ATS.Domain.Enum.EOperadorOrder;
using EStatusTag = ATS.Domain.Enum.EStatusTag;
using ETipoPagamento = ATS.Domain.DTO.TagExtratta.ETipoPagamento;
using ETipoTransacaoTag = ATS.Domain.DTO.TagExtratta.ETipoTransacaoTag;
using GridValePedagioHubRequest = ATS.Domain.DTO.TagExtratta.GridValePedagioHubRequest;
using VeiculoTagResponse = ATS.Domain.DTO.TagExtratta.VeiculoTagResponse;

namespace ATS.Application.Application
{
    public class TagExtrattaApp : AppBase, ITagExtrattaApp
    {
        private readonly ITagExtrattaService _tagExtrattaService;
        private readonly IEmpresaService _empresaService;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosApp _parametrosApp;
        private readonly ICargaAvulsaApp _cargaAvulsaApp;
        private readonly ICargaAvulsaRepository _cargaAvulsaRepository;
        private readonly IAutenticacaoAplicacaoService _autenticacaoAplicacaoService;
        private readonly IViagemRepository _viagemRepository;
        public TagExtrattaApp(ITagExtrattaService tagExtrattaService, 
            IEmpresaService empresaService, 
            IParametrosApp parametrosApp, 
            IUserIdentity userIdentity, 
            ICargaAvulsaRepository cargaAvulsaRepository, 
            ICargaAvulsaApp cargaAvulsaApp,
            IAutenticacaoAplicacaoService autenticacaoAplicacaoService, 
            IViagemRepository viagemRepository)
        {
            _tagExtrattaService = tagExtrattaService;
            _empresaService = empresaService;
            _parametrosApp = parametrosApp;
            _userIdentity = userIdentity;
            _cargaAvulsaRepository = cargaAvulsaRepository;
            _cargaAvulsaApp = cargaAvulsaApp;
            _autenticacaoAplicacaoService = autenticacaoAplicacaoService;
            _viagemRepository = viagemRepository;
        }

        public byte[] GerarRelatorioConsultaSituacaoTags(int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa, int? idUsuario, DateTime? dataInicio, DateTime? dataFim)
        {
            try
            {
                var dados = GridTags(take, page, order, filters,idEmpresa, dataInicio, dataFim);

                var empresa = _empresaService.All().Select(x => new
                {
                    x.IdEmpresa,
                    x.Logo
                }).FirstOrDefault(x => x.IdEmpresa == idEmpresa);

                var logo = empresa?.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

                var dadosReport = Mapper.Map<List<RelatorioConsultaSituacaoTagsDataType>>(dados.Value.items);

                return _tagExtrattaService.GerarRelatorioConsultaSituacaoTags(logo, extensao, dadosReport);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<GridRemessaEnvioResponse> GridRemessaEnvio(GridRemessaEnvioRequest request)
        {
            try
            {
                #region Filtros da consulta

                if (request.Filters == null)
                    request.Filters = new List<QueryFilters>();

                if (request.IdEmpresa.HasValue)
                {
                    request.Filters.Add(new QueryFilters()
                    {
                        Valor = request.IdEmpresa.ToString(),
                        Campo = "IdEmpresa",
                        Operador = EOperador.Exact,
                        CampoTipo = EFieldTipo.Number
                    });
                }
                
                //Range de data
                request.Filters.Add(new QueryFilters()
                {
                    Valor = request.DataInicio.StartOfDay().ToString(),
                    Campo = "DataCriacao",
                    Operador = EOperador.GreaterThanOrEqual,
                    CampoTipo = EFieldTipo.Date
                });
                
                request.Filters.Add(new QueryFilters()
                {
                    Valor = request.DataFim.EndOfDay().ToString(),
                    Campo = "DataCriacao",
                    Operador = EOperador.LessThanOrEqual,
                    CampoTipo = EFieldTipo.Date
                });

                #endregion

                var resultExtrattaTag = _tagExtrattaService.ConsultaRemessas(request.Take,request.Page,request.Filters,request.Order);
            
                if(!resultExtrattaTag.Success)
                    return BusinessResult<GridRemessaEnvioResponse>.Error(resultExtrattaTag.Messages);
                
                return BusinessResult<GridRemessaEnvioResponse>.Valid(new GridRemessaEnvioResponse()
                {
                    items = resultExtrattaTag.Value.Itens
                            .AsQueryable()
                            .ProjectTo<GridRemessaEnvioItemResponse>()
                            .ToList(),
                    totalItems = resultExtrattaTag.Value.CountItens
                });

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridRemessaEnvioResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<GridRemessaRecebimentoResponse> GridRemessaRecebimento(GridRemessaRecebimentoRequest request)
        {
            try
            {
                #region Filtros da consulta

                request.Filters ??= new List<QueryFilters>();
                
                //Range de data
                request.Filters.Add(new QueryFilters()
                {
                    Valor = request.DataInicio.StartOfDay().ToString(),
                    Campo = "DataCriacao",
                    Operador = EOperador.GreaterThanOrEqual,
                    CampoTipo = EFieldTipo.Date
                });
                
                request.Filters.Add(new QueryFilters()
                {
                    Valor = request.DataFim.EndOfDay().ToString(),
                    Campo = "DataCriacao",
                    Operador = EOperador.LessThanOrEqual,
                    CampoTipo = EFieldTipo.Date
                });

                if (request.IdEmpresa.HasValue)
                {
                    request.Filters.Add(new QueryFilters()
                    {
                        Valor = request.IdEmpresa.ToString(),
                        Campo = "IdEmpresa",
                        Operador = EOperador.Exact,
                        CampoTipo = EFieldTipo.Number
                    });
                }

                #endregion

                var resultExtrattaTag = _tagExtrattaService.ConsultaRemessas(request.Take,request.Page,request.Filters,request.Order);
            
                if(!resultExtrattaTag.Success)
                    return BusinessResult<GridRemessaRecebimentoResponse>.Error(resultExtrattaTag.Messages);
                
                return BusinessResult<GridRemessaRecebimentoResponse>.Valid(new GridRemessaRecebimentoResponse()
                {
                    items = resultExtrattaTag.Value.Itens
                            .AsQueryable()
                            .ProjectTo<GridRemessaRecebimentoItemResponse>()
                            .ToList(),
                    totalItems = resultExtrattaTag.Value.CountItens
                });

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridRemessaRecebimentoResponse>.Error(e.Message);
            }
        }

        public BusinessResult<ConsultarRemessaResponse> GetRemessa(int id)
        {
            try
            {
                var result = _tagExtrattaService.GetRemessa(id);
            
                if(!result.Success)
                    return BusinessResult<ConsultarRemessaResponse>.Error(result.Messages);
                
                return BusinessResult<ConsultarRemessaResponse>.Valid(Mapper.Map<ConsultarRemessaResponse>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<ConsultarRemessaResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<List<TagReduzidaResponse>> ConsultarLoteEstoque(long min,long max)
        {
            try
            {
                var result = _tagExtrattaService.GetLote(min,max,EStatusTag.Estoque);
            
                if(!result.Success)
                    return BusinessResult<List<TagReduzidaResponse>>.Error(result.Messages);

                return BusinessResult<List<TagReduzidaResponse>>.Valid(result.Value
                    .AsQueryable()
                    .OrderBy(x => x.SerialNumber)
                    .ProjectTo<TagReduzidaResponse>()
                    .ToList());
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<List<TagReduzidaResponse>>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> EnviarRemessa(TagRemessaEnvioRequest request)
        {
            try
            {
                var requestSaveRemessaTag = Mapper.Map<RemessaCadastrarModelRequest>(request);
                var result = _tagExtrattaService.EnviarRemessa(requestSaveRemessaTag);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Remessa enviada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> ReceberRemessa(int idRemessa)
        {
            try
            {
                var result = _tagExtrattaService.ReceberRemessa(idRemessa);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Remessa recebida com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<TagReduzidaResponse> GetTagSerialEstoque(long serial)
        {
            try
            {
                var result = _tagExtrattaService.GetTagSerial(serial,EStatusTag.Estoque);
            
                if(!result.Success)
                    return BusinessResult<TagReduzidaResponse>.Error(result.Messages);

                return BusinessResult<TagReduzidaResponse>.Valid(Mapper.Map<TagReduzidaResponse>(result.Value));
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<TagReduzidaResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<TagReduzidaGridResponse> GridTagsAtivas(int take, int page, OrderFilters order, List<QueryFilters> filters, List<long> tagsSelecionadas)
        {
            try
            {
                filters ??= new List<QueryFilters>();
              
                // Filtro TAGS estoque 
                filters.Add(new QueryFilters()
                {
                    Valor = "1",
                    Campo = "Status",
                    Operador = EOperador.Exact,
                    CampoTipo = EFieldTipo.Number
                });
                
                var result = _tagExtrattaService.GetTags(take,page,filters,order,tagsSelecionadas);
            
                if(!result.Success)
                    return BusinessResult<TagReduzidaGridResponse>.Error(result.Messages);
              
                return BusinessResult<TagReduzidaGridResponse>.Valid(new TagReduzidaGridResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<TagReduzidaResponse>().ToList(),
                    totalItems = (result.Value.CountItens -  (tagsSelecionadas?.Count ?? 0))
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<TagReduzidaGridResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<GridTagResponse> GridTags(int? take, int? page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, DateTime? dataInicio, DateTime? dataFim)
        {
            try
            {
                filters ??= new List<QueryFilters>();

                //Filtros
                if (idEmpresa.HasValue)
                {
                    filters.Add(new QueryFilters()
                    {
                        Valor = idEmpresa.ToString(),
                        Campo = "IdEmpresa",
                        Operador = EOperador.Exact,
                        CampoTipo = EFieldTipo.Number
                    });
                }
                
                if (dataInicio.HasValue)
                    dataInicio = dataInicio.Value.StartOfDay();
                if(dataFim.HasValue)
                    dataFim = dataFim.Value.EndOfDay();
                
                var result = _tagExtrattaService.GetTags(take,page,filters,order,null,dataInicio,dataFim);
            
                if(!result.Success)
                    return BusinessResult<GridTagResponse>.Error(result.Messages);
                
                return BusinessResult<GridTagResponse>.Valid(new GridTagResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<GridTagItemResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridTagResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<VeiculoTagResponse> GetVeiculo(string placa)
        {
            try
            {
                var result = _tagExtrattaService.GetVeiculo(placa.RemoveSpecialCharacters());
            
                if(!result.Success)
                    return BusinessResult<VeiculoTagResponse>.Error(result.Messages);

                return BusinessResult<VeiculoTagResponse>.Valid(Mapper.Map<VeiculoTagResponse>(result.Value));
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<VeiculoTagResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<TagReduzidaGridResponse> GridTagsReduzida(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var result = _tagExtrattaService.GetTags(take,page,filters,order);
            
                if(!result.Success)
                    return BusinessResult<TagReduzidaGridResponse>.Error(result.Messages);
                
                return BusinessResult<TagReduzidaGridResponse>.Valid(new TagReduzidaGridResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<TagReduzidaResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<TagReduzidaGridResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<ModeloMoveMaisGridResponse> GridModeloMoveMais(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var result = _tagExtrattaService.GetModelosMoveMais(new GetModelosVeiculoMoveMaisRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters),
                    Order = new ApiOrderFilters()
                    {
                        Campo = order.Campo,
                        Operador = order.Operador == EOperadorOrder.Ascending
                        ? TagExtrattaClient.EOperadorOrder._0
                        : TagExtrattaClient.EOperadorOrder._1
                    }
                });
            
                if(!result.Success)
                    return BusinessResult<ModeloMoveMaisGridResponse>.Error(result.Messages);
                
                return BusinessResult<ModeloMoveMaisGridResponse>.Valid(new ModeloMoveMaisGridResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<ModeloMoveMaisItem>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<ModeloMoveMaisGridResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> Vincular(SalvarTagRequest request)
        {
            try
            {
                var result = _tagExtrattaService.Vincular(Mapper.Map<TagVincularModelRequest>(request),request.Serial,request.Placa.RemoveSpecialCharacters());
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> Bloquear(long serial)
        {
            try
            {
                var result = _tagExtrattaService.Bloquear(serial);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> Desbloquear(long serial)
        {
            try
            {
                var result = _tagExtrattaService.Desbloquear(serial);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> Desvincular(long serial)
        {
            try
            {
                var result = _tagExtrattaService.Desvincular(serial);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<BloqueiosTagUsuarioResponse> GetBloqueios(int usuarioId)
        {
            try
            {
                var result = _tagExtrattaService.GetBloqueios(usuarioId);
            
                if(!result.Success)
                    return BusinessResult<BloqueiosTagUsuarioResponse>.Error(result.Messages);

                return BusinessResult<BloqueiosTagUsuarioResponse>.Valid(Mapper.Map<BloqueiosTagUsuarioResponse>(result.Value));
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<BloqueiosTagUsuarioResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> CadastrarBloqueios(BloqueiosTagUsuarioRequest request)
        {
            try
            {
                var result = _tagExtrattaService.CadastrarBloqueios(Mapper.Map<BloqueioCadastrarModelRequest>(request));
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<ModeloMoveMaisGridResponse> GetModelosMovemais(int? take, int? page)
        {
            try
            {
                var result = _tagExtrattaService.GetModelosMoveMais(new GetModelosVeiculoMoveMaisRequest()
                {
                    Take = take,
                    Page = page
                });
            
                if(!result.Success)
                    return BusinessResult<ModeloMoveMaisGridResponse>.Error(result.Messages);
                
                return BusinessResult<ModeloMoveMaisGridResponse>.Valid(new ModeloMoveMaisGridResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<ModeloMoveMaisItem>().ToList(),
                    totalItems = result.Value.CountItens
                });
                
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<ModeloMoveMaisGridResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<GridValePedagioHubResponse> GridValePedagioHub(GridValePedagioHubRequest request)
        {
            try
            {
                if ((int)EPerfil.Administrador != _userIdentity.Perfil)
                    request.Cnpj = _empresaService.GetCnpjById(_userIdentity.IdEmpresa ?? 0);
                
                request.Filters ??= new List<QueryFilters>();

                if (!string.IsNullOrWhiteSpace(request.Cnpj))
                {
                    request.Filters.Add(new QueryFilters()
                    {
                        Valor = request.Cnpj.OnlyNumbers(),
                        Campo = "Empresa.Cnpj",
                        Operador = EOperador.Exact,
                        CampoTipo = EFieldTipo.String
                    });
                }
                
                var result = _tagExtrattaService.GridValePedagioHub(request.Take,request.Page,request.Filters,request.Order,request.DataInicio.StartOfDay(),request.DataFim.EndOfDay());
            
                if(!result.Success)
                    return BusinessResult<GridValePedagioHubResponse>.Error(result.Messages);
                
                return BusinessResult<GridValePedagioHubResponse>.Valid(new GridValePedagioHubResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<GridValePedagioHubItemResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridValePedagioHubResponse>.Error(e.Message);
            }
        }
        
        public byte[] GerarRelatorioPassagensPedagioCompraHub(ReportPassagensPedagioCompraHub request)
        {
            try
            {
                var data = ConsultarPassagensPedagioCompraHub(request.CompraId,request.Fornecedor);

                if (!data.Success)
                    return new byte[] { };
                    
                return _tagExtrattaService.GerarRelatorioPracasPedagioMoveMais(data.Value,request.Extensao,_userIdentity.IdEmpresa);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }
        
        public byte[] GerarRelatorioGridValePedagioHub(OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa,DateTime dataInicio,DateTime dataFim)
        {
            try
            {
                var dataGrid = GridValePedagioHub(new GridValePedagioHubRequest()
                {
                    Filters = filters,
                    Order = order,
                    DataFim = dataFim,
                    DataInicio = dataInicio
                });

                return !dataGrid.Success 
                    ? new byte[] { } 
                    : _tagExtrattaService.GerarRelatorioGridValePedagioHub(dataGrid.Value.items,extensao,idEmpresa);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<string> CadastrarModelosMoveMais()
        {
            try
            {
                return _tagExtrattaService.CadastrarModelosMoveMais();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> CadastrarEstoqueTags()
        {
            try
            {
                return _tagExtrattaService.CadastrarEstoqueTags();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest requestModel)
        {
            try
            {
                return _tagExtrattaService.NotificarPassagemPraca(requestModel);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<Guid>.Error(e.Message);
            }
        }

        public BusinessResult<GridPagamentosTagResponse> GridPagamentos(GridPagamentosTagRequest request)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    request.IdEmpresa = _userIdentity.IdEmpresa;

                var result = _tagExtrattaService.GetPagamentos(request.Take,request.Page,request.Filters,request.Order,request.DataInicio.StartOfDay(),request.DataFim.EndOfDay(), request.IdEmpresa,request.Fornecedor);

                if(!result.Success)
                    return BusinessResult<GridPagamentosTagResponse>.Error(result.Messages);

                return BusinessResult<GridPagamentosTagResponse>.Valid(new GridPagamentosTagResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<PagamentosItemTagResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridPagamentosTagResponse>.Error(e.Message);
            }
        }


        public BusinessResult<GridPassagensWebhookResponse> GridPassagensWebhook(int? take, int? page, OrderFilters order, List<QueryFilters> filters, DateTime? dataInicio, DateTime? dataFim,int? empresaId)
        {
            try
            {
                if (dataInicio.HasValue)
                    dataInicio = dataInicio.Value.StartOfDay();
                if(dataFim.HasValue)
                    dataFim = dataFim.Value.EndOfDay();
                
                var result = _tagExtrattaService.GetPassagensPraca(take, page, filters, order, dataInicio, dataFim,empresaId);

                if (!result.Success)
                    return BusinessResult<GridPassagensWebhookResponse>.Error(result.Messages);

                return BusinessResult<GridPassagensWebhookResponse>.Valid(new GridPassagensWebhookResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<GridPassagemWebhookItemResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridPassagensWebhookResponse>.Error(e.Message);
            }
        }

        public byte[] GerarRelatorioGridPassagemWebhook(OrderFilters order, List<QueryFilters> filters, string extensao,DateTime dataInicio, DateTime dataFim,int? idEmpresa)
        {
            try
            {
                var result = _tagExtrattaService.GetPassagensPraca(null,null,filters,order,dataInicio,dataFim,idEmpresa);

                if(!result.Success)
                    return new byte[] { };

                return _tagExtrattaService.GerarRelatorioGridPassagemWebhook(result.Value.Itens.AsQueryable().ProjectTo<GridPassagemWebhookItemResponse>().ToList(),extensao);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<string> GenerateTokenWebhook(string user,string password)
        {
            var userCredential = _parametrosApp.GetUserWebhook();
            var passwordCredential = _parametrosApp.GetSenhaWebhook();

            if (userCredential != user || passwordCredential != password)
                return BusinessResult<string>.Error("Usuário ou senha inválido!");
            
            return BusinessResult<string>.Valid(GetWebhookToken());
        }

        public string GetWebhookToken()
        {
            var userCredential = _parametrosApp.GetUserWebhook();
            var passwordCredential = _parametrosApp.GetSenhaWebhook();
            
            byte[] credentialsBytes = Encoding.UTF8.GetBytes($"{userCredential}:{passwordCredential}");
            string base64Credentials = Convert.ToBase64String(credentialsBytes);
            
            return $"Basic {base64Credentials}";
        }

        public byte[] GerarRelatorioGridPagamentos(OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa,DateTime dataInicio,DateTime dataFim, FornecedorEnum fornecedor)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var dataGrid = GridPagamentos(new GridPagamentosTagRequest()
                {
                    Filters = filters,
                    Order = order,
                    DataFim = dataFim,
                    DataInicio = dataInicio,
                    IdEmpresa = idEmpresa,
                    Fornecedor = fornecedor 
                });

                if (!dataGrid.Success)
                    return new byte[] { };

                return _tagExtrattaService.GerarRelatorioGridPagamentos(dataGrid.Value.items,extensao);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<ConsultarPagamentoResponse> GetPagamento(long id)
        {
            try
            {
                var result = _tagExtrattaService.GetPagamento(id);

                if(!result.Success)
                    return BusinessResult<ConsultarPagamentoResponse>.Error(result.Messages);

                return BusinessResult<ConsultarPagamentoResponse>.Valid(Mapper.Map<ConsultarPagamentoResponse>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<ConsultarPagamentoResponse>.Error(e.Message);
            }
        }

        public BusinessResult<string> PagamentoManualEventoTag(PagamentoTagRequest request)
        {
            try
            {
                var result = _tagExtrattaService.PagamentoManualEventoTag(Mapper.Map<PagamentoManualRequest>(request));

                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid(result.Value);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }

        public BusinessResult<string> EstornoManualEventoTag(EstornoTagRequest request)
        {
            try
            {
                var result = _tagExtrattaService.EstornoManualEventoTag(Mapper.Map<PagamentoManualRequest>(request));

                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid(result.Value);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> DesvincularEmpresa(long serial)
        {
            try
            {
                var result = _tagExtrattaService.DesvincularEmpresa(serial);
            
                if(!result.Success)
                    return BusinessResult<string>.Error(result.Messages);

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }
        
        public BusinessResult<GridFaturamentoTagResponse> GridFaturamento(int? take, int? page,OrderFilters order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor)
        {
            try
            {
                // Ajuste na data para consultar o mês inteiro
                var ultimoDia = DateTime.DaysInMonth(dataFim.Year, dataFim.Month);

                dataInicio = new DateTime(dataInicio.Year, dataInicio.Month, 1, 00, 00, 00);
                dataFim = new DateTime(dataFim.Year, dataFim.Month, ultimoDia, 23, 59, 59);
                
                var result = _tagExtrattaService.ConsultaFaturamento(take,page,filters,order,dataInicio,dataFim,fornecedor);

                if(!result.Success)
                    return BusinessResult<GridFaturamentoTagResponse>.Error(result.Messages);

                return BusinessResult<GridFaturamentoTagResponse>.Valid(new GridFaturamentoTagResponse()
                {
                    items = result.Value.Itens.AsQueryable().ProjectTo<FaturamentoTagItemResponse>().ToList(),
                    totalItems = result.Value.CountItens
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<GridFaturamentoTagResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult<FaturamentoTotalizadorResponse> GetTotalizadorFaturamento(FaturamentoTotalizadorRequest request)
        {
            try
            {
                // Ajuste na data para consultar o mês inteiro
                var ultimoDia = DateTime.DaysInMonth(request.DataFim.Year, request.DataFim.Month);

                request.DataInicio = new DateTime(request.DataInicio.Year, request.DataInicio.Month, 1, 00, 00, 00);
                request.DataFim = new DateTime(request.DataFim.Year, request.DataFim.Month, ultimoDia, 23, 59, 59);
                
                var result = _tagExtrattaService.ConsultaTotalizadorFaturamento(Mapper.Map<FaturamentoTotalizadorGetRequest>(request));

                if(!result.Success)
                    return BusinessResult<FaturamentoTotalizadorResponse>.Error(result.Messages);

                return BusinessResult<FaturamentoTotalizadorResponse>.Valid(Mapper.Map<FaturamentoTotalizadorResponse>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<FaturamentoTotalizadorResponse>.Error(e.Message);
            }
        }
        
        public byte[] GerarRelatorioGridFaturamento(OrderFilters order, List<QueryFilters> filters, string extensao,DateTime dataInicio,DateTime dataFim,FornecedorEnum fornecedor)
        {
            try
            { 
                var dataGrid = GridFaturamento(null,null,order,filters,dataInicio,dataFim,fornecedor);
                var totalizador = GetTotalizadorFaturamento(new FaturamentoTotalizadorRequest()
                {
                    DataInicio = dataInicio,
                    DataFim = dataFim
                });
                
                if(!dataGrid.Success || !totalizador.Success)
                    return new byte[] { };
                
                return _tagExtrattaService.GerarRelatorioGridFaturamento(dataGrid.Value.items,totalizador.Value,extensao);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<FaturaTagGetResponse> ConsultaFatura(DateTime dataInicio, DateTime dataFim, int empresaId, FornecedorTagEnum fornecedorTag)
        {
            try
            {
                var result = _tagExtrattaService.ConsultaFatura(dataInicio,dataFim,empresaId, fornecedorTag);

                if(!result.Success)
                    return BusinessResult<FaturaTagGetResponse>.Error(result.Messages);

                return BusinessResult<FaturaTagGetResponse>.Valid(Mapper.Map<FaturaTagGetResponse>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<FaturaTagGetResponse>.Error(e.Message);
            }
        }

        public BusinessResult<string> PagamentoEventoTag(PagamentoTagRequest request)
        {
            try
            {
                if(request.PagarViaCargaAvulsa)
                {
                    if (request.TipoTransacao == ETipoTransacaoTag.Cobranca)
                    { 
                        var cargaAvulsa = _cargaAvulsaRepository
                            .Include(x => x.Empresa)
                            .Where(x => x.EventoSaldoTagId == request.Id);

                        if (request.Tipo == ETipoPagamento.Taxa)
                            cargaAvulsa = cargaAvulsa.Where(x => x.Valor == request.Taxa);
                        else
                            cargaAvulsa = cargaAvulsa.Where(x => x.Valor == request.Valor);

                        var resultDb = cargaAvulsa.Select(x => new
                        {
                            x.IdCargaAvulsa
                        }).ToList().FirstOrDefault();
                        
                        if (resultDb == null)
                        {
                            var cargaAvulsaAdd = new ProvisionarRequest()
                            {
                                EventoSaldoTagId = request.Id,
                                Origem = request.IdViagem.HasValue
                                    ? OrigemProvisionamento.ValePedagio
                                    : OrigemProvisionamento.Pedagio,
                                Recibo = request.Recibo,
                                IdViagem = request.IdViagem,
                                IdEmpresa = request.IdEmpresa,
                                CnpjEmpresa = request.CnpjEmpresa,
                                Praca = request.Praca,
                                Placa = request.Placa.FormatarPlaca(),
                                ValorTaxa = request.Taxa,
                                Valor = request.Valor,
                                Fornecedor = FornecedorEnum.ExtrattaTag
                            };

                            if (request.Tipo == ETipoPagamento.Taxa)
                            {
                                var resultProvisionamento = _cargaAvulsaApp.ProvisionarTaxa(cargaAvulsaAdd);

                                if (!resultProvisionamento.Success)
                                    return BusinessResult<string>.Error(resultProvisionamento.Messages);
                            }
                            else
                            {
                                var resultProvisionamento = _cargaAvulsaApp.ProvisionarValor(cargaAvulsaAdd);

                                if (!resultProvisionamento.Success)
                                    return BusinessResult<string>.Error(resultProvisionamento.Messages);
                            }
                        }
                        else
                        {
                            var resultReprocessamento = _cargaAvulsaApp.ReprocessarCargaAvulsa(resultDb.IdCargaAvulsa);

                            if (!resultReprocessamento.IsValid)
                                return BusinessResult<string>.Error(resultReprocessamento.Errors.FirstOrDefault()?.Message);

                        }
                        
                    }
                    else
                    {
                        var cargaAvulsaEstorno = _cargaAvulsaRepository
                            .Include(x => x.Empresa)
                            .Where(x => x.EventoSaldoTagId == request.Id);

                        if (request.Tipo == ETipoPagamento.Taxa)
                            cargaAvulsaEstorno = cargaAvulsaEstorno.Where(x => x.Valor == request.Taxa);
                        else
                            cargaAvulsaEstorno = cargaAvulsaEstorno.Where(x => x.Valor == request.Valor);

                        var resultDb = cargaAvulsaEstorno.Select(x => new
                        {
                            x.IdCargaAvulsa,
                            x.Empresa.CNPJ
                        }).ToList().FirstOrDefault();
                          
                        if(resultDb == null)
                            return BusinessResult<string>.Error("Carga Avulsa não localizada!");

                        var resultEstorno =
                            _cargaAvulsaApp.EstornarCargaAvulsa(resultDb.IdCargaAvulsa, null, resultDb.CNPJ);
                        
                        if(!resultEstorno.Sucesso)
                            return BusinessResult<string>.Error(resultEstorno.Mensagem);
                    }
                }
                else
                {
                    var result = _tagExtrattaService.PagamentoManualEventoTag(Mapper.Map<PagamentoManualRequest>(request));

                    if(!result.Success)
                        return BusinessResult<string>.Error(result.Messages);
                }

                return BusinessResult<string>.Valid("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<string>.Error(e.Message);
            }
        }

        public BusinessResult<PlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorEnum fornecedor)
        {
            try
            {
                if (fornecedor != FornecedorEnum.MoveMais)
                    return BusinessResult<PlacaFornecedorResponse>.Valid(new PlacaFornecedorResponse { 
                        Status = StatusPlaca.Indisponivel,
                        Mensagem = "Fornecedor atualmente indisponivel para consulta da placa"
                    });
                return _tagExtrattaService.ConsultarPlacasFornecedor(placa, fornecedor);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<PlacaFornecedorResponse>.Error(e.Message);
            }
        }

        public byte[] GerarFatura(FaturaRequest request)
        {
            try
            { 
                var ultimoDia = DateTime.DaysInMonth(request.DataFim.Year, request.DataFim.Month);

                request.DataInicio = new DateTime(request.DataInicio.Year, request.DataInicio.Month, 1, 00, 00, 00);
                request.DataFim = new DateTime(request.DataFim.Year, request.DataFim.Month, ultimoDia, 23, 59, 59);
                var fornecedorTag = (FornecedorTagEnum)request.FornecedorTag;

                var dataGrid = ConsultaFatura(request.DataInicio,request.DataFim,request.EmpresaId, fornecedorTag);
                
                if(!dataGrid.Success)
                    return new byte[] { };
                
                return _tagExtrattaService.GerarRelatorioFatura(dataGrid.Value, fornecedorTag);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }
        
        public BusinessResult<PracasPedagioResponseDTO> ConsultarPassagensPedagioCompraHub(int compraId,FornecedorEnum fornecedor)
        {
            try
            {
                var result = _tagExtrattaService.GetPassagensPedagioCompraHub(compraId,(FornecedorTagEnum)fornecedor);

                if(!result.Success)
                    return BusinessResult<PracasPedagioResponseDTO>.Error(result.Messages);

                return BusinessResult<PracasPedagioResponseDTO>.Valid(Mapper.Map<PracasPedagioResponseDTO>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<PracasPedagioResponseDTO>.Error(e.Message);
            }
        }
        
        public BusinessResult<SaldoValePedagioVeiculoTagResponse> ConsultarSaldoValePedagioVeiculo(string placa)
        {
            try
            {
                var result = _tagExtrattaService.ConsultarSaldoValePedagioVeiculo(placa.RemoveSpecialCharacters());
            
                if(!result.Success)
                    return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Error(result.Messages);

                return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Valid(result.Value);
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Error(e.Message);
            }
        }

        public BusinessResult<ConsultarSituacaoTagResponseModel> ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null)
                    return BusinessResult<ConsultarSituacaoTagResponseModel>.Error("Empresa não encontrada.");
                
                if(placa.Length != 7)
                    return BusinessResult<ConsultarSituacaoTagResponseModel>.Error($"Placa {placa} informada com formato inválido.");

                if (fornecedor == Fornecedor2.ConectCar)
                    placa = placa.FormatarPlaca();
                
                placa = placa.ToUpper();

                if (fornecedor == Fornecedor2.ExtrattaTag)
                {
                    var veiculo = GetVeiculo(placa);
                    var retorno = new ConsultarSituacaoTagResponseModel
                    {
                        Fornecedor = ConsultarSituacaoTagPlacaResponseFornecedor.ExtrattaTag,
                        Sucesso = true,
                        TagAtiva = veiculo.Success && veiculo.Value?.TagId != null
                    };
                    if (!veiculo.Success) retorno.MensagemStatusTag = veiculo.Messages?.FirstOrDefault();
                    return BusinessResult<ConsultarSituacaoTagResponseModel>.Valid(retorno);
                }

                var token = _empresaService.All().Where(c => c.IdEmpresa == _userIdentity.IdEmpresa.Value)
                    .Select(c => c.TokenMicroServices).FirstOrDefault();
                if (string.IsNullOrWhiteSpace(token))
                    return BusinessResult<ConsultarSituacaoTagResponseModel>.Error($"Empresa sem autenticação para consultar o serviço de pedágio.");
                var pedagioService = new PedagioService(_userIdentity.IdEmpresa, token,
                    string.IsNullOrWhiteSpace(_userIdentity.CpfCnpj) ? "***********" : _userIdentity.CpfCnpj,
                    _userIdentity.Nome, _viagemRepository);
                var result = pedagioService.ConsultarSituacaoTagPlaca(placa, (Fornecedor2) fornecedor);
            
                if(!result.Sucesso)
                    return BusinessResult<ConsultarSituacaoTagResponseModel>.Error(result.MensagemRetorno);

                return BusinessResult<ConsultarSituacaoTagResponseModel>.Valid(result);
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<ConsultarSituacaoTagResponseModel>.Error(e.Message);
            }
        }
        
        public BusinessResult ContestarPassagem(ContestarPassagemRequest request)
        {
            try
            {
                var requestApi = Mapper.Map<ContestacaoPassagemPedagioModelRequest>(request);
                var result = _tagExtrattaService.ContestarPassagem(requestApi);

                if(!result.Success)
                    return BusinessResult.Error(result.Messages);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult<PracasPedagioVeiculoResponseDTO> ConsultarPassagensVeiculo(DateTime? dataInicio,DateTime? dataFim, string placa)
        {
            try
            {
                var result = _tagExtrattaService.GetPassagensVeiculoPraca(dataInicio,dataFim,placa.RemoveSpecialCharacters());

                if(!result.Success)
                    return BusinessResult<PracasPedagioVeiculoResponseDTO>.Error(result.Messages);

                var response = Mapper.Map<PracasPedagioVeiculoResponseDTO>(result.Value);

                var saldoResult = ConsultarSaldoValePedagioVeiculo(placa);

                if (saldoResult.Success)
                    response.SaldoVP = saldoResult.Value.Saldo;
                
                return BusinessResult<PracasPedagioVeiculoResponseDTO>.Valid(response);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaApp)} / {e.Message}");
                return BusinessResult<PracasPedagioVeiculoResponseDTO>.Error(e.Message);
            }
        }
    }
}
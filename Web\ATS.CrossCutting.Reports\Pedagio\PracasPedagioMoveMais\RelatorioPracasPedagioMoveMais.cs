﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Pedagio.PracasPedagioMoveMais
{
    public class RelatorioPracasPedagioMoveMais
    {
        public byte[] GetReport(string tipo, RelatorioPracasPedagioMoveMaisDataType dadosRelatorio, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaPracasPedagioMoveMais"
                });

                var path = ReportUtils.CreateLogo(logo);

                var parametros = new List<ReportParameter>();
                parametros.Add(new ReportParameter("Logo", "file:///" + path));
                parametros.Add(new ReportParameter("ValorTotalPassagem", dadosRelatorio.ValorTotalPassagem));
                parametros.Add(new ReportParameter("ValorTotalEstimado", dadosRelatorio.ValorTotalEstimado));

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Pedagio.PracasPedagioMoveMais.RelatorioPracasPedagioMoveMais.rdlc";

                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

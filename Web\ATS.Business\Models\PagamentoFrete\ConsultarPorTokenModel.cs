﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Models.PagamentoFrete
{
    public class ConsultarPorTokenModel
    {
        public EStatusViagem StatusViagem { get; set; }
        public int IdEmpresa { get; set; }
        public string CPFMotorista { get; set; }
        public decimal? PesoChegada { get; set; }
        public decimal? PesoSaida { get; set; }
        public string RazaoSocialFilial { get; set; }
        public string NomeMotorista { get; set; }
        public string CNHMotorista { get; set; }
        public string NomeProprietario { get; set; }
        public string CPFCNPJProprietario { get; set; }
        public string Placa { get; set; }
        public string Coleta { get; set; }
        public string Entrega { get; set; }
        public string Origem { get; set; }
        public string NumeroDocumento { get; set; }
        public DateTime? DataEmissao { get; set; }
        public decimal? ValorMercadoria { get; set; }
        public string Produto { get; set; }
        public decimal Quantidade { get; set; }
        public EUnidadeMedida Unidade { get; set; }
        public decimal INSS { get; set; }
        public decimal SESTSENAT { get; set; }
        public decimal IRRPF { get; set; }
        public IEnumerable<ConsultaPorTokenViagemRegrasModel> ViagemRegras { get; set; }
        public List<string> Carretas { get; set; }
        public bool PedagioBaixado { get; set; }
        public decimal ValorPedagio { get; set; }
        public string NumeroCartao { get; set; }
        public bool ValidaChaveBaixaEvento { get; set; }
        public bool ValidaChaveMHBaixaEvento { get; set; }
        public int TempoValidadeChave { get; set; }
        public string NomeFantasiaEmpresa { get; set; }
        public string Remetente { get; set; }
        public string Destinatario { get; set; }
        public string Tomador { get; set; }
        public double? PagamentoFreteToleranciaPesoChegadaMais { get; set; }
        public double? PagamentoFreteToleranciaPesoChegadaMenos { get; set; }
        public string TokenMicroServices { get; set; }
        public decimal? PesoChegadaTriagem { get; set; }
        public ConsultaPortokenProprietarioModel Proprietario { get; set; }
        public int IdViagem { get; set; }
        public DateTime? DataDescarga { get; set; }
        public IEnumerable<ConsultaPortokenViagemEstabelecimentoModel> ViagemEstabelecimentos { get; set; }
    }

    public class ConsultaPorTokenViagemRegrasModel
    {
        public ETipoQuebraMercadoria TipoQuebraMercadoria { get; set; }
        public decimal? ToleranciaPeso { get; set; }
        public decimal? TarifaTonelada { get; set; }
        public decimal? TotalFrete { get; set; }
        public bool FreteLotacao { get; set; }
        public string UnidadeMedida { get; set; }
    }

    public class ConsultaPortokenProprietarioModel
    {
        public int? IdProprietario { get; set; }
        public string CNPJCPF { get; set; }
        public string NomeFantasia { get; set; }
    }
    
    public class ConsultaPortokenViagemEstabelecimentoModel
    {
        public int? IdEstabelecimento { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class CteService : ServiceBase, ICteService
    {
        private readonly ICteRepository _cteRepository;

        public CteService(ICteRepository cteRepository)
        {
            _cteRepository = cteRepository;
        }

        public IQueryable<Cte> ConsultaCte(string cpfMotorista, DateTime dataInicial, DateTime? dataFinal)
        {
            if (dataFinal.HasValue)
            {
                dataFinal = dataFinal.Value.AddDays(1);
                dataFinal = dataFinal.Value.AddTicks(-1);
                return _cteRepository
                    .Find(x => x.DataAlteracao >= dataInicial
                    && x.DataAlteracao <= dataFinal
                    && x.CpfMotorista == cpfMotorista);
            }
                
            return _cteRepository
                .Find(x => x.DataAlteracao >= dataInicial
                && x.CpfMotorista == cpfMotorista);
        }

        public ValidationResult Update(Cte cte)
        {
            try
            {
                cte.DataAlteracao = DateTime.Now;
                _cteRepository.Update(cte);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Add(Cte cte)
        {
            try
            {
                cte.Status = EStatusCte.Pendente;
                cte.DataAlteracao = DateTime.Now;
                _cteRepository.Add(cte);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Cte ConsultaPorId(int idCte)
        {
            return _cteRepository.Find(x => x.IdCte == idCte).FirstOrDefault();
        }

        public int GetTotalCtes(string cPFCNPJUsuario)
        {
            return _cteRepository.Find(x => x.CpfMotorista == cPFCNPJUsuario && x.Status != EStatusCte.Cancelado).Count();
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services.ViagemServices
{
    public class CartaoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;

        public CartaoViagem(IVersaoAnttLazyLoadService versaoAntt, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App)
        {
            _versaoAntt = versaoAntt;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
        }

        public KeyValuePair<bool, string> ValidarCartaoMeioHomologado(string documentoMotorista,
            string documentoProprietario, int idEmpresa, ICartoesApp cartoesApp,
            out CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, out CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, 
            IList<ETipoEventoViagem> eventosViagem, bool isApi = false, bool buscarCartoesBloqueados = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return ValidarCartaoMeioHomologadoV2(documentoMotorista, documentoProprietario, idEmpresa, cartoesApp,
                        out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, eventosViagem,isApi,buscarCartoesBloqueados);
                case EVersaoAntt.Versao3:
                    return ValidarCartaoMeioHomologadoV3(documentoMotorista, documentoProprietario, idEmpresa, cartoesApp,
                        out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, eventosViagem, buscarCartoesBloqueados);
                default:
                    return ValidarCartaoMeioHomologadoV2(documentoMotorista, documentoProprietario, idEmpresa, cartoesApp,
                        out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, eventosViagem,isApi,buscarCartoesBloqueados);
            }
        }

        public KeyValuePair<bool, string> ValidarSaldoCartao(string documentoPortador, int idEmpresa, decimal valorTotal, ICartoesApp cartoesApp)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return ValidarSaldoCartaoV2(documentoPortador, idEmpresa, valorTotal, cartoesApp);
                case EVersaoAntt.Versao3:
                    return ValidarSaldoCartaoV3(documentoPortador, idEmpresa, valorTotal, cartoesApp);
                default:
                    return ValidarSaldoCartaoV2(documentoPortador, idEmpresa, valorTotal, cartoesApp);
            }
        }

        #region Validação Cartão

        private KeyValuePair<bool, string> ValidarCartaoMeioHomologadoV2(string documentoMotorista,
            string documentoProprietario, int idEmpresa, ICartoesApp cartoesApp, 
            out CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, out CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, 
            IList<ETipoEventoViagem> eventosViagem,bool isApi = false, bool buscarCartoesBloqueados = false)
        {
            var retorno = new KeyValuePair<bool, string>(true, string.Empty);
            cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
            cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();

            var produtos = new List<int>();
            produtos.Add(cartoesApp.GetIdProdutoCartaoFretePadrao());

            #region Cartão do proprietário

            if (!string.IsNullOrWhiteSpace(documentoProprietario))
            {
                if (documentoMotorista.OnlyNumbers() == documentoProprietario.OnlyNumbers()
                          && documentoMotorista.OnlyNumbers().Length == 11)
                {
                    cartaoVinculadoProprietario = cartoesApp.GetCartoesVinculados(documentoProprietario, produtos, 
                        true, buscarCartoesBloqueados:buscarCartoesBloqueados);
                }
                else
                {
                    cartaoVinculadoProprietario = cartoesApp.GetCartoesVinculados(documentoProprietario, produtos, 
                        documentoProprietario != documentoMotorista, buscarCartoesBloqueados:buscarCartoesBloqueados);
                }

                if (!cartaoVinculadoProprietario.Cartoes.Any()) 
                    retorno = new KeyValuePair<bool, string>(false, "Proprietario nao tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");
            }

            #endregion

            if (eventosViagem.Any())
            {
                var hasTransferencia = _parametrosApp.HasTransferenciaFrete(eventosViagem, documentoProprietario, idEmpresa, documentoMotorista) ?? false;

                if (hasTransferencia)
                {
                    #region Cartão do motorista

                    cartaoVinculadoMotorista = cartoesApp.GetCartoesVinculados(StringExtension.OnlyNumbers(documentoMotorista), produtos, buscarCartoesBloqueados:buscarCartoesBloqueados);

                    if (!cartaoVinculadoMotorista.Cartoes.Any()) 
                        retorno = new KeyValuePair<bool, string>(false, "Motorista não tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");

                    #endregion
                }
            }

            return retorno;
        }
        
        private KeyValuePair<bool, string> ValidarCartaoMeioHomologadoV3(string documentoMotorista,
            string documentoProprietario, int idEmpresa, ICartoesApp cartoesApp, 
            out CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, out CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, 
            IList<ETipoEventoViagem> eventosViagem, bool buscarCartoesBloqueados = false)
        {
            var retorno = new KeyValuePair<bool, string>(true, string.Empty);
            cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
            cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();

            var produtos = new List<int>();
            produtos.Add(cartoesApp.GetIdProdutoCartaoFretePadrao());

            #region Cartão do proprietário

            if (!string.IsNullOrWhiteSpace(documentoProprietario))
            {
                cartaoVinculadoProprietario = cartoesApp.GetCartoesVinculados(documentoProprietario, produtos,
                    documentoProprietario != documentoMotorista);

                if (!cartaoVinculadoProprietario.Cartoes.Any())
                    retorno = new KeyValuePair<bool, string>(false,
                        "Proprietario nao tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");
            }

            #endregion

            if (eventosViagem.Any())
            {
                var hasTransferencia =
                    _parametrosApp.HasTransferenciaFrete(eventosViagem, documentoProprietario, idEmpresa, documentoMotorista) ?? false;

                if (hasTransferencia)
                {
                    #region Cartão do motorista

                    cartaoVinculadoMotorista =
                        cartoesApp.GetCartoesVinculados(StringExtension.OnlyNumbers(documentoMotorista), produtos, buscarCartoesBloqueados:buscarCartoesBloqueados);

                    if (!cartaoVinculadoMotorista.Cartoes.Any())
                        retorno = new KeyValuePair<bool, string>(false,
                            "Motorista não tem cartao vinculado a processadora ou cartão encontra-se bloqueado. Em caso de dúvidas entre em contato com suporte!");

                    #endregion
                }
            }

            return retorno;
        }

        #endregion

        #region Validação Saldo Cartão

        private KeyValuePair<bool, string> ValidarSaldoCartaoV2(string documentoPortador, int idEmpresa, decimal valorTotal, ICartoesApp cartoesApp)
        {
            var saldoProprietario = cartoesApp.ConsultarSaldoCartao(documentoPortador);
            
            if(saldoProprietario.Status == ConsultarSaldoCartaoResponseStatus.Falha)
                return new KeyValuePair<bool, string>(false, $"Erro ao consultar saldo do cartão: {saldoProprietario.Mensagem}.");

            if (saldoProprietario.ValorSaldoDisponivel < valorTotal) return new KeyValuePair<bool, string>(false, $"Saldo do cartão do portador {documentoPortador} insuficiente.");

            return new KeyValuePair<bool, string>(true, string.Empty);
        }
        
        private KeyValuePair<bool, string> ValidarSaldoCartaoV3(string documentoPortador, int idEmpresa, decimal valorTotal, ICartoesApp cartoesApp)
        {
            var saldoProprietario = cartoesApp.ConsultarSaldoCartao(documentoPortador);
            
            if(saldoProprietario.Status == ConsultarSaldoCartaoResponseStatus.Falha)
                return new KeyValuePair<bool, string>(false, $"Erro ao consultar saldo do cartão: {saldoProprietario.Mensagem}.");

            if (saldoProprietario.ValorSaldoDisponivel < valorTotal)
                return new KeyValuePair<bool, string>(false,
                    $"Saldo do cartão do portador {documentoPortador} insuficiente.");

            return new KeyValuePair<bool, string>(true, string.Empty);
        }

        #endregion
    }
}
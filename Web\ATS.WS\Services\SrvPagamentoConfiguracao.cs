﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.WS.Models.Common;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;

namespace ATS.WS.Services
{
    public class SrvPagamentoConfiguracao : SrvBase
    {
        private readonly IPagamentoConfiguracaoApp _pagamentoConfiguracaoApp;
        private readonly IPagamentoConfiguracaoProcessoApp _pagamentoConfiguracaoProcessoApp;
        private readonly IUserIdentity _userIdentity;

        public SrvPagamentoConfiguracao(IPagamentoConfiguracaoApp pagamentoConfiguracaoApp, IPagamentoConfiguracaoProcessoApp pagamentoConfiguracaoProcessoApp, IUserIdentity userIdentity)
        {
            _pagamentoConfiguracaoApp = pagamentoConfiguracaoApp;
            _pagamentoConfiguracaoProcessoApp = pagamentoConfiguracaoProcessoApp;
            _userIdentity = userIdentity;
        }

        /// <summary>
        /// Retorna o pagamento es documento vínculados
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<PagamentoConfiguracaoModel> ConsultarPorId(int IdPagamentoConfiguracao )
        {
            try
            {
                var data = _pagamentoConfiguracaoApp.ConsultarPorId(IdPagamentoConfiguracao);

                var dataModel = new PagamentoConfiguracaoModel()
                {
                    IdPagamentoConfiguracao = data.IdPagamentoConfiguracao,
                    IdEmpresa = data.IdEmpresa,
                    IdFilial = data.IdFilial,
                    RazaoSocialEmpresa = data.Empresa?.RazaoSocial,
                    RazaoSocialFilial = data.Filial?.RazaoSocial,
                    TaxaAntecipacao = data.ValorTaxa,
                    DoctosAntecipacao = new List<DocumentoModel>(),
                    DoctosCredenciamento= new List<DocumentoModel>(),
                    DoctosPgtoFrete = new List<DocumentoModel>(),
                    DoctosProtocolo = new List<DocumentoModel>(),
                    BonificarMotorista = data.BonificarMotorista
                };


                dataModel.DoctosAntecipacao = _pagamentoConfiguracaoProcessoApp.GetItemsByProcess(data.IdPagamentoConfiguracao, EProcessoPgtoFrete.Anteciapacao)?
                    .Select(x => new DocumentoModel() { IdDocumento = x.Documento.IdDocumento, Descricao = x.Documento.Descricao })
                    .ToList();

                dataModel.DoctosCredenciamento = _pagamentoConfiguracaoProcessoApp.GetItemsByProcess(data.IdPagamentoConfiguracao, EProcessoPgtoFrete.Credenciamento)?
                    .Select(x => new DocumentoModel() { IdDocumento = x.Documento.IdDocumento, Descricao = x.Documento.Descricao })
                    .ToList();

                dataModel.DoctosPgtoFrete = _pagamentoConfiguracaoProcessoApp.GetItemsByProcess(data.IdPagamentoConfiguracao, EProcessoPgtoFrete.PagamentoFrete)?
                    .Select(x => new DocumentoModel() { IdDocumento = x.Documento.IdDocumento, Descricao = x.Documento.Descricao })
                    .ToList();

                dataModel.DoctosProtocolo = _pagamentoConfiguracaoProcessoApp.GetItemsByProcess(data.IdPagamentoConfiguracao, EProcessoPgtoFrete.Protocolo)?
                    .Select(x => new DocumentoModel() { IdDocumento = x.Documento.IdDocumento, Descricao = x.Documento.Descricao })
                    .ToList();

                return new Retorno<PagamentoConfiguracaoModel>(true, string.Empty, dataModel);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<PagamentoConfiguracaoModel>($"{nameof(ConsultarPorId)} >> {e.Message}");
            }
        }
        
        public void Cadastrar(PagamentoConfiguracaoModel model)
        {
            try
            {
                PagamentoConfiguracao configuracao = new PagamentoConfiguracao();
                
                configuracao.IdEmpresa = model.IdEmpresa;
                configuracao.IdFilial = model.IdFilial;
                configuracao.PagamentoConfiguracoesProcesso = new List<PagamentoConfiguracaoProcesso>();
                configuracao.ValorTaxa = model.TaxaAntecipacao;
                configuracao.BonificarMotorista = model.BonificarMotorista;

                if (model.DoctosCredenciamento != null && model.DoctosCredenciamento.Count > 0)
                {
                    foreach (var docto in model.DoctosCredenciamento)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Credenciamento

                        });
                    }
                }

                if (model.DoctosAntecipacao != null && model.DoctosAntecipacao.Count > 0)
                {
                    foreach (var docto in model.DoctosAntecipacao)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Anteciapacao
                        });
                    }
                }

                if (model.DoctosPgtoFrete != null && model.DoctosPgtoFrete.Count > 0)
                {
                    foreach (var docto in model.DoctosPgtoFrete)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.PagamentoFrete
                        });
                    }
                }

                if (model.DoctosProtocolo != null && model.DoctosProtocolo.Count > 0)
                {
                    foreach (var docto in model.DoctosProtocolo)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Protocolo
                        });
                    }
                }
                _pagamentoConfiguracaoApp.Add(configuracao);
            } catch(Exception e)
            {
                _logger.Error(e);
                throw e;
            }
        }

        public void Atualizar(PagamentoConfiguracaoModel model)
        {
            try
            {
                PagamentoConfiguracao configuracao = _pagamentoConfiguracaoApp.ConsultarPorId(model.IdPagamentoConfiguracao);

                if (_userIdentity.Perfil != (int)EPerfil.Administrador && model.IdFilial != configuracao.IdFilial)
                    throw new InvalidOperationException("Usuário não autenticado.");
                
                configuracao.IdEmpresa = model.IdEmpresa;
                configuracao.IdFilial = model.IdFilial;
                configuracao.PagamentoConfiguracoesProcesso = new List<PagamentoConfiguracaoProcesso>();
                configuracao.ValorTaxa = model.TaxaAntecipacao;
                configuracao.BonificarMotorista = model.BonificarMotorista;
                
                if (model.DoctosCredenciamento != null && model.DoctosCredenciamento.Count > 0)
                {
                    foreach (var docto in model.DoctosCredenciamento)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdConfiguracao = model.IdPagamentoConfiguracao,
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Credenciamento

                        });
                    }
                }

                if (model.DoctosAntecipacao != null && model.DoctosAntecipacao.Count > 0)
                {
                    foreach (var docto in model.DoctosAntecipacao)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdConfiguracao = model.IdPagamentoConfiguracao,
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Anteciapacao
                        });
                    }
                }

                if (model.DoctosPgtoFrete != null && model.DoctosPgtoFrete.Count > 0)
                {
                    foreach (var docto in model.DoctosPgtoFrete)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdConfiguracao = model.IdPagamentoConfiguracao,
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.PagamentoFrete
                        });
                    }
                }

                if (model.DoctosProtocolo != null && model.DoctosProtocolo.Count > 0)
                {
                    foreach (var docto in model.DoctosProtocolo)
                    {
                        configuracao.PagamentoConfiguracoesProcesso.Add(new PagamentoConfiguracaoProcesso()
                        {
                            IdConfiguracao = model.IdPagamentoConfiguracao,
                            IdDocumento = docto.IdDocumento,
                            Processo = EProcessoPgtoFrete.Protocolo
                        });
                    }
                }

                
                _pagamentoConfiguracaoApp.Update(configuracao);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw e;
            }
        }
    }
}
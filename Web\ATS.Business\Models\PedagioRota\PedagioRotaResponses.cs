using System.Collections.Generic;
using ATS.Domain.Validation;

namespace ATS.Domain.Models.PedagioRota
{
    public class PedagioRotaSalvarResponse
    {
        public ValidationResult Resultado { get; set; } = new ValidationResult();
        public int Id { get; set; }
        public string Descricao { get; set; }
    }

    public class PedagioRotaGridResponse
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public string Origem { get; set; }
        public string Destino { get; set; }
    }

    public class PedagioRotaDetalheResponse
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public IList<PedagioRotaPontoDetalheResponse> Pontos { get; set; }
    }

    public class PedagioRotaPontoDetalheResponse
    {
        public int Sequencia { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Cidade { get; set; }
        public string Estado { get; set; }
        public string Pais { get; set; }
    }
}
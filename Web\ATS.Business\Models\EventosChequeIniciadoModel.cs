using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class EventosChequeIniciadoModel
    {
        public string Token { get; set; }

        public string DescricaoTipoEvento { get; set; }

        public decimal PesoSaida { get; set; }

        public decimal PesoChegada { get; set; }

        public decimal Valor { get; set; }

        public int IdProprietario { get; set; }

        public string CpfCnpjProprietario { get; set; }

        public string NomeProprietario { get; set; }

        public string NomeMotorista { get; set; }

        public int? IdPagamentoChequeAgrupador { get; set; }
    }
}

﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class PontosRotaModelo
    {
        public int IdPonto { get; set; }
        public int IdRotaModelo { get; set; }
        public string Descricao { get; set; }
        public decimal Ibge { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        #region Referências

        [ForeignKey("IdRotaModelo")]
        public virtual RotaModelo RotaModelo { get; set; }

        #endregion
    }
}
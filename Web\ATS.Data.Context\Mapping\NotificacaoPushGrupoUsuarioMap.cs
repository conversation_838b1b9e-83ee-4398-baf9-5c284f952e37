﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class NotificacaoPushGrupoUsuarioMap : EntityTypeConfiguration<NotificacaoPushGrupoUsuario>
    {
        public NotificacaoPushGrupoUsuarioMap()
        {
            ToTable("NOTIFICACAO_PUSH_GRUPO_USUARIO");

            HasKey(x => new { x.IdGrupoUsuario, x.IdNotificacaoPush });

            HasRequired(x => x.GrupoUsuario)
                .WithMany(x => x.NotificacaoPushGrupoUsuario)
                .HasForeignKey(x => x.IdGrupoUsuario);

            HasRequired(x => x.NotificacaoPush)
                .WithMany(x => x.NotificacaoPushGrupoUsuario)
                .HasForeignKey(x => x.IdNotificacaoPush);
        }
    }
}

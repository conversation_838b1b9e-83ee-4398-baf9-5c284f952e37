﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using ATS.Domain.Interface.Service;
using System;
using System.Linq;
using System.Collections.Generic;

namespace ATS.Domain.Service
{
    public class UsuarioFilialService : ServiceBase, IUsuarioFilialService
    {
        private readonly IUsuarioFilialRepository _usuarioFilialRepository;

        public UsuarioFilialService(IUsuarioFilialRepository usuarioFilialRepository)
        {
            _usuarioFilialRepository = usuarioFilialRepository;
        }

        /// <summary>
        /// Retorna todos as filiais de um determinado usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public IQueryable<UsuarioFilial> GetFiliaisPorIdUsuario(int? idUsuario)
        {
            return _usuarioFilialRepository.GetFiliaisPorIdUsuario(idUsuario);
        }

        /// <summary>
        /// Retorna a única filial do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public int GetFilialPorIdUsuario(int? idUsuario)
        {
            return _usuarioFilialRepository
                            .Find(x => x.IdUsuario == idUsuario)
                            .Select(x => x.IdFilial)
                            .FirstOrDefault();
        }

    }
}
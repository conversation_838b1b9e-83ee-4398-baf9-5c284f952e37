﻿using System;
using System.Collections.Generic;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Webservice.Request.Cartoes
{
    public class CartaoConsultarPedagioAtsRequest : RequestBase
    {
        public int? IdCidadeOrigem { get; set; }
        public int? IdCidadeDestino { get; set; }
        public DateTime? DataInicio { get; set; }
        public DateTime? DataFim { get; set; }
        public List<StatusCompraDTO> StatusCompraList { get; set; }
    }

    public class StatusCompraDTO
    {
        public CompraStatusTipo Valor { get; set; }
        public string Descricao { get; set; }
    }
}

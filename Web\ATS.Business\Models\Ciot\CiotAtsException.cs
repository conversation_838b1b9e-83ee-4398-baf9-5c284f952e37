using System;
using System.Runtime.Serialization;

namespace ATS.Domain.Models.Ciot
{
    public class CiotAtsException : Exception
    {
        public CiotAtsException()
        {
        }

        public CiotAtsException(string message) : base(message)
        {
        }

        public CiotAtsException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected CiotAtsException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoCarretaApp : AppBase, ITipoCarretaApp
    {
        private readonly ITipoCarretaService _tipoCarretaService;

        public TipoCarretaApp(ITipoCarretaService tipoCarretaService)
        {
            _tipoCarretaService = tipoCarretaService;
        }

        /// <summary>
        /// Retorna o registro
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TipoCarreta Get(int id)
        {
            return _tipoCarretaService.Get(id);
        }

        /// <summary>
        /// Retorna apenas o objeto de tipo carreta
        /// </summary>
        /// <param name="id"></param>
        /// <returns>TipoCarreta</returns>
        public TipoCarreta GetTipoCarreta(int id)
        {
            return _tipoCarretaService.GetTipoCarreta(id);
        }

        /// <summary>
        /// Retorna uma lista de tipos de cavalo ativos
        /// </summary>
        /// <returns>Lista de TipoCavalo</returns>
        public List<TipoCarreta> GetTodos(int? aIdEmpresa = null)
        {
            return _tipoCarretaService.All().Where(x => x.Ativo && (aIdEmpresa == null ||
                                                                         (aIdEmpresa != null &&
                                                                          x.IdEmpresa == aIdEmpresa
                                                                         ))).ToList();
        }

        public List<TipoCarreta> GetTiposCarretaPorEmpresa(int? aIdEmpresa = null)
        {
            var tiposCarreta = _tipoCarretaService.All().Where(x => x.Ativo);
            if (aIdEmpresa.HasValue)
                return tiposCarreta.Where(x => x.IdEmpresa == aIdEmpresa.Value).OrderBy(x => x.Nome).ToList();

            return tiposCarreta.Where(x => x.IdEmpresa == null).ToList();
        }

        /// <summary>
        /// Adicionar 
        /// </summary>
        /// <param name="tipoCarreta"></param>
        /// <returns></returns>
        public ValidationResult Add(TipoCarreta tipoCarreta)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoCarretaService.Add(tipoCarreta);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro
        /// </summary>
        /// <param name="tipoCarreta"></param>
        /// <returns></returns>
        public ValidationResult Update(TipoCarreta tipoCarreta)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _tipoCarretaService.Update(tipoCarreta);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para consultar Tipo de Carreta.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Carreta.</param>
        /// <param name="idEmpresa"></param>
        /// <returns>IQueryable de TipoCarretaGrid</returns>
        public IQueryable<TipoCarretaGrid> Consultar(string nome, int? idEmpresa)
        {
            return _tipoCarretaService.Consultar(nome, idEmpresa);
        }

        /// <summary>
        /// Inativar o tipo de carreta
        /// </summary>
        /// <param name="idTipoCarreta">Código do tipo de carreta a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idTipoCarreta)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoCarretaService.Inativar(idTipoCarreta);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar o tipo de carreta
        /// </summary>
        /// <param name="idTipoCarreta">Código do tipo de carreta a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idTipoCarreta)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoCarretaService.Reativar(idTipoCarreta);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public object ConsultarSemEmpresa()
        {
            return _tipoCarretaService.ConsultarSemEmpresa();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Carreta por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Carreta</param>
        /// <returns>IQueryable de Tipo de Carreta</returns>
        public IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria)
        {
            try
            {
                return _tipoCarretaService.GetPorCategoria(categoria);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Carreta Atualizados
        /// </summary>
        /// <param name="dataBase">Data atualização</param>
        /// <param name="idsEmpresa"></param>
        /// <returns>IQueryable de TipoCarreta</returns>
        public IEnumerable<TipoCarreta> GetRegistrosAtualizados(DateTime dataBase, List<int> idsEmpresa)
        {
            return _tipoCarretaService.GetIdsAtualizados(dataBase, idsEmpresa);
        }

        /// <summary>
        /// Método utilizado para listar todos os Tipos de Carretas ativos
        /// </summary>
        /// <returns>IQueryable de TipoCarreta</returns>
        public IQueryable<TipoCarreta> All()
        {
            return _tipoCarretaService.All();
        }

        public TipoCarreta GetPorDescricao(string nome, int idEmpresa)
        {
            return _tipoCarretaService.GetPorDescricao(nome, idEmpresa);
        }

        public object ConsultaGrid(int? idEmpresa, int? idTipoCarreta, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _tipoCarretaService.ConsultaGrid(idEmpresa, idTipoCarreta, descricao, take, page, orderFilters, filters);
        }
    }
}
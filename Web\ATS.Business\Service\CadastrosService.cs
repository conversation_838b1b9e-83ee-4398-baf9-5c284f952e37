using System.Collections.Generic;
using ATS.Data.Repository.External.SistemaInfo.Cadastro;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Cadastros.ApiClient;

namespace ATS.Domain.Service
{
    public class CadastrosService : ICadastrosService
    {
        private readonly CadastroExternalRepository repository;
        public CadastrosService()
        {
            repository = new CadastroExternalRepository();
        }
        public ConsultarBancoResponseDTO Bancos()
        {
            var bancos = repository.Bancos();
            
            return Mapper.Map<ConsultarBancoResponseDTO>(bancos);
        }

        public List<ConsultarNaturezaCargaResponse> GetNaturezasCarga()
        {
            return repository.GetNaturezasCarga();
        }
    }
}
namespace ATS.Domain.DTO.Ciot
{
    public class DadosCiotV2Dto
    {
        public ContratanteCiotV2Dto Contratante { get; set; }
        public RemetenteCiotV2Dto Remetente { get; set; }
        public DestinatarioCiotV2Dto Destinatario { get; set; }
    }
    
    public class ContratanteCiotV2Dto : DadosBaseCiotV2Dto
    {
        public string CNPJ { get; set; }
        public string Telefone { get; set; }
    }

    public class RemetenteCiotV2Dto : DadosBaseCiotV2Dto
    {
        public string CNPJCPF { get; set; }
    }
    
    public class DestinatarioCiotV2Dto : DadosBaseCiotV2Dto
    {
        public string CNPJCPF { get; set; }
    }
    
    public class DadosBaseCiotV2Dto
    {
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public int? IBGE { get; set; }
        public string CEP { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public int? Numero { get; set; }
        public string Celular { get; set; }
    }
}
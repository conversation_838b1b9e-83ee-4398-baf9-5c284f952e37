﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using Newtonsoft.Json;

namespace ATS.Domain.Models.Protocolo
{
    public class ProtocoloConsultaModel
    {
        public int IdProtocolo { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public string CpfUsuarioAprovacao { get; set; }
        public decimal ValorProtocolo { get; set; }
        public string DataGeracao { get; set; }
        public string DataPagamento { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public bool Processado { get; set; }
        public int StatusProtocolo { get; set; }
        public string DescricaoStatusProtocolo { get; set; }
        public List<ProtocoloEventoConsultaModel> Eventos { get; set; }
        public List<ProtocoloAnexoConsultaModel> Anexos { get; set; }
        public List<ProtocoloAntecipacaoConsultaModel> Antecipacoes { get; set; }
    }

    public class ProtocoloEventoConsultaModel
    {
        public int IdProtocoloEvento { get; set; }
        public int IdProtocolo { get; set; }
        public int IdViagemEvento { get; set; }
        public int? IdViagem { get; set; }
        public int? IdMotivo { get; set; }
        public EStatusProtocoloEvento Status { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public string DataHoraPagamento { get; set; }
        public string NumeroRecibo { get; set; }
        
        /// <summary>
        /// Peso de chegada corrigido pela triagem
        /// </summary>
        public decimal? PesoChegada { get; set; }
        
        /// <summary>
        /// Diferença do peso de chegada corrigido pela triagem
        /// </summary>
        public decimal? PesoDiferenca { get; set; }
        
        /// <summary>
        /// Peso de chegada informado pelo posto
        /// </summary>
        public decimal? PesoChegadaPosto { get; set; }
        
        /// <summary>
        /// Diferença do peso de chegada informado pelo posto
        /// </summary>
        public decimal? PesoDiferencaPosto { get; set; }
        
        public decimal? ValorDifFreteMotorista { get; set; }
        public decimal? ValorQuebraMercadoria { get; set; }
        public decimal? ValorQuebraAbonada { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DescricaoMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ObservacaoDesconto { get; set; }
        
        public bool HabilitarPagamentoCartao { get; set; }
        
        public string DataDescarga { get; set; }
    }

    public class ProtocoloAnexoConsultaModel
    {
        public int IdProtocoloAnexo { get; set; }
        public int IdProtocolo { get; set; }
        public int IdDocumento { get; set; }
        public string Token { get; set; }
    }

    public class ProtocoloAntecipacaoConsultaModel
    {
        public int IdProtocoloAntecipacao { get; set; }
        public int IdProtocolo { get; set; }
        public EStatusProtocoloAntecipacao Status { get; set; }
        public string DataSolicitacao { get; set; }
        public decimal ValorPagamentoAntecipado { get; set; }
        public int? IdMotivo { get; set; }
    }
}